package scripts

import (
	"strconv"
	"strings"
)

func DedecmsBakeUpFileFound(args *ScriptScanArgs) (*ScriptScanResult, error) {
	bakFileUriList := []string{
		"/data/backupdata/dede_h~", "/data/backupdata/dede_m~", "/data/backupdata/dede_p~",
		"/data/backupdata/dede_a~", "/data/backupdata/dede_s~"}
	for _, bakUri := range bakFileUriList{
		tmpUrl := constructURL(args, bakUri)
		for i := 1; i < 6; i++ {
			targetUrl := tmpUrl + strconv.Itoa(i) + ".txt"
			statusCode, respBody, err := httpGet(targetUrl)
			if err != nil {
				return &invulnerableResult, err
			}
			if statusCode == 200 {
				if strings.Contains(string(respBody),"admin") || strings.Contains(string(respBody),"INSERT INTO"){
					return &ScriptScanResult{
						Vulnerable: true,
						Output:     targetUrl,
						Body:       respBody,
					}, nil
				}
			}
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("dedecms_backup_file_found.xml", DedecmsBakeUpFileFound)
}
