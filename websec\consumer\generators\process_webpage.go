package generators

import (
	"encoding/json"
	"errors"
	"fmt"
	"mime"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"websec/common"
	"websec/common/consts"
	"websec/common/resource"
	"websec/common/schema"
	"websec/detect"
	"websec/utils"
	"websec/utils/log"
	"websec/utils/stream"
)

var XApiKey string

type TotalLinkDetail struct {
	TotalFail  int64 `json:"totalFail"`
	TotalLinks int64 `json:"totalLinks"`
}

func (generator *Generator) newDetectOptions() *detect.Options {
	op := new(detect.Options)
	op.SensitiveMatcher = resource.DefaultSensitiveWordsMatcher()
	op.BlackMatcher = resource.DefaultBlackWordsMatcher()
	op.FghkOcrMatcher = detect.NewFghkMatcher()
	op.JiebaFenCi = resource.DefaultJiebaFC()
	op.IgnoredBlackLinkDomains = resource.DefaultIgnoredBlackLinkDomains()
	op.SafeBrowsingURL = generator.settings.SafeBrowsingURL
	op.JSUnpackPath = generator.settings.JSUnpackPath
	log.Infoln("init trojan detecter", generator.settings.TrojanRulePath)
	op.TrojanDetecter, _ = detect.NewTrojanDetecter(generator.settings.TrojanRulePath)
	op.OcrAPIAddress = generator.ocrPath

	wangdunBlackLinkApi := generator.settings.WangDunBlackLinkDetecterURL
	if len(wangdunBlackLinkApi) > 0 {
		op.WangdunBlackLinkDetecter = detect.NewWangBlackLinkDetecter(wangdunBlackLinkApi)
	}

	return op
}

func (generator *Generator) specialDetectOptions(options *detect.Options, asset *schema.Asset, host string) {
	for _, v := range asset.MonitorTypes {
		switch v {
		case consts.MonitorTypeSensitiveWord:
			options.Operation |= detect.CheckSensitiveWords
		case consts.MonitorTypeContentChange:
			options.Operation |= detect.CheckPageChange
		case consts.MonitorTypeTrojan:
			options.Operation |= detect.CheckTrojan
		case consts.MonitorTypeBlackLink:
			options.Operation |= detect.CheckBlackWords
		case consts.MonitorTypeVul:
			options.Operation |= detect.CheckVuls
		}
	}
	if asset.Options.SensitiveWord.CheckImage {
		options.Operation |= detect.CheckImage
	}
	if asset.Options.SensitiveWord.UseSensitiveWordGroup {
		options.SensitiveMatcherGroups = resource.GetSensitiveWordMatcherGroups()
	}

	if len(asset.Options.SensitiveWord.Customized) > 0 {
		options.SensitiveMatcher = generator.sMatcher.GetCustomizedSensitiveWordMatcher(host,
			asset.Options.SensitiveWord.Customized)
	} else {
		options.SensitiveMatcher = resource.DefaultSensitiveWordsMatcher()
	}

	if len(asset.Options.BlackLink.Customized) > 0 {
		options.BlackMatcher = generator.bMatcher.GetCustomizedBlackWordMatcher(host,
			asset.Options.BlackLink.Customized)
	} else {
		options.BlackMatcher = resource.DefaultBlackWordsMatcher()
	}
}

func (generator *Generator) processWebPage(msg *stream.Message) error {
	var page = new(common.Webpage)
	err := json.Unmarshal(msg.Value, page)
	if err != nil {
		log.Errorln("Unmarshal failed:", err)
		return err
	}

	if strings.TrimSpace(page.URL) == "" {
		return nil
	}

	parts, err := url.Parse(page.URL)

	if err != nil {
		return errors.New("host is empty string")
	}

	page.Host = utils.HostFilter(parts.Scheme, parts.Host)

	detectOptions := generator.newDetectOptions()
	detectOptions.Domain = parts.Hostname()

	asset, err := generator.dbConnection.GetAssetByID(page.AssetID, true)
	if err != nil {
		log.Errorf("GetAssetByID err:%v host:%s", page.AssetID, page.Host, err)
		return err
	}

	assetID := asset.ID.Hex()
	generator.checkNewURL(page)

	detectOptions.AssetID = assetID
	detectOptions.JobID = page.JobID

	contentType := strings.ToLower(page.Headers.Get("Content-Type"))
	if contentType != "" {
		contentType = strings.Split(contentType, ";")[0]
		if _, ok := ContentJS[contentType]; ok {
			log.Infoln("need to check jsonp url:", page.URL)
			result, err := CheckSenseJsonp(page.URL)
			if err != nil {
				log.Errorln("check jsonp url err", page.URL, err)
			} else {
				if result {
					vul := &schema.FoundVulDoc{
						Host: page.Host,
						Vul: schema.FoundVulsVul{
							VulXML:   consts.JsonpXML,
							VulURL:   page.URL,
							Severity: consts.VulMiddle,
							From:     page.Host,
						},
						FoundAt: page.CrawledAt,
					}
					vul.AssetID = assetID
					vul.JobID = page.JobID
					generator.addVul(vul)
				}
			}
		}
	}

	generator.specialDetectOptions(detectOptions, asset, page.Host)

	newDetect, err := detect.NewDetecter(detectOptions)
	if err != nil {
		log.Error(err)
		return err
	}

	dResult, err := newDetect.DetectV1(page)
	if err != nil {
		log.Errorln("detect error page.url:", page.URL, "err:", err)
		return err
	}

	log.Infoln("detect finish ", page.URL, " isNew:", page.IsNew, " newURL:", generator.newURL, "page.Depth:", page.Depth, "MaxDepth:", asset.Options.MaxDepth)

	versionTime, contentChangeResult := generator.checkPageChanges(page, asset, detectOptions.BlackMatcher)
	for i := range contentChangeResult {
		result := contentChangeResult[i]
		generator.addRawContentChange(result)
	}

	var wg sync.WaitGroup

	if page.Depth < int32(page.ExternalScanDepth) {
		wg.Add(1)
		go func() {
			defer wg.Done()
			_, outerContentChangeResult := generator.checkOuterPageChanges(page, asset, detectOptions.BlackMatcher)
			for i := range outerContentChangeResult {
				result := outerContentChangeResult[i]
				generator.addRawContentChange(result)
			}
		}()
	}

	wg.Add(1)
	go func() {
		defer wg.Done()
		imgChangeResult := generator.getImageDiff(page, asset, versionTime)
		for i := range imgChangeResult {
			result := imgChangeResult[i]
			generator.addRawContentChange(result)
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		rawWordResult := generator.getSensitiveResult(page, dResult, asset.Options.SensitiveWord.Customized, versionTime)
		log.Infoln("------ Processing sensitive words for", page.URL, "count:", len(rawWordResult))
		for _, v := range rawWordResult {
			v.AssetID = assetID
			v.JobID = page.JobID
			generator.addRawSensitiveWord(v)
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		generator.getOcrSensitiveWordResult(page, dResult, asset.Options.SensitiveWord.Customized)
		log.Infoln("-------- detection of sensitive words complete for ", page.URL)
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		rawBlackListResult := generator.getBlackListResult(page, dResult, versionTime)
		if rawBlackListResult != nil {
			rawBlackListResult.AssetID = assetID
			generator.addRawBlackLink(rawBlackListResult)
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		trojanResults := getTrojanResult(page, dResult)
		if len(trojanResults) > 0 {
			for _, trojanResult := range trojanResults {
				trojanResult.AssetID = assetID
				trojanResult.JobID = page.JobID
				generator.addRawTrojan(trojanResult)
			}
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		vulResults := getVulResult(page, dResult)
		for _, v := range vulResults {
			v.AssetID = assetID
			v.JobID = page.JobID
			generator.addVul(v)
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		generator.checkNetworkWebPageChange(page)
	}()

	wg.Wait()

	if page.SaveToMongo && versionTime == page.VersionTime() {
		hotPageArchive := &schema.FoundPageArchiveDoc{
			AssetID:        page.AssetID,
			JobID:          page.JobID,
			Host:           page.Host,
			URL:            page.URL,
			URLHash:        page.URLHash(),
			MainFrameURL:   page.MainFrameURL,
			StatusCode:     int16(page.StatusCode),
			Header:         page.Headers,
			VersionTime:    page.VersionTime(),
			OldVersionTime: page.OldVersionTime,
			ContentHash:    page.ContentHash(),
			Content:        page.Content,
		}

		generator.addHotPageArchive(hotPageArchive)
	}

	return nil
}

func (generator *Generator) checkNewURL(page *common.Webpage) error {
	if !generator.newURL {
		return nil
	}

	if !page.IsNew {
		return nil
	}

	urlHash := page.URLHash()

	newURLs := &schema.FoundNewURLs{URLs: []*schema.SiteURL{}}

	var newURL *schema.SiteURL

	if !generator.urlFilter(page.Host, page.Method, page.URL) {

		newURL = generateSiteURL(page.URL, page.Referer, page.Method, page.Headers, page.Depth, page.Data)

		if newURL != nil {
			newURL.AssetID = page.AssetID
			newURL.JobID = page.JobID
			newURL.ReferURL = page.Referer
			newURL.RefererHost = page.Host
			newURL.RefererURLHash = urlHash
			newURLs.URLs = append(newURLs.URLs, newURL)
		}
	}

	for _, v := range page.NetworkWebpages {
		if strings.TrimSpace(v.URL) == "" {
			continue
		}

		host := page.Host
		if v.IsOuterURL {
			parts, err := url.Parse(v.URL)
			if err != nil {
				continue
			}
			host = parts.Host
		}

		if !generator.urlFilter(host, v.Method, v.URL) {
			newURL = generateSiteURL(v.URL, page.URL, v.Method, v.Headers, page.Depth+1, v.Data)
			if newURL != nil {
				newURL.AssetID = page.AssetID
				newURL.JobID = page.JobID
				newURL.ReferURL = page.URL
				newURL.RefererHost = page.Host
				newURL.RefererURLHash = urlHash
				newURLs.URLs = append(newURLs.URLs, newURL)
			}
		}
	}

	for _, outer := range page.OuterWebpages {
		for _, v := range outer.Frames {
			if strings.TrimSpace(v.URL) == "" {
				continue
			}

			parts, err := url.Parse(v.URL)
			if err != nil {
				log.Errorln(err)
				continue
			}

			if !generator.urlFilter(parts.Host, http.MethodGet, v.URL) {
				newURL = generateSiteURL(v.URL, page.URL, http.MethodGet, outer.Headers, page.Depth+1, "")
				if newURL != nil {
					newURL.AssetID = page.AssetID
					newURL.JobID = page.JobID
					newURL.ReferURL = page.URL
					newURL.RefererHost = page.Host
					newURL.RefererURLHash = urlHash
					newURLs.URLs = append(newURLs.URLs, newURL)
				}
			}
		}
	}

	log.Debugln("-------- found urls count", len(newURLs.URLs), " -- by URL ", page.URL)

	if len(newURLs.URLs) > 0 {
		generator.addFoundNewURL(newURLs)

		urls := ""
		for _, v := range newURLs.URLs {
			urls += v.URL + "  "
		}
		log.Debugln("-------- found urls: ", urls)
	}

	return nil
}

func (generator *Generator) urlFilter(host, method, pageURL string) bool {
	urlSum := resource.GetHostURLSum(host)
	if urlSum != nil {
		res := generator.dbConnection.BloomFilter(fmt.Sprintf("%s{%s}", host, host),
			method+pageURL, urlSum.Sum, urlSum.Precision)
		if res {
			// TODO tempprary disabled
			return false
		}
	}

	return false
}

func generateSiteURL(rawURL, referURL, method string, headers http.Header, depth int32, postData string) *schema.SiteURL {
	if rawURL == referURL {
		return nil
	}

	parts, err := url.Parse(rawURL)
	if err != nil {
		log.Errorf("failed to parse url:%s err:%v", rawURL, err)
		return nil
	}

	if parts.Host == "" {
		log.Warnln("host is empty ", rawURL)
		return nil
	}

	methods := consts.GetMethodBit(method)
	if methods == 0 {
		log.Errorln("method is 0 ", rawURL, method)
		return nil
	}

	contentType := headers.Get("Content-Type")
	if contentType == "" {
		contentType = headers.Get("Content-Disposition")
	}
	mediaType, _, err := mime.ParseMediaType(contentType)
	if err != nil {
		log.Warnln("failed to parse media type:", contentType)
		mediaType = ""
	}
	return &schema.SiteURL{
		Methods:   consts.GetMethodBit(method),
		Scheme:    parts.Scheme,
		Host:      parts.Host,
		URI:       parts.RequestURI(),
		URL:       rawURL,
		ReferURL:  referURL,
		URLHash:   common.URLSha1(rawURL),
		Depth:     depth,
		MediaType: mediaType,
		PostData:  postData,
	}
}
