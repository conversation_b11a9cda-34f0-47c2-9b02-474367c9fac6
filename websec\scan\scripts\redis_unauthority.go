package scripts

import (
	"bytes"
	"net"
	"time"
)

func RedisUnauthority(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addr := args.Host + ":6379"
	conn, err := net.DialTimeout("tcp", addr, time.Second*3)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	payload := []byte("\x2a\x31\x0d\x0a\x24\x34\x0d\x0a\x69\x6e\x66\x6f\x0d\x0a")
	conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
	conn.Write(payload)
	response := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(time.Second * 5))
	_, err = conn.Read(response)
	if err != nil {
		return nil, err
	}
	if bytes.Contains(response, []byte("redis_version")) {
		return &ScriptScanResult{Vulnerable: true, Output: addr, Body: response}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("redis_unauthority.xml", RedisUnauthority)
}
