package utils

import (
	"fmt"
)

const initSize int = 50

type Stack struct {
	size int

	top int

	data []rune
}

func CreateStack() Stack {
	s := Stack{}
	s.size = initSize
	s.top = -1
	s.data = make([]rune, initSize)
	return s
}

func (s *Stack) IsEmpty() bool {
	return s.top == -1
}

func (s *Stack) GetTop() rune {
	if s.IsEmpty() {
		fmt.Println("stack is empty , pop error")
		return rune(' ')
	}
	return s.data[s.top]
}

func (s *Stack) IsFull() bool {
	return s.top == s.size-1
}

func (s *Stack) Push(data rune) bool {

	if s.IsFull() {
		fmt.Println("stack is full, push failed")
		return false
	}

	s.top++

	s.data[s.top] = data
	return true
}

func (s *Stack) GetData() []rune {
	return s.data
}

func (s *Stack) Pop() rune {

	if s.IsEmpty() {
		fmt.Println("stack is empty , pop error")
		return rune(' ')
	}

	tmp := s.data[s.top]

	s.top--
	return tmp
}

func (s *Stack) GetLength() int {
	length := s.top + 1
	return length
}

func (s *Stack) Clear() {
	s.top = -1
}

func (s *Stack) Traverse() {

	if s.IsEmpty() {
		fmt.Println("stack is empty")
	}

	for i := 0; i <= s.top; i++ {
		fmt.Println(s.data[i], " ")
	}
}

func (s *Stack) PrintInfo() {
	fmt.Println("栈容量：", s.size)
	fmt.Println("栈顶指针：", s.top)
	fmt.Println("栈元素长度:", s.GetLength())
	fmt.Println("栈是否为空:", s.IsEmpty())
	fmt.Println("栈是否已满:", s.IsFull())
	fmt.Println("========遍历======")
	s.Traverse()
	fmt.Println("=========遍历结束========")
}
