package main

import (
	"net"
	"net/http"
	_ "net/http/pprof" // Import for side effects: registers pprof handlers
	"strings"

	// For os.Exit if log.Fatalf is not available/suitable
	log "websec/utils/log" // Use the application's logger
)

// StartPProfServer starts the pprof HTTP server on the specified address.
// It performs a pre-check to see if the port is available.
func StartPProfServer(addr string) {
	log.Infof("Checking if pprof server port %s is available...", addr)

	// Pre-check: Try to listen on the port.
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		if strings.Contains(err.Error(), "address already in use") {
			log.Infof("PProf server port %s is already in use, likely by another service instance. This instance will not start a pprof server.", addr)
		} else {
			log.Errorf("Failed to pre-check (net.Listen) for pprof server port %s: %v. This instance will not start a pprof server.", addr, err)
		}
		return // Do not proceed if pre-check fails
	}

	// If we got here, net.Listen succeeded. Close the listener immediately as we only used it for the check.
	log.Infof("PProf server port %s appears to be available. Closing pre-check listener and attempting to start HTTP server.", addr)
	listener.Close()

	// Now, attempt to start the actual HTTP server for pprof.
	log.Infof("Attempting to start pprof HTTP server on %s.", addr)
	err = http.ListenAndServe(addr, nil)
	if err != nil {
		// This error is less likely to be "address already in use" now, but handle other potential errors.
		log.Errorf("PProf HTTP server failed to start on %s after successful pre-check: %v. This instance will not have pprof.", addr, err)
	}
	// If err was nil, http.ListenAndServe is now running and blocking this goroutine.
	// If err was not nil, the goroutine will exit, and the main application continues.
}
