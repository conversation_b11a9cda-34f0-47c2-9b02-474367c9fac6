package api

import (
	"context"
	"encoding/json"
	"net/http"
	"os/exec"
	"strings"
	"time"
	"websec/common/consts"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ThesaurusWord struct {
	ID        primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	Status    int                `json:"status" bson:"status"`
	Type      string             `json:"type" bson:"type"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	Word      string             `json:"word" bson:"word"`
	Strength  int                `json:"strength,omitempty" bson:"strength,omitempty"`
}

type sensitiveThesaurusWord struct {
	Word     string `json:"word"`
	Strength int    `json:"strength"`
}

func (api *API) blackwordsHandler(rw http.ResponseWriter, req *http.Request) {
	method := req.Method
	switch method {
	case http.MethodGet:
		api.blackwordsGetHandler(rw, req)
	case http.MethodPost:
		api.blackwordsPostHandler(rw, req)
	case http.MethodDelete:
		api.blackwordsDeleteHandler(rw, req)
	default:
		api.writeResponse(rw, newFailResponse("method not allowed"))
	}
}

func (api *API) blackwordsGetHandler(rw http.ResponseWriter, req *http.Request) {
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cursor, err := mdb.Collection(consts.CollectionBlackSensitiveWords).Find(
		ctx, bson.M{"type": "blackwords", "status": 0})

	if err != nil {
		log.Errorln("get blackwords error:", err)
		api.writeResponse(rw, newFailResponse("get blackwords error."))
		return
	}

	var words []string
	for cursor.Next(ctx) {
		var doc ThesaurusWord
		err := cursor.Decode(&doc)
		if err != nil {
			log.Errorln(err)
			continue
		}
		words = append(words, doc.Word)
	}

	rw.Header().Set("Access-Control-Allow-Origin", "*")
	data, _ := json.Marshal(words)
	_, err = rw.Write(data)
	if err != nil {
		log.Errorln(err)
	}
}

func (api *API) blackwordsPostHandler(rw http.ResponseWriter, req *http.Request) {
	reqData := []string{}
	err := json.Unmarshal(getHttpBody(req), &reqData)
	if err != nil {
		log.Errorln("get request body error: ", err)
		api.writeResponse(rw, newFailResponse("get request body error"))
		return
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	for _, word := range reqData {
		var existsWord ThesaurusWord
		err := mdb.Collection(consts.CollectionBlackSensitiveWords).FindOne(
			ctx, bson.M{"type": "blackwords", "word": word}).Decode(&existsWord)
		if err != nil {
			log.Errorln(err)
		}

		if existsWord.Word != "" {
			result, err := mdb.Collection(consts.CollectionBlackSensitiveWords).UpdateOne(
				ctx, bson.M{"type": "blackwords", "word": word},
				bson.M{"$set": bson.M{"status": 0}},
			)
			if err != nil {
				log.Errorln(err)
				continue
			}
			log.Debugf("%v %v", word, result.ModifiedCount)
		} else {
			newWord := ThesaurusWord{
				Type:      "blackwords",
				Status:    0,
				Word:      word,
				CreatedAt: time.Now(),
			}
			_, err := mdb.Collection(consts.CollectionBlackSensitiveWords).InsertOne(
				ctx, &newWord)
			if err != nil {
				log.Errorln(err)
				continue
			}
		}
	}

	api.writeResponse(rw, newSuccessResponse("success"))
}

func (api *API) blackwordsDeleteHandler(rw http.ResponseWriter, req *http.Request) {
	reqData := []string{}
	err := json.Unmarshal(getHttpBody(req), &reqData)
	if err != nil {
		log.Errorln("get request body error: ", err)
		api.writeResponse(rw, newFailResponse("get request body error"))
		return
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err = mdb.Collection(consts.CollectionBlackSensitiveWords).UpdateMany(
		ctx,
		bson.M{"type": "blackwords", "word": bson.M{"$in": reqData}},
		bson.M{"$set": bson.M{"status": 1}})

	if err != nil {
		log.Errorln(err)
		api.writeResponse(rw, newFailResponse("delete blackwords error"))
		return
	}

	api.writeResponse(rw, newSuccessResponse("success"))
}

func (api *API) sensitiveWordsHandler(rw http.ResponseWriter, req *http.Request) {
	method := req.Method
	switch method {
	case http.MethodGet:
		api.sensitiveWordsGetHandler(rw, req)
	case http.MethodPost:
		api.sensitiveWordsPostHandler(rw, req)
	case http.MethodDelete:
		api.sensitiveWordsDeleteHandler(rw, req)
	case http.MethodPatch:
		api.sensitiveWordsPatchHandler(rw, req)
	default:
		api.writeResponse(rw, newFailResponse("method not allowed"))
	}
}

func (api *API) sensitiveWordsGetHandler(rw http.ResponseWriter, req *http.Request) {
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cursor, err := mdb.Collection(consts.CollectionBlackSensitiveWords).Find(
		ctx, bson.M{"type": "sensitivewords", "status": 0})

	if err != nil {
		log.Errorln("get blackwords error:", err)
		api.writeResponse(rw, newFailResponse("get blackwords error."))
		return
	}

	var words []*sensitiveThesaurusWord
	for cursor.Next(ctx) {
		var doc ThesaurusWord
		err := cursor.Decode(&doc)
		if err != nil {
			log.Errorln(err, doc)
			continue
		}
		tmpWord := &sensitiveThesaurusWord{
			Word:     doc.Word,
			Strength: doc.Strength,
		}
		words = append(words, tmpWord)
	}

	data, _ := json.Marshal(words)
	_, err = rw.Write(data)
	if err != nil {
		log.Errorln(err)
	}
}

func (api *API) sensitiveWordsPostHandler(rw http.ResponseWriter, req *http.Request) {
	reqData := []sensitiveThesaurusWord{}
	err := json.Unmarshal(getHttpBody(req), &reqData)
	if err != nil {
		log.Errorln("get request body error: ", err)
		api.writeResponse(rw, newFailResponse("get request body error"))
		return
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	for _, word := range reqData {
		var existsWord ThesaurusWord
		err := mdb.Collection(consts.CollectionBlackSensitiveWords).FindOne(
			ctx, bson.M{"type": "sensitivewords", "word": word.Word}).Decode(&existsWord)
		if err != nil {
			log.Errorln(err)
		}

		if existsWord.Word != "" {
			result, err := mdb.Collection(consts.CollectionBlackSensitiveWords).UpdateOne(
				ctx, bson.M{"type": "sensitivewords", "word": word.Word},
				bson.M{"$set": bson.M{"status": 0, "strength": word.Strength}},
			)
			if err != nil {
				log.Errorln(err)
				continue
			}
			log.Debugf("%v %v", word, result.ModifiedCount)
		} else {
			newWord := ThesaurusWord{
				Type:      "sensitivewords",
				Status:    0,
				Word:      word.Word,
				Strength:  word.Strength,
				CreatedAt: time.Now(),
			}
			_, err := mdb.Collection(consts.CollectionBlackSensitiveWords).InsertOne(
				ctx, &newWord)
			if err != nil {
				log.Errorln(err)
				continue
			}
		}
	}

	api.writeResponse(rw, newSuccessResponse("success"))

}

func (api *API) sensitiveWordsPatchHandler(rw http.ResponseWriter, req *http.Request) {
	reqData := []sensitiveThesaurusWord{}
	err := json.Unmarshal(getHttpBody(req), &reqData)
	if err != nil {
		log.Errorln("get request body error: ", err)
		api.writeResponse(rw, newFailResponse("get request body error"))
		return
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	for _, word := range reqData {
		_, err := mdb.Collection(consts.CollectionBlackSensitiveWords).UpdateOne(
			ctx,
			bson.M{"type": "sensitivewords", "word": word.Word},
			bson.M{"$set": bson.M{"strength": word.Strength}},
		)

		if err != nil {
			log.Errorln(err)
			continue
		}
	}

	api.writeResponse(rw, newSuccessResponse("success"))
}

func (api *API) sensitiveWordsDeleteHandler(rw http.ResponseWriter, req *http.Request) {
	reqData := []string{}
	err := json.Unmarshal(getHttpBody(req), &reqData)
	if err != nil {
		log.Errorln("get request body error: ", err)
		api.writeResponse(rw, newFailResponse("get request body error"))
		return
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err = mdb.Collection(consts.CollectionBlackSensitiveWords).UpdateMany(
		ctx,
		bson.M{"type": "sensitivewords", "word": bson.M{"$in": reqData}},
		bson.M{"$set": bson.M{"status": 1}})

	if err != nil {
		log.Errorln(err)
		api.writeResponse(rw, newFailResponse("delete sensitvewords error"))
		return
	}

	api.writeResponse(rw, newSuccessResponse("success"))
}

func (api *API) reloadSensitiveWords(rw http.ResponseWriter, req *http.Request) {
	reqData := map[string]string{}
	err := json.Unmarshal(getHttpBody(req), &reqData)
	if err != nil {
		log.Errorln("get request body error: ", err)
		api.writeResponse(rw, newFailResponse("get request body error"))
		return
	}

	_, err = restartGeneratorService()

	if err != nil {
		api.writeResponse(rw, newFailResponse("fail"))
	}

	api.writeResponse(rw, newSuccessResponse("success"))
}

func restartGeneratorService() (string, error) {
	cmd := exec.Command("supervisorctl", "restart", "engine:generator")
	out, err := cmd.CombinedOutput()
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(out)), nil
}
