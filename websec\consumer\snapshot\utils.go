package snapshot

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"time"
	"websec/utils/log"
)

var headRe = regexp.MustCompile(`(?i)<head( \s*[^>]*\s*>|\s*>)`)
var htmlRe = regexp.MustCompile(`(?i)<html( \s*[^>]*\s*>|\s*>)`)
var baseRe = regexp.MustCompile(`(?i)<base\s+href\s*=\s*["']([^"']*)["']`)
var linkRe = regexp.MustCompile(`(?i)<[^>]*\s+(?:href|src)\s*=\s*["']([^"']*)["'][^>]*>`)

var scriptPat = regexp.MustCompile(`(?Uis)<script( \s*[^>]*\s*>|\s*>).*<\/script>`)

// styleLinkPat matches any <link> tag that contains an href attribute.
// (?i) makes the match case-insensitive.
// (?s) makes the dot (.) match newline characters as well.
// [^>]*? lazily matches any characters until the next part of the pattern.
// This pattern will match tags like: <link rel="stylesheet" href="main.css">
var styleLinkPat = regexp.MustCompile(`(?is)<link[^>]*?href\s*=\s*["'][^"']*["'][^>]*?>`)

func EnsureCharsetUTF8(page []byte) []byte {
	if !htmlRe.Match(page) {
		var pageBuf bytes.Buffer
		pageBuf.Write([]byte("<html>"))
		pageBuf.Write(page)
		pageBuf.Write([]byte("</html>"))
		page = pageBuf.Bytes()
	}

	var newPage []byte
	if headRe.Match(page) {
		newPage = headRe.ReplaceAll(page, []byte(`$0<meta http-equiv="Content-Type" content="text/html; charset=utf8" />`))
	} else {
		newPage = htmlRe.ReplaceAll(page, []byte(`$0<head><meta http-equiv="Content-Type" content="text/html; charset=utf8" /></head>`))
	}
	return newPage
}

func CreateFile(data []byte, dir string) (string, error) {
	pattern := "page_*.html"
	tmpFile, err := ioutil.TempFile(dir, pattern)
	if err != nil {
		log.Error(err)
		return "", err
	}

	if _, err := tmpFile.Write(data); err != nil {
		log.Error(err)
		os.Remove(tmpFile.Name())
		return "", err
	}
	if err := tmpFile.Close(); err != nil {
		log.Error(err)
		os.Remove(tmpFile.Name())
		return "", err
	}
	return tmpFile.Name(), nil
}

func CreateTempDir(prefix string) (string, error) {
	dir := TempDir + "/" + prefix + GetDate()
	exist, _ := PathExists(dir)
	if !exist {
		err := os.Mkdir(dir, os.ModePerm)
		if err != nil {
			return "", err
		}
	}
	return dir, nil
}

func GetDate() string {
	now := time.Now()
	return fmt.Sprintf("%d%02d%02d", now.Year(), now.Month(), now.Day())
}

func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func RemoveScriptFromPage(page []byte) []byte {
	return scriptPat.ReplaceAll(page, []byte(""))
}

func linkReplFunc(URL []byte) (func([]byte) []byte, error) {
	baseRelURL, err := url.Parse(string(URL))
	if err != nil {
		log.Errorf("Failed to parse url %s", URL)
		return nil, err
	}

	f := func(link []byte) []byte {
		lowerLink := bytes.ToLower(link)
		if bytes.HasPrefix(lowerLink, []byte("http://")) ||
			bytes.HasPrefix(lowerLink, []byte("https://")) ||
			bytes.HasPrefix(lowerLink, []byte("//")) ||
			bytes.HasPrefix(lowerLink, []byte("#")) ||
			bytes.HasPrefix(lowerLink, []byte("mailto:")) ||
			bytes.HasPrefix(lowerLink, []byte("data:")) {
			return link
		}
		linkURL, err := url.Parse(string(link))
		if err != nil {
			return link
		}
		return []byte(baseRelURL.ResolveReference(linkURL).String())
	}

	return f, nil
}

func replaceAllSubmatchFunc(re *regexp.Regexp, in []byte, repl func([]byte) []byte) []byte {
	allIndex := re.FindAllSubmatchIndex(in, -1)
	if allIndex == nil {
		return in
	}

	var out bytes.Buffer
	start := 0
	end := 0
	for _, loc := range allIndex {
		end = loc[2]
		out.Write(in[start:end])
		start = end
		end = loc[3]
		repled := repl(in[start:end])
		out.Write(repled)
		start = end
	}
	out.Write(in[end:])
	return out.Bytes()
}

func MakeLinksAbsolute(in, URL []byte) []byte {
	found := baseRe.Find(in)
	if found != nil {
		return in
	}
	f, err := linkReplFunc(URL)
	if err != nil {
		return in
	}
	return replaceAllSubmatchFunc(linkRe, in, f)
}

func RemoveScriptTagFromFile(fipath string) (string, error) {
	data, err := ioutil.ReadFile(fipath)
	if err != nil { //if failed, just return original file path
		return fipath, err
	}

	newData := RemoveScriptFromPage(data)
	dir := filepath.Dir(fipath)
	fopath := filepath.Join(dir, "without_js_"+filepath.Base(fipath))

	err = ioutil.WriteFile(fopath, newData, 0644)
	if err != nil {
		return fipath, err
	}
	return fopath, nil
}

// RemoveStyleLinkFromPage removes all <link> tags that contain an href attribute from the page.
func RemoveStyleLinkFromPage(page []byte) []byte {
	return styleLinkPat.ReplaceAll(page, []byte(""))
}

func RemoveStyleLinkTagFromFile(filePath string) (string, error) {
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return filePath, err
	}
	newData := RemoveStyleLinkFromPage(data)

	dir := filepath.Dir(filePath)
	outputPath := filepath.Join(dir, "without_stylesheet_"+filepath.Base(filePath))

	err = ioutil.WriteFile(outputPath, newData, 0644)
	if err != nil {
		return filePath, err
	}
	return outputPath, nil
}
