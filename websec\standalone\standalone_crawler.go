package standalone

import (
	"context"
	"math"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"
	"websec/common"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/crawl"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/stream"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Options struct {
	Producer           *stream.Producer
	DBConnection       *db.DBConnection
	Concurrency        int64
	RequestConcurrency int64
	ChromeHost         string
	ChromePort         uint16
	UserAgent          string
	Depth              int32
	Cycle              float64
	ScheduleTag        string
	AssetOffset        int64
}

type StandaloneCrawler struct {
	options      *Options
	producer     *stream.Producer
	dbConnection *db.DBConnection
	sema         *semaphore.Weighted
	ctx          context.Context
	isCanceld    bool
	crawlers     *sync.Map
	wg           sync.WaitGroup
}

func NewStandaloneCrawler(options *Options) (*StandaloneCrawler, error) {
	sdacrawler := &StandaloneCrawler{
		options:      options,
		producer:     options.Producer,
		dbConnection: options.DBConnection,
		sema:         semaphore.NewWeighted(options.Concurrency),
		ctx:          context.TODO(),
		wg:           sync.WaitGroup{},
		crawlers:     &sync.Map{},
	}

	return sdacrawler, nil
}

func (sdacrawler *StandaloneCrawler) Run() {
	sdacrawler.producer.Go()

	setupSignal(sdacrawler.Stop)

	for {
		if sdacrawler.isCanceld {
			break
		}
		startTime := time.Now()
		sdacrawler.process()

		costTime := time.Now().Sub(startTime).Seconds()
		log.Infof("process cost time: %v s", costTime)
		sleepTime := time.Duration(math.Max(sdacrawler.options.Cycle-costTime, 10))
		time.Sleep(sleepTime * time.Second)
	}
}

func (sdacrawler *StandaloneCrawler) Stop() {
	sdacrawler.isCanceld = true
	sdacrawler.crawlers.Range(func(key, value interface{}) bool {
		crawler := value.(crawl.Crawler)
		crawler.Stop()
		return true
	})
}

func (sdacrawler *StandaloneCrawler) processAsset(asset *schema.Asset, isSync bool) {
	log.Infof("start process %v at: %v", asset.Host, time.Now().String())
	var err error
	var linksChan = make(chan *common.Link, 10)

	crawler, err := sdacrawler.createCrawler(asset.ID.Hex(), asset.JobID, asset.Host, linksChan)
	if err != nil {
		log.Errorln(err)
		return
	}

	sdacrawler.crawlers.Store(asset.ID.Hex(), crawler)

	go sdacrawler.watchCrawler(asset.ID.Hex(), crawler, isSync)

	entries := asset.Entries
	for j := range entries {
		rawURL := entries[j]
		link := common.GenLink(rawURL, http.MethodGet, "", "", 0)
		link.Host = asset.Host
		linksChan <- link
	}
	close(linksChan)
}

func (sdacrawler *StandaloneCrawler) process() {
	log.Infof("start process at: %v", time.Now().String())
	var err error
	var assetChan = make(chan *schema.Asset, 10)

	sdacrawler.wg.Add(1)
	go sdacrawler.loadAssets(assetChan)

	for asset := range assetChan {
		if err = sdacrawler.sema.Acquire(sdacrawler.ctx, 1); err != nil {
			log.Errorln(err)
			sdacrawler.processAsset(asset, true)
		} else {
			sdacrawler.wg.Add(1)
			go sdacrawler.processAsset(asset, false)
		}
	}

	sdacrawler.wg.Wait()
	log.Infof("finish process at: %v", time.Now().String())
}

func (sdacrawler *StandaloneCrawler) createCrawler(assetID string, jobID string, host string, linksChan chan *common.Link) (crawl.Crawler, error) {
	semaphores := &sync.Map{}
	semaphores.Store(assetID, &crawl.RequestSemaphore{
		Sema: semaphore.NewWeighted(sdacrawler.options.RequestConcurrency),
		Ctx:  context.TODO(),
	})

	crawlOptions := &crawl.Options{
		ByChrome:              true,
		Host:                  host,
		AssetID:               assetID,
		JobID:                 jobID,
		UserAgent:             sdacrawler.options.UserAgent,
		ChromeHost:            sdacrawler.options.ChromeHost,
		ChromePort:            sdacrawler.options.ChromePort,
		Timeout:               time.Duration(sdacrawler.options.Cycle/2) * time.Second,
		Concurrency:           sdacrawler.options.RequestConcurrency,
		LinksChan:             linksChan,
		ShouldCrawlFoundLinks: true,
		MaxDepth:              sdacrawler.options.Depth,
		Semaphores:            semaphores,
	}
	return crawl.NewCrawler(crawlOptions, nil)
}

func (sdacrawler *StandaloneCrawler) watchCrawler(assetID string, crawler crawl.Crawler, isSync bool) {
	defer sdacrawler.crawlers.Delete(assetID)
	if !isSync {
		defer sdacrawler.sema.Release(1)
		defer sdacrawler.wg.Done()
	}

	crawlChan, err := crawler.Go()
	if err != nil {
		log.Errorln(err)
		return
	}

	for webPage := range crawlChan {
		if webPage.URL == "" || len(webPage.Content) == 0 {
			continue
		}

		log.Infof("webPage %s %d", webPage.URL, webPage.Depth)
		sdacrawler.producer.Produce(consts.TopicCrawledWebpages, webPage)
	}

	log.Infoln("finish watch crawler.")
}

func (sdacrawler *StandaloneCrawler) loadAssets(assetChan chan *schema.Asset) {
	defer func() {
		sdacrawler.wg.Done()
		close(assetChan)
		log.Infof("finish load assets at: %v", time.Now().String())
	}()

	mdb := sdacrawler.dbConnection.GetMongoDatabase()
	ctx, ctxCancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer ctxCancel()

	queryOpts := bson.M{
		"status":               1,
		"options.expired_at":   bson.M{"$gt": time.Now()},
		"options.schedule_tag": sdacrawler.options.ScheduleTag,
	}
	findOpts := options.Find().SetSort(bson.M{"_id": 1})

	if sdacrawler.options.Depth == 0 {
		queryOpts["options.quick_monitor_homepage"] = 5
	} else {
		queryOpts["options.quick_monitor_secondpage"] = 5
		findOpts = findOpts.SetSkip(sdacrawler.options.AssetOffset).SetLimit(10000)
	}

	cursor, err := mdb.Collection(consts.CollectionAssets).Find(ctx, queryOpts, options.Find().SetSort(bson.M{"_id": 1}))
	if err != nil {
		log.Errorln(err)
		return
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var result schema.Asset
		err := cursor.Decode(&result)
		if err != nil {
			log.Errorln("failed to decode:", err)
			continue
		}
		assetChan <- &result
	}
	if err = cursor.Err(); err != nil {
		log.Errorln(err)
		return
	}
}

func setupSignal(closeCb func()) {
	go func() {
		ch := make(chan os.Signal, 1)
		signal.Notify(ch, syscall.SIGINT, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGHUP)
		for si := range ch {
			log.Infoln("receive signal", si)
			switch si {
			case syscall.SIGHUP:
				log.Infoln("signal SIGHUP.")
			case syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT:
				closeCb()
				break
			default:
				log.Errorln("invalid sig", si)
			}
		}
	}()
}
