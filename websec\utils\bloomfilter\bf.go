package bf

import (
	"hash/fnv"

	"github.com/steakknife/bloomfilter"
)

type BloomFilter struct {
	filter *bloomfilter.Filter
}

func (bf *BloomFilter) Add(bytes []byte) {
	v := fnv.New64a()
	v.Write(bytes)
	bf.filter.Add(v)
}

func (bf *BloomFilter) Contains(bytes []byte) bool {
	v := fnv.New64a()
	v.Write(bytes)
	return bf.filter.Contains(v)
}

func NewBloomFilter(maxN uint64, p float64) (*BloomFilter, error) {
	bf := &BloomFilter{}
	filter, err := bloomfilter.NewOptimal(maxN, p)
	if err != nil {
		return nil, err
	}
	bf.filter = filter
	return bf, nil
}
