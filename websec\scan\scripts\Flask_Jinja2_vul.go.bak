package scripts

import (
	"bytes"
	"io/ioutil"
	"net/http"
)


func FlaskJinja2(args *ScriptScanArgs) (*ScriptScanResult, error) {
	targetURL := constructURL(args, "/?name={{233*233}}")
	resp, err := http.Get(targetURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if bytes.Contains(body, []byte("54289")) {
		return &ScriptScanResult{Vulnerable: true, Output: targetURL, Body: body}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("Flask_Jinja2_vul.xml", FlaskJinja2)
}
