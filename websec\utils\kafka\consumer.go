package kafka

import (
	"runtime/debug"
	"websec/utils/log"

	"github.com/confluentinc/confluent-kafka-go/kafka"
)

type IConsumerHandler interface {
	MessageHandler(topic string, msg []byte, timestamp int64) error
	Close()
}

type Consumer struct {
	consumer *kafka.Consumer
	stop     chan bool
	handler  IConsumerHandler
}

func NewConsumer(brokers, group string, topics []string, handler IConsumerHandler) *Consumer {
	c := &Consumer{
		stop:    make(chan bool),
		handler: handler,
	}
	var err error
	c.consumer, err = kafka.NewConsumer(&kafka.ConfigMap{
		"bootstrap.servers":               brokers,
		"group.id":                        group,
		"session.timeout.ms":              6000,
		"go.events.channel.enable":        true,
		"go.application.rebalance.enable": true,
		"default.topic.config":            kafka.ConfigMap{"auto.offset.reset": "earliest"}})
	if err != nil {
		log.Error("InitComsumer error", err)
		return nil
	}
	err = c.consumer.SubscribeTopics(topics, nil)
	if err != nil {
		log.Error(err)
		return nil
	}

	return c
}

func (self *Consumer) Serve() {
	defer func() {
		if err := recover(); err != nil {
			log.Error("[异常] ", err, "\n", string(debug.Stack()))
		}
	}()

	for ev := range self.consumer.Events() {
		switch e := ev.(type) {
		case kafka.AssignedPartitions:
			log.Infof("kafka.AssignedPartitions  %v", e)
			self.consumer.Assign(e.Partitions)
		case kafka.RevokedPartitions:
			log.Infof("kafka.RevokedPartitions  %v", e)
			self.consumer.Unassign()
		case *kafka.Message:
			log.Infof("consumer receive kafka msg  topicpartition:%v, header:%v, timestamp:%d",
				e.TopicPartition, e.Headers, e.Timestamp.Unix())
			self.handler.MessageHandler(*e.TopicPartition.Topic, e.Value, e.Timestamp.Unix())
		case kafka.PartitionEOF:
			log.Infof("PartitionEOF Reached %v", e)
		case kafka.Error:
			log.Error("kafka.Error %v", e)
		}

	}
}

func (self *Consumer) Stop() {
	self.consumer.Close()
}
