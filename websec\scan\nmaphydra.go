package scan

import (
	"context"
	"io/ioutil"
	"net/http"
	"net/url"
	"time"
	"websec/utils/log"

	"github.com/sirupsen/logrus"
	"golang.org/x/net/context/ctxhttp"
)

func (scanner *WSScanner) triggerNmapHydraScan(assetID, host string) error {
	serviceURL := scanner.Options.NmapHydraService
	if len(serviceURL) == 0 {
		return nil
	}
	log.Info(serviceURL)
	form := make(url.Values)

	form.Add("host", host)
	form.Add("asset_id", assetID)
	form.Add("port", "")
	form.Add("is_https", "False")

	ctx, cancel := context.WithTimeout(context.TODO(), time.Minute*5)
	defer cancel()

	res, err := ctxhttp.PostForm(ctx, &http.Client{}, serviceURL, form)
	if err != nil {
		return err
	}
	defer res.Body.Close()
	newBody, err := ioutil.ReadAll(res.Body)
	if err != nil {

		return err
	}
	logrus.Info("weakpass check", string(newBody))
	return err
}
