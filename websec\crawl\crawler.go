package crawl

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"websec/common"
	"websec/common/schema"

	"websec/detect"
	"websec/utils"
	bytesbuff "websec/utils/bytespool"
	"websec/utils/log"
	"websec/utils/semaphore"

	"github.com/PuerkitoBio/goquery"
	"github.com/google/uuid"
	"github.com/scylladb/go-set/strset"
	"github.com/valyala/fasthttp"
	"golang.org/x/net/publicsuffix"
)

const (
	SMALLIMAGESIZE = 1024 * 4
	defaultReferer = "http://www.so.com/link?m=x&link?url=x&?from=x&?src=x&jump?u=x&?uID=x"
)

var ignoredContentType = map[string]bool{
	"application/octet-stream":               true,
	"application/pdf":                        true,
	"application/x-zip-compressed":           true,
	"image/jpeg":                             true,
	"application/msword":                     true,
	"image":                                  true,
	"application/vns.ms-works":               true,
	"video/1d-interleaved-parityfec":         true,
	"video/3gpp":                             true,
	"video/3gpp2":                            true,
	"video/3gpp-tt":                          true,
	"video/BMPEG":                            true,
	"video/BT656":                            true,
	"video/CelB":                             true,
	"video/DV":                               true,
	"video/encaprtp":                         true,
	"video/H261":                             true,
	"video/H263":                             true,
	"video/H263-1998":                        true,
	"video/H263-2000":                        true,
	"video/H264":                             true,
	"video/H264-RCDO":                        true,
	"video/H264-SVC":                         true,
	"video/JPEG":                             true,
	"video/jpeg2000":                         true,
	"video/mj2":                              true,
	"video/MP1S":                             true,
	"video/MP2P":                             true,
	"video/MP2T":                             true,
	"video/mp4":                              true,
	"video/MP4V-ES":                          true,
	"video/mpeg":                             true,
	"video/mpeg4-generic":                    true,
	"video/MPV":                              true,
	"video/nv":                               true,
	"video/ogg":                              true,
	"video/parityfec":                        true,
	"video/pointer":                          true,
	"video/quicktime":                        true,
	"video/raptorfec":                        true,
	"video/raw":                              true,
	"video/rtp-enc-aescm128":                 true,
	"video/rtploopback":                      true,
	"video/rtx":                              true,
	"video/SMPTE292M":                        true,
	"video/ulpfec":                           true,
	"video/vc1":                              true,
	"video/vnd.CCTV":                         true,
	"video/vnd.dece.hd":                      true,
	"video/vnd.dece.mobile":                  true,
	"video/vnd.dece.mp4":                     true,
	"video/vnd.dece.pd":                      true,
	"video/vnd.dece.sd":                      true,
	"video/vnd.dece.video":                   true,
	"video/vnd.directv.mpeg":                 true,
	"video/vnd.directv.mpeg-tts":             true,
	"video/vnd.dlna.mpeg-tts":                true,
	"video/vnd.dvb.file":                     true,
	"video/vnd.fvt":                          true,
	"video/vnd.hns.video":                    true,
	"video/vnd.iptvforum.1dparityfec-1010":   true,
	"video/vnd.iptvforum.1dparityfec-2005":   true,
	"video/vnd.iptvforum.2dparityfec-1010":   true,
	"video/vnd.iptvforum.2dparityfec-2005":   true,
	"video/vnd.iptvforum.ttsavc":             true,
	"video/vnd.iptvforum.ttsmpeg2":           true,
	"video/vnd.motorola.video":               true,
	"video/vnd.motorola.videop":              true,
	"video/vnd.mpegurl":                      true,
	"video/vnd.ms-playready.media.pyv":       true,
	"video/vnd.nokia.interleaved-multimedia": true,
	"video/vnd.nokia.videovoip":              true,
	"video/vnd.objectvideo":                  true,
	"video/vnd.sealed.mpeg1":                 true,
	"video/vnd.sealed.mpeg4":                 true,
	"video/vnd.sealed.swf":                   true,
	"video/vnd.sealedmedia.softseal.mov":     true,
	"video/vnd.uvvu.mp4":                     true,
	"video/vnd.vivo":                         true,
	"video/webm":                             true,
	"video/x-annodex":                        true,
	"video/x-flv":                            true,
	"video/x-javafx":                         true,
	"video/x-ms-asf":                         true,
	"video/x-ms-wm":                          true,
	"video/x-ms-wmv":                         true,
	"video/x-ms-wmx":                         true,
	"video/x-ms-wvx":                         true,
	"video/x-msvideo":                        true,
	"video/x-sgi-movie":                      true,
}

var htmlExts = map[string]bool{
	"htm":   true,
	"html":  true,
	"xhtml": true,
	"shtml": true,
	"js":    true,
	"swf":   true,
	"jpg":   true,
	"jpeg":  true,
	"png":   true,
	"webp":  true,
	"gif":   true,
}

var imageExts = map[string]bool{
	"jpg": true, "jpeg": true, "png": true, "gif": true,
}

var followTagNames = map[string]bool{
	"a": true, "area": true, "frame": true, "iframe": true, "script": true, "img": true,
}

type VulHandler interface {
	AddVul(v *schema.FoundVulDoc)
}

type WSCrawler struct {
	VulHandler

	Options *Options
	Host    string
	AssetID string
	JobID   string

	MaxDirFindDepth int32
	dirHandler      *DirHandler
	ScanDirs        sync.Map
	ScanDirResults  sync.Map

	goroutines       sync.WaitGroup
	foundLinksFilter FilterInterface
	linksQueue       *utils.Queue
	webPageChan      chan *common.Webpage
	startedAt        time.Time
	expiredAt        time.Time
	httpClient       *fasthttp.Client
	chromeClient     *fasthttp.Client
	chromeURI        string
	connectionClose  bool

	DetectDirDisclosure bool

	crawledOuterLinksFilter map[string]bool
	cookiejar               *utils.MyCookieJar
	curLink                 string
	semaphores              *sync.Map

	processorFlags
	Stats
	sema
	mutexCrawledOuterLinksFilter *sync.Mutex
	mutexFoundLinksFilter        *sync.Mutex
	FilterReg                    *regexp.Regexp
	CurrentLinkNum               uint64
}

type processorFlags struct {
	isCanceled        bool
	isCanceldActive   bool
	isAborting        bool
	loadLinksFinished bool
	isPaused          bool
}

type Stats struct {
	ProcessingCount int64
	ErrorCount      int64
	RequestCount    int64
	FoundURLsCount  int64
	OuterURLsCount  int64
	ErrorReason     string
	TaskStatus      uint8
}

func (s Stats) String() string {
	return fmt.Sprintf("processingCount: %d errorCount: %d requestCount: %d foundURLsCount: %d outerURLsCount: %d taskstatus: %d",
		s.ProcessingCount, s.ErrorCount, s.RequestCount, s.FoundURLsCount, s.OuterURLsCount, s.TaskStatus)
}

type sema struct {
	linkSema           *semaphore.Weighted
	linkCtx            context.Context
	linkCtxCancel      context.CancelFunc
	outerLinkSema      *semaphore.Weighted
	outerLinkCtx       context.Context
	outerLinkCtxCancel context.CancelFunc
	defaultSemaphore   *RequestSemaphore
}

func (crawler *WSCrawler) initDirDisclosure() {

	crawler.DetectDirDisclosure = true
	if len(crawler.Options.SpecificXMLs) > 0 {
		crawler.DetectDirDisclosure = false
		for i := range crawler.Options.SpecificXMLs {
			crawler.Options.SpecificXMLs[i] = detect.VulDirDisclosure
			crawler.DetectDirDisclosure = true
			return
		}
	}
}

func (crawler *WSCrawler) Go() (chan *common.Webpage, error) {
	go crawler.run()
	return crawler.webPageChan, nil
}

func (crawler *WSCrawler) Stop() {
	crawler.isCanceled = true
	crawler.linkCtxCancel()
}

func (crawler *WSCrawler) run() {
	go crawler.loadLinks()

Processing:
	for {
		crawler.waitIfPaused()

		if crawler.shouldStop() || crawler.linksQueue.IsClosed() {
			log.Infoln("crawler.linksQueue.IsClosed is", crawler.linksQueue.IsClosed())
			log.Infof("Host: %s crawler should stop,reason: %s, linksQueue length:%d %s,",
				crawler.Host,
				crawler.Stats.ErrorReason,
				crawler.linksQueue.Len(),
				crawler.GetStats().String(),
			)

			break Processing
		} else if link := crawler.linksQueue.Pop(); link != nil {
			if crawler.FilterReg != nil {
				uri, err := url.Parse(link.(*common.Link).URL)
				if err != nil {
					common.PutLink(link.(*common.Link))
					log.Errorln("failed to Parse uri", err)
					continue
				}
				if crawler.FilterReg.MatchString(uri.Path) {
					common.PutLink(link.(*common.Link))
					log.Info("not scan url", uri.Path)
					continue
				}
			}
			if crawler.beforeProcessing() {
				crawler.goroutines.Add(1)
				go crawler.processLink(link.(*common.Link))
			}
		} else {
			time.Sleep(2 * time.Second)
		}
	}
	crawler.finish()
}

func (crawler *WSCrawler) finish() {
	crawler.goroutines.Wait()
	close(crawler.webPageChan)
	crawler.linksQueue.Close()
	//TODO
	// s := crawler.GetStats()
	// s.ProcessingCount
	// s.ErrorCount
	// s.RequestCount
	// s.FoundURLsCount
	// s.OuterURLsCount
	// s.TaskStatus
	log.Infoln("crawler finish... status: ", crawler.GetStats().String())
}

func (crawler *WSCrawler) Cancel() {
	crawler.isCanceled = true
	crawler.outerLinkCtxCancel()
}

func (crawler *WSCrawler) loadLinks() {
	offset := crawler.Options.Offset
	crawler.curLink = offset

	isAdd := false
	if offset == "" {
		isAdd = true
	}

	for link := range crawler.Options.LinksChan {
		log.Infoln(link)
		link.Headers.Add("Referer", defaultReferer)
		if !IsValidURL(link.URL) {
			common.PutLink(link)
			continue
		}
		if !isAdd && (link.URL == offset) {
			isAdd = true
		}
		if isAdd {
			crawler.addLink(link)
		} else {
			common.PutLink(link)
		}
	}
	crawler.loadLinksFinished = true
	log.Infoln("crawler load links finished...", crawler.linksQueue.Len())
}

func (crawler *WSCrawler) addLink(link *common.Link) {
	if link.Depth >= crawler.Options.MaxDepth && crawler.Options.MaxDepth != -1 &&
		crawler.CurrentLinkNum < crawler.Options.MaxLinkNum {
		common.PutLink(link)
		return
	}

	for k := range crawler.Options.Headers {
		v := crawler.Options.Headers.Get(k)
		link.Headers.Add(k, v)
	}

	crawler.mutexFoundLinksFilter.Lock()
	defer crawler.mutexFoundLinksFilter.Unlock()

	fp := link.Fingerprint()
	if exists := crawler.foundLinksFilter.Contains([]byte(fp)); !exists {
		crawler.foundLinksFilter.Add([]byte(fp))
		if link.UserAgent != "" {
			link.UserAgent = crawler.Options.UserAgent + link.UserAgent + " CrawlerSpider"
		} else {
			link.UserAgent = crawler.Options.UserAgent + " CrawlerSpider"
		}
		crawler.linksQueue.Push(link)
	} else {
		common.PutLink(link)
	}
}

func (crawler *WSCrawler) addScanWebpage(wp *common.Webpage) {
	crawler.mutexFoundLinksFilter.Lock()
	defer crawler.mutexFoundLinksFilter.Unlock()

	link := &common.Link{
		AssetID: wp.AssetID,
		JobID:   wp.JobID,
		URL:     wp.URL,
		Method:  http.MethodGet,
	}
	fp := link.Fingerprint()
	if exists := crawler.foundLinksFilter.Contains([]byte(fp)); !exists {
		crawler.foundLinksFilter.Add([]byte(fp))
		wp.Host = crawler.Host
		wp.AssetID = crawler.AssetID
		wp.JobID = crawler.JobID
		crawler.webPageChan <- wp
	}
}

func (crawler *WSCrawler) shouldPause() bool {
	return crawler.isPaused
}

func (crawler *WSCrawler) waitIfPaused() {
	for {
		if crawler.shouldStop() {
			return
		}

		if crawler.shouldPause() {
			time.Sleep(time.Second * 2)
			continue
		} else {
			return
		}
	}
}

func (crawler *WSCrawler) shouldStop() bool {
	errorReason := ""
	if crawler.isCanceled ||
		crawler.isAborting ||
		time.Now().After(crawler.expiredAt) {

		errorReason = fmt.Sprintf("crawler isCanced %v isAborting %v expired %v",
			crawler.isCanceled, crawler.isAborting, time.Now().After(crawler.expiredAt))
		return true
	}

	pCount := atomic.LoadInt64(&crawler.ProcessingCount)

	if pCount == 0 &&
		crawler.linksQueue.Len() == 0 &&
		crawler.loadLinksFinished {

		if crawler.dirHandler != nil && crawler.dirHandler.DirQueue.Len() > 0 {
			return false
		}

		errorReason = "crawler links queue is empty"
		crawler.Stats.ErrorReason = errorReason
		return true
	}

	linkNum := atomic.LoadUint64(&crawler.CurrentLinkNum)
	if linkNum >= crawler.Options.MaxLinkNum && crawler.linksQueue.Len() == 0 {
		errorReason = "crawler links num greater than max link num"
		crawler.Stats.ErrorReason = errorReason
		return true
	}

	return false
}

func (crawler *WSCrawler) beforeProcessing() bool {
	err := crawler.linkSema.Acquire(crawler.linkCtx, 1)
	if err != nil {
		return false
	}
	atomic.AddInt64(&crawler.ProcessingCount, 1)
	atomic.AddUint64(&crawler.CurrentLinkNum, 1)
	return true
}

func (crawler *WSCrawler) afterProcessing() {
	crawler.goroutines.Done()
	crawler.linkSema.Release(1)
	atomic.AddInt64(&crawler.ProcessingCount, -1)
}

func (crawler *WSCrawler) getWithJS(link *common.Link, withJS bool) (bool, error) {
	returnWithJS := withJS

	if link.Depth == 0 {

		returnWithJS = true
	} else if withJS {
		parts, err := url.Parse(link.URL)
		if err != nil {
			log.Errorln("getWithJS url parse err:", err, "url:", link.URL)
			return returnWithJS, err
		}

		path := parts.Path
		pathParts := strings.Split(path, ".")
		if len(pathParts) > 1 {
			ext := pathParts[len(pathParts)-1]
			ext = strings.ToLower(ext)
			if _, ok := IgnoredExts[ext]; ok {
				returnWithJS = false
			}
			if _, ok := htmlExts[ext]; ok {
				returnWithJS = false
			}
		}

		dotParts := strings.Split(link.URL, ".")
		tailExt := dotParts[len(dotParts)-1]
		tailExt = strings.ToLower(tailExt)
		if _, ok := IgnoredExts[tailExt]; ok {
			returnWithJS = false
		}
		if _, ok := htmlExts[tailExt]; ok {
			returnWithJS = false
		}

		if link.Method == http.MethodPost {

			returnWithJS = false
		}
	}
	return returnWithJS, nil
}

func (crawler *WSCrawler) processLink(link *common.Link) {
	log.Info("processing link", link.URL, link.Headers)
	var err error
	startTime := time.Now()
	defer func() {
		common.PutLink(link)
		endTime := time.Now()
		costTime := endTime.Sub(startTime).Seconds()
		if costTime < 1.0 {
			sleepTime := time.Duration(math.Max(1.0-costTime, 0.2) * 1000)
			time.Sleep(sleepTime * time.Millisecond)
		}
		crawler.afterProcessing()
	}()

	crawler.curLink = link.URL

	withJS := crawler.Options.ByChrome
	withJS, err = crawler.getWithJS(link, withJS)
	if err != nil {
		return
	}

	var webPages []*common.Webpage
	if link.Depth == 0 && (link.URI == "" || link.URI == "/") {
		webPages, err = crawler.fetchEntryWebPage(link, withJS, true)
	} else {
		webPages, err = crawler.fetchWebPage(link, withJS, true)
	}

	if err != nil || webPages == nil {
		log.Errorf("fetchwebpage err:%v url:%s", err, link.URL)
		wp := bytesbuff.Pool.Get().(*common.Webpage)
		wp.URL = link.URL
		wp.EffectiveURL = link.URL
		wp.Method = link.Method
		wp.Depth = link.Depth
		wp.Referer = link.Referer
		wp.IsStatic = link.Static
		wp.CrawledAt = time.Now()
		wp.UID = crawler.getUUID()
		wp.Host = crawler.Host
		wp.AssetID = crawler.AssetID
		wp.JobID = crawler.JobID
		wp.IsNew = link.IsNew
		wp.Headers = mergeHeaders(link.Headers, nil)
		crawler.webPageChan <- wp
		return
	}

	for i := range webPages {
		wp := webPages[i]
		if wp.StatusCode == 599 {
			log.Errorln("wp status code 599. url:", link.URL)
			wp.Reset()
			return
		}

		contentType := strings.ToLower(wp.Headers.Get("Content-Type"))
		if contentType != "" {
			contentType = strings.Split(contentType, ";")[0]
			if _, ok := ignoredContentType[contentType]; ok {
				log.Infoln("ignoredcontenttype url:", link.URL)
				wp.Reset()
				return
			}

		}

		if len(contentType) == 0 {
			contentType = http.DetectContentType(wp.Content)
		}
		if strings.HasPrefix(contentType, "image") && len(wp.Content) < SMALLIMAGESIZE {
			wp.Reset()
			return
		}

		if strings.HasPrefix(contentType, "video") {
			wp.Reset()
			return
		}
		contentDisposition := wp.Headers.Get("Content-Disposition")
		if contentDisposition != "" {
			contentDisposition = strings.ToLower(contentDisposition)
			re := regexp.MustCompile(`filename=.+\.([a-z0-9]+)`)
			match := re.FindStringSubmatch(contentDisposition)
			if len(match) > 0 {
				ext := match[1]
				ext = strings.ToLower(ext)
				if _, ok := IgnoredExts[ext]; ok {
					log.Infoln("ignoredexts url:", link.URL)
					wp.Reset()
					return
				}
			}
		}

		if !link.NotProcessWebPage {
			crawler.processWebPage(link, wp)
		}

		if crawler.FilterReg != nil {
			if crawler.Options.IgnoreContent {
				wp.ClearContent()
			}

			j := 0
			for _, curWp := range wp.NetworkWebpages {

				uri, err := url.Parse(curWp.URL)
				if err != nil {
					log.Errorln("failed to Parse uri", err)
					curWp.Clear()
					continue
				}

				if crawler.FilterReg.MatchString(uri.Path) {
					log.Info("not scan url", uri.Path)
					curWp.Clear()
					continue
				}

				wp.NetworkWebpages[j] = curWp
				if crawler.Options.IgnoreContent {
					wp.NetworkWebpages[j].ClearContent()
				}
				j++
			}
			wp.NetworkWebpages = wp.NetworkWebpages[:j]
		} else {
			if crawler.Options.IgnoreContent {

				wp.Content = []byte{}
				for i := range wp.NetworkWebpages {
					wp.NetworkWebpages[i].ClearContent()
				}
			}
		}

		wp.Depth = link.Depth
		wp.Host = crawler.Host

		if wp.URL == link.URL {
			wp.IsNew = link.IsNew
		} else {
			wp.IsNew = true
		}
		if wp.EffectiveURL == "" {
			wp.EffectiveURL = wp.URL
		}
		crawler.webPageChan <- wp
	}
}

func (crawler *WSCrawler) processWebPage(link *common.Link, wp *common.Webpage) {
	pageURL := wp.EffectiveURL

	linkParts, err := url.Parse(link.URL)
	if err != nil {
		return
	}

	fullDomainURL, err := url.Parse(pageURL)
	if err != nil {
		log.Errorln(err)
		return
	}

	domain := linkParts.Host
	regDomain, err := publicsuffix.EffectiveTLDPlusOne(linkParts.Host)
	if err != nil {
		return
	}
	HTMLHeaderRe := regexp.MustCompile(`<html>`)

	content := wp.ContentSave.Bytes()

	reader := bytes.NewReader(content)
	doc, err := goquery.NewDocumentFromReader(reader)
	if err != nil {
		index := HTMLHeaderRe.FindSubmatchIndex(content)
		if len(index) > 0 {
			start := index[1]
			content = content[start : len(content)-1]
			reader = bytes.NewReader(content)
			doc, err = goquery.NewDocumentFromReader(reader)
			if err != nil {
				log.Errorln("Can't process content.", pageURL)
				return
			}
		} else {
			log.Errorln("No <html> found in ", pageURL)
			return
		}
	}

	var titleContent string

	doc.Find("title").Each(func(i int, s *goquery.Selection) {
		titleContent = s.Text()
	})

	if titleContent == "The page is not found" {
		log.Errorln("wrong redirect page")
		return
	}

	doc.Find("input[name='__VIEWSTATE']").Each(func(i int, s *goquery.Selection) {
		s.SetAttr("value", "")
	})

	shouldCrawlFoundLinks := crawler.Options.ShouldCrawlFoundLinks
	if link.Depth == 0 {
		shouldCrawlFoundLinks = true
	}

	outerURLs := make([]string, 0, 2)

	allOutURLs := make([]string, 0, 2)

	effectiveURLParts, err := url.Parse(wp.EffectiveURL)
	if err != nil {
		log.Errorln(err)
		return
	}
	/*
		* comment here, since the second param of ExtractLinksFromDoc shall the url of the page, but not
		* the base url, It's wrong codes in the comments code

		effectiveFullDomain := fmt.Sprintf("%s://%s", effectiveURLParts.Scheme, effectiveURLParts.Host)
		effectiveFullDomainURL, err := url.Parse(effectiveFullDomain)
		if err != nil {
			log.Errorln(err)
			return
		}
		extractLinksChan := ExtractLinksFromDoc(doc, effectiveFullDomainURL)
	*/
	extractLinksChan := ExtractLinksFromDoc(doc, effectiveURLParts)
	images := make([]string, 0, 1)
	swfs := make([]string, 0, 1)

	tmpURLs := make(map[string]bool)
	for eLink := range extractLinksChan {
		eLinkParts, err := url.Parse(eLink.URL)
		if err != nil {
			continue
		}
		pathParts := strings.Split(eLinkParts.Path, ".")
		if len(pathParts) > 1 {
			tail := pathParts[len(pathParts)-1]
			if _, ok := imageExts[tail]; ok {
				if _, exists := tmpURLs[eLink.URL]; exists {
					continue
				}

				tmpURLs[eLink.URL] = true
				log.Infoln("images", link.URL, eLink.URL)
				images = append(images, eLink.URL)
				continue
			}
		}

		/*
			if crawler.Options.ExtractEntrySwfs {
				eLinkParts, err := url.Parse(eLink.URL)
				if err != nil {
					continue
				}
				pathParts := strings.Split(eLinkParts.Path, ".")
				if len(pathParts) > 1 {
					tail := pathParts[len(pathParts)-1]
					if _, ok := flashExts[tail]; ok {
						swfs = append(swfs, eLink.URL)
						continue
					}
				}
			}
		*/

		if !IsValidURL(eLink.URL) {
			scanWP := crawler.newScanWebPage(eLink, pageURL, domain, link)
			if scanWP != nil {
				if scanWP.StatusCode >= 200 && scanWP.StatusCode < 400 {
					crawler.addScanWebpage(scanWP)
				}
			}
			continue
		}
		isOuter := IsOuterLink(domain, eLink.URL)
		if isOuter {
			allOutURLs = append(allOutURLs, eLink.URL)
		}

		if _, ok := followTagNames[eLink.TagName]; ok {
			if wp.ByChrome {
				if eLink.TagName == "a" || eLink.TagName == "script" || eLink.TagName == "img" {
					if IsInternalLink(domain, eLink.URL) {
						if shouldCrawlFoundLinks {
							crawler.addLink(common.GenLink(eLink.URL, http.MethodGet, pageURL, "", link.Depth+1))
						}
					} else {
						// log.Infoln("if IsInternalLink else ", eLink.TagName, crawler.isOuterLinkIgnore(eLink, regDomain), eLink.URL)
						if eLink.TagName == "script" || !crawler.isOuterLinkIgnore(eLink, regDomain) {
							outerURLs = append(outerURLs, eLink.URL)
						}

						if eLink.TagName == "a" && link.Depth < crawler.Options.ExternalScanDepth {
							outerURLs = append(outerURLs, eLink.URL)
						}

						if eLink.TagName == "img" && link.Depth < crawler.Options.ExternalScanDepth {
							outerURLs = append(outerURLs, eLink.URL)
						}
					}
				}
			} else {
				isOuter := IsOuterLink(domain, eLink.URL)
				if isOuter {
					if (eLink.TagName == "a" || eLink.TagName == "script" || eLink.TagName == "img") &&
						!crawler.isOuterLinkIgnore(eLink, regDomain) {
						log.Infoln("add link to outURLS", eLink.URL)
						outerURLs = append(outerURLs, eLink.URL)
						continue
					}
				}

				if (!isOuter || (isOuter && !crawler.isOuterLinkIgnore(eLink, regDomain))) || eLink.TagName == "script" || eLink.TagName == "img" {
					newLink := common.GenLink(eLink.URL, http.MethodGet, pageURL, "", link.Depth+1)
					newLink.Outer = isOuter
					if eLink.TagName == "frame" || eLink.TagName == "iframe" {
						newLink.MainFrameURL = pageURL
						if isOuter {
							// TODO
							// external scan depth
							if crawler.Options.Task.ExternalScanDepth <= 0 {
								newLink.NotProcessWebPage = true
							} else if link.Depth < crawler.Options.ExternalScanDepth {
								newLink.NotProcessWebPage = false
							}
						}
					}
					crawler.addLink(newLink)
				}
			}
		} else if eLink.TagName != "form" {
			scanWP := crawler.newScanWebPage(eLink, pageURL, domain, link)
			if scanWP != nil {
				crawler.addScanWebpage(scanWP)
			}
		}
	}

	wp.Images = images
	wp.Swfs = swfs

	outerURLsFoundByRegex := findAllOuterURLs(string(content), regDomain)

	var s1 *strset.Set
	if len(outerURLsFoundByRegex) > 0 {
		s1 = strset.New(outerURLsFoundByRegex...)
		s2 := strset.New(allOutURLs...)
		s1.Separate(s2)

		log.Infoln(link.URL, ":", s1.List(), link.MediaType, link.Headers.Get("Content-Type"))
	}

	if s1 != nil && !s1.IsEmpty() {
		outerURLs = append(outerURLs, s1.List()...)
	}

	if len(outerURLs) > 0 {
		wp.OuterWebpages = crawler.processOuterLinks(outerURLs, link)
	}

	noJSForms := new(goquery.Selection)
	doc.Find("form").Each(func(i int, s *goquery.Selection) {
		action := s.AttrOr("action", pageURL)
		action = URLJoin(fullDomainURL, action)
		method := s.AttrOr("method", "")
		method = strings.ToUpper(method)

		if !IsInternalLink(domain, action) {
			return
		}

		parts, err := url.Parse(action)
		if err != nil {
			return
		}

		if link.Data != "" && parts.Path == linkParts.Path {
			return
		}
		fields := crawler.getFormFields(s)
		if fields == "" {
			if noJSForms.Length() == 0 {
				tmpForms := crawler.fetchNoJSForms(link)
				if tmpForms != nil {
					noJSForms = tmpForms
				}
			}

			if i < noJSForms.Length() {
				tmpForm := noJSForms.Eq(i)
				fields = crawler.getFormFields(tmpForm)
			}
		}

		if method == http.MethodGet && fields != "" {
			if len(parts.Query()) > 0 {
				action = fmt.Sprintf("%s&%s", action, fields)
			} else {
				action = fmt.Sprintf("%s?%s", action, fields)
			}
		}

		if shouldCrawlFoundLinks {
			crawler.addLink(common.GenLink(action, method, pageURL, fields, link.Depth+1))
		}
	})
}

func (crawler *WSCrawler) newScanWebPage(eLink *ExtractLink, pageURL string, domain string, link *common.Link) *common.Webpage {
	log.Debugln("URL ~ Current depth ~ ExternalScanDepth", eLink.URL, link.Depth, crawler.Options.ExternalScanDepth)
	if !IsDomainEqual(domain, eLink.URL) && link.Depth > crawler.Options.ExternalScanDepth {
		return nil
	}

	isStatic, ok := StaticElemTag[eLink.TagName]
	if !ok {
		isStatic = false
	}

	scanWP := common.Webpage{}
	scanWP.URL = eLink.URL
	scanWP.EffectiveURL = eLink.URL
	scanWP.Method = http.MethodGet
	scanWP.Referer = pageURL
	scanWP.IsStatic = isStatic
	scanWP.Depth = link.Depth + 1
	scanWP.ExternalScanDepth = int(crawler.Options.ExternalScanDepth)
	scanWP.CrawledAt = time.Now()
	scanWP.UID = crawler.getUUID()
	scanWP.IsNew = true
	scanWP.JobID = link.JobID
	scanWP.Headers = mergeHeaders(link.Headers, nil)
	return &scanWP
}

func (crawler *WSCrawler) isOuterLinkIgnore(eLink *ExtractLink, regDomain string) bool {
	if !crawler.Options.CrawlOuterLinks {
		return true
	}

	eLinkParts, err := url.Parse(eLink.URL)
	if err != nil {
		return true
	}

	eLinkRegDomian, err := publicsuffix.EffectiveTLDPlusOne(eLinkParts.Host)
	if err != nil {
		return true
	}

	eLinkSuffix, icann := publicsuffix.PublicSuffix(eLinkParts.Host)
	if !icann {
		return true
	}

	if eLinkSuffix == "edu.cn" || eLinkSuffix == "gov.cn" || eLinkSuffix == "edu" {
		return true
	}

	if IsBlocked(eLink.URL) {
		return true
	}

	crawler.mutexCrawledOuterLinksFilter.Lock()
	defer crawler.mutexCrawledOuterLinksFilter.Unlock()
	if eLinkRegDomian != regDomain &&
		eLinkRegDomian != "" &&
		!crawler.Options.IgnoredBlackLinkDomains.IsIgnored(eLinkRegDomian) &&
		!IsInMap(eLink.URL, crawler.crawledOuterLinksFilter) {

		crawler.crawledOuterLinksFilter[eLink.URL] = true
		return false
	}
	return true
}

func (crawler *WSCrawler) fetchNoJSForms(link *common.Link) *goquery.Selection {
	webpages, err := crawler.fetchWebPage(link, false, false)
	if err != nil {
		return nil
	}

	if len(webpages) > 0 {
		wp := webpages[0]
		if len(wp.Content) > 0 {
			content := wp.Content
			reader := bytes.NewReader(content)
			doc, err := goquery.NewDocumentFromReader(reader)
			if err != nil {
				return nil
			}

			s := doc.Find("form")
			return s
		}
	}
	return nil
}

func (crawler *WSCrawler) getFormFields(s *goquery.Selection) string {

	defaultVal := "8"
	params := url.Values{}
	checkboxs := map[string]string{}
	s.Find("input[type=checkbox]").Each(func(i int, cs *goquery.Selection) {
		name := cs.AttrOr("name", "")
		value := cs.AttrOr("value", "")
		if value == "" {
			value = defaultVal
		}
		if name != "" {
			if _, ok := checkboxs[name]; !ok {
				checkboxs[name] = value
			}
		}
	})

	s.Find("*[name]").Each(func(i int, cs *goquery.Selection) {
		name := cs.AttrOr("name", "")
		value := cs.AttrOr("value", "")
		if value == "" {
			value = defaultVal
		}
		if name != "" && name != "__VIEWSTATE" {
			if _, ok := checkboxs[name]; !ok {
				params.Add(name, value)
			}
		}
	})

	for k, v := range checkboxs {
		params.Add(k, v)
	}

	return params.Encode()
}

func (crawler *WSCrawler) processOuterLinks(outerURLs []string, link *common.Link) map[string]*common.OuterWebPage {
	ch := make(chan *common.OuterWebPage, 100)
	stopCh := make(chan struct{}, 1)
	outerWPS := map[string]*common.OuterWebPage{}

	timer := time.NewTimer(60 * time.Second)

	go crawler.fetchOuterLinks(outerURLs, link, ch, stopCh)

OuterLinkProcessing:
	for {
		select {
		case wp := <-ch:
			if wp == nil {
				break OuterLinkProcessing
			}
			outerWPS[wp.URL] = wp

		case <-timer.C:

			close(stopCh)
			break OuterLinkProcessing
		}
	}

	return outerWPS
}

func (crawler *WSCrawler) fetchOuterLinks(outerURLs []string, link *common.Link, ch chan *common.OuterWebPage, stopCh chan struct{}) {
	for i := range outerURLs {
		select {
		case <-stopCh:
			log.Infoln("exit fetchOuterLinks.", link.URL, link.Fingerprint())
			return
		default:
		}

		atomic.AddInt64(&crawler.OuterURLsCount, 1)
		err := crawler.outerLinkSema.Acquire(crawler.outerLinkCtx, 1)
		if err != nil {
			log.Errorln("Failed to acquire outerLinkSema. ", outerURLs[i])
			continue
		}
		go crawler.fetchOuterLink(outerURLs[i], link, ch, stopCh)
	}
}

func (crawler *WSCrawler) fetchOuterLink(u string, link *common.Link, ch chan *common.OuterWebPage, stopCh chan struct{}) {
	defer crawler.outerLinkSema.Release(1)
	userAgent := "CrawlerSpider"
	if link.UserAgent != "" {
		userAgent = link.UserAgent + " " + userAgent
	}

	headers := common.HttpHeaders{}
	for key, values := range link.Headers {
		for i := range values {
			if i == 0 {
				headers.Set(key, string(values[i]))
			} else {
				headers.Add(key, string(values[i]))
			}
		}
	}
	headers.Set("Referer", defaultReferer)

	outerLink := common.Link{
		AssetID:   link.AssetID,
		JobID:     link.JobID,
		URL:       u,
		Method:    http.MethodGet,
		Referer:   link.URL,
		Depth:     link.Depth + 1,
		UserAgent: userAgent,
		Headers:   headers,
		Outer:     true,
	}

	withJS, err := crawler.getWithJS(&outerLink, true)
	if err != nil {
		return
	}

	webpages, err := crawler.fetchWebPage(&outerLink, withJS, true)
	if err != nil {
		log.Errorln(err, link.URL)
	} else {
		outerPage := new(common.OuterWebPage)
		outerPage.URL = u

		for j := range webpages {

			wp := webpages[j]

			if wp.StatusCode != 521 && wp.StatusCode != 599 {
				frame := common.OuterWebPageFrame{}
				frame.URL = wp.URL
				frame.StatusCode = wp.StatusCode
				frame.ContentSave = wp.ContentSave
				frame.MainFrameURL = wp.MainFrameURL
				outerPage.Frames = append(outerPage.Frames, frame)
				if j == 0 {
					outerPage.EffectiveURL = frame.URL
					outerPage.Headers = wp.Headers
				}
				wp.ResetNetwork()
			} else {
				wp.Reset()
			}
		}

		select {
		case <-stopCh:
			log.Infoln("exit fetchOuterLink", u, link.Fingerprint())
			return
		case ch <- outerPage:

		}
	}
}

func (crawler *WSCrawler) fetchEntryWebPage(link *common.Link, withJS, retryWhenJSFailed bool) ([]*common.Webpage, error) {
	var webPages []*common.Webpage
	var err error
	retryTimes := 1

	for i := 0; i < retryTimes; i++ {
		webPages, err = crawler.fetchWebPage(link, withJS, false)
		if err != nil || webPages == nil {
			time.Sleep(1 * time.Second)
			continue
		}

		if webPages[0].StatusCode == 599 {
			time.Sleep(2 * time.Second)
			continue
		}

		break
	}
	if len(webPages) == 0 {
		webPages, err = crawler.fetchWebPage(link, withJS, true)
	}
	return webPages, err
}

func (crawler *WSCrawler) fetchWebPage(link *common.Link, withJS, retryWhenJSFailed bool) ([]*common.Webpage, error) {
	var err error
	webPages := make([]*common.Webpage, 0, 1)

	requestSemaphore := crawler.getRequestSemaphore(link.GetHost())
	err = requestSemaphore.Sema.Acquire(requestSemaphore.Ctx, 1)
	if err != nil {
		return webPages, err
	}
	defer requestSemaphore.Sema.Release(1)

	if link.Depth > 1 && link.Depth < 4 && !withJS && crawler.Options.GetLastGetAt != nil {
		isModified, _ := crawler.isModified(link)
		if !isModified {
			return webPages, nil
		}
	}

	atomic.AddInt64(&crawler.RequestCount, 1)

	if withJS {
		webPages, err = crawler.chromeFetch(link)
		if err != nil {
			if retryWhenJSFailed {
				log.Errorln("Chrome Request error, so retry HTTP Request, ", link.Method, link.URL, "***", link.Headers, "***", link.Data, err)
				webPages, err = crawler.httpFetch(link)
				if err != nil {
					atomic.AddInt64(&crawler.ErrorCount, 1)
					log.Errorln("Retry HTTP Request err", link.Method, link.URL, "***", link.Headers, "***", link.Data, err)
					return nil, err
				}
			} else {
				atomic.AddInt64(&crawler.ErrorCount, 1)
			}
		}
	} else {
		webPages, err = crawler.httpFetch(link)
		if err != nil {
			atomic.AddInt64(&crawler.ErrorCount, 1)
			log.Errorln("HTTP Request err", link.Method, link.URL, "***", link.Headers, "***", link.Data, err)
			return nil, err
		}
	}

	if link.Depth > 1 && link.Depth < 4 && !withJS && crawler.Options.SetLastGetAt != nil {
		for i := range webPages {
			wp := webPages[i]
			lastGetAt := common.PageLastGetAt{
				LastGetAt: time.Now().Unix(),
			}

			host, err := GetDomain(wp.URL)
			if err == nil {
				crawler.Options.SetLastGetAt(host, wp.URLHash(), &lastGetAt)
			}
		}
	}
	return webPages, err
}

func (crawler *WSCrawler) isModified(link *common.Link) (bool, *common.PageLastGetAt) {
	lastGetAt := crawler.Options.GetLastGetAt(link.Host, link.URLSha1())
	if lastGetAt == nil {
		return true, nil
	}
	now := time.Now()
	if now.Sub(lastGetAt.GetTime()) > time.Duration(1*time.Hour) {
		return true, nil
	}
	headers, err := crawler.doHeadRequest(link)
	if err != nil {
		return true, nil
	}
	lastModifid := headers.Get("Last-Modified")
	if lastModifid == "" {
		return true, nil
	}
	timeLastModifid, err := time.Parse(time.RFC1123, lastModifid)
	if err != nil {
		return true, nil
	}
	if timeLastModifid.Sub(lastGetAt.GetTime()) > 0 {
		return true, nil
	}
	return false, lastGetAt
}

func (crawler *WSCrawler) doHeadRequest(link *common.Link) (common.HttpHeaders, error) {
	httpResponse, _, err := crawler.doHTTPRequest(link, http.MethodHead)
	if err != nil {
		return nil, err
	}
	defer fasthttp.ReleaseResponse(httpResponse)
	headers := crawler.getHTTPHeaders(&httpResponse.Header)
	return headers, nil
}

const maxRedirectsCount = 16

func (crawler *WSCrawler) doHTTPRequest(link *common.Link, method string) (*fasthttp.Response, string, error) {

	var effectiveURL string

	httpRequest := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(httpRequest)

	if crawler.cookiejar != nil {
		crawler.cookiejar.CopyToRequest(httpRequest)
	}

	httpRequest.SetRequestURI(link.URL)

	header := &httpRequest.Header
	if method != http.MethodHead {
		method = link.Method
	}
	header.SetMethod(method)
	for key, values := range link.Headers {
		for i := range values {
			if i == 0 {

				header.Set(key, string(values[i]))
			} else {
				header.Add(key, string(values[i]))
			}
		}
	}
	header.SetUserAgent(link.UserAgent)

	if crawler.connectionClose {
		httpRequest.SetConnectionClose()
	}

	if link.Data != "" {
		httpRequest.SetBodyString(link.Data)
	}

	httpResponse := fasthttp.AcquireResponse()
	var err error
	redirectsCount := 0
	for {
		if httpRequest.ConnectionClose() {

			httpClient := newHTTPClient(crawler.Options.UserAgent)
			err = httpClient.DoTimeout(httpRequest, httpResponse, 20*time.Second)
		} else {
			err = crawler.httpClient.DoTimeout(httpRequest, httpResponse, 20*time.Second)
		}

		if err == fasthttp.ErrConnectionClosed && !httpRequest.ConnectionClose() {

			log.Errorln("HTTP Request retry:", link.Method, link.URL, "***", link.Headers, "***", link.Data)
			crawler.connectionClose = true
			httpRequest.SetConnectionClose()
			continue
		} else if err != nil {
			break
		}

		statusCode := httpResponse.Header.StatusCode()
		if statusCode != fasthttp.StatusMovedPermanently && statusCode != fasthttp.StatusFound && statusCode != fasthttp.StatusSeeOther {
			break
		}

		redirectsCount++
		if redirectsCount > maxRedirectsCount {
			err = errors.New("too many redirects")
			break
		}
		location := httpResponse.Header.Peek("Location")
		if len(location) == 0 {
			err = errors.New("missing location")
			break
		}
		redirectsURL := GetRedirectURL(link.URL, location)
		effectiveURL = redirectsURL
		httpRequest.SetRequestURI(redirectsURL)
	}

	if err != nil {

		fasthttp.ReleaseResponse(httpResponse)
		log.Errorln("HTTP Request:", link.Method, link.URL, "***", link.Headers, "***", link.Data, "err:", err)
		return nil, effectiveURL, err
	}

	log.Infof("HTTP Request: %s %s *** %s *** %s depth %d outer %v ok.",
		link.Method, link.URL, link.Headers, link.Data, link.Depth, link.Outer)

	return httpResponse, effectiveURL, nil
}

func (crawler *WSCrawler) httpFetch(link *common.Link) ([]*common.Webpage, error) {
	httpResponse, effectiveURL, err := crawler.doHTTPRequest(link, "")
	if err != nil {
		return nil, err
	}
	defer fasthttp.ReleaseResponse(httpResponse)

	wp := common.InitNewWebpage()

	wp.URL = link.URL
	if len(effectiveURL) > 0 {
		wp.EffectiveURL = effectiveURL
	} else {
		wp.EffectiveURL = link.URL
	}
	wp.Data = link.Data
	wp.Method = link.Method
	wp.Referer = link.Referer
	wp.IsStatic = link.Static
	wp.ByChrome = false
	wp.StatusCode = httpResponse.StatusCode()
	wp.Depth = link.Depth
	wp.ExternalScanDepth = int(crawler.Options.ExternalScanDepth)
	wp.CrawledAt = time.Now()
	wp.UID = crawler.getUUID()

	body, err := utils.GetUtf8Body(httpResponse)
	if err != nil {
		return nil, err
	}

	wp.ContentSave = common.ContentPool.Get()

	_, err = wp.ContentSave.WriteString(string(body))
	if err != nil {
		return nil, err
	}

	log.Infof("Page Length by httpFecth: %s, %d", wp.EffectiveURL, len(wp.Content))
	wp.Headers = crawler.getHTTPHeaders(&httpResponse.Header)
	wp.Headers = mergeHeaders(link.Headers, wp.Headers)
	if link.MainFrameURL != "" {
		wp.MainFrameURL = link.MainFrameURL
	}
	return []*common.Webpage{wp}, nil
}

func (crawler *WSCrawler) chromeFetch(link *common.Link) ([]*common.Webpage, error) {
	if link.Depth > 3 {
		return nil, errors.New("depth > 3")
	}

	crawlRequest := common.CrawlRequest{
		AssetID:   link.AssetID,
		JobID:     link.JobID,
		URL:       link.URL,
		Method:    link.Method,
		Depth:     link.Depth,
		Referer:   link.Referer,
		Static:    link.Static,
		Outer:     link.Outer,
		Headers:   link.Headers,
		UserAgent: link.UserAgent,
		FilterReg: crawler.Options.FilterReg,
	}
	crawlRequestData, err := json.Marshal(crawlRequest)
	if err != nil {
		return nil, err
	}

	httpRequest := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(httpRequest)
	httpResponse := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(httpResponse)

	header := &httpRequest.Header
	header.SetMethod(http.MethodPost)

	httpRequest.SetBody(crawlRequestData)

	httpRequest.SetRequestURI(crawler.chromeURI)
	err = crawler.chromeClient.DoTimeout(httpRequest, httpResponse, 60*time.Second)

	if err != nil {
		log.Errorln("Chrome Request:", link.Method, link.URL, "***", link.Headers, "***", link.Data, "err:", err)
		return nil, err
	}

	log.Infof("Chrome Request: %s %s *** %s *** %s depth %d outer: %v ok.",
		link.Method, link.URL, link.Headers, link.Data, link.Depth, link.Outer)

	body := httpResponse.Body()
	if len(body) == 0 {
		return nil, errors.New("nil chrome response")
	}

	log.Infof("Page Length by chrome whole: %s, %d", link.URL, len(body))
	var crawlResponse common.CrawlResponse
	err = json.Unmarshal(body, &crawlResponse)
	if err != nil {
		log.Errorln("Unmarshal error:", err)
		return nil, err
	}

	if crawlResponse.Status > 0 {

		log.Errorln("Chrome Request failed:", crawlResponse.Status, link.Method, link.URL, "***", link.Headers)
		return nil, errors.New("chrome crawl error")
	}

	webpages := crawlResponse.Webpages
	for i := range webpages {
		webpages[i].ContentSave = common.ContentPool.Get()
		webpages[i].CrawledAt = time.Now()
		webpages[i].UID = crawler.getUUID()
		webpages[i].SaveBytesBuff(crawler.Options.MaxResponseLength)
		for netPageIndex := range webpages[i].NetworkWebpages {
			webpages[i].NetworkWebpages[netPageIndex].SaveBytesBuff(crawler.Options.MaxResponseLength)
		}
	}

	return webpages, nil
}

func (crawler *WSCrawler) getHTTPHeaders(headers *fasthttp.ResponseHeader) common.HttpHeaders {
	retHeaders := make(common.HttpHeaders)
	headers.VisitAll(func(key, value []byte) {
		retHeaders.Add(string(key), string(value))
	})
	return retHeaders
}

func (crawler *WSCrawler) SetMaxDepth(depth int32) {
	crawler.Options.MaxDepth = depth
}

func (crawler *WSCrawler) SetMaxLinkNum(num uint64) {
	crawler.Options.MaxLinkNum = num
}

func (crawler *WSCrawler) SetByChrome(byChrome bool) {
	crawler.Options.ByChrome = byChrome
}

func (crawler *WSCrawler) SetCrawlOuterLinks(crawl bool) {
	crawler.Options.CrawlOuterLinks = crawl
}

func (crawler *WSCrawler) SetConcurrency(concurrency int64) {
	crawler.Options.Concurrency = concurrency
}

func (crawler *WSCrawler) SetShouldCrawlFoundLinks(should bool) {
	crawler.Options.ShouldCrawlFoundLinks = should
}

func (crawler *WSCrawler) SetFingerPrintFunc(fpFunc FingerPrintFunc) {
	crawler.Options.FPFunc = fpFunc
}

func (crawler *WSCrawler) SetTimeout(timeout time.Duration) {
	crawler.Options.Timeout = timeout
	crawler.expiredAt = crawler.startedAt.Add(timeout)
}

func (crawler *WSCrawler) SetChromeHost(host string) {
	crawler.Options.ChromeHost = host
}

func (crawler *WSCrawler) SetChromePort(port uint16) {
	crawler.Options.ChromePort = port
}

func (crawler *WSCrawler) SetHeaders(headers common.HttpHeaders) {
	crawler.Options.Headers = headers
}

func (crawler *WSCrawler) SetUserAgent(userAgent string) {
	crawler.Options.UserAgent = userAgent
}

func (crawler *WSCrawler) GetStats() Stats {
	s := Stats{
		ProcessingCount: atomic.LoadInt64(&crawler.ProcessingCount),
		ErrorCount:      atomic.LoadInt64(&crawler.ErrorCount),
		RequestCount:    atomic.LoadInt64(&crawler.RequestCount),
		FoundURLsCount:  atomic.LoadInt64(&crawler.FoundURLsCount),
		OuterURLsCount:  atomic.LoadInt64(&crawler.OuterURLsCount),
	}

	return s
}

func (crawler *WSCrawler) SetCookies(cookies map[string]string) {
	for key, value := range cookies {
		crawler.SetCookie(key, value)
	}
}

func (crawler *WSCrawler) SetCookie(key, value string) {
	if key != "" && value != "" {
		crawler.cookiejar.SetCookie(key, value)
	}
}

func (crawler *WSCrawler) GetOffset() string {
	return crawler.curLink
}

func (crawler *WSCrawler) getUUID() string {
	uid, err := uuid.NewUUID()
	if err != nil {
		log.Errorln(err)
		return ""
	}
	return uid.String()
}

func (crawler *WSCrawler) getRequestSemaphore(host string) *RequestSemaphore {
	if v, ok := crawler.semaphores.Load(host); ok {
		return v.(*RequestSemaphore)
	}
	return crawler.defaultSemaphore
}

func (crawler *WSCrawler) RunDirScanner(baseurl string, reqMethod string, ext []string, agent string,
	cookie string, only200 bool, handler *DirHandler, depth int32) {

	if _, ok := crawler.ScanDirs.Load(baseurl); ok {
		return
	}

	if depth > crawler.MaxDirFindDepth {
		return
	}

	if cookie == "" && crawler.Options.Headers != nil {
		cookie = crawler.Options.Headers.Get("Cookie")
	}

	item := newDirValue()
	item.ExtUrl = baseurl
	item.ExtList = []string{"php", "html"}
	item.Agent = agent
	item.Cookie = cookie
	item.Depth = depth + 1

	handler.DirQueue.Push(item)

}

func (crawler *WSCrawler) GetDirVuls() (vuls []*schema.FoundVulDoc, err error) {
	if !crawler.DetectDirDisclosure {
		err = errors.New("no need to crawler dir_disclosure")
		log.Info("getDirVuls err", err)
		return
	}

	crawler.ScanDirResults.Range(func(k, v interface{}) bool {

		curVuls := &schema.FoundVulDoc{
			Host:    crawler.Host,
			AssetID: crawler.AssetID,
			JobID:   crawler.JobID,
			Vul: schema.FoundVulsVul{
				VulXML:   detect.VulDirDisclosure,
				VulURL:   k.(string),
				Severity: "info",
				From:     crawler.Host,
			},
			FoundAt: time.Now(),
		}
		vuls = append(vuls, curVuls)
		return true
	})

	return
}

func (crawler *WSCrawler) Die() {
	return
	/*
		crawler.Options = nil
		crawler.ScanDirs = sync.Map{}
		crawler.ScanDirResults = sync.Map{}
		crawler.foundLinksFilter = map[string]struct{}{}
		crawler.httpClient = nil
		crawler.chromeClient = nil
		crawler.specificVulXMLs = map[string]bool{}
		crawler.crawledOuterLinksFilter = map[string]bool{}
		crawler.cookiejar = nil
		crawler.semaphores = nil
		crawler.mutexCrawledOuterLinksFilter = nil
		crawler.mutexFoundLinksFilter = nil
	*/
}

func (crawler *WSCrawler) IsCanceldActive() bool {
	return crawler.isCanceldActive
}
