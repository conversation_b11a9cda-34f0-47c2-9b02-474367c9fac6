package scan

type DummyScanner struct {
}

func (scanner *DummyScanner) Go() (chan *ScanResult, error) {
	return nil, nil
}

func (scanner *DummyScanner) Add(link *ScanLink) {
}

func (scanner *DummyScanner) AddDone() {
}

func (scanner *DummyScanner) IsRunning() bool {
	return false
}

func (scanner *DummyScanner) Stop() {
}

func (scanner *DummyScanner) GetStats() *Stats {
	return &Stats{
		RequestCount:   0,
		FoundVulsCount: 0,
		ErrorReason:    "",
		ErrorCount:     0,
	}
}

func (scanner *DummyScanner) GetOffset() string {
	return ""
}
