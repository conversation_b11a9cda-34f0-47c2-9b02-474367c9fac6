package schedule

import (
	"container/list"
	"context"
	"errors"
	"fmt"
	"net"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"websec/common/schema"
	"websec/distributed/endpoint"
	"websec/distributed/protocol"
	"websec/utils/log"
)

type WorkerManager struct {
	pendingWorkers *list.List
	muPending      *sync.RWMutex

	runningWorkers map[string]*workerStat
	muRunning      *sync.RWMutex

	lostWorkers map[string]*workerStat
	muLost      *sync.RWMutex

	hostWorkers  map[string]*workerStat
	muHostWorker *sync.RWMutex

	resolver *net.Resolver

	aliveCount int32

	maxTaskCount int32
}

func newWorkerManager(maxTaskCount int32) *WorkerManager {
	m := &WorkerManager{
		muPending:      &sync.RWMutex{},
		pendingWorkers: list.New(),
		muRunning:      &sync.RWMutex{},
		runningWorkers: make(map[string]*workerStat),
		muLost:         &sync.RWMutex{},
		lostWorkers:    make(map[string]*workerStat),
		muHostWorker:   &sync.RWMutex{},
		hostWorkers:    make(map[string]*workerStat),
		maxTaskCount:   maxTaskCount,
		resolver: &net.Resolver{
			PreferGo: true,
		},
	}
	go m.PeriodSaveWorkerState()
	return m
}

func (manager *WorkerManager) AddPending(handler *endpoint.Endpoint) {
	log.Infoln("add pending:", handler.Tag)
	manager.muPending.Lock()
	defer manager.muPending.Unlock()

	for e := manager.pendingWorkers.Front(); e != nil; e = e.Next() {
		if e.Value.(*workerStat).handler == handler {
			log.Fatalln("WARNING: this should not happen")
			return
		}
	}
	newWorker := newWorkerStat(handler)
	manager.pendingWorkers.PushBack(newWorker)

	go handler.Consume()
	go manager.waitRegister(newWorker)
}

func (manager *WorkerManager) waitRegister(worker *workerStat) {
	log.Infoln("wait register:", worker.handler.Tag)
Waiting:
	for msg := range worker.handler.Requests() {
		switch msg.Action {
		case protocol.ActionRegister:
			worker.Name = msg.ClientID
			worker.registered = true
			manager.moveToRunning(worker)
			worker.handler.Reply(protocol.StatusOK, msg, nil)
			break Waiting
		default:
			worker.handler.Reply(protocol.StatusNotRegistered, msg, "not registered.")
			worker.handler.Close()
		}
	}
}

func (manager *WorkerManager) moveToRunning(worker *workerStat) {
	atomic.AddInt32(&manager.aliveCount, 1)
	log.Infoln("aliveCount:", atomic.LoadInt32(&manager.aliveCount))

	manager.muPending.Lock()
	defer manager.muPending.Unlock()
	manager.muRunning.Lock()
	defer manager.muRunning.Unlock()

	var elem *list.Element
	for e := manager.pendingWorkers.Front(); e != nil; e = e.Next() {
		if e.Value.(*workerStat) == worker {
			elem = e
			break
		}
	}
	if elem != nil {
		v := manager.pendingWorkers.Remove(elem)
		pendingWorker, ok := v.(*workerStat)
		if !ok {
			return
		}
		if pendingWorker == nil {
			return
		}
	} else {
		return
	}

	name := worker.Name
	if exWorker, ok := manager.runningWorkers[name]; ok {
		log.Warnln("WARNING: this should not happen")
		exWorker.MoveTo(worker)
		exWorker.Close()
	} else {
		manager.muLost.RLock()
		defer manager.muLost.RUnlock()
		if exWorker, ok = manager.lostWorkers[name]; ok {
			exWorker.MoveTo(worker)
			exWorker.Close()
		}
	}
	manager.runningWorkers[name] = worker

	go worker.Run()
}

func (manager *WorkerManager) Lost(tag string) *workerStat {
	log.Infoln("client is lost:", tag)

	manager.muPending.Lock()
	defer manager.muPending.Unlock()
	manager.muRunning.Lock()
	defer manager.muRunning.Unlock()

	var worker *workerStat
	var elem *list.Element

	for e := manager.pendingWorkers.Front(); e != nil; e = e.Next() {
		tmpStat := e.Value.(*workerStat)
		if tmpStat.handler.Tag == tag {
			elem = e
			break
		}
	}
	if elem != nil { //found in pending workers
		v := manager.pendingWorkers.Remove(elem)
		worker, ok := v.(*workerStat)
		if ok {
			return worker
		}
	}

	for name := range manager.runningWorkers {
		if manager.runningWorkers[name].handler.Tag == tag {
			worker = manager.runningWorkers[name]
			break
		}
	}
	if worker != nil {
		atomic.AddInt32(&manager.aliveCount, -1)
		log.Infoln("aliveCount:", atomic.LoadInt32(&manager.aliveCount))

		worker.Close()

		delete(manager.runningWorkers, worker.Name)
		manager.muLost.Lock()
		defer manager.muLost.Unlock()

		manager.lostWorkers[worker.Name] = worker
	}
	return worker
}

func (manager *WorkerManager) RemoveLost(name string, delay time.Duration) {
	timer := time.NewTimer(delay)
	select {
	case <-timer.C:
		log.Infoln("remove lost client:", name)
		manager.muLost.Lock()
		defer manager.muLost.Unlock()
		if worker, ok := manager.lostWorkers[name]; ok {
			for id := range worker.pendingTasks {
				failedTasks <- worker.pendingTasks[id]
			}
			delete(manager.lostWorkers, name)
		}
	}
}

var RRptr int64 = 0

func (manager *WorkerManager) ChooseWorkerRR(task *schema.Task) (*workerStat, error) {
	var worker *workerStat

	manager.muRunning.RLock()
	defer manager.muRunning.RUnlock()

	workerNum := int64(len(manager.runningWorkers))
	if workerNum == 0 {
		return nil, errors.New("no running workers")
	}

	ptrNow := atomic.LoadInt64(&RRptr)

	var i int64

	for _, v := range manager.runningWorkers {
		if ptrNow%workerNum == i {
			worker = v
			break
		}
		i++
	}

	if worker == nil {
		return nil, errors.New("failed to choose worker RR")
	}

	atomic.AddInt64(&RRptr, 1)
	if ptrNow == 9999999 {
		atomic.StoreInt64(&RRptr, 0)
	}
	atomic.AddInt32(&worker.count, 1)
	atomic.AddInt32(&worker.capacity, -1)

	return worker, nil
}

func (manager *WorkerManager) ChooseWorker(task *schema.Task) (*workerStat, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	host := strings.Split(task.Host, ":")[0]
	address, err := manager.resolver.LookupHost(ctx, host)
	if err != nil || len(address) == 0 {
		log.Warnln("failed to lookup ip address of host:", task.Host, err)
		task.Track.Address = task.Host
	} else {
		task.Track.Address = address[0]
	}

	manager.muHostWorker.Lock()
	defer manager.muHostWorker.Unlock()

	worker, ok := manager.hostWorkers[task.Track.Address]
	if ok {
		if worker.Closed() || !worker.ConnectionAliveBool() || !worker.ProcessingHostTasks(task.Track.Address) {
			delete(manager.hostWorkers, task.Track.Address)
		} else {
			if worker.GetTaskCount() < manager.maxTaskCount+100 {
				return worker, nil
			}

			log.Debug("wait idle worker", task.ID)
			return nil, errors.New("no idle worker error")
		}
	}

	manager.muRunning.RLock()
	defer manager.muRunning.RUnlock()

	if len(manager.runningWorkers) == 0 {
		return nil, errors.New("no runnign workds in manager.runningWorkers")
	}

	worker = nil
	for _, v := range manager.runningWorkers {
		count := v.GetTaskCount()
		if count > manager.maxTaskCount || v.GetCapacity() <= 0 || v.GetHostCount() >= v.MaxHostCount {
			continue
		}

		worker = v
		break
	}

	if worker == nil {
		return nil, errors.New("no proper worker in runningworkers")
	}

	if worker == nil || !worker.ConnectionAliveBool() || worker.GetCapacity() <= 0 || worker.GetTaskCount() > manager.maxTaskCount {
		return nil, fmt.Errorf("alive %v, capacity %d, task count %d", worker.ConnectionAliveBool(), worker.GetCapacity(), worker.GetTaskCount())
	}

	atomic.AddInt32(&worker.count, 1)
	atomic.AddInt32(&worker.capacity, -1)

	log.Debug("dispatch success", worker.Name, worker.GetTaskCount(), worker.GetCapacity())
	manager.hostWorkers[task.Track.Address] = worker

	return worker, nil
}

func (manager *WorkerManager) chooseWorkerForScanAtOnce(task *schema.Task) *workerStat {
	manager.muHostWorker.Lock()
	defer manager.muHostWorker.Unlock()

	worker, ok := manager.hostWorkers[task.Track.Address]
	if ok {
		if worker.Closed() || !worker.ConnectionAliveBool() || !worker.ProcessingHostTasks(task.Track.Address) {
			delete(manager.hostWorkers, task.Track.Address)
		} else {
			return worker
		}
	}

	manager.muRunning.RLock()
	defer manager.muRunning.RUnlock()

	worker = nil
	for _, v := range manager.runningWorkers {
		if v.GetCapacity() <= 0 {
			continue
		}

		worker = v
		break
	}

	if worker == nil || !worker.ConnectionAliveBool() || worker.GetCapacity() <= 0 {
		return nil
	}

	atomic.AddInt32(&worker.count, 1)
	atomic.AddInt32(&worker.capacity, -1)

	log.Debug("dispatch success scan atonce", worker.Name, worker.GetTaskCount(), worker.GetCapacity())
	manager.hostWorkers[task.Track.Address] = worker

	return worker
}

func (manager *WorkerManager) RestoreTask(task *schema.Task) {
	workerName := task.Track.ClientID
	worker, ok := manager.lostWorkers[workerName]
	if !ok {
		worker = newClosedWorkerStat()
		worker.Name = workerName
		manager.lostWorkers[workerName] = worker
		go workers.RemoveLost(workerName, time.Second*60)
	}
	atomic.AddInt32(&worker.count, 1)

	worker.muPending.Lock()
	defer worker.muPending.Unlock()

	worker.pendingTasks[task.ID] = task
	if v, ok := worker.hostTaskCount[task.Host]; ok {
		worker.hostTaskCount[task.Host] = v + 1
	} else {
		worker.hostTaskCount[task.Host] = 1
	}

	manager.hostWorkers[task.Track.Address] = worker
}

func (manager *WorkerManager) Count() int {
	return int(atomic.LoadInt32(&manager.aliveCount))
}

func (manager *WorkerManager) GetWorkerByAddress(address string) *workerStat {
	manager.muHostWorker.RLock()
	defer manager.muHostWorker.RUnlock()

	for k := range manager.hostWorkers {
		log.Info("hostWorkers", k, manager.hostWorkers[k].Name)
	}
	log.Info("hostWorkers", address, manager.hostWorkers[address])
	if v, ok := manager.hostWorkers[address]; ok {
		return v
	}
	return nil
}

func (manager *WorkerManager) PeriodSaveWorkerState() {
	t := time.NewTicker(time.Minute * 5)
	defer t.Stop()
	for {
		<-t.C
		for k := range manager.runningWorkers {
			ws := manager.runningWorkers[k]
			if ws == nil {
				continue
			}
			stat := NewWorkerTaskStat(ws)
			stat.SaveToDB()
			ws.SetTaskFinishedCount(0)
		}
	}
}
