package sensitiveimage

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"
	"websec/utils/log"
)

var (
	imageCheckURL = "http://***********:5006/api/image/detect"
	levelDescMap  = map[string]string{
		"0":   "二维码",
		"1":   "人-非色情",
		"2":   "其他",
		"3":   "截图-非聊天",
		"4":   "文本",
		"5":   "聊天记录截图",
		"600": "色情图片",
		"7":   "证件",
	}
)

const (
	Appid         string = "2fsdif@"
	Version              = "1.0"
	Uid                  = "123"
	Secret               = "key"
	ImageCheckURL        = "http://imgcheck.websec.cn/detect"
)

type ImageInfo struct {
	FileName string `json:"file_name"`
	Type     string `json:"type"`
	Base64   string `json:"base64"`
}

type ImageCheckReq struct {
	ImageInfo ImageInfo `json:"image_info"`
	Action    string    `json:"action"`
}

type HeaderReq struct {
	Header Header `json:"header"`

	Ask string `json:"ask"`
}
type Header struct {
	Appid     string `json:"appid"`
	Nonce     string `json:"nonce"`
	Timestamp int64  `json:"timestamp"`
	V         string `json:"v"`
	Sign      string `json:"sign"`
}

type ImageCheckResp struct {
	Description string `json:"description"`
	Data        struct {
		Add   int `json:"add"`
		Query struct {
			Tag   string `json:"tag"`
			MD5   string `json:"md5"`
			TagID int    `json:"tag_id"`
		} `json:"query"`
	} `json:"data"`
	Status string `json:"Status"`
}

func (resp *ImageCheckResp) IsSuccessful() bool {
	return resp.Status == "0"
}

func (resp *ImageCheckResp) IllegalDesc() string {
	switch resp.Data.Query.TagID {
	case 600:
		return levelDescMap[strconv.Itoa(resp.Data.Query.TagID)]
	}
	return ""
}

func CheckImage(ctx context.Context, name string, content []byte) (*ImageCheckResp, error) {
	b64 := base64.StdEncoding.EncodeToString(content)
	prefix := http.DetectContentType(content)
	prefixs := strings.Split(prefix, "/")
	if len(prefixs) == 2 {
		prefix = prefixs[1]
	}
	imageReq := ImageCheckReq{
		ImageInfo: ImageInfo{
			FileName: name,
			Base64:   b64,
			Type:     prefix,
		},
		Action: "add",
	}
	req := &HeaderReq{
		Header: Header{
			Appid:     Appid,
			Nonce:     nonce(),
			Timestamp: int64(time.Now().Unix() * 1000),
			V:         Version,
		},
	}
	req.encode(imageReq)
	reqBin, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	bytesBuffer := []byte{}
	var hendLen byte = 0x06
	var hendV byte = 0x51
	msgId := make([]byte, 2)
	binary.BigEndian.PutUint16(msgId, uint16(rand.Intn(10)))
	last := make([]byte, 2)
	binary.BigEndian.PutUint16(last, uint16(0))
	bytesBuffer = append(bytesBuffer, hendLen)
	bytesBuffer = append(bytesBuffer, hendV)
	bytesBuffer = append(bytesBuffer, msgId...)
	bytesBuffer = append(bytesBuffer, last...)
	bytesBuffer = append(bytesBuffer, reqBin...)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", ImageCheckURL, bytes.NewBuffer(bytesBuffer))
	if err != nil {
		return nil, err
	}
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Add("Host", "imgcheck.websec.cn")
	httpClient := http.Client{}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		log.Errorf("request check img failed err:%v", err)
		return nil, err
	}
	defer resp.Body.Close()
	ret := new(ImageCheckResp)
	err = json.NewDecoder(resp.Body).Decode(ret)
	if err != nil {
		log.Errorf("request check img response json failed err:%v", err)
		return nil, err
	}
	log.Infof("check img res %+v", ret)
	return ret, err
}

func (h *HeaderReq) encode(ask ImageCheckReq) {
	str := fmt.Sprintf("%s%s%s%s", h.Header.Appid, h.Header.Nonce, strconv.Itoa(int(h.Header.Timestamp)), h.Header.V)
	askByt, err := json.Marshal(ask)
	if err != nil {
		return
	}
	str = fmt.Sprintf("%s%s", str, string(askByt))
	str = fmt.Sprintf("%s%s", str, Secret)
	h.Header.Sign = md5String(str)
	h.Ask, err = aesEncrypt(Secret, h.Header.Sign, askByt)
	if err != nil {
		return
	}
	return
}
func nonce() string {
	return md5String(fmt.Sprintf("%d%d", rand.Intn(10), time.Now().Unix()))
}

func md5String(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	s := hex.EncodeToString(h.Sum(nil))
	return s[len(s)-16:]
}
func aesEncrypt(key, iv string, val []byte) (string, error) {

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	blockSize := block.BlockSize()
	originData := pad(val, blockSize)

	blockMode := cipher.NewCBCEncrypter(block, []byte(iv))

	crypted := make([]byte, len(originData))
	blockMode.CryptBlocks(crypted, originData)
	return base64.StdEncoding.EncodeToString(crypted), nil
}
func pad(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(0)}, padding)
	return append(ciphertext, padtext...)
}
