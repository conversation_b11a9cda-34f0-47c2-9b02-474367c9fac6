package scan

import (
	"net/http"
	"net/url"
	"path/filepath"
	"time"
	"websec/scan/rules"
	"websec/utils"
	"websec/utils/log"

	"github.com/valyala/fasthttp"
)

func genFileTestURL(URL string, param *rules.Param) string {
	parts, err := url.Parse(URL)
	if err != nil {
		log.Errorln("failed to parse url for file test:", URL)
		return ""
	}
	parts.RawPath = parts.Path + param.Value
	return utils.ToString(parts)
}

var ignoredFileExts = []string{".do", ".action", ".portal"}

func (scanner *WSScanner) doVulnerableTestFile(link *AffectLink, vul *rules.Vulnerability, param *rules.Param) (bool, *ScanResult) {
	parts, err := url.Parse(link.URL)
	if err != nil {
		log.Errorln("failed to parse url:", link.URL, err)
		return false, nil
	}
	ext := filepath.Ext(parts.Path)
	if vul.VulXML == XMLFileBackupCheck {
		for i := range ignoredFileExts {
			if ext == ignoredFileExts[i] {
				return false, nil
			}
		}
	}
	vulnerable, scanResult := scanner.doVulnerableTest(link, vul, param)
	if vulnerable && vul.VulXML == XMLFileBackupCheck {
		foundVuls := scanResult.FoundVuls
		if len(foundVuls) == 0 {
			log.Errorln("this should NOT happen: vulnerable but FoundVuls is empty:", link, vul, param)
			return vulnerable, scanResult
		}
		foundVul := foundVuls[len(foundVuls)-1]
		neverURL := foundVul.VulURL + "never"
		for retry := 0; retry < 3; retry++ {
			if retry > 0 {
				time.Sleep(2 * time.Second)
			}
			request := &ScanRequest{Method: http.MethodGet, URL: neverURL}
			response, _, err := scanner.doRequest(request)
			scanResult.RequestCount++
			if err != nil {
				scanResult.ErrorCount++
				continue
			}
			defer fasthttp.ReleaseResponse(response)
			status := response.StatusCode()
			if status == 200 {
				vulnerable = false
				break
			}
			if status == 302 {

				if lastStatus, ok := foundVul.Context["response_code"]; ok {
					s, _ := lastStatus.(int)
					if s == 302 {
						vulnerable = false
						break
					}
				}
			}
			if retry == 2 {

				if status == 599 {

					vulnerable = false
					break
				}
			} else {

				if status == 599 {

					continue
				} else {

					retry = 3
				}
			}
			context := foundVul.Context
			body, err := utils.GetUtf8Body(response)
			context["never"] = map[string]interface{}{
				"url":    neverURL,
				"status": status,
				"body":   body,
			}
		}
		if !vulnerable {
			scanResult.FoundVuls = foundVuls[:len(foundVuls)-1]
		}
	}
	return vulnerable, scanResult
}
