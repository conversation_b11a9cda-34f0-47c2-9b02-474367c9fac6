package protocol

import (
	"fmt"

	"github.com/ugorji/go/codec"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	TypeRequest uint8 = iota
	TypeResponse
)

const (
	ProtoBuf uint8 = iota
	Thrift
	MsgPack
	JSON
)

const (
	StatusOK uint8 = iota
	StatusNotRegistered
	StatusInvalidAction
	StatusTaskExisted
	StatusFailed
)

const (
	ActionRegister  uint8 = 1
	ActionTask            = 2
	ActionFinish          = 3
	ActionHeartbeat       = 4
	ActionStopTask        = 5
)

type Message struct {
	ID            uint32      `codec:"id"`
	Type          uint8       `codec:"type"`
	SerializeType uint8       `codec:"serialize_type,omitempty"`
	Action        uint8       `codec:"action,omitempty"`
	Status        uint8       `codec:"status,omitempty"`
	ClientID      string      `codec:"client_id,omitempty"`
	Body          interface{} `codec:"body,omitempty"`
}

func (msg *Message) String() string {
	return fmt.Sprintf(
		"ID: %v, Type: %v, SerializeType: %v, Action: %v, Client: %v, Status: %v, Body: %v",
		msg.ID, msg.Type, msg.SerializeType, msg.Action, msg.ClientID, msg.Status, msg.Body,
	)
}

func (msg *Message) DecodeBody(body interface{}) error {
	var buf []byte
	var mh codec.MsgpackHandle
	enc := codec.NewEncoderBytes(&buf, &mh)

	err := enc.Encode(msg.Body)
	if err == nil {
		dec := codec.NewDecoderBytes(buf, &mh)
		err = dec.Decode(&body)
		if err == nil {
			msg.Body = body
		}
	}
	return err
}

type MessageError struct {
	Error string `codec:"error"`
}

type RegisterBody struct {
}

type TaskFinishBody struct {
	TaskID  primitive.ObjectID `codec:"task_id"`
	AssetID primitive.ObjectID `codec:"asset_id"`
	Status  int64              `codec:"status"`
	Reason  string             `codec:"reason,omitempty"`
}

type TaskSummaryBody struct {
	TaskID     primitive.ObjectID `codec:"task_id"`
	AssetID    primitive.ObjectID `codec:"asset_id"`
	StartTime  string             `codec:"start_time"`
	EndTime    string             `codec:"end_time"`
	TotalLinks int64              `codec:"total_links"`
	TotalFail  int64              `codec:"total_fail"`
}

type HeartbeatBody struct {
	Count    int32 `codec:"count"`
	Capacity int32 `codec:"capacity"`
}

type AssetScanFinishBody struct {
	AssetID     string `json:"asset_id"`
	VisitedUrls uint64 `json:"visited_urls"`
}
