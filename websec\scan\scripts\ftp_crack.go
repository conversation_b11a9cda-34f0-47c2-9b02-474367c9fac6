package scripts

import (
	"time"

	"github.com/jlaffaye/ftp"
)

func FTPCrack(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addr := args.Host + ":21"
	conn, err := ftp.DialTimeout(addr, time.Second*10)
	if err != nil {
		return nil, err
	}
	defer conn.Quit()

	for _, pair := range [][]string{
		[]string{"admin", "admin"},
		[]string{"Admin", "123456"},
		[]string{"anonymous", ""},
	} {
		username, password := pair[0], pair[1]
		err = conn.Login(username, password)
		if err != nil {
			continue
		}

		return &ScriptScanResult{Vulnerable: true, Output: "ftp://" + username + ":" + password + "@" + addr}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("ftp_weak_password_vul.xml", FTPCrack)
}
