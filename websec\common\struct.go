package common

import (
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"unsafe"
	"websec/utils/acsmx"
	"websec/utils/log"

	"sync/atomic"

	"github.com/PuerkitoBio/goquery"
	"github.com/valyala/bytebufferpool"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

//#include<string.h>
import "C"

var (
	ignoredPath = []string{
		"error",
		"/search.php",
		"/search.aspx",
		"/reg",
		"/login",
		"/captcha",
	}

	ignoredParams = []string{
		"form", "pageSize", "q", "return", "sort",
		"orderby", "location", "phpsessid", "hash",
		"filter", "keyword", "dateline", "jsessionid",
		"token", "error",
	}
	ignoredDigitParams = []string{
		"page", "random", "pageIndex", "offset",
	}

	ContentPool bytebufferpool.Pool
)

type DetectStats struct {
	PageCount    int64
	ErrorCount   int64
	ErrorReasons string
}

func URLSha1(rawURL string) string {
	rawURL = strings.ToLower(rawURL)
	parts, err := url.Parse(rawURL)
	if err != nil {
		return ""
	}
	keyValues := removeNonSenseValues(parts.Query())
	keyValuesSlice := toStringSlice(keyValues)
	query := strings.Join(keyValuesSlice, "&")

	var u string
	if query != "" {
		u = fmt.Sprintf("%s://%s%s?%s", parts.Scheme, parts.Host, parts.Path, query)
	} else {
		u = fmt.Sprintf("%s://%s%s", parts.Scheme, parts.Host, parts.Path)
	}

	sha1Hash := sha1.New()
	sha1Hash.Write([]byte(u))
	return hex.EncodeToString(sha1Hash.Sum(nil))
}

type OuterWebPageFrame struct {
	URL          string                     `json:"url"`
	Content      []byte                     `json:"content"`
	StatusCode   int                        `json:"status_code"`
	Images       []string                   `json:"images"`
	MainFrameURL string                     `json:"mainframe_url"`
	GQDocument   *goquery.Document          `json:"-"` //detect 模块才用到
	sha1         string                     `json:"-"`
	ContentSave  *bytebufferpool.ByteBuffer `json:"-"`
}

func (outer *OuterWebPageFrame) URLHash() string {
	if outer.sha1 == "" {
		outer.sha1 = URLSha1(outer.URL)
	}
	return outer.sha1
}

func (outer OuterWebPageFrame) ContentHash() string {
	if len(outer.Content) > 0 {
		hash := md5.New()
		hash.Write(outer.Content)
		return hex.EncodeToString(hash.Sum(nil))
	}
	return ""
}

type OuterWebPage struct {
	URL          string              `json:"url"`
	EffectiveURL string              `json:"effective_url"` // 最终跳转到的URL
	StatusCode   int                 `json:"status_code"`
	Headers      HttpHeaders         `json:"headers"`
	Frames       []OuterWebPageFrame `json:"frame"`
}

type NetworkWebpage struct {
	URL        string      `json:"url"`
	Method     string      `json:"method"`
	Data       string      `json:"data"`
	StatusCode int         `json:"status_code"`
	Headers    HttpHeaders `json:"headers"`
	Content    []byte      `json:"content"`
	IsOuterURL bool        `json:"is_outer_url"`
	IsStatic   bool        `json:"is_static"`
	urlSha1    string      `json:"-"`

	ContentSave *bytebufferpool.ByteBuffer `json:"-"`
}

func (page *NetworkWebpage) URLHash() string {
	if page.urlSha1 != "" {
		return page.urlSha1
	}

	page.urlSha1 = URLSha1(page.URL)
	return page.urlSha1
}

func (page NetworkWebpage) Clear() {
	page.ClearContent()
	return
}

func (page NetworkWebpage) ClearContent() {
	if page.ContentSave != nil {
		ContentPool.Put(page.ContentSave)
		page.ContentSave = nil
	}
	return
}

func (page NetworkWebpage) ContentHash() string {
	if len(page.Content) > 0 {
		hash := md5.New()
		hash.Write(page.Content)
		return hex.EncodeToString(hash.Sum(nil))
	}
	return ""
}

func (page NetworkWebpage) SaveBytesBuff(maxByte int) error {
	if len(page.Content) > maxByte {
		log.Warnln("Chrome  networkpages response too large, so truncate: ", page.URL)

		page.ContentSave.B = page.ContentSave.B[:maxByte]

	}

	return nil
}

type Webpage struct {
	URL               string      `json:"url,omitempty"`
	Method            string      `json:"method,omitempty"`
	Data              string      `json:"data,omitempty"`
	EffectiveURL      string      `json:"effective_url,omitempty"` // 最终跳转到的URL
	ByChrome          bool        `json:"by_chrome,omitempty"`
	StatusCode        int         `json:"status_code,omitempty"`
	IsStatic          bool        `json:"is_static,omitempty"`
	Headers           HttpHeaders `json:"headers,omitempty"`
	Referer           string      `json:"referer,omitempty"`
	AssetID           string      `json:"asset_id,omitempty"`
	JobID             string      `json:"job_id,omitempty"`
	ExternalScanDepth int         `json:"external_scan_depth,omitempty"`

	Content         []byte                   `json:"content,omitempty"`
	IsOuterURL      bool                     `json:"is_outer_url,omitempty"`
	Images          []string                 `json:"images,omitempty"`
	Swfs            []string                 `json:"swfs,omitempty"`
	OuterWebpages   map[string]*OuterWebPage `json:"outer_webpages,omitempty"`
	NetworkWebpages []NetworkWebpage         `json:"network_webpages,omitempty"` // 该页面上发起的请求
	MainFrameURL    string                   `json:"mainframe_url,omitempty"`
	Depth           int32                    `json:"depth,omitempty"`
	CrawledAt       time.Time                `json:"crawled_at,omitempty"`
	UID             string                   `json:"uid,omitempty"`
	IsNew           bool                     `json:"is_new"`

	Host             string            `json:"-"`
	contentHash      string            `json:"-"`
	urlSha1          string            `json:"-"`
	effectiveURLSha1 string            `json:"-"`
	GQDocument       *goquery.Document `json:"-"`
	SaveToMongo      bool              `json:"-"`
	OldVersionTime   int64             `json:"-"`

	ContentSave *bytebufferpool.ByteBuffer `json:"-"`
}

func InitNewWebpage() *Webpage {
	return &Webpage{}
}

func (webpage *Webpage) ContentHash() string {
	if webpage.contentHash != "" {
		return webpage.contentHash
	}
	if len(webpage.Content) > 0 {
		hash := md5.New()
		hash.Write(webpage.Content)
		webpage.contentHash = hex.EncodeToString(hash.Sum(nil))
		return webpage.contentHash
	}
	return ""
}

func (webpage *Webpage) URLHash() string {
	if webpage.urlSha1 != "" {
		return webpage.urlSha1
	}

	webpage.urlSha1 = URLSha1(webpage.URL)
	return webpage.urlSha1
}

func (webpage *Webpage) SaveBytesBuff(maxByte int) error {
	if len(webpage.Content) > maxByte {
		log.Warnln("Chrome response too large, so truncate: ", webpage.EffectiveURL)

		webpage.ContentSave.Write(webpage.Content[:maxByte])
	} else {
		webpage.ContentSave.Write(webpage.Content)
	}
	webpage.Content = webpage.Content[:0]
	return nil
}

func (webpage *Webpage) EffectiveURLHash() string {
	if webpage.effectiveURLSha1 != "" {
		return webpage.effectiveURLSha1
	}

	webpage.effectiveURLSha1 = URLSha1(webpage.EffectiveURL)
	return webpage.effectiveURLSha1
}

func (webpage Webpage) VersionTime() int64 {
	return webpage.CrawledAt.UTC().UnixNano() / 1e6
}

func (webpage *Webpage) Reset() {
	for _, networkPage := range webpage.NetworkWebpages {
		networkPage.ClearContent()

	}
	webpage.ClearContent()

}

func (webpage *Webpage) ResetNetwork() {
	for _, networkPage := range webpage.NetworkWebpages {
		networkPage.ClearContent()
	}

}

func (webpage *Webpage) ClearContent() {
	if webpage.ContentSave != nil {

		ContentPool.Put(webpage.ContentSave)
		webpage.ContentSave = nil
	}
}

func memSet(s unsafe.Pointer, c byte, n uintptr) {
	C.memset(s, (C.int)(c), (C.size_t)(n))
}

type HttpHeaders = http.Header

type IgnoredBlackLinkDomains struct {
	domains map[string]bool
	m       *sync.RWMutex
}

func (ignoredDomains *IgnoredBlackLinkDomains) IsIgnored(domain string) bool {
	ignoredDomains.m.RLock()
	defer ignoredDomains.m.RUnlock()

	if _, ok := ignoredDomains.domains[domain]; ok {
		return true
	}
	return false
}

func (ignoredDomains *IgnoredBlackLinkDomains) UpdateDomains(domains map[string]bool) {
	ignoredDomains.m.Lock()
	defer ignoredDomains.m.Unlock()

	ignoredDomains.domains = domains
}

func NewIgnoredBlackLinkDomains() *IgnoredBlackLinkDomains {
	return &IgnoredBlackLinkDomains{
		domains: make(map[string]bool),
		m:       &sync.RWMutex{},
	}
}

type CrawlRequest struct {
	AssetID   string      `json:"asset_id"`
	JobID     string      `json:"job_id"`
	URL       string      `json:"url"`
	Method    string      `json:"method"`
	Depth     int32       `json:"depth"`
	Referer   string      `json:"referer"`
	Static    bool        `json:"static"`
	Outer     bool        `json:"outer"`
	Headers   HttpHeaders `json:"headers"`
	UserAgent string      `json:"string"`
	FilterReg string      `json:"filter_reg"`
}

type CrawlResponse struct {
	Status   CrawlResponseStatus `json:"status"`
	Webpages []*Webpage          `json:"webpages,omitempy"`
}

type CrawlResponseStatus = int

const (
	CrawlSuccess                   CrawlResponseStatus = 0
	CrawlReadRequestBodyError                          = 1
	CrawlUnmarshalRequestBodyError                     = 2
	CrawlNoAvailableTab                                = 3
	CrawlNavigateError                                 = 4
)

type InjectJavaScript struct {
	InjectBeforeLoad string

	InjectAfterLoad string
}

type OCRRequest struct {
	ImgURL string `json:"img_url"`
}

type OCRResponse struct {
	Status       Status `json:"status"`
	Message      string `json:"message,omitempty"`
	Content      string `json:"content,omitempty"`
	ImageURLHash string `json:"image_url_hash"`
	ImageData    string `json:"image_data,omitempty"`
	ImageAddress string `json:"image_address,omitempty"`
	IsNew        bool   `json:"is_new,omitempty"`
}

type OCRImage struct {
	ID            primitive.ObjectID `bson:"_id,omitempty"`
	ImageURL      string             `bson:"image_url"`
	ImageURLHash  string             `bson:"image_url_hash"`
	ImageMD5      string             `bson:"image_md5,omitempty"`
	Content       string             `bson:"content,omitempty"`
	ImageAddress  string             `bson:"image_address,omitempty"`
	CreatedAt     time.Time          `bson:"created_at"`
	LastCheckedAt time.Time          `bson:"last_checked_at"`
}

type Status string

const (
	SUCCESS Status = "success"
	FAIL           = "fail"
)

type PageLastGetAt struct {
	LastGetAt int64 `json:"at"`
}

func (lastGetAt *PageLastGetAt) GetTime() time.Time {
	if lastGetAt.LastGetAt == 0 {

		return time.Unix(time.Now().Unix()-2*60*60, 0)
	}
	return time.Unix(lastGetAt.LastGetAt, 0)
}

type GetLastGetAtFunc func(string, string) *PageLastGetAt

type SetLastGetAtFunc func(string, string, *PageLastGetAt) bool

type SetCrawlingProgressFunc func(string, string) bool

type URLReferee struct {
	ID primitive.ObjectID `bson:"_id,omitempty"`

	Host         string `bson:"host"`
	URLHash      string `bson:"url_hash"`
	ReferHost    string `bson:"referee_host"`
	ReferURLHash string `bson:"referee_url_hash"`
}

type Link struct {
	URL               string
	Method            string
	Scheme            string
	Host              string
	URI               string
	URLHash           string
	Depth             int32
	MediaType         string
	Data              string
	Referer           string
	Static            bool
	Outer             bool
	Headers           HttpHeaders
	UserAgent         string
	NotProcessWebPage bool
	IsNew             bool
	MainFrameURL      string

	ImgHash string

	AssetID string
	JobID   string
}

func (link *Link) URLSha1() string {
	if link.URLHash != "" {
		return link.URLHash
	}
	link.URLHash = URLSha1(link.URL)
	return link.URLHash
}

func (link *Link) Reset() {
	link.URL = ""
	link.Method = ""
	link.Scheme = ""
	link.Host = ""
	link.URI = ""
	link.URLHash = ""
	link.Depth = 0
	link.MediaType = ""
	link.Data = ""
	link.Referer = ""
	link.Static = false
	link.Outer = false
	link.Headers = nil
	link.UserAgent = ""
	link.NotProcessWebPage = false
	link.IsNew = false
	link.MainFrameURL = ""
}
func (link *Link) Clear() {
	PutLink(link)
}

func (link *Link) GetHost() string {
	if link.Host != "" {
		return link.Host
	}

	parts, _ := url.Parse(link.URL)
	link.Host = parts.Host
	return link.Host
}

func (link *Link) Fingerprint() string {
	if link.Depth > 2 {
		return link.extreamFingerprint()
	}

	parts, err := url.Parse(link.URL)
	if err != nil {
		return ""
	}
	path := parts.Path
	query := ""

	lowerPath := strings.ToLower(parts.Path)
	isIgnored := false
	for _, v := range ignoredPath {
		if strings.Contains(lowerPath, v) {
			isIgnored = true
			break
		}
	}
	if !isIgnored {
		keyValues := removeNonSenseValues(parts.Query())
		keyValuesSlice := toStringSlice(keyValues)
		query = strings.Join(keyValuesSlice, "&")
	}

	u := fmt.Sprintf("%s://%s%s?%s", parts.Scheme, parts.Host, path, query)

	sha1Hash := sha1.New()
	sha1Hash.Write([]byte(strings.ToLower(u)))

	if link.Method == "POST" {
		if values, err := url.ParseQuery(link.Data); err == nil {
			keys := getSortedKeys(values)
			for _, key := range keys {
				sha1Hash.Write([]byte(key))
			}
		}
	}

	return hex.EncodeToString(sha1Hash.Sum(nil))[:8]
}

func (link *Link) extreamFingerprint() string {
	parts, err := url.Parse(link.URL)
	if err != nil {
		return ""
	}
	path := parts.Path
	query := ""

	lowerPath := strings.ToLower(parts.Path)
	isIgnored := false
	for _, v := range ignoredPath {
		if strings.Contains(lowerPath, v) {
			isIgnored = true
			break
		}
	}
	if !isIgnored {
		keyValues := removeNonSenseValues(parts.Query())

		ks := make([]string, 0, len(keyValues))
		for i := range keyValues {
			ks = append(ks, i)
		}

		if len(ks) > 1 {
			sort.SliceStable(ks, func(i, j int) bool { return strings.Compare(ks[i], ks[j]) < 0 })

			for i := range ks {
				query += ks[i]
				if i != len(ks)-1 {
					query += "&"
				}
			}
		}
	}

	u := fmt.Sprintf("%s://%s%s?%s", parts.Scheme, parts.Host, path, query)

	sha1Hash := sha1.New()
	sha1Hash.Write([]byte(strings.ToLower(u)))

	if link.Method == "POST" {
		if values, err := url.ParseQuery(link.Data); err == nil {
			keys := getSortedKeys(values)
			for _, key := range keys {
				sha1Hash.Write([]byte(key))
			}
		}
	}

	return hex.EncodeToString(sha1Hash.Sum(nil))[:8]
}

var linkPool sync.Pool
var linkCount int64

func newLink() *Link {
	defer func() {
		atomic.AddInt64(&linkCount, 1)
	}()
	if v := linkPool.Get(); v != nil {
		l := v.(*Link)
		l.Reset()
		return l
	}
	return new(Link)
}

func PutLink(l *Link) {
	l.Reset()
	linkPool.Put(l)
	atomic.AddInt64(&linkCount, -1)
}

func PrintLinkPoolCount() {
	cnt := atomic.LoadInt64(&linkCount)
	log.Infoln("linkPool PrintLinkPoolCount:", cnt)
}

func GenLink(rawURL, method, referer, data string, depth int32) *Link {
	link := newLink()
	link.URL = rawURL
	link.Method = method
	link.Referer = referer
	link.Data = data
	link.Depth = depth
	link.Headers = HttpHeaders{}
	link.IsNew = true

	return link
}

func removeNonSenseValues(qs map[string][]string) map[string]string {
	result := make(map[string]string)

	for key := range qs {
		v := qs[key][0]
		if strings.HasPrefix(key, " ") {
			v = ""
		} else {
			lowerKey := strings.ToLower(key)
			for _, vv := range ignoredParams {
				if strings.Contains(lowerKey, vv) {
					v = ""
					break
				}
			}

			if v != "" {
				if cv, err := strconv.Atoi(v); err == nil {
					if cv >= 0 {
						for _, vv := range ignoredDigitParams {
							if vv == lowerKey {
								v = ""
								break
							}
						}
					}
				}
			}
		}

		result[key] = v
	}
	return result
}

func toStringSlice(m map[string]string) []string {
	result := []string{}
	for k, v := range m {
		tmp := fmt.Sprintf("%s=%s", k, v)
		result = append(result, tmp)
	}
	sort.Strings(result)
	return result
}

func getSortedKeys(m map[string][]string) []string {
	result := []string{}
	for key := range m {
		result = append(result, key)
	}
	sort.Strings(result)
	return result
}

type MatcherGroup struct {
	FirstMatcher  *acsmx.Matcher
	SecondMatcher *acsmx.Matcher
}

func (group *MatcherGroup) SearchAll(content []byte) (int, []acsmx.MatchedWord) {
	matchedWords := make([]acsmx.MatchedWord, 0, 10)

	_, words := group.SecondMatcher.SearchAll(content)
	for i := range words {
		endPosition := words[i].Position
		startPosition := 0
		if endPosition > 20 {
			startPosition = endPosition - 20
		}
		n, prevWords := group.FirstMatcher.SearchAll(content[startPosition:endPosition])
		if n > 0 {

			matchedWords = append(matchedWords, acsmx.MatchedWord{
				Position: words[i].Position - len([]byte(prevWords[n-1].Word)),
				Word:     prevWords[n-1].Word + words[i].Word,
			})
		}
	}
	return len(matchedWords), matchedWords
}

func (webpage *Webpage) MarshalJSON() (data []byte, err error) {
	type Item Webpage

	if webpage.ContentSave == nil {
		return json.Marshal((*Item)(webpage))
	}

	return json.Marshal(&struct {
		*Item
		A []byte `json:"content,omitempty"`
	}{
		Item: (*Item)(webpage),
		A:    webpage.ContentSave.Bytes()})
}

func (webpage *Webpage) UnmarshalJSON(data []byte) error {
	type Item Webpage
	aux := &struct {
		*Item
	}{
		Item: (*Item)(webpage),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}
	if len(aux.Content) > 0 {
		p := ContentPool.Get()
		p.Write(aux.Content)
		webpage.ContentSave = p
	}

	return nil
}

func (networkPage *NetworkWebpage) MarshalJSON() (data []byte, err error) {
	type Item NetworkWebpage

	if networkPage.ContentSave == nil {
		return json.Marshal((*Item)(networkPage))
	}

	return json.Marshal(&struct {
		*Item
		A []byte `json:"content,omitempty"`
	}{
		Item: (*Item)(networkPage),
		A:    networkPage.ContentSave.Bytes()})
}

func (networkPage *NetworkWebpage) UnmarshalJSON(data []byte) (err error) {
	type Item NetworkWebpage

	aux := &struct {
		*Item
	}{
		Item: (*Item)(networkPage),
	}
	if len(aux.Content) > 0 {
		p := ContentPool.Get()
		p.Write(aux.Content)
		networkPage.ContentSave = p
	}

	return nil
}

func (outPage OuterWebPageFrame) MarshalJSON() (data []byte, err error) {
	type Item OuterWebPageFrame

	if outPage.ContentSave == nil {
		return json.Marshal(outPage)
	}

	return json.Marshal(&struct {
		Item
		A []byte `json:"content,omitempty"`
	}{
		Item: Item(outPage),
		A:    outPage.ContentSave.Bytes()})
}

func (outPage *OuterWebPageFrame) UnmarshalJSON(data []byte) (err error) {
	type Item OuterWebPageFrame

	aux := &struct {
		*Item
	}{
		Item: (*Item)(outPage),
	}

	err = json.Unmarshal(data, aux)
	if err != nil {
		return err
	}

	if len(aux.Content) > 0 {
		p := ContentPool.Get()
		p.Write(aux.Content)
		outPage.ContentSave = p
	}

	return nil
}
