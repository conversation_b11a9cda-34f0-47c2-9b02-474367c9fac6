package scan

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
	"websec/common"

	"github.com/go-cmd/cmd"
	"github.com/mhqiang/logger"

	"websec/utils/log"

	"github.com/google/uuid"
)

const (
	XrayPath      string = "/opt/projects/xray/"
	DefaultConfig        = "/opt/projects/xray/config.yaml"
)

func generateConfig(headerSets common.HttpHeaders) (configFile, outFile string, err error) {
	uuidWithHyphen := uuid.New()

	uuid := strings.Replace(uuidWithHyphen.String(), "-", "", -1)
	configFileName := uuid + ".yaml"
	outFileName := uuid + ".json"
	configFile = filepath.Join(XrayPath, configFileName)
	outFile = filepath.Join(XrayPath, outFileName)

	var commonds []string
	commonds = append(commonds, "cat "+DefaultConfig)

	for k, v := range headerSets {
		setValue := strings.Join(v, ";")
		curCommond := fmt.Sprintf(`sed 's/# %s: .*/%s: %s/g'`, k, k, setValue)
		commonds = append(commonds, curCommond)
	}

	configCommond := strings.Join(commonds, "|") + " > " + configFile
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	c := cmd.NewCmd("bash", "-c", configCommond)
	select {
	case <-ctx.Done():
		err = fmt.Errorf(" get  config timeout")
		c.Stop()
	case <-c.Start():
	}

	return
}

func (scanner *WSScanner) ScanXray(linkOrg *AffectLink, files ...string) {

	link := linkOrg.DeepCopy()
	var configFile, outputFile string
	var outputFiles []string
	var err error

	if len(files) == 0 {

		if len(link.Headers) > 0 {
			configFile, outputFile, err = generateConfig(link.Headers)
			if err != nil {
				logger.Error("generateConfig xray config err", err)
				return
			}

		}

		logger.Info(configFile, outputFile)
		commands := []string{"./xray_linux_amd64", "--config", configFile, "webscan --url", fmt.Sprintf(`'%s'`, link.URL)}

		if link.Data != "" {
			commands = append(commands, []string{"--data", link.Data}...)
		}
		commands = append(commands, []string{"--json-output", outputFile}...)
		cmd := strings.Join(commands, "  ")
		logger.Info(cmd)
		err = sysExec1(300, cmd)
		if err != nil {
			logger.Error("sysExec xray err", err)
			close(scanner.affectLinksChannel)
			return
		}
		outputFiles = append(outputFiles, outputFile)
	} else {
		outputFiles = files
	}

	for _, fileName := range outputFiles {
		result, err := readJsonFileGetResult(fileName, link)
		if err != nil {
			log.Error("readJsonFileGetResult err", err)
			continue
		}
		if result != nil {
			scanner.outputResult(result)
		}
	}
}

func readJsonFileGetResult(fileName string, link *AffectLink) (result *ScanResult, err error) {
	filePtr, err := os.Open(fileName)
	if err != nil {
		logger.Error("文件打开失败 [Err:%s]", err.Error())
		return
	}
	defer filePtr.Close()

	foundVuls := []*FoundVul{}
	var info []Website

	decoder := json.NewDecoder(filePtr)
	err = decoder.Decode(&info)
	if err != nil {
		log.Error("解码失败", err.Error())
		return
	}
	for _, item := range info {
		curVul := item.ToFoundVul(link)
		if curVul != nil {
			foundVuls = append(foundVuls, curVul)
		}

	}

	if len(foundVuls) > 0 {
		result = &ScanResult{
			FoundVuls: foundVuls,
		}

	}

	return
}

func sysExec(timeSecond time.Duration, name string, args ...string) (err error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeSecond*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, name, args...)
	log.Info(cmd.String())
	if _, err = cmd.Output(); err != nil {
		if ctx.Err() != nil && ctx.Err() == context.DeadlineExceeded {
			log.Error("synccall err", args, err)
			return
		}
	}

	return
}

func sysExec1(timeSecond time.Duration, commands string) (err error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeSecond*time.Second)
	defer cancel()

	c := cmd.NewCmd("bash", "-c", commands)

	select {
	case <-ctx.Done():
		err = fmt.Errorf(" get  config timeout")
		c.Stop()
	case <-c.Start():
	}

	return
}
