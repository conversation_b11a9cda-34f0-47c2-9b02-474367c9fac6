package scripts

import (
	"bytes"
	"io/ioutil"
	"net"
	"strconv"
	"time"
)

func HTTPHeaderHostnameVul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var port = int(args.Port)
	var addr = args.Host + ":" + strconv.Itoa(port)
	var rawurl = constructURL(args, "/")
	conn, err := net.DialTimeout("tcp", addr, 10*time.Second)

	if err != nil {

		return nil, err
	}

	defer conn.Close()
	conn.SetDeadline(time.Now().Add(5 * time.Second))
	smsg := "GET / HTTP/1.1\r\nAccept: */*\r\nConnection: close\r\nUser-Agent: Mozilla/5.0 " +
		"(Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) " +
		"Chrome/28.0.1500.63 Safari/537.36\r\nAccept-Encoding:gzip,deflate\r\nHost: webscantest\r\n\r\n"
	_, err = conn.Write([]byte(smsg))
	if err != nil {

		return nil, err
	}

	conn.SetReadDeadline(time.Now().Add(time.Second * 10))
	result, err := ioutil.ReadAll(conn)
	if err != nil {

		return nil, err
	}
	bl := bytes.SplitN(result, []byte("\r\n\r\n"), 2)
	if len(bl) != 2 {
		return nil, err
	}

	header := bl[0]
	html := bl[1]
	if bytes.Contains(header, []byte("200")) && bytes.Contains(html, []byte("webscantest")) {
		return &ScriptScanResult{Vulnerable: true, Output: rawurl, Body: result}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("http_header_hostname_vul.xml", HTTPHeaderHostnameVul)
}
