package utils

import (
	"bytes"
	"fmt"
	"regexp"
	"strings"
	"unicode"
	"unicode/utf8"

	"golang.org/x/net/html/charset"
	"golang.org/x/text/encoding"
	"golang.org/x/text/transform"
)

var charsetPattern = regexp.MustCompile(`(?i)<meta[^>]+charset\s*=\s*["]{0,1}([a-z0-9-]*)`)

func detectHtmlCharset(body []byte) string {
	if len(body) > 1024 {
		body = body[:1024]
	}
	match := charsetPattern.FindSubmatch(body)
	if match == nil {
		return ""
	}
	return string(match[1])
}

func ForceHtmlUtf8(body []byte, contentType string) ([]byte, string) {
	htmlCharset := detectHtmlCharset(body)
	if htmlCharset == "" {
		_, htmlCharset, _ = charset.DetermineEncoding(body, contentType)
	}
	return ForceUtf8(body, htmlCharset)
}

func ForceUtf8(body []byte, charsetName string) ([]byte, string) {

	if charsetName == "utf-8" && utf8.Valid(body) {
		newBody := make([]byte, len(body))
		copy(newBody, body)
		return newBody, charsetName
	}
	if charsetName == "windows-1252" {
		charsetName = "gb2312"
	}

	var defaultResult []byte
	var decoder *encoding.Decoder
	var encoder *encoding.Encoder
	for _, encName := range []string{
		charsetName,
		"gb18030",
		"big5",
		"utf8",
	} {
		enc, canonicalName := charset.Lookup(encName)
		if enc == nil {
			continue
		}
		decoder = enc.NewDecoder()
		encoder = enc.NewEncoder()
		result, n, err := transform.Bytes(decoder, body)
		fmt.Println(n)
		if err != nil {
			continue
		}

		if encName == charsetName {
			defaultResult = make([]byte, len(result))
			copy(defaultResult, result)
		}

		encodedBytes, n, err := transform.Bytes(encoder, result)
		fmt.Println(n)
		if err != nil {
			continue
		}
		if len(body) == len(encodedBytes) && bytes.Compare(body, encodedBytes) == 0 {
			return result, canonicalName
		}
	}

	if len(defaultResult) == 0 {
		defaultResult := make([]byte, len(body))
		copy(defaultResult, body)
	}
	return defaultResult, ""
}

func RemoveInvalidUtf8(s string) string {
	if !utf8.ValidString(s) {
		v := make([]rune, 0, len(s))
		for i, r := range s {
			if r == utf8.RuneError {
				_, size := utf8.DecodeRuneInString(s[i:])
				if size == 1 {
					continue
				}
			}
			v = append(v, r)
		}
		return string(v)
	}
	return s
}

func RemoveNonLetterNumber(s string) string {
	return strings.Map(func(r rune) rune {
		if unicode.IsLetter(r) || unicode.IsNumber(r) {
			return r
		}
		return ' '
	}, s)
}

func IsAlphaNumber(s string) bool {
	for _, r := range s {
		if uint32(r) > unicode.MaxASCII || (!unicode.IsLetter(r) && !unicode.IsNumber(r)) {
			return false
		}
	}
	return true
}
