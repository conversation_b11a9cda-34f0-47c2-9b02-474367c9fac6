package filters

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"sync"
	"time"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/logger"
	"websec/common/schema"
	"websec/scan"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/stream"

	"github.com/robfig/cron/v3"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Filter struct {
	consumer *stream.Consumer
	producer *stream.Producer

	consumeSema   *semaphore.Weighted
	semaWaitGroup sync.WaitGroup

	dbConnection *db.DBConnection
}

func (filter *Filter) Topics() []string {
	return []string{
		consts.TopicRawSensitiveWordResults,
		consts.TopicRawContentChangeResults,
		consts.TopicRawBlackLinkResults,
		consts.TopicRawTrojanResults,
		consts.TopicRawPhishingResults,
		consts.TopicRawVulResults,
	}
}

type resultRes struct {
	Headers     interface{} `json:"Headers"`
	ClientIP    string      `json:"Client_IP"`
	URLID       string      `json:"urlid"`
	MessageType string      `json:"type"`
	ScreenShot  string      `json:"screenshot"`
	location    string      `json:"location"`
	toplocation string      `json:"toplocation"`
	title       string      `json:"title"`
}

type HeaderRes struct {
	Conn           string `json:"Connection"`
	ContentLen     string `json:"Content-Length"`
	Origin         string `json:"Origin"`
	Referer        string `json:"Referer"`
	UserAgent      string `json:"User-Agent"`
	ContentType    string `json:"Content-Type"`
	Accept         string `json:"Accept"`
	AcceptEncoding string `json:"Accept-Encoding"`
	AcceptLanguage string `json:"Accept-Language:"`
}

type xssStorage struct {
	filter *Filter
	c      *cron.Cron
}

func (store xssStorage) Run() {
	defer func() {
		http.Get("https://e.scan.websec.cn/ytStoredXSS403/getData1.php?yuntan_clear")
	}()
	resp, err := http.Get("https://e.scan.websec.cn/ytStoredXSS403/getData1.php?yuntan")
	if err != nil {
		log.Errorln("get xss storage err", err)
		return
	}

	defer resp.Body.Close()
	if resp.StatusCode > 400 {
		log.Errorln("get xss storage err", err)
		return
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {

		log.Errorln("get xss readall err", err)
		return
	}

	result := []resultRes{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		log.Errorln("get result unmarshal err", err)
		return
	}

	for _, item := range result {

		logger.Info("xss storage item id  %v", item.URLID)
		id, err := primitive.ObjectIDFromHex(item.URLID)
		if err != nil {
			log.Errorln("id format err", err)
			continue
		}
		filterSearch := bson.M{
			"_id": id,
		}

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
		defer cancel()

		var resultURL schema.SiteURL
		err = store.filter.dbConnection.GetMongoDatabase().Collection(consts.CollectionURLs).
			FindOne(ctx, filterSearch).Decode(&resultURL)
		if err != nil {
			log.Errorln("failed to load urls:", err)
			cancel()
			continue
		}

		var curMethod string
		if resultURL.Methods&consts.MethodBitGet != 0 {
			curMethod = http.MethodGet
		} else if resultURL.Methods&consts.MethodBitPost != 0 {
			curMethod = http.MethodPost
		} else {
			logger.Error("the method not right %v", resultURL.Methods)
			continue
		}

		reqHeader := make(http.Header)
		curHeader := item.Headers.(map[string]interface{})

		for k, v := range curHeader {
			switch v.(type) {
			case string:
				reqHeader.Set(k, v.(string))
			}
		}
		refUrl := item.location
		if refUrl == "" {
			refUrl = reqHeader.Get("Referer")
			if refUrl == "" {
				continue
			}
		}

		link := scan.AffectLink{
			UrlID:  item.URLID,
			Affect: scan.AffectParameter,
			URL:    refUrl,
			Method: curMethod,
		}

		vul := schema.FoundVulDoc{
			Host: resultURL.Host,
			Scan: schema.FoundVulsScan{
				Affect:      scan.AffectParameter,
				Method:      curMethod,
				URL:         refUrl,
				Headers:     reqHeader,
				Fingerprint: link.Fingerprint(),
			},

			Vul: schema.FoundVulsVul{
				VulXML:   scan.XMLXssStorage,
				VulURL:   item.location,
				Severity: consts.VulMiddle,
				From:     "暂无相关信息",
			},

			FoundAt: time.Now(),
			AssetID: resultURL.AssetID,
			JobID:   resultURL.JobID,
		}

		logger.Info("xss store vul: %v", vul)
		store.filter.AddFinalVul(&vul)
	}

}

func (store xssStorage) XssStorageGet() {
	store.c.AddJob("* */1 * * *", store)
	store.c.Start()

}

func (filter *Filter) Process(msg *stream.Message, sync bool) error {
	defer func() {
		if !sync {
			filter.consumeSema.Release(1)
			filter.semaWaitGroup.Done()
		}
	}()

	topic := msg.Topic

	switch topic {
	case consts.TopicRawSensitiveWordResults:
		return filter.processSensitiveWord(msg)
	case consts.TopicRawContentChangeResults:
		return filter.processContentChange(msg)
	case consts.TopicRawBlackLinkResults:
		return filter.processBlackLink(msg)
	case consts.TopicRawTrojanResults:
		return filter.processTrojan(msg)
	case consts.TopicRawPhishingResults:
		return filter.processPhishing(msg)
	case consts.TopicRawVulResults:
		return filter.processVul(msg)
	default:
		log.Errorln("unknown message topic:", topic)
	}
	return nil
}

func (filter *Filter) Run() {
	filter.producer.Go()
	filter.consumer.SubscribeTopics(filter.Topics())
	filter.consumer.Go()

	c := cron.New()

	xssStore := xssStorage{
		filter: filter,
		c:      c,
	}
	xssStore.XssStorageGet()
	defer c.Stop()

	var err error
	for msg := range filter.consumer.Messages() {
		if err = filter.consumeSema.Acquire(context.TODO(), 1); err == nil {
			filter.semaWaitGroup.Add(1)
			go filter.Process(msg, false)
		} else {
			filter.Process(msg, true)
		}
	}

	filter.semaWaitGroup.Wait()
	filter.producer.Close()
	log.Infoln("Filter Grace Exit")
}

func (filter *Filter) Stop() {
	filter.consumer.Close()
}

func NewFilter(consumer *stream.Consumer, producer *stream.Producer, options ...OptionFn) (*Filter, error) {
	var err error
	filter := &Filter{
		consumer:    consumer,
		producer:    producer,
		consumeSema: semaphore.NewWeighted(20),
	}

	for _, opt := range options {
		err = opt(filter)
		if err != nil {
			return nil, err
		}
	}
	return filter, nil
}
