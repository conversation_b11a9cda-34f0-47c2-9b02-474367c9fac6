package matcher

import (
	"fmt"
	"sync"
	"time"
	"websec/common/resource"
	"websec/utils"
	"websec/utils/acsmx"
	"websec/utils/log"
)

type SensitiveWordMatcherManager struct {
	matchers  sync.Map
	existTime int //matcher exist time
}

type sensitiveWordMatcher struct {
	matcher *acsmx.Matcher
	timeOut time.Time //超时重新生成时间
	md5     string    //唯一hash，根据自定义敏感词来生成
}

func NewSensitiveWordMatcherManager(sec int) *SensitiveWordMatcherManager {
	s := &SensitiveWordMatcherManager{
		matchers:  sync.Map{},
		existTime: sec,
	}
	go s.timerDelete()
	return s
}

func (s *SensitiveWordMatcherManager) timerDelete() {
	t := time.NewTicker(60 * time.Second)
	for {
		select {
		case <-t.C:
			needDel := make([]string, 0, 1)
			s.matchers.Range(func(key, value interface{}) bool {
				host := key.(string)
				matcher := value.(*sensitiveWordMatcher)

				if matcher.timeOut.Before(time.Now()) {
					needDel = append(needDel, host)
				}
				return true
			})

			for _, v := range needDel {
				s.matchers.Delete(v)
				log.Infof("SensitiveWordMatcherManager del %s timeEnd", v)
			}
		}
	}
}

func (s *SensitiveWordMatcherManager) GetCustomizedSensitiveWordMatcher(host string, customized []string) *acsmx.Matcher {
	v, ok := s.matchers.Load(host)
	newMd5 := utils.Md5(fmt.Sprintf("%v", customized))
	var smatcher *sensitiveWordMatcher
	if ok {
		smatcher = v.(*sensitiveWordMatcher)
		if smatcher.md5 == newMd5 {
			smatcher.timeOut = time.Now().Add(time.Duration(s.existTime) * time.Second)
			return smatcher.matcher
		}
	}

	smatcher = &sensitiveWordMatcher{
		md5:     newMd5,
		timeOut: time.Now().Add(time.Duration(s.existTime) * time.Second),
		matcher: newSensitiveMatcher(customized),
	}

	s.matchers.Store(host, smatcher)
	return smatcher.matcher
}

func newSensitiveMatcher(customized []string) *acsmx.Matcher {
	matcher := acsmx.NewMatcher()
	flag := false
	defaultSensitiveWords := resource.DefaultSensitiveWords()
	for _, word := range customized {
		if len(word) == 0 {
			continue
		}
		if !defaultSensitiveWords.Contains(word) {
			matcher.AddPatternString(word)
			flag = true
		}
	}
	if flag {
		for word := range defaultSensitiveWords {
			matcher.AddPatternString(word)
		}
		matcher.Compile()
	} else {

		matcher = resource.DefaultSensitiveWordsMatcher()
	}

	return matcher
}
