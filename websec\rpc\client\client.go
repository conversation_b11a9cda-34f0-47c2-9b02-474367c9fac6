package client

import (
	"bufio"
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"io"
	"net"
	"reflect"
	"sync"
	"time"
	"websec/rpc/log"
	"websec/rpc/protocol"
	"websec/rpc/share"

	quicconn "github.com/marten-seemann/quic-conn"
)

type ServiceError string

func (e ServiceError) Error() string {
	return string(e)
}

var (
	ErrShutdown         = errors.New("connection is shut down")
	ErrUnsupportedCodec = errors.New("unsupported codec")
)

const (
	ReaderBuffsize = 16 * 1024

	WriterBuffsize = 16 * 1024
)

var DefaultOption = Option{
	Retries:        3,
	ConnectTimeout: 10 * time.Second,
	SerializeType:  protocol.MsgPack,
	CompressType:   protocol.None,
}

type RPCClient interface {
	Connect(network, address string) error
	Go(ctx context.Context, servicePath, serviceMethod string, args interface{}, reply interface{}, done chan *share.Call) *share.Call
	Call(ctx context.Context, servicePath, serviceMethod string, args interface{}, reply interface{}) error
	SendRaw(ctx context.Context, r *protocol.Message) (map[string]string, []byte, error)
	Close() error
	IsClosing() bool
	IsShutdown() bool
}

type Client struct {
	option Option

	Conn net.Conn
	r    *bufio.Reader

	mutex        sync.Mutex // protects following
	seq          uint64
	pending      map[uint64]*share.Call
	closing      bool // user has called Close
	shutdown     bool // server has told us to stop
	pluginClosed bool // the plugin has been called

	Plugins PluginContainer

	serviceMapMu sync.RWMutex
	serviceMap   map[string]*service

	serverMessageChan chan *protocol.Message
}

func NewClient(option Option) *Client {
	return &Client{
		option:            option,
		serverMessageChan: make(chan *protocol.Message, 1000),
	}
}

type Option struct {
	Group string

	Retries int

	TLSConfig *tls.Config

	ConnectTimeout time.Duration

	ReadTimeout time.Duration

	WriteTimeout time.Duration

	SerializeType protocol.SerializeType
	CompressType  protocol.CompressType

	Heartbeat         bool
	HeartbeatInterval time.Duration
}

func (c *Client) Connect(network, address string) error {
	var conn net.Conn
	var err error

	switch network {
	case "tcp":
		conn, err = newDirectConn(c, network, address)
	case "quic":
		conn, err = newQuicConn(c, address)
	default:
		return errors.New("invaild netowrk")
	}

	if err == nil && conn != nil {
		if c.option.ReadTimeout != 0 {
			conn.SetReadDeadline(time.Now().Add(c.option.ReadTimeout))
		}
		if c.option.WriteTimeout != 0 {
			conn.SetWriteDeadline(time.Now().Add(c.option.WriteTimeout))
		}

		c.Conn = conn
		c.r = bufio.NewReaderSize(conn, ReaderBuffsize)

		go c.input()
		go c.handleServerRequest()

		if c.option.Heartbeat && c.option.HeartbeatInterval > 0 {
			go c.heartbeat()
		}
	}

	return err
}

func newDirectConn(c *Client, network, address string) (net.Conn, error) {
	var conn net.Conn
	var tlsConn *tls.Conn
	var err error

	if c != nil && c.option.TLSConfig != nil {
		dialer := &net.Dialer{
			Timeout: c.option.ConnectTimeout,
		}
		tlsConn, err = tls.DialWithDialer(dialer, network, address, c.option.TLSConfig)

		conn = net.Conn(tlsConn)
	} else {
		conn, err = net.DialTimeout(network, address, c.option.ConnectTimeout)
	}

	if err != nil {
		log.Warnf("failed to dial server: %v", err)
		return nil, err
	}

	if tc, ok := conn.(*net.TCPConn); ok {
		tc.SetKeepAlive(true)
		tc.SetKeepAlivePeriod(3 * time.Minute)
	}

	return conn, nil
}

func newQuicConn(c *Client, address string) (net.Conn, error) {
	if c.option.TLSConfig == nil {
		return nil, errors.New("quic tlsconfig can not nil")
	}

	return quicconn.Dial(address, c.option.TLSConfig)
}

func (client *Client) IsClosing() bool {
	return client.closing
}

func (client *Client) IsShutdown() bool {
	return client.shutdown
}

func (client *Client) Go(ctx context.Context, servicePath, serviceMethod string, args interface{}, reply interface{}, done chan *share.Call) *share.Call {
	call := new(share.Call)
	call.ServicePath = servicePath
	call.ServiceMethod = serviceMethod
	meta := ctx.Value(share.ReqMetaDataKey)
	if meta != nil { //copy meta in context to meta in requests
		call.Metadata = meta.(map[string]string)
	}
	call.Args = args
	call.Reply = reply
	if done == nil {
		done = make(chan *share.Call, 10) // buffered.
	} else {

		if cap(done) == 0 {
			log.Panic("rpc: done channel is unbuffered")
		}
	}
	call.Done = done
	client.send(ctx, call)
	return call
}

func (client *Client) Call(ctx context.Context, servicePath, serviceMethod string, args interface{}, reply interface{}) error {
	return client.call(ctx, servicePath, serviceMethod, args, reply)
}

func (client *Client) call(ctx context.Context, servicePath, serviceMethod string, args interface{}, reply interface{}) error {
	seq := new(uint64)
	ctx = context.WithValue(ctx, share.SeqKey{}, seq)
	Done := client.Go(ctx, servicePath, serviceMethod, args, reply, make(chan *share.Call, 1)).Done

	var err error
	select {
	case <-ctx.Done(): //cancel by context
		client.mutex.Lock()
		call := client.pending[*seq]
		delete(client.pending, *seq)
		client.mutex.Unlock()
		if call != nil {
			call.Error = ctx.Err()
			call.CallDone()
		}

		return ctx.Err()
	case call := <-Done:
		err = call.Error
		meta := ctx.Value(share.ResMetaDataKey)
		if meta != nil && len(call.ResMetadata) > 0 {
			resMeta := meta.(map[string]string)
			for k, v := range call.ResMetadata {
				resMeta[k] = v
			}
		}
	}

	return err
}

func (client *Client) SendRaw(ctx context.Context, r *protocol.Message) (map[string]string, []byte, error) {
	ctx = context.WithValue(ctx, share.SeqKey{}, r.Seq())

	call := new(share.Call)
	call.Raw = true
	call.ServicePath = r.ServicePath
	call.ServiceMethod = r.ServiceMethod
	meta := ctx.Value(share.ReqMetaDataKey)

	rmeta := make(map[string]string)
	if meta != nil {
		for k, v := range meta.(map[string]string) {
			rmeta[k] = v
		}
	}
	if r.Metadata != nil {
		for k, v := range r.Metadata {
			rmeta[k] = v
		}
	}

	if meta != nil { //copy meta in context to meta in requests
		call.Metadata = rmeta
	}
	done := make(chan *share.Call, 10)
	call.Done = done

	seq := r.Seq()
	client.mutex.Lock()
	if client.pending == nil {
		client.pending = make(map[uint64]*share.Call)
	}
	client.pending[seq] = call
	client.mutex.Unlock()

	data := r.Encode()
	_, err := client.Conn.Write(data)
	if err != nil {
		client.mutex.Lock()
		call = client.pending[seq]
		delete(client.pending, seq)
		client.mutex.Unlock()
		if call != nil {
			call.Error = err
			call.CallDone()
		}
		return nil, nil, err
	}
	if r.IsOneway() {
		client.mutex.Lock()
		call = client.pending[seq]
		delete(client.pending, seq)
		client.mutex.Unlock()
		if call != nil {
			call.CallDone()
		}
		return nil, nil, nil
	}

	var m map[string]string
	var payload []byte

	select {
	case <-ctx.Done(): //cancel by context
		client.mutex.Lock()
		call := client.pending[seq]
		delete(client.pending, seq)
		client.mutex.Unlock()
		if call != nil {
			call.Error = ctx.Err()
			call.CallDone()
		}

		return nil, nil, ctx.Err()
	case call := <-done:
		err = call.Error
		m = call.Metadata
		if call.Reply != nil {
			payload = call.Reply.([]byte)
		}
	}

	return m, payload, err
}

func (client *Client) send(ctx context.Context, call *share.Call) {

	client.mutex.Lock()
	if client.shutdown || client.closing {
		call.Error = ErrShutdown
		client.mutex.Unlock()
		call.CallDone()
		return
	}

	codec := share.Codecs[client.option.SerializeType]
	if codec == nil {
		call.Error = ErrUnsupportedCodec
		client.mutex.Unlock()
		call.CallDone()
		return
	}

	if client.pending == nil {
		client.pending = make(map[uint64]*share.Call)
	}

	seq := client.seq
	client.seq++
	client.pending[seq] = call
	client.mutex.Unlock()

	if cseq, ok := ctx.Value(share.SeqKey{}).(*uint64); ok {
		*cseq = seq
	}

	req := protocol.GetPooledMsg()
	defer protocol.FreeMsg(req)
	req.SetMessageType(protocol.Request)
	req.SetSeq(seq)
	if call.Reply == nil {
		req.SetOneway(true)
	}

	if call.ServicePath == "" && call.ServiceMethod == "" {
		req.SetHeartbeat(true)
	} else {
		req.SetSerializeType(client.option.SerializeType)
		if call.Metadata != nil {
			req.Metadata = call.Metadata
		}

		req.ServicePath = call.ServicePath
		req.ServiceMethod = call.ServiceMethod

		data, err := codec.Encode(call.Args)
		if err != nil {
			call.Error = err
			call.CallDone()
			return
		}
		if len(data) > 1024 && client.option.CompressType != protocol.None {
			req.SetCompressType(client.option.CompressType)
		}

		req.Payload = data
	}

	data := req.Encode()

	_, err := client.Conn.Write(data)
	if err != nil {
		client.mutex.Lock()
		call = client.pending[seq]
		delete(client.pending, seq)
		client.mutex.Unlock()
		if call != nil {
			call.Error = err
			call.CallDone()
		}
		return
	}

	isOneway := req.IsOneway()

	if isOneway {
		client.mutex.Lock()
		call = client.pending[seq]
		delete(client.pending, seq)
		client.mutex.Unlock()
		if call != nil {
			call.CallDone()
		}
	}

	if client.option.WriteTimeout != 0 {
		client.Conn.SetWriteDeadline(time.Now().Add(client.option.WriteTimeout))
	}
}

func (client *Client) input() {
	var err error
	var res = protocol.NewMessage()

	for err == nil {
		if client.option.ReadTimeout != 0 {
			client.Conn.SetReadDeadline(time.Now().Add(client.option.ReadTimeout))
		}

		err = res.Decode(client.r)

		if err != nil {
			break
		}
		seq := res.Seq()
		var call *share.Call
		isServerMessage := (res.MessageType() == protocol.Request && !res.IsHeartbeat())
		if !isServerMessage {
			client.mutex.Lock()
			call = client.pending[seq]
			delete(client.pending, seq)
			client.mutex.Unlock()
		}

		switch {
		case call == nil:
			if isServerMessage {
				if client.serverMessageChan != nil {
					client.serverMessageChan <- res
					res = protocol.NewMessage()
				}
				continue
			}
		case res.MessageStatusType() == protocol.Error:

			if len(res.Metadata) > 0 {
				meta := make(map[string]string, len(res.Metadata))
				for k, v := range res.Metadata {
					meta[k] = v
				}
				call.ResMetadata = meta
				call.Error = ServiceError(meta[protocol.ServiceError])
			}

			if call.Raw {
				call.Metadata, call.Reply, _ = protocol.ConvertRes2Raw(res)
				call.Metadata[protocol.XErrorMessage] = call.Error.Error()
			}
			call.CallDone()
		default:
			if call.Raw {
				call.Metadata, call.Reply, _ = protocol.ConvertRes2Raw(res)
			} else {
				data := res.Payload
				if len(data) > 0 {
					codec := share.Codecs[res.SerializeType()]
					if codec == nil {
						call.Error = ServiceError(ErrUnsupportedCodec.Error())
					} else {
						err = codec.Decode(data, call.Reply)
						if err != nil {
							call.Error = ServiceError(err.Error())
						}
					}
				}
				if len(res.Metadata) > 0 {
					meta := make(map[string]string, len(res.Metadata))
					for k, v := range res.Metadata {
						meta[k] = v
					}
					call.ResMetadata = res.Metadata
				}

			}

			call.CallDone()
		}

		res.Reset()
	}

	client.mutex.Lock()
	if !client.pluginClosed {
		if client.Plugins != nil {
			client.Plugins.DoClientConnectionClose(client.Conn)
		}
		client.pluginClosed = true
		client.Conn.Close()
		close(client.serverMessageChan)
	}
	client.shutdown = true
	closing := client.closing
	if err == io.EOF {
		if closing {
			err = ErrShutdown
		} else {
			err = io.ErrUnexpectedEOF
		}
	}
	for _, call := range client.pending {
		call.Error = err
		call.CallDone()
	}

	client.mutex.Unlock()

	if err != nil && err != io.EOF && !closing {
		log.Error("rpc: client protocol error:", err)
	}
}

func (client *Client) handleServerRequest() {
	defer func() {
		if r := recover(); r != nil {
			log.Error("handleServerRequest", r)
		}
	}()

	for {
		if client.serverMessageChan == nil {
			log.Error("serverMessageChan is nil")
			break
		}
		req, ok := <-client.serverMessageChan
		if !ok {
			log.Error("the serverMessageChan is close")
			break
		}

		log.Infof("server request message %s.%s ", req.ServicePath, req.ServiceMethod)

		resMetadata := make(map[string]string)
		newCtx := context.WithValue(context.WithValue(context.Background(), share.ReqMetaDataKey, req.Metadata),
			share.ResMetaDataKey, resMetadata)

		res, err := client.handleRequest(newCtx, req)
		if err != nil {
			log.Warnf("rpc: failed to handle request: %v", err)
		}

		if !req.IsOneway() {
			if len(resMetadata) > 0 { //copy meta in context to request
				meta := res.Metadata
				if meta == nil {
					res.Metadata = resMetadata
				} else {
					for k, v := range resMetadata {
						meta[k] = v
					}
				}
			}

			if len(res.Payload) > 1024 && req.CompressType() != protocol.None {
				res.SetCompressType(req.CompressType())
			}
			data := res.Encode()
			_, err = client.Conn.Write(data)
			if err != nil {
				log.Error("write error", err)
			}
		}

		protocol.FreeMsg(req)
		protocol.FreeMsg(res)
	}
}

func (client *Client) handleRequest(ctx context.Context, req *protocol.Message) (res *protocol.Message, err error) {
	serviceName := req.ServicePath
	methodName := req.ServiceMethod

	res = req.Clone()

	res.SetMessageType(protocol.Response)
	client.serviceMapMu.RLock()
	service := client.serviceMap[serviceName]
	client.serviceMapMu.RUnlock()
	if service == nil {
		err = errors.New("can't find service " + serviceName)
		return handleError(res, err)
	}
	mtype := service.method[methodName]
	if mtype == nil {
		if service.function[methodName] != nil { //check raw functions
			return client.handleRequestForFunction(ctx, req)
		}
		err = errors.New("rpc: can't find method " + methodName)
		return handleError(res, err)
	}

	var argv = argsReplyPools.Get(mtype.ArgType)

	codec := share.Codecs[req.SerializeType()]
	if codec == nil {
		err = fmt.Errorf("can not find codec for %d", req.SerializeType())
		return handleError(res, err)
	}

	err = codec.Decode(req.Payload, argv)
	if err != nil {
		return handleError(res, err)
	}

	replyv := argsReplyPools.Get(mtype.ReplyType)

	if mtype.ArgType.Kind() != reflect.Ptr {
		err = service.call(ctx, mtype, reflect.ValueOf(argv).Elem(), reflect.ValueOf(replyv))
	} else {
		err = service.call(ctx, mtype, reflect.ValueOf(argv), reflect.ValueOf(replyv))
	}

	argsReplyPools.Put(mtype.ArgType, argv)
	if err != nil {
		argsReplyPools.Put(mtype.ReplyType, replyv)
		return handleError(res, err)
	}

	if !req.IsOneway() {
		data, err := codec.Encode(replyv)
		argsReplyPools.Put(mtype.ReplyType, replyv)
		if err != nil {
			return handleError(res, err)

		}
		res.Payload = data
	}

	return res, nil
}

func (client *Client) handleRequestForFunction(ctx context.Context, req *protocol.Message) (res *protocol.Message, err error) {
	res = req.Clone()

	res.SetMessageType(protocol.Response)

	serviceName := req.ServicePath
	methodName := req.ServiceMethod
	client.serviceMapMu.RLock()
	service := client.serviceMap[serviceName]
	client.serviceMapMu.RUnlock()
	if service == nil {
		err = errors.New("rpc: can't find service  for func raw function")
		return handleError(res, err)
	}
	mtype := service.function[methodName]
	if mtype == nil {
		err = errors.New("rpc: can't find method " + methodName)
		return handleError(res, err)
	}

	var argv = argsReplyPools.Get(mtype.ArgType)

	codec := share.Codecs[req.SerializeType()]
	if codec == nil {
		err = fmt.Errorf("can not find codec for %d", req.SerializeType())
		return handleError(res, err)
	}

	err = codec.Decode(req.Payload, argv)
	if err != nil {
		return handleError(res, err)
	}

	replyv := argsReplyPools.Get(mtype.ReplyType)

	err = service.callForFunction(ctx, mtype, reflect.ValueOf(argv), reflect.ValueOf(replyv))

	argsReplyPools.Put(mtype.ArgType, argv)

	if err != nil {
		argsReplyPools.Put(mtype.ReplyType, replyv)
		return handleError(res, err)
	}

	if !req.IsOneway() {
		data, err := codec.Encode(replyv)
		argsReplyPools.Put(mtype.ReplyType, replyv)
		if err != nil {
			return handleError(res, err)

		}
		res.Payload = data
	}

	return res, nil
}

func handleError(res *protocol.Message, err error) (*protocol.Message, error) {
	res.SetMessageStatusType(protocol.Error)
	if res.Metadata == nil {
		res.Metadata = make(map[string]string)
	}
	res.Metadata[protocol.ServiceError] = err.Error()
	return res, err
}

func (client *Client) heartbeat() {
	t := time.NewTicker(client.option.HeartbeatInterval)

	for range t.C {
		if client.shutdown || client.closing {
			return
		}

		err := client.Call(context.Background(), "", "", nil, nil)
		if err != nil {
			log.Warnf("failed to heartbeat to %s", client.Conn.RemoteAddr().String())
		}
	}
}

func (client *Client) Close() error {
	client.mutex.Lock()

	for seq, call := range client.pending {
		delete(client.pending, seq)
		if call != nil {
			call.Error = ErrShutdown
			call.CallDone()
		}
	}

	var err error
	if !client.pluginClosed {
		if client.Plugins != nil {
			client.Plugins.DoClientConnectionClose(client.Conn)
		}

		client.pluginClosed = true
		err = client.Conn.Close()
	}

	if client.closing || client.shutdown {
		client.mutex.Unlock()
		return ErrShutdown
	}

	client.closing = true
	client.mutex.Unlock()
	return err
}
