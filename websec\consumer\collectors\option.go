package collectors

import (
	"websec/common/db"
	"websec/utils/semaphore"
)

type OptionFn func(*Collector) error

func WithDBConnection(dbConnection *db.DBConnection) OptionFn {
	return func(collector *Collector) error {
		collector.dbConnection = dbConnection
		collector.saver = NewSaver(dbConnection)
		return nil
	}
}

func WithConsumeConcurrency(concurrency int64) OptionFn {
	return func(collector *Collector) error {
		collector.consumeSema = semaphore.NewWeighted(concurrency)
		return nil
	}
}

func WithNewURL(v bool) OptionFn {
	return func(collector *Collector) error {
		collector.newURL = v
		return nil
	}
}
