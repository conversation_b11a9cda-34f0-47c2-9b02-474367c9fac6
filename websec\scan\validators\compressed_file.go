package validators

import (
	"context"
	"net/http"
	"os/exec"
	"strings"
	"time"
)

func isCompressedFile(filename string) bool {
	for _, ext := range []string{".zip", ".rar", ".7z", ".tar", ".tar.gz", ".bz", ".bz2"} {
		if strings.HasSuffix(filename, ext) {
			return true
		}
	}
	return false
}

func ValidateCompressedFile(args *ValidationArgs) (*ValidationResult, error) {
	filename := args.VulURL
	if isCompressedFile(filename) {
		resp, err := http.Head(filename)
		if err != nil {
			return nil, err
		}
		if resp.StatusCode == http.StatusOK {
			contentType := strings.ToLower(resp.Header.Get("Content-Type"))
			if strings.Contains(contentType, "stream") || strings.Contains(contentType, "text") {
				ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				defer cancel()

				cmd := exec.CommandContext(ctx, "curl", "-I", filename)
				output, err := cmd.CombinedOutput()
				if err != nil {
					return nil, err
				}
				outputStr := string(output)

				return &ValidationResult{
					Status:       VulIsValid,
					Command:      strings.Join(cmd.Args, " "),
					Output:       outputStr,
					Highlight:    "200 OK",
					NeedSnapshot: true,
				}, nil
			}
		}
	}
	return &ValidationResult{Status: VulIsInvalid}, nil
}
