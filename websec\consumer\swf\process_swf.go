package swf

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"time"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/stream"
)

func (processor *Processor) processSwfURLsMessage(msg *stream.Message) error {
	var crawledSwfURLs = new(schema.CrawledSwfURLs)
	err := json.Unmarshal(msg.Value, crawledSwfURLs)
	if err != nil {
		log.Errorln("Unmarshal failed:", err)
		return err
	}

	assetID := crawledSwfURLs.AssetID

	for _, swfURL := range crawledSwfURLs.Swfs {
		ctx, cancel := context.WithTimeout(context.TODO(), time.Minute*2)
		defer cancel()

		swf := &schema.SwfDoc{
			Host:           crawledSwfURLs.Host,
			AssetID:        assetID,
			JobID:          crawledSwfURLs.JobID,
			SwfURL:         swfURL,
			SwfURLChecksum: checksum(swfURL),
			Referer:        crawledSwfURLs.URL,
			FoundAt:        crawledSwfURLs.FoundAt,
		}
		processor.processSwf(ctx, swf)
	}
	return nil
}

func (processor *Processor) processSwf(ctx context.Context, swf *schema.SwfDoc) error {
	request, err := http.NewRequestWithContext(ctx, http.MethodGet, swf.SwfURL, nil)
	if err != nil {
		log.Errorln("download swf new request error.", err)
		return err
	}
	request.Header.Add("Referer", swf.Referer)

	httpClient := &http.Client{
		Timeout: 2 * time.Minute,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	resp, err := httpClient.Do(request)
	if err != nil {
		log.Errorln("downloadImage error.", swf.SwfURL, err)
		return err
	} else {
		log.Info("doloading swf success", swf.SwfURL)
	}
	defer resp.Body.Close()

	swfDat, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorln("swf read from socket error.", swf.SwfURL, err)
		return err
	}

	urls, err := processor.jpexClient.CheckSwfFileEmbededURLs(ctx, swfDat)
	if err != nil {
		log.Errorln("failed to parse embedurls CheckSwfFileEmbededURLs", swf.SwfURL, err)
		return err
	}

	if len(urls) == 0 {
		log.Info("thers's not embed urls", swf.SwfURL)
		return nil
	}

	foundSwfWithURLDoc, err := newFoundSwfWithURLDoc(swf, urls)
	if err != nil {
		log.Errorln("newFoundSwfWithURLDoc", swf.SwfURL, err)
		return err
	}
	fmt.Printf("found swf urls %+v, %+v\n", swf.SwfURL, foundSwfWithURLDoc)

	return processor.AddFoundSwfWithURLResult(ctx, foundSwfWithURLDoc)
}

func newFoundSwfWithURLDoc(swf *schema.SwfDoc, urlLst []string) (*schema.FoundSwfWithURLDoc, error) {
	doc := new(schema.FoundSwfWithURLDoc)
	doc.Host = swf.Host
	doc.URL = swf.Referer
	doc.URLHash = checksum(doc.URL)
	doc.AssetID = swf.AssetID
	doc.JobID = swf.JobID
	doc.FoundAt = swf.FoundAt
	doc.SwfURL = swf.SwfURL

	baseURL, err := url.Parse(doc.URL)
	if err != nil {
		return nil, err
	}

	blackLinkLst := make([]schema.FoundBlackLink, 0)
	for _, urlx := range urlLst {

		absURL, err := urlJoin(baseURL, urlx)
		if err != nil {
			continue
		}

		isOuterURL := false
		_url, err := url.Parse(absURL)
		if err == nil {
			isOuterURL = _url.Host != baseURL.Host
		}

		blackLink := schema.FoundBlackLink{
			URL:        absURL,
			Words:      make(map[string][]int),
			IsOuterURL: isOuterURL,
		}
		blackLinkLst = append(blackLinkLst, blackLink)
	}

	doc.Results = blackLinkLst
	return doc, nil
}
