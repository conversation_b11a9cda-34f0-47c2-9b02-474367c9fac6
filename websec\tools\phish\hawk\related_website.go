package hawk

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
	"websec/utils"
)

var (
	platform   = ""
	apiKey     = ""
	apiPath    = "http://127.0.0.1:8181/phishing"
	pageSize   = 100
	httpClient = http.Client{Timeout: 10 * time.Second}
)

type Response struct {
	ErrorCode int          `json:"error_code"`
	Msg       string       `json:"msg"`
	Status    int          `json:"status"`
	Data      ResponseData `json:"data"`
}

type ResponseData struct {
	CurrentPage int      `json:"currentPage"`
	TotalPage   int      `json:"totalPage"`
	Total       int      `json:"total"`
	List        []string `json:"list"`
}

func genURL(keyWord string, start, end int64, page int) string {
	now := time.Now().Unix()
	token := utils.Md5(fmt.Sprintf("%s%s%d", platform, apiKey, now))
	return fmt.Sprintf("%s?keyword=%s&start_time=%d&end_time=%d&platform=%s&timestamp=%d&token=%s&page=%d&pageSize=%d",
		apiPath, keyWord, start, end, platform, now, token, page, pageSize)
}

func GetRelatedWebSite(keyWord string, start, end int64, page int) (*Response, error) {
	url := genURL(keyWord, start, end, page)
	resp, err := httpClient.Get(url)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	data := new(Response)
	err = json.Unmarshal(body, data)
	if err != nil {
		return nil, err
	}

	return data, nil
}
