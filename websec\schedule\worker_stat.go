package schedule

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"
	"websec/common/schema"
	"websec/distributed/endpoint"
	"websec/distributed/protocol"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type workerStat struct {
	Name string

	handler       *endpoint.Endpoint
	registered    bool
	lastHeartbeat time.Time

	muPending     *sync.RWMutex
	pendingTasks  map[primitive.ObjectID]*schema.Task
	hostTaskCount map[string]uint // stores host-count tasks
	MaxHostCount  int

	capacity      int32
	count         int32
	finishedCount int32
}

func (worker *workerStat) ConnectionAliveBool() bool {
	return worker.handler.ConnectionAliveBool()
}

func (worker *workerStat) Close() {
	worker.handler.Close()
}

func (worker *workerStat) Closed() bool {
	return worker.handler.Closed()
}

func (worker *workerStat) GetTaskCount() int32 {
	return atomic.LoadInt32(&worker.count)
}

func (worker *workerStat) GetTaskFinishedCount() int32 {
	return atomic.LoadInt32(&worker.finishedCount)
}

func (worker *workerStat) GetHostCount() int {
	worker.muPending.Lock()
	defer worker.muPending.Unlock()

	return len(worker.hostTaskCount)
}

func (worker *workerStat) SetTaskCount(val int32) {
	atomic.StoreInt32(&worker.count, val)
}

func (worker *workerStat) SetTaskFinishedCount(val int32) {
	if worker == nil {
		return
	}
	atomic.StoreInt32(&worker.finishedCount, 0)
}

func (worker *workerStat) GetCapacity() int32 {
	return atomic.LoadInt32(&worker.capacity)
}

func (worker *workerStat) SetCapacity(val int32) {
	atomic.StoreInt32(&worker.capacity, val)
}

func (worker *workerStat) DispatchTask(task *schema.Task) error {
	msg := &protocol.Message{
		Action: protocol.ActionTask,
		Body:   task,
	}
	resp, err := worker.handler.Request(msg)
	if err != nil {
		atomic.AddInt32(&worker.count, -1)
		atomic.AddInt32(&worker.finishedCount, 1)
		atomic.AddInt32(&worker.capacity, 1)
		return err
	}
	if resp.Status != protocol.StatusOK && resp.Status != protocol.StatusTaskExisted {
		atomic.AddInt32(&worker.count, -1)
		atomic.AddInt32(&worker.finishedCount, 1)
		atomic.AddInt32(&worker.capacity, 1)
		return fmt.Errorf("worker response: %v", resp.Body)
	}
	worker.muPending.Lock()
	defer worker.muPending.Unlock()
	if len(worker.hostTaskCount) >= worker.MaxHostCount {
		return fmt.Errorf("max host number found on thie worker:%s %d", worker.Name, len(worker.hostTaskCount))
	}

	if worker.pendingTasks == nil { // worker has moved to newworker
		return fmt.Errorf("worker has moved to new worker")
	}
	worker.pendingTasks[task.ID] = task

	if v, ok := worker.hostTaskCount[task.Host]; ok {
		worker.hostTaskCount[task.Host] = v + 1
	} else {
		worker.hostTaskCount[task.Host] = 1
	}

	return nil
}
func (worker *workerStat) StopTask(task *schema.Task) error {
	msg := &protocol.Message{
		Action: protocol.ActionStopTask,
		Body:   task,
	}
	resp, err := worker.handler.Request(msg)
	if err != nil {
		return err
	}

	if resp.Status != protocol.StatusOK {
		return fmt.Errorf("stop worker response: %v", resp.Body)
	}

	return nil
}

func (worker *workerStat) ExistTask(taskID primitive.ObjectID) bool {
	worker.muPending.RLock()
	defer worker.muPending.RUnlock()

	if _, ok := worker.pendingTasks[taskID]; ok {
		return true
	}
	return false
}

func (worker *workerStat) MoveTo(newWorker *workerStat) {
	log.Infoln("move to new worker:", worker.Name, worker.handler.Tag, "->", newWorker.handler.Tag)
	worker.handler.MoveTo(newWorker.handler)
	newWorker.SetCapacity(worker.GetCapacity())
	newWorker.SetTaskCount(worker.GetTaskCount())
	log.Debug("move to", worker.Name, worker.GetTaskCount(), worker.GetCapacity())
	newWorker.pendingTasks = worker.pendingTasks
	newWorker.hostTaskCount = worker.hostTaskCount
	worker.pendingTasks = nil
	worker.hostTaskCount = nil
}

func (worker *workerStat) reply(status uint8, request *protocol.Message, body interface{}) error {
	return worker.handler.Reply(status, request, body)
}

func (worker *workerStat) processFinishedTask(msg *protocol.Message) (uint8, interface{}) {
	body := new(protocol.TaskFinishBody)
	err := msg.DecodeBody(body)
	if err != nil {
		log.Errorln("failed to decode message body:", err)
		return protocol.StatusFailed, err.Error()
	}
	worker.muPending.Lock()
	task := worker.pendingTasks[body.TaskID]
	delete(worker.pendingTasks, body.TaskID)

	if task != nil {
		if v, ok := worker.hostTaskCount[task.Host]; ok {
			worker.hostTaskCount[task.Host] = v - 1
			if worker.hostTaskCount[task.Host] == 0 {
				delete(worker.hostTaskCount, task.Host)
			}
		}
	}
	worker.muPending.Unlock()

	atomic.AddInt32(&worker.count, -1)
	atomic.AddInt32(&worker.finishedCount, 1)
	atomic.AddInt32(&worker.capacity, 1)
	log.Debug("finish task", worker.Name, worker.GetTaskCount())
	go func() { finishedTasks <- body }()
	return protocol.StatusOK, nil
}

func (worker *workerStat) ProcessingHostTasks(address string) bool {
	worker.muPending.RLock()
	defer worker.muPending.RUnlock()
	for taskID := range worker.pendingTasks {
		task := worker.pendingTasks[taskID]
		if task.Track.Address == address {
			return true
		}
	}
	return false
}

func (worker *workerStat) processHeartbeat(msg *protocol.Message) (uint8, interface{}) {
	beat := &protocol.HeartbeatBody{}
	err := msg.DecodeBody(beat)
	if err != nil {
		log.Errorln("failed to decode message body:", err)
		return protocol.StatusFailed, err.Error()
	}
	worker.lastHeartbeat = time.Now()
	log.Debugf("%s heart worker oldcount %d newcount %d oldcapacity %d newcapacit %d",
		worker.Name, worker.GetTaskCount(), beat.Count, worker.GetCapacity(), beat.Capacity)
	worker.SetCapacity(beat.Capacity)
	worker.SetTaskCount(beat.Count)
	return protocol.StatusOK, nil
}

func (worker *workerStat) watchHeartbeat() {
	ticker := time.NewTicker(time.Second * 5)
	defer ticker.Stop()

	for range ticker.C {
		if time.Now().Sub(worker.lastHeartbeat) > time.Minute*3 {
			if !worker.Closed() {
				worker.Close()
			}
			break
		}
	}
}

func (worker *workerStat) Run() {
	var err error

	log.Infoln("workerStat run:", worker.Name)
	log.Infoln("send pending responses:", worker.Name)
	worker.handler.SendPendingResponses()

	go worker.watchHeartbeat()

	var (
		status uint8
		body   interface{}
	)
Consuming:
	for msg := range worker.handler.Requests() {
		switch msg.Action {
		case protocol.ActionFinish:
			status, body = worker.processFinishedTask(msg)
			err = worker.reply(status, msg, body)
		case protocol.ActionHeartbeat:
			status, body = worker.processHeartbeat(msg)
			err = worker.reply(status, msg, body)
		default:
			status = protocol.StatusInvalidAction
			err = worker.reply(status, msg, fmt.Sprintf("unknown action:%v", msg.Action))
		}
		if err != nil {
			log.Errorln("failed to reply:", err)
			worker.handler.AddPendingResponse(status, msg, body)
			break Consuming
		}
	}
}

func newWorkerStat(handler *endpoint.Endpoint) *workerStat {
	return &workerStat{
		Name:          "",
		handler:       handler,
		registered:    false,
		lastHeartbeat: time.Now(),
		muPending:     &sync.RWMutex{},
		pendingTasks:  map[primitive.ObjectID]*schema.Task{},
		hostTaskCount: map[string]uint{},
		MaxHostCount:  300,
	}
}

func newClosedWorkerStat() *workerStat {
	ep := endpoint.NewClosedEndpoint()
	return newWorkerStat(ep)
}
