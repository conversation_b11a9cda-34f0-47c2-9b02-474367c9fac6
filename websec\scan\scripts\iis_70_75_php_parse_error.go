package scripts

import (
	"bytes"
	"regexp"
	"strings"
	"websec/utils"
)

var cssLinkPattern1 = regexp.MustCompile(`(?i)<link[^>]+?href="?([^"]+?\.css)"?[^>]+?(?:type="?text/css"?|rel="?stylesheet"?)[^>]*?>`)
var cssLinkPattern2 = regexp.MustCompile(`(?i)<link[^>]+?(?:type="?text/css"?|rel="?stylesheet"?)[^>]+?href="?([^"]+?\.css)"?[^>]*?>`)

func findCSSLink(rawurl string, body []byte, pattern *regexp.Regexp) string {
	link := ""
	for _, matches := range pattern.FindAllSubmatch(body, -1) {
		if len(matches) < 2 {
			continue
		}
		_link, err := utils.URLJoin(rawurl, string(matches[1]))
		if err != nil {
			continue
		}
		if strings.HasPrefix(_link, rawurl) {
			link = strings.TrimRight(_link, "/") + "/xxxx.php"
			break
		}
	}
	return link
}

func IISPHPParseError(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var rawurl = constructURL(args, "/")

	_, body, err := httpGet(rawurl)
	if err != nil {
		return nil, err
	}

	link := findCSSLink(rawurl, body, cssLinkPattern1)
	if link == "" {
		link = findCSSLink(rawurl, body, cssLinkPattern2)
	}
	if link != "" {
		_, body, err = httpGet(link)
		if err != nil {
			return nil, err
		}

		if bytes.Contains(body, []byte("background:")) &&
			bytes.Contains(body, []byte("width:")) &&
			bytes.Contains(body, []byte("center")) &&
			bytes.Contains(body, []byte("bottom")) &&
			!bytes.Contains(body, []byte("<style")) {

			return &ScriptScanResult{Vulnerable: true, Output: link, Body: body}, nil
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("iis_7.0_7.5_php_parse_error.xml", IISPHPParseError)
}
