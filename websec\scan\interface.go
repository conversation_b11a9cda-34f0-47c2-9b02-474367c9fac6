package scan

import (
	"context"
	"websec/scan/rules"
	"websec/utils/semaphore"
)

type Scanner interface {
	Go() (chan *<PERSON>an<PERSON><PERSON>ult, error)

	Add(*ScanLink)

	AddDone()

	IsRunning() bool

	Stop()

	Cancel()

	GetStats() *Stats

	GetOffset() string
	GetWsdlVul(linkUrl []string)
	GetResultChan() (chan *ScanResult, error)
	ScanSingleLink(link *AffectLink) (chan *Scan<PERSON><PERSON><PERSON>, error)
	ScanPythonScripts(entriesURL string) error
	ScanXray(link *AffectLink, fileName ...string) // 可以直接扫描对应漏洞，且可以根据第三方输出文件输出
	IsCanceldActive() bool
	Result() chan *ScanResult
}

func NewScanner(options *Options) (Scanner, error) {
	options.setDefaultValue()

	scanner := &WSScanner{}
	scanner.Options = options
	scanner.scanLinksChannel = make(chan *ScanLink, 100)
	scanner.affectLinksChannel = make(chan *<PERSON>ffect<PERSON>ink, 10)
	scanner.scanResultChannel = make(chan *<PERSON>an<PERSON><PERSON>ult, 100)
	scanner.scannedLinksFilter = make(map[string]struct{})
	scanner.linkSema = semaphore.NewWeighted(options.Concurrency)
	scanner.requestSema = options.Sema
	scanner.linkCtx, scanner.linkCtxCancel = context.WithCancel(context.Background())
	scanner.requestCtx = options.Ctx
	scanner.textVulsFilter = newVulsFilter()
	scanner.bucketSelect = options.BucketSelect
	scanner.expiredAt = options.ExpiredAt
	scanner.initAffectOptionsAndXMLs()

	scanner.affectModules = map[string][]*rules.Module{
		AffectContent:   options.Blueprint.GetModules("text", []string{}),
		AffectFile:      options.Blueprint.GetModules("file", []string{}),
		AffectDirectory: options.Blueprint.GetModules("directory", []string{}),
		AffectServer:    options.Blueprint.GetModules("server", []string{"libserver.dll"}),
		AffectScript:    options.Blueprint.GetModules("server", []string{"libscriptserver.dll"}),
		AffectParameter: options.Blueprint.GetModules("parameter", []string{"libparam.dll", "libspecialxss.dll", "libpvalue.dll"}),
		AffectSQLInject: options.Blueprint.GetModules("sqlinject", []string{}),
	}
	scanner.httpClient = newHTTPClient(options.UserAgent)
	scanner.cookiejar = newMyCookieJar()
	return scanner, nil
}
