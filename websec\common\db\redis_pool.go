package db

import (
	"strconv"
	"strings"
	"sync"
	"time"
	"websec/config"

	"github.com/go-redis/redis"
)

func getRedisPool(options config.RedisConfig) *redis.ClusterClient {
	hosts := strings.Split(options.Host, ",")
	return redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:        hosts,
		Password:     options.Password,
		PoolSize:     8,
		IdleTimeout:  5 * time.Minute,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
		MinIdleConns: 2,
	})
}

type redisManager struct {
	sessions map[string]*redis.ClusterClient
	lock     *sync.RWMutex
}

func newRedisManager() *redisManager {
	return &redisManager{
		sessions: make(map[string]*redis.ClusterClient),
		lock:     new(sync.RWMutex),
	}
}

func getRedisKey(info config.RedisConfig) string {
	return info.Host + ":" + strconv.Itoa(int(info.Port)) + ":" + strconv.Itoa(info.Database)
}

func (m *redisManager) Get(info config.RedisConfig) (*redis.ClusterClient, error) {
	key := getRedisKey(info)
	m.lock.Lock()
	defer m.lock.Unlock()
	if session, ok := m.sessions[key]; ok {
		return session, nil
	}

	pool := getRedisPool(info)
	m.sessions[key] = pool
	return pool, nil
}

var redisMgr = newRedisManager()

func GetRedisPool(info config.RedisConfig) (*redis.ClusterClient, error) {
	return redisMgr.Get(info)
}
