package consts

const (
	TaskTypeCrawl                  string = "crawl"
	TaskTypeScan                          = "scan"
	TaskTypePhishing                      = "phishing"
	TaskTypeTrojan                        = "trojan"
	TaskTypeWeakpass                      = "weakpass"
	TaskTypeOCR                           = "ocr"
	TaskTypeQuickMonitorHomePage          = "quick_home_page"
	TaskTypeQuickMonitorSecondPage        = "quick_second_page"
	TaskTypeCrawlThenScan                 = "crawl_then_scan"
)

const (
	TaskTagCrawlAll        string = "crawl_all"
	TaskTagCrawlHomepage          = "crawl_homepage"
	TaskTagCrawlSecondpage        = "crawl_secondpage"
	TaskTagCrawlActive            = "crawl_active"
	TaskTagScanAll                = "scan_all"
	TaskTagScanAtOnce             = "scan_atonce"
	TaskTagScanSmart              = "scan_smart"
)

const (
	MonitorTypeVul           string = "vul"
	MonitorTypeBlackLink            = "black_link"
	MonitorTypeSensitiveWord        = "sensitive_word"
	MonitorTypeContentChange        = "content_change"
	MonitorTypePhishing             = "phishing"
	MonitorTypeTrojan               = "trojan"
)

const (
	TaskStatusNew      int64 = 0
	TaskStatusRunning        = 21
	TaskStatusFinished       = 22
	TaskStatusFailed         = 40
)

const (
	TaskResultOK       int64 = 0
	TaskResultCanceled       = 20
	TaskResultPaused         = 30
	TaskResultError          = 40
)

const (
	ToBeSnapshot uint8 = iota
	SnapshotRunning
	SnapshotFinished
	SnapshotError
)

const (
	ActionNone     int = iota //没有动作
	ActionStop                //停止
	ActionContinue            //继续
	ActionTODO                //手动添加
)

const (
	AllDomain string = "all" // 获取所有链接
)
