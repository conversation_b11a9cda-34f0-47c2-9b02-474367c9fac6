package main

type RunMode = string

const (
	ModeGenerator      RunMode = "generator"
	ModeFilter                 = "filter"
	ModeCollector              = "collector"
	ModeSnapshot               = "snapshot"
	ModeOcr                    = "ocr"
	ModeSensitiveImage         = "sensitiveimage"
	ModeSwfURL                 = "swf"
)

const (
	GroupGenerator      string = "generator"
	GroupFilter                = "filter"
	GroupCollector             = "collector"
	GroupSnapshot              = "snapshot"
	GroupOcr                   = "ocr"
	GroupSensitiveImage        = "sensitive-image"
)
