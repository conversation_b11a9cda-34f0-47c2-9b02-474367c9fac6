package detect

import (
	"net/url"
	"websec/common"
	"websec/utils/acsmx"
	"websec/utils/semaphore"

	"github.com/yanyiwu/gojieba"
)

type FghkMatcher struct {
	FirstMathcer  *acsmx.Matcher
	SecondMatcher *acsmx.Matcher
}

func (m *FghkMatcher) SearchAll(content []byte) (int, []acsmx.MatchedWord) {
	matchedWords := []acsmx.MatchedWord{}

	_, words := m.FirstMathcer.SearchAll(content)
	if len(words) > 0 {
		_, secWords := m.SecondMatcher.SearchAll(content)
		if len(secWords) > 0 {
			matchedWords = append(matchedWords, words...)
			matchedWords = append(matchedWords, secWords...)
		}
	}
	return len(matchedWords), matchedWords
}

func NewFghkMatcher() *FghkMatcher {
	firstMatcherWords := []string{"中国共产党"}
	secondMatcherWords := []string{
		"嘻嘻", "呵呵", "哈哈", "傻逼", "笨猪",
		"欠扁", "欠抽", "白痴", "走狗",
	}

	firstMatcher := acsmx.NewMatcher()
	for i := range firstMatcherWords {
		firstMatcher.AddPatternString(firstMatcherWords[i])
	}

	secondMatcher := acsmx.NewMatcher()
	for i := range secondMatcherWords {
		secondMatcher.AddPatternString(secondMatcherWords[i])
	}

	firstMatcher.Compile()
	secondMatcher.Compile()

	fghkMatcher := &FghkMatcher{
		FirstMathcer:  firstMatcher,
		SecondMatcher: secondMatcher,
	}
	return fghkMatcher
}

type Options struct {
	SensitiveMatcher         *acsmx.Matcher
	BlackMatcher             *acsmx.Matcher
	SensitiveMatcherGroups   []*common.MatcherGroup
	FghkOcrMatcher           *FghkMatcher
	JiebaFenCi               *gojieba.Jieba
	Operation                Operation
	Domain                   string
	AssetID                  string
	JobID                    string
	IgnoredBlackLinkDomains  *common.IgnoredBlackLinkDomains
	SafeBrowsingURL          string
	JSUnpackPath             string
	OcrAPIAddress            string
	OcrSema                  *semaphore.Weighted
	TrojanDetecter           *TrojanDetecter
	WangdunBlackLinkDetecter *WangdunBlackLinkDetecter
}

type WordResult struct {
	Word     string
	Position int
	Context  string
}

type SensitiveWordResult struct {
	URL            string
	URLHash        string
	Host           string
	IsOuterURL     bool
	SensitiveWords []WordResult
	FromImage      bool
	Content        []byte
	ContentHash    string
}

type BlackLinkResult struct {
	URL             string //  黒链url
	Position        int
	Word            string
	IsOuterURL      bool
	Context         string
	OutPageFrameURL string //  产生黒链的iframe的url
	OutPage         *common.OuterWebPage
}

type FoundVul struct {
	Affect   string
	VulXML   string
	VulURL   string
	Severity string
	Context  map[string]interface{}
}

type FailedPageResult struct {
	Referer    string
	StatusCode int
	URL        string
}

type TrojanResult struct {
	URL             string
	Confidence      string
	Evidence        string
	Info            string
	PlatformType    string
	ThreatType      string
	ThreatEntryType string
	Source          string
}

type OcrResult struct {
	ImageURL       string
	ImageURLHash   string
	Content        string
	SensitiveWords []WordResult
	IsNew          bool
	ImageAddress   string
}

type OcrSensitiveWordResult struct {
	URL         string
	ContentHash string
	Results     []*OcrResult
}

type DetectResult struct {
	SensitiveWords    []*SensitiveWordResult
	OcrSensitiveWords []*OcrSensitiveWordResult
	BlackLinks        []*BlackLinkResult
	FoundVuls         []*FoundVul
	Trojans           []*TrojanResult
}

func (result *SensitiveWordResult) GetHost() string {
	parts, _ := url.Parse(result.URL)
	result.Host = parts.Host
	return result.Host
}
