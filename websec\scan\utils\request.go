package utils

import (
	"crypto/tls"
	"errors"
	"log"
	"net"
	"os"
	"time"

	"github.com/valyala/fasthttp"
)

var logger = log.New(os.Stdout, "", log.Lshortfile|log.Ldate|log.Ltime)

const maxRedirectsCount = 16

func DoHTTPRequest(client *fasthttp.Client, httpRequest *fasthttp.Request, httpResponse *fasthttp.Response) error {
	var err error
	var cookiejar = NewMyCookieJar()
	redirectsCount := 0
	reqURL := string(httpRequest.RequestURI())
	for {
		cookiejar.CopyToRequest(httpRequest)
		if httpRequest.ConnectionClose() {

			httpClient := NewHTTPClient("tmpClient")
			err = httpClient.DoTimeout(httpRequest, httpResponse, 20*time.Second)
		} else {
			err = client.DoTimeout(httpRequest, httpResponse, 20*time.Second)
		}

		if err == fasthttp.ErrConnectionClosed && !httpRequest.ConnectionClose() {

			httpRequest.SetConnectionClose()
			continue
		} else if err != nil {
			break
		}
		cookiejar.CopyFromResponse(httpResponse)

		statusCode := httpResponse.Header.StatusCode()
		if statusCode != fasthttp.StatusMovedPermanently && statusCode != fasthttp.StatusFound && statusCode != fasthttp.StatusSeeOther {
			break
		}

		redirectsCount++
		if redirectsCount > maxRedirectsCount {
			err = errors.New("too many redirects")
			break
		}
		location := httpResponse.Header.Peek("Location")
		if len(location) == 0 {
			err = errors.New("missing location")
			break
		}
		redirectsURL := getRedirectURL(reqURL, location)
		httpRequest.SetRequestURI(redirectsURL)
	}

	if err != nil {

		fasthttp.ReleaseResponse(httpResponse)
		return err
	}

	return nil
}

func getRedirectURL(baseURL string, location []byte) string {
	u := fasthttp.AcquireURI()
	u.Update(baseURL)
	u.UpdateBytes(location)
	redirectURL := u.String()
	fasthttp.ReleaseURI(u)
	return redirectURL
}

func NewHTTPClient(name string) *fasthttp.Client {
	return &fasthttp.Client{
		Name:                          name,
		ReadTimeout:                   20 * time.Second,
		WriteTimeout:                  20 * time.Second,
		MaxResponseBodySize:           1024 * 1024 * 2,
		DisableHeaderNamesNormalizing: true,
		Dial: func(addr string) (net.Conn, error) {
			return fasthttp.DialTimeout(addr, 20*time.Second)
		},
		TLSConfig: &tls.Config{

			InsecureSkipVerify: true,
		},
	}
}
