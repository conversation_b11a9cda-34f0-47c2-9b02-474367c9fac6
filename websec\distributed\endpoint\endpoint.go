package endpoint

import (
	"errors"
	"fmt"
	"io"
	"net"
	"os"
	"sync"
	"sync/atomic"
	"time"
	"websec/distributed/protocol"
	"websec/utils/log"

	"github.com/google/uuid"
	"github.com/ugorji/go/codec"
)

type OnLostFunc func(string) error

type OnRequestFunc func(string, *protocol.Message) (*protocol.Message, error)

type pendingResponse struct {
	Request *protocol.Message
	Status  uint8
	Body    interface{}
}

type Endpoint struct {
	Tag string

	timeout   time.Duration
	currentID uint32
	clientID  string

	conn    net.Conn
	decoder *codec.Decoder
	encoder *codec.Encoder

	closed      int32
	connectLock *sync.Mutex

	lastConnectTime time.Time

	requests chan *protocol.Message

	pendingRequests  sync.Map
	pendingResponses []*pendingResponse

	onLostFuncs []OnLostFunc

	muWrite *sync.Mutex
	err     error
}

func (ep *Endpoint) Close() {
	if atomic.CompareAndSwapInt32(&ep.closed, 0, 1) {
		log.Infof("%s closed", ep.Tag)
		if ep.conn != nil {
			ep.conn.Close()
			ep.conn = nil
		}
	}
}

func (ep *Endpoint) SetTimeout(timeout time.Duration) error {
	ep.timeout = timeout
	return nil
}

func (ep *Endpoint) Closed() bool {
	return atomic.LoadInt32(&ep.closed) == 1
}

func (ep *Endpoint) Requests() <-chan *protocol.Message {
	return ep.requests
}

func (ep *Endpoint) MoveTo(newEp *Endpoint) {
	newEp.pendingRequests = ep.pendingRequests
	newEp.pendingResponses = ep.pendingResponses
	ep.pendingRequests = sync.Map{}
	ep.pendingResponses = nil
}

func (ep *Endpoint) SendPendingResponses() {
	var err error
	for _, item := range ep.pendingResponses {
		err = ep.Reply(item.Status, item.Request, item.Body)
		if err != nil {
			log.Errorln("send pending responses failed:", err)
			break
		}
	}
}

func (ep *Endpoint) AddPendingResponse(status uint8, msg *protocol.Message, body interface{}) {
	ep.pendingResponses = append(ep.pendingResponses,
		&pendingResponse{
			Request: msg,
			Status:  status,
			Body:    body,
		})
}

func (ep *Endpoint) disconnect() {
	log.Infoln("disconnect")

	ep.connectLock.Lock()
	now := time.Now()
	if ep.conn != nil && now.Sub(ep.lastConnectTime) > time.Second*5 {
		ep.Close()
		ep.lastConnectTime = now
		go ep.callOnLost()
	}
	ep.connectLock.Unlock()

	/*
		ep.connectedCond.L.Lock()
		for ep.conn == nil && !ep.Closed() {
			ep.connectedCond.Wait()
		}
		ep.connectedCond.L.Unlock()
	*/
}

func (ep *Endpoint) callOnLost() {
	for _, f := range ep.onLostFuncs {
		f(ep.Tag)
	}
}

func (ep *Endpoint) Consume() {
	defer func() {
		close(ep.requests)
	}()

	var err error
	for !ep.Closed() {

		var msg protocol.Message
		err = ep.decoder.Decode(&msg)
		if err != nil {
			log.Errorln("failed to read:", err)
			ep.err = err
			ep.disconnect()

			break
		}
		log.Infoln("<<<<", &msg)
		switch msg.Type {
		case protocol.TypeRequest:
			ep.requests <- &msg
		case protocol.TypeResponse:
			future, ok := ep.pendingRequests.Load(msg.ID)
			if !ok {
				log.Errorln("pendingRequests future is not found:", msg.ID)
			} else {
				c := future.(chan *protocol.Message)
				c <- &msg
			}
		}
	}
	log.Infoln("closed:", ep.Tag)
}

func (ep *Endpoint) SetConnection(conn net.Conn) error {
	var mh codec.MsgpackHandle

	ep.connectLock.Lock()
	ep.conn = conn
	ep.decoder = codec.NewDecoder(io.Reader(conn), &mh)
	ep.encoder = codec.NewEncoder(io.Writer(conn), &mh)
	atomic.StoreInt32(&ep.closed, 0)
	ep.connectLock.Unlock()

	return nil
}

func (ep *Endpoint) write(msg interface{}) error {

	ep.muWrite.Lock()
	defer ep.muWrite.Unlock()

	err := ep.encoder.Encode(msg)
	if err != nil {
		log.Errorln("failed to write:", msg, err)
		ep.err = err
	} else {
		log.Infoln(">>>>", msg)
	}
	return err
}

func (ep *Endpoint) ConnectionAliveBool() bool {
	return ep.err == nil
}

func (ep *Endpoint) AddLostHandle(f OnLostFunc) error {
	ep.onLostFuncs = append(ep.onLostFuncs, f)
	return nil
}

func (ep *Endpoint) generateMessageID() uint32 {
	return atomic.AddUint32(&ep.currentID, 1)
}

func (ep *Endpoint) Request(msg *protocol.Message) (*protocol.Message, error) {
	msg.ID = ep.generateMessageID()
	msg.Type = protocol.TypeRequest
	return ep.Send(msg, true)
}

func (ep *Endpoint) Reply(status uint8, request *protocol.Message, body interface{}) error {
	response := &protocol.Message{}
	response.ID = request.ID
	response.Type = protocol.TypeResponse
	response.Status = status
	response.Body = body
	_, err := ep.Send(response, false)
	return err
}

func (ep *Endpoint) Send(msg *protocol.Message, waitResponse bool) (*protocol.Message, error) {
	var future chan *protocol.Message

	t := time.NewTimer(ep.timeout)

	msg.ClientID = ep.clientID

	if waitResponse {
		future = make(chan *protocol.Message, 1)
		ep.pendingRequests.Store(msg.ID, future)
		defer ep.pendingRequests.Delete(msg.ID)
	}

	err := ep.write(msg)
	if err != nil {
		ep.disconnect()
		return nil, err
	}

	if waitResponse {
		select {
		case resp := <-future:
			return resp, nil
		case <-t.C:
			return nil, errors.New("timeout")
		}
	} else {
		return nil, nil
	}
}

func generateClientID() string {
	var hostname, err = os.Hostname()
	if err != nil {
		log.Fatalln("failed to get hostname:", err)
		return ""
	}
	pid := int32(os.Getpid())
	return fmt.Sprintf("%v:%v", hostname, pid)
}

func NewEndpoint(options ...optionFunc) (*Endpoint, error) {
	uid, err := uuid.NewUUID()
	if err != nil {
		return nil, err
	}
	ep := &Endpoint{
		Tag: uid.String(),

		connectLock:     &sync.Mutex{},
		clientID:        generateClientID(),
		timeout:         time.Second * 10,
		requests:        make(chan *protocol.Message, 10),
		pendingRequests: sync.Map{},
		muWrite:         &sync.Mutex{},
	}
	for _, opt := range options {
		err := opt(ep)
		if err != nil {
			return nil, err
		}
	}
	return ep, nil
}

func NewClosedEndpoint() *Endpoint {
	return &Endpoint{
		closed: 1,
	}
}
