package scripts

import (
	"bytes"
	"net"
	"strings"
	"time"
)

var iscVersionRequest = []byte("\xd5\xe4\x01\x00\x00\x01\x00\x00\x00\x00\x00\x00\x07version\x04bind\x00\x00\x10\x00\x03")

func checkBindVersion(version string) bool {
	for _, prefix := range []string{"9.0.", "9.1.", "9.2.", "9.3.", "9.4.", "9.5.", "9.6.", "9.7.", "9.8."} {
		if strings.HasPrefix(version, prefix) {
			return true
		}
	}
	if strings.HasPrefix(version, "9.9.9") {
		return !(strings.HasPrefix(version, "9.9.9-P3") || strings.HasPrefix(version, "9.9.9-P4"))
	}
	if strings.HasPrefix(version, "9.10.") {
		return !strings.HasPrefix(version, "9.10.4-P3")
	}
	if strings.HasPrefix(version, "9.11.0") {
		return !strings.HasPrefix(version, "9.11.0rc3")
	}
	return false
}

func ISCBindDOS(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addr := args.Host + ":53"
	conn, err := net.DialTimeout("tcp", addr, time.Second*30)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
	conn.Write(iscVersionRequest)
	response := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(time.Second * 5))
	n, err := conn.Read(response)
	if err != nil {
		return nil, err
	}
	if n > 43 {
		version := string(bytes.Split(response[43:n], []byte("\xc0\x0c"))[0])
		if checkBindVersion(version) {
			return &ScriptScanResult{Vulnerable: true, Output: addr + "|Bind Version:\t" + version, Body: response}, nil
		}

	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("ISC_BIND_9_dos.xml", ISCBindDOS)
}
