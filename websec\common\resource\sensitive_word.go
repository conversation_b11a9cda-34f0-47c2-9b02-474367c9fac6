package resource

import (
	"context"
	"strings"
	"time"
	"websec/common"
	"websec/common/consts"
	"websec/config"
	"websec/utils"
	"websec/utils/acsmx"
	"websec/utils/log"

	"github.com/yanyiwu/gojieba"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	defaultSensitiveWords            utils.StringSet
	defaultSensitiveWordsMatcher     *acsmx.Matcher
	defaultBlackWords                utils.StringSet
	defaultBlackWordsMatcher         *acsmx.Matcher
	defaultJiebaFC                   *gojieba.Jieba
	defaultSensitiveWordsStrengthMap map[string]int
	defaultFakeSensitiveWordsMap     map[string]int
	sensitiveWordMatcherGroups       []*common.MatcherGroup
)

type blackSensitiveWord struct {
	Type     string `bson:"type"`
	Word     string `bson:"word"`
	Strength int    `bson:"strength"`
}

type emphasizeWord struct {
	Word string `bson:"word"`
}

type sensitiveWordGroup struct {
	Group string   `bson:"group"`
	Verbs []string `bson:"verbs"`
	Nouns []string `bson:"nouns"`
}

func LoadDefaultBlackSensitiveWords(mongodb *mongo.Database) error {
	log.Infoln("loading default black/sensitive words from:", consts.CollectionBlackSensitiveWords)

	var (
		blackWords                = utils.StringSet{}
		blackMatcher              = acsmx.NewMatcher()
		sensitiveWords            = utils.StringSet{}
		sensitiveMatcher          = acsmx.NewMatcher()
		sensitiveWordsStrengthMap = make(map[string]int)
	)

	coll := mongodb.Collection(consts.CollectionBlackSensitiveWords)
	cursor, err := coll.Find(context.Background(), bson.M{"status": 0})
	if err != nil {
		log.Errorln("failed to load sensitive words.", err)
		return err
	}
	defer cursor.Close(context.Background())

	var doc blackSensitiveWord
	for cursor.Next(context.Background()) {
		cursor.Decode(&doc)
		if doc.Word != "" && len(strings.TrimSpace(doc.Word)) > 0 {
			switch doc.Type {
			case "sensitivewords":
				sensitiveWords.Add(doc.Word)
				sensitiveMatcher.AddPatternString(doc.Word)
				sensitiveWordsStrengthMap[doc.Word] = doc.Strength
			case "blackwords":
				blackWords.Add(doc.Word)
				blackMatcher.AddPatternString(doc.Word)
			default:
				log.Errorln("unknown blacksensitive word type:", doc.Type)
			}
		}
	}
	defaultBlackWords = blackWords
	blackMatcher.Compile()
	defaultBlackWordsMatcher = blackMatcher
	defaultSensitiveWords = sensitiveWords
	sensitiveMatcher.Compile()
	defaultSensitiveWordsMatcher = sensitiveMatcher
	defaultSensitiveWordsStrengthMap = sensitiveWordsStrengthMap
	defaultFakeSensitiveWordsMap = map[string]int{
		"赌博":   0,
		"色情":   0,
		"强奸":   0,
		"走私汽车": 0,
		"套牌车":  0,
		"外挂":   0,
	}
	log.Infof("loading black/sensitive words finished. blackwords count:%d, sensitivewords count:%d",
		len(blackWords), len(sensitiveWords))
	return nil
}

func LoadEmphasizeWords(mongodb *mongo.Database, jiebaconfig config.JiebaConfig) error {
	log.Infoln("loading emphasize words from:", consts.CollectionEmphasizeWords)
	jiebaFC := gojieba.NewJieba(jiebaconfig.DictPath, jiebaconfig.HMMPath, jiebaconfig.UserDictPath,
		jiebaconfig.IDFPath, jiebaconfig.StopWordsPath)
	if defaultJiebaFC == nil {
		defaultJiebaFC = jiebaFC
	}

	coll := mongodb.Collection(consts.CollectionEmphasizeWords)
	cursor, err := coll.Find(context.Background(), bson.M{"deleted": 0})
	if err != nil {
		log.Errorln("failed to load sensitive words.", err)
		return err
	}
	defer cursor.Close(context.Background())

	var doc emphasizeWord
	var count int
	for cursor.Next(context.Background()) {
		cursor.Decode(&doc)
		count++
		jiebaFC.AddWord(doc.Word)
	}
	defaultJiebaFC = jiebaFC
	log.Infoln("loading emphasize words finished. words count:", count)
	return nil
}

func LoadSensitiveWordGroups(mongodb *mongo.Database) error {
	log.Infoln("loading sensitive word groups from:", consts.CollectionSensitiveWordGroups)
	groups := make([]*common.MatcherGroup, 0, 100)

	coll := mongodb.Collection(consts.CollectionSensitiveWordGroups)
	cursor, err := coll.Find(context.Background(), bson.M{"status": 0})
	if err != nil {
		log.Error("failed to load sensitive word groups.", err)
		return err
	}
	defer cursor.Close(context.Background())

	var doc sensitiveWordGroup
	for cursor.Next(context.Background()) {
		cursor.Decode(&doc)
		verbMatcher := acsmx.NewMatcher()
		nounMatcher := acsmx.NewMatcher()
		for _, word := range doc.Verbs {
			verbMatcher.AddPatternString(word)
		}
		verbMatcher.Compile()
		for _, word := range doc.Nouns {
			nounMatcher.AddPatternString(word)
		}
		nounMatcher.Compile()
		groups = append(groups, &common.MatcherGroup{FirstMatcher: verbMatcher, SecondMatcher: nounMatcher})
	}
	sensitiveWordMatcherGroups = groups
	log.Infoln("loading sensitivewordgroups finished. count:", len(groups))
	return nil
}

func DefaultSensitiveWords() utils.StringSet {
	return defaultSensitiveWords
}

func DefaultBlackWords() utils.StringSet {
	return defaultBlackWords
}

func DefaultSensitiveWordsMatcher() *acsmx.Matcher {
	return defaultSensitiveWordsMatcher
}

func DefaultBlackWordsMatcher() *acsmx.Matcher {
	return defaultBlackWordsMatcher
}

func DefaultJiebaFC() *gojieba.Jieba {
	return defaultJiebaFC
}

func GetSensitiveWordMatcherGroups() []*common.MatcherGroup {
	return sensitiveWordMatcherGroups
}

func LoadWordsPeriodically(mongodb *mongo.Database, jiebaConfig config.JiebaConfig, period time.Duration) {
	go func() {
		ticker := time.NewTicker(period)
		for t := range ticker.C {
			log.Infoln("---- load sensitive words ---- at:", t.String())
			LoadSensitiveWordGroups(mongodb)
			LoadEmphasizeWords(mongodb, jiebaConfig)
			LoadSensitiveWordGroups(mongodb)
		}
	}()
}

func GetSensitiveWordStrength(sensitiveWordMap []string, customSensitiveWords []string) int {

	wordStrength := 0
	if len(sensitiveWordMap) > 0 {
		trueWordList := make([]string, 0, 10)
		fakeWordList := make([]string, 0, 10)
		customWordList := make([]string, 0, 10)

		for _, sensiveWord := range sensitiveWordMap {
			level, ok := defaultSensitiveWordsStrengthMap[sensiveWord]
			if ok {
				wordStrength |= level
				trueWordList = append(trueWordList, sensiveWord)
			}

			level, ok = defaultFakeSensitiveWordsMap[sensiveWord]
			if ok {
				wordStrength |= level
				fakeWordList = append(fakeWordList, sensiveWord)
			}

			for _, customWord := range customSensitiveWords {
				if customWord == sensiveWord {
					wordStrength |= 2
					customWordList = append(customWordList, sensiveWord)
					break
				}
			}
		}

		if len(fakeWordList) == 1 && len(trueWordList) == 1 && len(customWordList) <= 1 {
			if fakeWordList[0] == trueWordList[0] {
				if len(customWordList) == 1 {
					if customWordList[0] == fakeWordList[0] {
						wordStrength = -1
					}
				} else {
					wordStrength = -1
				}
			}
		}
	}
	return wordStrength
}
