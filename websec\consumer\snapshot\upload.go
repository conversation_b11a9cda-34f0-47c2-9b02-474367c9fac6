package snapshot

import (
	"context"
	"sync"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/semaphore"
)

type UploadService struct {
	sensitiveWordChan chan *SensitiveWordUpload
	contentChange<PERSON>han chan *ContentChangeUpload
	wg                sync.WaitGroup
	processor         *Processor
}

func NewUploadService(processor *Processor) *UploadService {
	return &UploadService{
		sensitiveWordChan: make(chan *SensitiveWordUpload, 100),
		contentChangeChan: make(chan *ContentChangeUpload, 50),
		processor:         processor,
	}
}

func (u *UploadService) Start() {
	u.wg.Add(2)
	go u.sensitiveWordRun()
	go u.contentChangeRun()
}

func (u *UploadService) Stop() {
	close(u.sensitiveWordChan)
	close(u.contentChangeChan)
	u.wg.Wait()
}

func (u *UploadService) AddSensitiveWordUpload(msg *SensitiveWordUpload) {
	u.sensitiveWord<PERSON>han <- msg
}

func (u *UploadService) AddContentChangeUpload(msg *ContentChangeUpload) {
	u.contentChangeChan <- msg
}

func (u *UploadService) sensitiveWordRun() {
	defer u.wg.Done()

	seMa := semaphore.NewWeighted(10)
	var err error
	for msg := range u.sensitiveWordChan {
		if err = seMa.Acquire(context.Background(), 1); err == nil {
			go u.doSensitiveWord(msg, seMa, true)
		} else {
			log.Errorln(err)
			u.doSensitiveWord(msg, seMa, false)
		}
	}
}

func (u *UploadService) doSensitiveWord(msg *SensitiveWordUpload, seMa *semaphore.Weighted, acquire bool) {
	if acquire {
		defer func() {
			seMa.Release(1)
		}()
	}

	result := &schema.SnapshotSensitiveWordDoc{
		ID:         msg.ID,
		Host:       msg.Host,
		AssetID:    msg.AssetID,
		SnapshotID: msg.SnapshotID,
		Tool:       msg.Tool,
		JS:         msg.JS,
		Status:     consts.SnapshotError,
	}

	start := time.Now()
	if msg.Err == nil {
		path, err := u.processor.imgUploader.UploadImg(msg.ImageData, ".jpg", "")
		if err == nil {
			result.SnapshotURL = path
			result.Status = consts.SnapshotFinished
		} else {
			result.Err = err.Error()
		}
	} else {
		result.Err = msg.Err.Error()
	}

	log.Infof("upload SensitiveWord %s %f", msg.SnapshotID, time.Now().Sub(start).Seconds())
	if result.Err != "" {
		log.Errorln("sensitiveword faild, err:", result.Err)
	} else {
		log.Infoln("sensitiveword success ", result.SnapshotURL)
	}

	u.processor.producer.Produce(consts.TopicFinalSnapshotSensitiveWordResults, result)
}

func (u *UploadService) contentChangeRun() {
	defer u.wg.Done()

	seMa := semaphore.NewWeighted(10)
	var err error
	for msg := range u.contentChangeChan {
		if err = seMa.Acquire(context.Background(), 1); err == nil {
			go u.doContentChange(msg, seMa, true)
		} else {
			log.Errorln(err)
			u.doContentChange(msg, seMa, false)
		}
	}
}

func (u *UploadService) doContentChange(msg *ContentChangeUpload, seMa *semaphore.Weighted, acquire bool) {
	if acquire {
		defer func() {
			seMa.Release(1)
		}()
	}

	result := &schema.FinalSnapshotContentChangeResult{
		ID:      msg.ID,
		Host:    msg.Host,
		AssetID: msg.AssetID,
		JobID:   msg.JobID,
		OldTool: msg.OldTool,
		NewTool: msg.NewTool,
		OldJS:   msg.OldJS,
		NewJS:   msg.NewJS,
		Status:  consts.SnapshotError,
	}

	start := time.Now()

	if msg.Err == nil {
		oldPath, err1 := u.processor.imgUploader.UploadImg(msg.OldImageData, ".jpg", "")
		newPath, err2 := u.processor.imgUploader.UploadImg(msg.NewImageData, ".jpg", "")
		if err1 == nil || err2 == nil {
			result.OldSnapshotURL = oldPath
			result.NewSnapshotURL = newPath
			result.Status = consts.SnapshotFinished
		}
		if err1 != nil {
			result.Err = err1.Error()
		}
		if err2 != nil {
			result.Err = err2.Error()
		}
	} else {
		log.Errorln("-------------------- snapshot generation error:", msg.Err.Error())
		result.Err = msg.Err.Error()
	}

	log.Infof("upload contentChange %s %f", msg.Host, time.Now().Sub(start).Seconds())

	if result.Err != "" {
		log.Errorln("contentChange faild, err:", result.Err, msg.JobID, msg.ID)
	} else {
		log.Infoln("contentChange success oldSnapshot ~ newSnapshot:", result.ID, result.JobID, result.OldSnapshotURL, " ~ ", result.NewSnapshotURL)
	}

	u.processor.producer.Produce(consts.TopicFinalSnapshotContentChangeResults, result)
}
