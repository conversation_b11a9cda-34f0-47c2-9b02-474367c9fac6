package scan

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"net"
	"sync"
	"time"

	"github.com/valyala/fasthttp"
)

type vulsFilter struct {
	data map[string]map[string]bool
	lock sync.RWMutex
}

func newVulsFilter() *vulsFilter {
	return &vulsFilter{
		data: map[string]map[string]bool{},
		lock: sync.RWMutex{},
	}
}

func (filter *vulsFilter) ContainsOrFull(xmlName string, linkURL string) bool {
	filter.lock.RLock()
	defer filter.lock.RUnlock()

	if urls, ok := filter.data[xmlName]; ok {
		if len(urls) >= MaxVulCountPerTextXML {
			return true
		}
		if _, ok = urls[linkURL]; ok {
			return true
		}
	}
	return false
}

func (filter *vulsFilter) Add(xmlName string, linkURL string) {
	filter.lock.Lock()
	defer filter.lock.Unlock()

	if urls, ok := filter.data[xmlName]; ok {
		urls[linkURL] = true
	} else {
		newURLs := map[string]bool{}
		newURLs[linkURL] = true
		filter.data[xmlName] = newURLs
	}
}

type myCookieJar struct {
	entries map[string]*fasthttp.Cookie
	mu      sync.RWMutex
}

func newMyCookieJar() *myCookieJar {
	return &myCookieJar{
		entries: map[string]*fasthttp.Cookie{},
		mu:      sync.RWMutex{},
	}
}

func (jar *myCookieJar) GetCookie(key string) (*fasthttp.Cookie, bool) {
	jar.mu.RLock()
	defer jar.mu.RUnlock()

	cookie, ok := jar.entries[key]
	return cookie, ok
}

func (jar *myCookieJar) SetCookie(key, value string) {
	jar.mu.Lock()
	defer jar.mu.Unlock()

	if cookie, ok := jar.entries[key]; ok {
		if cookie.String() != value {
			cookie.Parse(value)
		}
	} else {
		cookie := fasthttp.AcquireCookie()
		cookie.Parse(value)
		jar.entries[key] = cookie
	}
}

func (jar *myCookieJar) CopyToRequest(request *fasthttp.Request) {
	jar.mu.RLock()
	defer jar.mu.RUnlock()

	now := time.Now()
	for key, cookie := range jar.entries {
		if cookie.Expire().Before(now) {
			request.Header.SetCookie(key, string(cookie.Value()))
		}
	}
}

func (jar *myCookieJar) CopyFromResponse(response *fasthttp.Response) {
	jar.mu.Lock()
	defer jar.mu.Unlock()

	response.Header.VisitAllCookie(func(key, value []byte) {
		keyStr := string(key)
		if cookie, ok := jar.entries[keyStr]; ok {
			if !bytes.Equal(cookie.Value(), value) {
				cookie.ParseBytes(value)
			}
		} else {
			cookie = fasthttp.AcquireCookie()
			cookie.ParseBytes(value)
			jar.entries[keyStr] = cookie
		}
	})
}

func (jar *myCookieJar) Release() {
	jar.mu.Lock()
	defer jar.mu.Unlock()

	for key, cookie := range jar.entries {
		fasthttp.ReleaseCookie(cookie)
		delete(jar.entries, key)
	}
}

func newHTTPClient(name string) *fasthttp.Client {
	return &fasthttp.Client{
		Name:                          name,
		ReadTimeout:                   20 * time.Second,
		WriteTimeout:                  20 * time.Second,
		MaxResponseBodySize:           1024 * 1024 * 2,
		DisableHeaderNamesNormalizing: true,
		Dial: func(addr string) (net.Conn, error) {
			return fasthttp.DialTimeout(addr, 20*time.Second)
		},
		TLSConfig: &tls.Config{

			InsecureSkipVerify: true,
		},
	}
}

func (jar *myCookieJar) CopyToString() (cookies string) {
	jar.mu.RLock()
	defer jar.mu.RUnlock()

	now := time.Now()
	for key, cookie := range jar.entries {
		if cookie.Expire().Before(now) {

			cookies += fmt.Sprintf("%v: %v;", key, string(cookie.Value()))
		}
	}
	return
}
