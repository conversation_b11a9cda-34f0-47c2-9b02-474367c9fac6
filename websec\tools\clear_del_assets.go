package main

import (
	"context"
	"sync"
	"time"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/config"
	"websec/utils/log"
	"websec/utils/semaphore"

	"go.mongodb.org/mongo-driver/bson"
)

func main() {
	settings, err := config.ParseConfig()
	if err != nil {
		log.Error(err)
		return
	}
	dbConnection, err := db.NewDBConnection(
		db.WithRedisConfig(settings.Redis),
		db.WithMongoConfig(settings.MongoDB))
	if err != nil {
		log.Errorln(err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	cursor, err := dbConnection.GetMongoDatabase().Collection(consts.CollectionAssets).Find(ctx,
		bson.M{"status": 0})
	if err != nil {
		log.Error(err)
		return
	}

	assets := make([]*schema.Asset, 0, 10000)
	for cursor.Next(ctx) {
		doc := new(schema.Asset)
		err = cursor.Decode(doc)
		if err != nil {
			log.Error(err)
			continue
		}
		assets = append(assets, doc)
	}
	cursor.Close(ctx)

	var wg sync.WaitGroup
	sem := semaphore.NewWeighted(20)
	for _, v := range assets {
		if err = sem.Acquire(context.TODO(), 1); err == nil {
			wg.Add(1)
			go func(host string) {
				defer func() {
					sem.Release(1)
					wg.Done()
				}()
				err2 := dbConnection.DelKey(consts.RedisChangePagePrefix + host)
				log.Infoln("del ", host, err2)
			}(v.Host)
		}
	}

	wg.Wait()
}
