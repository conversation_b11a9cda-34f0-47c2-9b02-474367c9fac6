package main

import (
	"context"
	"crypto/tls"
	"time"
	"websec/rpc/client"
	"websec/rpc/example/genproto"
	"websec/rpc/log"
)

type Args struct {
	A int
	B int
}

type Reply struct {
	C int
}

type Arith int

func (t *Arith) Mul(ctx context.Context, args *Args, reply *Reply) error {
	reply.C = args.A * args.B
	return nil
}

func (t *Arith) Add(ctx context.Context, args *Args, reply *Reply) error {
	reply.C = args.A + args.B
	return nil
}

type ServerPush int

func (t *ServerPush) Test1(ctx context.Context, args *genproto.ServerPush, reply *genproto.Empty) error {
	log.Info("Test1", args)
	return nil
}

func main() {
	option := client.DefaultOption
	option.Heartbeat = true
	option.HeartbeatInterval = 20 * time.Second
	option.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	c := client.NewClient(option)
	c.Register(new(Arith), "")
	c.Register(new(ServerPush), "")
	err := c.Con<PERSON>("tcp", "127.0.0.1:7000")

	if err != nil {
		log.Fatalf("failed to connect: %v", err)
		return
	}

	args := &Args{
		A: 10,
		B: 20,
	}

	reply := &Reply{}
	err = c.Call(context.Background(), "Arith", "Mul", args, reply)
	if err != nil {
		log.Errorf("failed to call: %v", err)
	}

	if reply.C != 200 {
		log.Errorf("expect 200 but got %d", reply.C)
	}

	log.Info("reply", reply)
	select {}
}
