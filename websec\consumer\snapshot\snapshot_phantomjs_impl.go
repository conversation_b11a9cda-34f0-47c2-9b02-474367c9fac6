package snapshot

import (
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"websec/utils/log"
)

func captureByPhantomjs(ctx context.Context, url, dir string) ([]byte, error) {
	log.Debugf("capture by phantomjs: %s", url)

	pattern := "snapshot.*.jpg"
	tmpfile, err := ioutil.TempFile(dir, pattern)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	defer os.Remove(tmpfile.Name())

	args := []string{
		fmt.Sprintf("--config=%s", phantomjsConfig),
		phantomjsScript,
		url,
		tmpfile.Name()}
	cmd := exec.CommandContext(ctx, phantomjsExec, args...)
	if err := cmd.Run(); err != nil {
		log.Error(err)
		return nil, err
	}

	if err := tmpfile.Close(); err != nil {
		log.Error(err)
		return nil, err
	}

	if imgbuf, err := ioutil.ReadFile(tmpfile.Name()); err != nil {
		log.Error(err)
		return nil, err
	} else {
		log.Debugf("captureByPhantomjs size %v", len(imgbuf))
		if len(imgbuf) < 1024*8 {
			return imgbuf, errors.New("file size too small, seems failed with Phantomjs")
		}
		return imgbuf, nil
	}
}
