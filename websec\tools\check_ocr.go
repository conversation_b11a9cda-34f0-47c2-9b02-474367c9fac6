package main

import (
	"context"
	"fmt"
	"time"
	"websec/common"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/config"
	"websec/detect"
	"websec/utils/acsmx"
	"websec/utils/log"
	"websec/utils/stream"

	"go.mongodb.org/mongo-driver/bson"
)

func main() {
	settings, err := config.ParseConfig()
	if err != nil {
		log.Errorln(err)
		return
	}

	dbConnection, err := db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis))
	if err != nil {
		log.Errorln(err)
		return
	}

	run(dbConnection, settings)

}

func run(dbConnection *db.DBConnection, settings *config.Config) {

	producer, err := stream.NewProducer(&stream.ProducerOptions{
		Brokers:         settings.Kafka.Brokers,
		MessageMaxBytes: settings.Kafka.MessageMaxBytes,
	})
	if err != nil {
		log.Errorln(err)
		return
	}
	producer.Go()
	defer producer.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	asset := new(schema.Asset)
	err = dbConnection.GetMongoDatabase().Collection(consts.CollectionAssets).FindOne(ctx,
		bson.M{"host": "***********"}).Decode(asset)
	if err != nil {
		log.Errorln(err)
		return
	}

	matcher := acsmx.NewMatcher()

	if len(asset.Options.SensitiveWord.Customized) > 0 {
		for _, word := range asset.Options.SensitiveWord.Customized {
			matcher.AddPatternString(word)
		}

	}
	matcher.Compile()

	detecter, err := detect.NewDetecter(&detect.Options{
		Domain:           "***********",
		SensitiveMatcher: matcher,
		Operation:        detect.CheckSensitiveWords,
		OcrAPIAddress:    "http://127.0.0.1:8181/ocr",
	})
	if err != nil {
		log.Errorln(err)
		return
	}

	webpage := &common.Webpage{
		URL: "http://***********",
		Headers: common.HttpHeaders{
			"Content-Type": []string{"text/html"},
		},

		Images: []string{"http://***********:10049/sensitive_images/lt.jpg"},
	}

	OcrSensitiveWords := detecter.DetectOCRSensitivewordsAsSW(webpage)
	for _, item := range OcrSensitiveWords {
		fmt.Println(item.FromImage, item.SensitiveWords)
	}

}
