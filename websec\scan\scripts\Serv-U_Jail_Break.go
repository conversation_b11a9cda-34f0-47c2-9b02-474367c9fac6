package scripts

import (
	"bufio"
	"bytes"
	"net"
	"regexp"
	"time"
)

var ftpJailBreakVersion = regexp.MustCompile(`Server v(6\.4|7\.1|7\.3|8\.2|10\.5)`)

func ServUJailBreak(args *ScriptScanArgs) (*ScriptScanResult, error) {
	conn, err := net.DialTimeout("tcp", args.Host+":21", 5*time.Second)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	conn.SetReadDeadline(time.Now().Add(15 * time.Second))
	reader := bufio.NewReader(conn)
	line, _, err := reader.ReadLine()
	if err != nil {
		return nil, err
	}
	if bytes.HasPrefix(line, []byte("220")) {
		banner := line[3:]
		if ftpJailBreakVersion.Match(banner) {
			return &ScriptScanResult{Vulnerable: true, Output: args.Host, Body: line}, nil
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHand<PERSON>("Serv-U_Jail_Break_vul.xml", ServUJailBreak)
}
