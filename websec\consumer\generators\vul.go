package generators

import (
	"websec/common"
	"websec/common/schema"
	"websec/detect"
)

func getVulResult(page *common.Webpage, result *detect.DetectResult) []*schema.FoundVulDoc {
	if len(result.FoundVuls) == 0 {
		return nil
	}

	vulResult := make([]*schema.FoundVulDoc, 0, len(result.FoundVuls))
	for _, v := range result.FoundVuls {
		vul := &schema.FoundVulDoc{
			AssetID: page.AssetID,
			JobID:   page.JobID,
			Host:    page.Host,
			Context: v.Context,
			Vul: schema.FoundVulsVul{
				VulXML:   v.VulXML,
				VulURL:   v.VulURL,
				Severity: v.Severity,
				From:     page.Host,
			},
			FoundAt: page.CrawledAt,
		}
		vulResult = append(vulResult, vul)
	}

	return vulResult
}
