package scripts

import (
	"bytes"
	"encoding/binary"
	"net"
	"time"
)

func BytesToInt(b []byte) (r int, err error) {
	bytesBuffer := bytes.NewBuffer(b)
	var x int32
	err = binary.Read(bytesBuffer, binary.BigEndian, &x)
	if err != nil {
		return
	}
	r = int(x)
	return
}

func CheckSmbCve20200796(args *ScriptScanArgs) (*ScriptScanResult, error) {

	addr := args.Host + ":445"
	conn, err := net.DialTimeout("tcp", addr, time.Second*3)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	payload := "\x00\x00\x00\xc0\xfeSMB@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1f\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00$\x00\x08\x00\x01\x00\x00\x00\x7f\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00x\x00\x00\x00\x02\x00\x00\x00\x02\x02\x10\x02\"\x02$\x02\x00\x03\x02\x03\x10\x03\x11\x03\x00\x00\x00\x00\x01\x00&\x00\x00\x00\x00\x00\x01\x00 \x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x03\x00\n\x00\x00\x00\x00\x00\x01\x00\x00\x00\x01\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00"
	conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
	conn.Write([]byte(payload))

	tmp := make([]byte, 4)
	temp_data, err := conn.Read(tmp)
	if err != nil {
		return nil, err
	}
	length, err := BytesToInt(tmp[:temp_data])
	if err != nil {
		return nil, err
	}

	buffer := make([]byte, length)
	buffer_data, err := conn.Read(buffer)
	if err != nil {
		return nil, err
	}
	rece_data := buffer[:buffer_data]
	num1 := bytes.Compare(rece_data[68:70], []byte("\x11\x03"))
	num2 := bytes.Compare(rece_data[70:72], []byte("\x02\x00"))

	if num1 != 0 {
		return &invulnerableResult, nil
	}
	if num2 != 0 {
		return &invulnerableResult, nil
	} else {
		return &ScriptScanResult{Vulnerable: true, Output: addr}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("smb_cve_2020_0796.xml", CheckSmbCve20200796)
}
