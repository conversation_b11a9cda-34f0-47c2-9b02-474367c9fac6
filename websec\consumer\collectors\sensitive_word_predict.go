package collectors

import (
	"bytes"
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strings"
	"sync"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/semaphore"
)

var (
	defaultDelaySecond = 5
	maxDelaySecond     = 60
	maxRetryTimes      = 200
)

type predictRequest struct {
	Pages []predictPage `json:"pages"`
}

type predictPage struct {
	Sid           string `json:"sid"`
	URL           string `json:"url"`
	Typ           int    `json:"type"`
	URLSourcecode string `json:"url_sourcecode"`
}

type predictResponse struct {
	Code   int             `json:"code"`
	Status string          `json:"status"`
	Msg    string          `json:"msg"`
	Result []predictResult `json:"result"`
}

type predictResult struct {
	Sid        string `json:"sid"`
	URL        string `json:"url"`
	Prediction int    `json:"prediction"`
	InsertedAt string `json:"inserted_at"`
	Pid        int    `json:"pid"`
}

type SensitiveWordPredict struct {
	msgQueue   chan *schema.FinalSensitiveWordResult
	waitGroup  sync.WaitGroup
	collector  *Collector
	apiAddress string
	weighted   *semaphore.Weighted
	runFlag    bool
}

func NewSensitiveWordPredict(collector *Collector, addr string) *SensitiveWordPredict {
	return &SensitiveWordPredict{
		msgQueue:   make(chan *schema.FinalSensitiveWordResult, 10),
		collector:  collector,
		apiAddress: addr,
		weighted:   semaphore.NewWeighted(1),
	}
}

func (s *SensitiveWordPredict) Add(result *schema.FinalSensitiveWordResult) {
	s.msgQueue <- result
}

func (s *SensitiveWordPredict) Go() {
	s.runFlag = true
	s.waitGroup.Add(1)
	go s.start()
}

func (s *SensitiveWordPredict) start() {
	defer s.waitGroup.Done()
	for msg := range s.msgQueue {
		s.weighted.Acquire(context.Background(), 1)
		go s.predict(msg)
	}
}

func (s *SensitiveWordPredict) Close() {
	if s.runFlag {
		close(s.msgQueue)
		s.waitGroup.Wait()
		s.runFlag = false
	}
}

func (s *SensitiveWordPredict) predict(msg *schema.FinalSensitiveWordResult) {
	defer s.weighted.Release(1)

	for i := 1; i < maxRetryTimes; i++ {
		data, err := s.collector.DBConnection().GetHBaseContent(msg.JobID, msg.URL, msg.URLHash, msg.VersionTime)
		if err != nil {
			log.Errorln(err)
			if defaultDelaySecond*i > maxDelaySecond {
				time.Sleep(time.Duration(maxDelaySecond) * time.Second)
			} else {
				time.Sleep(time.Duration(defaultDelaySecond*1) * time.Second)
			}
			continue
		}

		id, err := s.collector.saveToMongo(consts.CollectionFoundSensitiveWords, msg)
		if err != nil {
			log.Errorln(err)
			return
		}

		page := predictPage{
			URL:           msg.URL,
			URLSourcecode: string(data),
			Sid:           id.Hex(),
			Typ:           2,
		}
		payLoad := &predictRequest{Pages: []predictPage{page}}

		var ret bool
		for j := 0; j < 3; j++ {
			ret, err = s.httpPost(payLoad)
			if err == nil {
				break
			}
			log.Errorln(err)
			time.Sleep(2 * time.Second)
		}

		if ret {
			msg.ID = id
			s.collector.producer.Produce(consts.TopicSensitiveWordResults, msg)
		}
	}
}

func (s *SensitiveWordPredict) httpPost(request *predictRequest) (bool, error) {
	buffer := new(bytes.Buffer)
	encoder := json.NewEncoder(buffer)
	encoder.SetEscapeHTML(false)
	err := encoder.Encode(request)
	if err != nil {
		return false, err
	}

	resp, err := http.Post(s.apiAddress, "application/json", strings.NewReader(buffer.String()))
	if err != nil {
		return false, err
	}

	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return false, err
	}

	res := new(predictResponse)
	err = json.Unmarshal(body, res)
	if err != nil {
		return false, err
	}

	log.Debugln("predict response", res)
	if res.Code == 0 || len(res.Result) == 0 {
		return false, nil
	}

	return true, nil
}
