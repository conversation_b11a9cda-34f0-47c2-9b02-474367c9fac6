package main

import (
	"fmt"
	"io"
	"net/http"
	"websec/crawl"
	"websec/scan"

	"github.com/gorilla/mux"
)

func setupHTTPServer(worker *Worker) {
	r := mux.NewRouter()
	r.<PERSON>("/tasks/{host}", showTasks(worker))
	r.<PERSON>("/tasks/{host}/{taskid}", showTasks(worker))
	http.Handle("/", r)
}

type HTTPHandler func(http.ResponseWriter, *http.Request)

func showTasks(worker *Worker) HTTPHandler {
	return func(w http.ResponseWriter, r *http.Request) {
		vars := mux.Vars(r)
		host := vars["host"]

		io.WriteString(w, "Current Tasks: \n")
		io.WriteString(w, "==================\n")
		worker.tasks.Range(func(key interface{}, value interface{}) bool {
			_, err := fmt.Fprintln(w, key)
			return err == nil
		})
		io.WriteString(w, "====\n")

		if host != "" {
			taskID := vars["taskid"]
			processor := worker.manager.get(host)
			io.WriteString(w, "Host: "+host+"\n")
			io.WriteString(w, "==================\n")
			if processor == nil {
				io.WriteString(w, "Processor not found. ")
			} else {
				if taskID == "" {
					if c, ok := processor.crawlerMap.Load(taskID); ok {
						crawler := c.(crawl.Crawler)
						io.WriteString(w, crawler.GetOffset())
					}
					if s, ok := processor.crawlerMap.Load(taskID); ok {
						scanner := s.(scan.Scanner)

						io.WriteString(w, fmt.Sprintf("%v", scanner.IsRunning()))
					}
				}
			}
		}
	}
}
