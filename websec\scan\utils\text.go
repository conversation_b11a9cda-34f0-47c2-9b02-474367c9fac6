package utils

import (
	"errors"
	"regexp"
	"strconv"
	"strings"
	"unicode"
	"unicode/utf8"

	"golang.org/x/net/html/charset"
	"golang.org/x/text/encoding"
	"golang.org/x/text/transform"
)

var charsetPattern = regexp.MustCompile(`(?i)<meta[^>]+charset\s*=\s*["]{0,1}([a-z0-9-]*)`)

func detectHTMLCharset(body []byte) string {
	if len(body) > 1024 {
		body = body[:1024]
	}
	match := charsetPattern.FindSubmatch(body)
	if match == nil {
		return ""
	}
	return string(match[1])
}

func ForceHTMLUtf8(body []byte, contentType string) ([]byte, string) {
	htmlCharset := detectHTMLCharset(body)
	if htmlCharset == "" {
		_, htmlCharset, _ = charset.DetermineEncoding(body, contentType)
	}
	return ForceUtf8(body, htmlCharset)
}

func ForceUtf8(body []byte, charsetName string) ([]byte, string) {

	if charsetName == "utf-8" && utf8.Valid(body) {
		newBody := make([]byte, len(body))
		copy(newBody, body)
		return newBody, charsetName
	}
	if charsetName == "windows-1252" {
		charsetName = "gb2312"
	}

	var decoder *encoding.Decoder
	for _, encName := range []string{
		charsetName,
		"gb18030",
		"big5",
		"utf8",
	} {
		enc, canonicalName := charset.Lookup(encName)
		if enc == nil {
			continue
		}
		decoder = enc.NewDecoder()
		result, _, err := transform.Bytes(decoder, body)
		if err != nil {
			continue
		}

		return result, canonicalName
	}

	return body, ""
}

func RemoveInvalidUtf8(s string) string {
	if !utf8.ValidString(s) {
		v := make([]rune, 0, len(s))
		for i, r := range s {
			if r == utf8.RuneError {
				_, size := utf8.DecodeRuneInString(s[i:])
				if size == 1 {
					continue
				}
			}
			v = append(v, r)
		}
		return string(v)
	}
	return s
}

func RemoveNonLetterNumber(s string) string {
	return strings.Map(func(r rune) rune {
		if unicode.IsLetter(r) || unicode.IsNumber(r) {
			return r
		}
		return ' '
	}, s)
}

func IsAlphaNumber(s string) bool {
	for _, r := range s {
		if uint32(r) > unicode.MaxASCII || (!unicode.IsLetter(r) && !unicode.IsNumber(r)) {
			return false
		}
	}
	return true
}

func VersionCompare(ver1 string, ver2 string) (int, error) {
	parts_ver1 := strings.Split(ver1, ".")
	parts_ver2 := strings.Split(ver2, ".")
	if len(parts_ver1) != len(parts_ver2) {
		return 0, errors.New("two version have different parts length")
	}
	for i := range parts_ver1 {
		part_ver1, err1 := strconv.Atoi(parts_ver1[i])
		part_ver2, err2 := strconv.Atoi(parts_ver2[i])
		if err1 != nil {
			return -2, err1
		}
		if err2 != nil {
			return -2, err2
		}
		if part_ver1 == part_ver2 {
			continue
		} else {
			return part_ver1 - part_ver2, nil
		}
	}
	return 0, nil
}
