package main

import (
	"context"
	"fmt"
	"sync"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/config"
	"websec/utils"
	"websec/utils/log"
	"websec/utils/semaphore"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func main() {
	settings, err := config.ParseConfig()
	if err != nil {
		log.Errorln(err)
		return
	}

	dbConnection, err := db.NewDBConnection(
		db.WithRedisConfig(settings.Redis),
		db.WithMongoConfig(settings.MongoDB),
	)
	if err != nil {
		log.Errorln(err)
		return
	}

	err = dbConnection.ScriptInit()
	if err != nil {
		log.Errorln(err)
		return
	}

	var startID primitive.ObjectID
	var wg sync.WaitGroup
	seMa := semaphore.NewWeighted(20)

	for {
		cursor, err := dbConnection.GetMongoDatabase().Collection(consts.CollectionURLs).Find(context.Background(),
			bson.M{"_id": bson.M{"$gt": startID}},
			options.Find().SetSort(bson.M{"_id": 1}).SetLimit(1000))
		if err != nil {
			log.Errorln(err)
			break
		}

		result := make([]*schema.SiteURL, 0, 1000)
		for cursor.Next(context.Background()) {
			doc := new(schema.SiteURL)
			err = cursor.Decode(doc)
			if err != nil {
				log.Errorln(err)
				continue
			}
			result = append(result, doc)
			startID = doc.ID
		}

		cursor.Close(context.Background())

		for _, v := range result {
			wg.Add(1)
			if err = seMa.Acquire(context.Background(), 1); err == nil {
				go func(url *schema.SiteURL) {
					defer func() {
						wg.Done()
						seMa.Release(1)
					}()
					domain := utils.GetLargeDomain(url.Host)
					dbConnection.BloomFilter(fmt.Sprintf("%s{%s}", domain, domain), consts.GetHttpMethod(url.Methods)+url.String(), 100000, 0.00001)
				}(v)
			}
		}

		log.Infoln("count:", len(result))
		if len(result) < 1000 {
			break
		}
	}
	wg.Wait()
}
