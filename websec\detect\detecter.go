package detect

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"unicode/utf8"
	"unsafe"
	"websec/common"
	"websec/utils"
	"websec/utils/acsmx"
	"websec/utils/log"
	"websec/utils/ocr"
	"websec/utils/tuchuang"

	"github.com/PuerkitoBio/goquery"
	"github.com/valyala/fasthttp"
	"golang.org/x/net/html"
	"golang.org/x/net/publicsuffix"
)

type WSDetecter struct {
	Options *Options

	ocrClient   *fasthttp.Client
	ocrv1Client *ocr.OCRClientV1

	rootDomain           string
	phoneDisclosureCount int32
	ipDisclosureCount    int32
	emailDisclosureCount int32
	unsafeFormCount      int32
	detectErrorCount     int64
	detectPageCount      int64
	errorReasons         string
}

var defaultConCurrency int64 = 10

func (detecter *WSDetecter) Detect(page *common.Webpage) (*DetectResult, error) {
	result := &DetectResult{}
	detecter.detectPageCount++

	contentType := page.Headers.Get("Content-Type")
	isScript := strings.HasPrefix(contentType, "application/")
	isText := strings.HasPrefix(contentType, "text/")
	if !(isScript || isText) {
		return nil, fmt.Errorf("neither text/* nor application/*: %s", page.URL)
	}

	if detecter.Options.Operation&CheckBlackWords != 0 && isText {
		log.Infoln("going to detect black links", page.URL)
		result.BlackLinks = detecter.detectBlackLinks(page)
	}
	if detecter.Options.Operation&CheckSensitiveWords != 0 && isText {
		result.SensitiveWords = detecter.detectSensitiveWords(page)

		log.Infoln("sensitivewords url", page.URL)
		OcrSensitiveWords := detecter.DetectOCRSensitivewordsAsSW(page)
		if len(OcrSensitiveWords) > 0 {
			result.SensitiveWords = append(result.SensitiveWords, OcrSensitiveWords...)
		}
	}

	if page.Depth < 2 {
		if detecter.Options.Operation&CheckTrojan != 0 && (isText || isScript) {
			log.Infoln("going to detect trojan", page.URL)

			if isText {
				result.Trojans = detecter.detectTrojan(page, FileText)
			} else {
				result.Trojans = detecter.detectTrojan(page, FileScript)
			}

			log.Infoln(page.URL, "found trojans number:", len(result.Trojans))
			log.Infoln("end detect trojan", page.URL)
		}
		if detecter.Options.Operation&CheckPrivateInfo != 0 && isText {
			result.FoundVuls = append(result.FoundVuls, detecter.detectPrivateInfos(page)...)
		}
		if detecter.Options.Operation&CheckUnsafeForms != 0 && isText {
			result.FoundVuls = append(result.FoundVuls, detecter.detectUnsafeForms(page)...)

		}
		if detecter.Options.Operation&CheckDirectoryList != 0 && isText {
			result.FoundVuls = append(result.FoundVuls, detecter.detectDirectoryList(page)...)
		}
		if detecter.Options.Operation&CheckOceanLotus != 0 && isText {
			result.FoundVuls = append(result.FoundVuls, detecter.detectOceanLotus(page)...)
		}
		if detecter.Options.Operation&CheckBTC != 0 && (isText || isScript) {
			result.FoundVuls = append(result.FoundVuls, detecter.detectBTC(page)...)
		}
		if detecter.Options.Operation&CheckJenkins != 0 && isText {
			result.FoundVuls = append(result.FoundVuls, detecter.detectJenkins(page)...)
		}
	}

	return result, nil
}

func (detecter *WSDetecter) DetectV1(page *common.Webpage) (*DetectResult, error) {
	result := &DetectResult{}
	var mu sync.Mutex
	var wg sync.WaitGroup

	log.Infoln("DetectV1 started for page:", page.URL)
	detecter.detectPageCount++

	contentType := page.Headers.Get("Content-Type")
	isScript := strings.HasPrefix(contentType, "application/")
	isText := strings.HasPrefix(contentType, "text/")
	if !(isScript || isText) {
		return nil, fmt.Errorf("neither text/* nor application/*: %s", page.URL)
	}

	if detecter.Options.Operation&CheckBlackWords != 0 && isText {
		wg.Add(1)
		go func() {
			defer wg.Done()
			log.Infoln("Starting black link detection for", page.URL)
			blackLinks := detecter.detectBlackLinks(page)
			if len(blackLinks) > 0 {
				mu.Lock()
				result.BlackLinks = append(result.BlackLinks, blackLinks...)
				mu.Unlock()
			}
			log.Infoln("Finished black link detection for", page.URL)
		}()
	}

	if detecter.Options.Operation&CheckSensitiveWords != 0 && isText {
		wg.Add(1)
		go func() {
			defer wg.Done()
			log.Infoln("Starting sensitive word detection (text) for", page.URL)
			sensitiveWords := detecter.detectSensitiveWords(page)
			if len(sensitiveWords) > 0 {
				mu.Lock()
				result.SensitiveWords = append(result.SensitiveWords, sensitiveWords...)
				mu.Unlock()
			}
			log.Infoln("Finished sensitive word detection (text) for", page.URL)
		}()

		// Goroutine for Sensitive Word Detection (OCR on Images)
		// Processes images concurrently within this goroutine.
		if len(page.Images) > 0 {
			wg.Add(1)
			go func() {
				defer wg.Done()
				log.Infoln("Starting sensitive word detection (OCR) for", page.URL)

				// WaitGroup and Mutex for concurrent image processing
				var imageWg sync.WaitGroup
				var imageMu sync.Mutex
				allOcrWords := []WordResult{}

				// Semaphore to limit concurrent image processing tasks
				const maxConcurrentImageTasks = 8 // Limit concurrent image processing
				imageTaskSema := make(chan struct{}, maxConcurrentImageTasks)

				log.Infoln("DetectV1: Processing", len(page.Images), "images via OCR for page:", page.URL)

				for _, imageURL := range page.Images {
					imageWg.Add(1) // Increment counter for each image goroutine
					// Launch a sub-goroutine for each image
					go func(imgURL string) {
						defer imageWg.Done()               // Decrement counter when image goroutine finishes
						imageTaskSema <- struct{}{}        // Acquire a slot from the semaphore
						defer func() { <-imageTaskSema }() // Release the slot

						log.Infoln("DetectV1: Starting OCR for image:", imgURL, "on page:", page.URL)

						imageData, err := detecter.downloadImage(imgURL)
						if err != nil {
							log.Errorf("DetectV1: download image error for %s on page %s: %v", imgURL, page.URL, err)
							return // Exit sub-goroutine on download error
						}

						// Limit logging if imageData is very large? Consider logging size instead of full data.
						// log.Infoln("------------ ocr image url", imgURL, "imageData", len(imageData))

						ocrResult, err := ocr.DoOCRV1(imageData)
						if err != nil {
							log.Errorf("DetectV1: Failed to do ocr for %s on page %s: %v", imgURL, page.URL, err)
							return // Exit sub-goroutine on OCR error
						}

						var content string
						for _, word := range ocrResult.Ret {
							content += word.Word + "\n"
						}

						if len(content) == 0 {
							return // No text found in OCR result
						}

						matches := detecter.searchWordsNotJieba(detecter.Options.SensitiveMatcher, []byte(content))
						if len(matches) > 0 {
							ocrSensitiveWords := make([]WordResult, len(matches))
							for k := range matches {
								ocrSensitiveWords[k].Word = matches[k].Word
								ocrSensitiveWords[k].Position = matches[k].Position
								ocrSensitiveWords[k].Context = string(matches[k].Context)
							}

							// Append results found in this image to the shared slice, protected by imageMu
							imageMu.Lock()
							allOcrWords = append(allOcrWords, ocrSensitiveWords...)
							imageMu.Unlock()
						}
						log.Infoln("DetectV1: Finished OCR for image:", imgURL, "on page:", page.URL)
					}(imageURL) // Pass imageURL to the goroutine
				} // End loop launching image goroutines

				// Wait for all image processing goroutines to complete
				imageWg.Wait()

				// If any sensitive words were found across all images, add them to the main result
				if len(allOcrWords) > 0 {
					sw := &SensitiveWordResult{
						URL:            page.URL,
						URLHash:        page.URLHash(),
						FromImage:      true,
						IsOuterURL:     detecter.isOuterURL(page.EffectiveURL),
						SensitiveWords: allOcrWords, // Assign collected words
					}
					// Lock the main result mutex before appending
					mu.Lock()
					result.SensitiveWords = append(result.SensitiveWords, sw)
					mu.Unlock()
				}
				log.Infoln("Finished sensitive word detection (OCR) for", page.URL)
			}()
		}
	}

	if page.Depth < 2 {
		if detecter.Options.Operation&CheckTrojan != 0 && (isText || isScript) {
			wg.Add(1)
			go func() {
				defer wg.Done()
				log.Infoln("Starting trojan detection for", page.URL)
				var trojans []*TrojanResult
				if isText {
					trojans = detecter.detectTrojan(page, FileText)
				} else {
					trojans = detecter.detectTrojan(page, FileScript)
				}
				if len(trojans) > 0 {
					mu.Lock()
					result.Trojans = append(result.Trojans, trojans...)
					mu.Unlock()
					log.Infoln(page.URL, "found trojans number:", len(trojans))
				}
				log.Infoln("Finished trojan detection for", page.URL)
			}()
		}
		if detecter.Options.Operation&CheckPrivateInfo != 0 && isText {
			wg.Add(1)
			go func() {
				defer wg.Done()
				log.Infoln("Starting private info detection for", page.URL)
				vuls := detecter.detectPrivateInfos(page)
				if len(vuls) > 0 {
					mu.Lock()
					result.FoundVuls = append(result.FoundVuls, vuls...)
					mu.Unlock()
				}
				log.Infoln("Finished private info detection for", page.URL)
			}()
		}
		if detecter.Options.Operation&CheckUnsafeForms != 0 && isText {
			wg.Add(1)
			go func() {
				defer wg.Done()
				log.Infoln("Starting unsafe forms detection for", page.URL)
				vuls := detecter.detectUnsafeForms(page)
				if len(vuls) > 0 {
					mu.Lock()
					result.FoundVuls = append(result.FoundVuls, vuls...)
					mu.Unlock()
				}
				log.Infoln("Finished unsafe forms detection for", page.URL)
			}()
		}
		if detecter.Options.Operation&CheckDirectoryList != 0 && isText {
			wg.Add(1)
			go func() {
				defer wg.Done()
				log.Infoln("Starting directory list detection for", page.URL)
				vuls := detecter.detectDirectoryList(page)
				if len(vuls) > 0 {
					mu.Lock()
					result.FoundVuls = append(result.FoundVuls, vuls...)
					mu.Unlock()
				}
				log.Infoln("Finished directory list detection for", page.URL)
			}()
		}
		if detecter.Options.Operation&CheckOceanLotus != 0 && isText {
			wg.Add(1)
			go func() {
				defer wg.Done()
				log.Infoln("Starting OceanLotus detection for", page.URL)
				vuls := detecter.detectOceanLotus(page)
				if len(vuls) > 0 {
					mu.Lock()
					result.FoundVuls = append(result.FoundVuls, vuls...)
					mu.Unlock()
				}
				log.Infoln("Finished OceanLotus detection for", page.URL)
			}()
		}
		if detecter.Options.Operation&CheckBTC != 0 && (isText || isScript) {
			wg.Add(1)
			go func() {
				defer wg.Done()
				log.Infoln("Starting BTC detection for", page.URL)
				vuls := detecter.detectBTC(page)
				if len(vuls) > 0 {
					mu.Lock()
					result.FoundVuls = append(result.FoundVuls, vuls...)
					mu.Unlock()
				}
				log.Infoln("Finished BTC detection for", page.URL)
			}()
		}
		if detecter.Options.Operation&CheckJenkins != 0 && isText {
			wg.Add(1)
			go func() {
				defer wg.Done()
				log.Infoln("Starting Jenkins detection for", page.URL)
				vuls := detecter.detectJenkins(page)
				if len(vuls) > 0 {
					mu.Lock()
					result.FoundVuls = append(result.FoundVuls, vuls...)
					mu.Unlock()
				}
				log.Infoln("Finished Jenkins detection for", page.URL)
			}()
		}
	}

	wg.Wait()

	log.Infoln("All detections finished for", page.URL)
	return result, nil
}

func (detecter *WSDetecter) detectOCRSensitivewords(page *common.Webpage) []*OcrSensitiveWordResult {
	results := []*OcrSensitiveWordResult{}

	if len(page.Images) > 0 {
		ocrResults := []*OcrResult{}
		for j := range page.Images {
			imageURL := page.Images[j]
			resp, err := detecter.doOcrRequest(imageURL)
			if err != nil {
				log.Errorln("doOcrRequest error,", page.URL, imageURL, err)
				continue
			}

			if resp.Status != common.SUCCESS {
				log.Errorln("doOcrRequest failed,", page.URL, imageURL, resp.Message)
			}

			if len(resp.Content) == 0 {
				continue
			}

			matches := detecter.searchWordsAndContext(detecter.Options.SensitiveMatcher, []byte(resp.Content))
			_, fghkWords := detecter.Options.FghkOcrMatcher.SearchAll([]byte(resp.Content))
			if len(fghkWords) > 0 {
				matches = append(matches, detecter.genContextOfMatchedWords(fghkWords, []byte(resp.Content))...)
			}

			sensitiveWords := make([]WordResult, len(matches))
			for k := range matches {
				sensitiveWords[k].Word = matches[k].Word
				sensitiveWords[k].Position = matches[k].Position
				sensitiveWords[k].Context = string(matches[k].Context)
			}

			if len(sensitiveWords) == 0 {
				continue
			}

			imageAddress := resp.ImageAddress
			if resp.IsNew {

				imageData, err := base64.StdEncoding.DecodeString(resp.ImageData)
				if err == nil {
					u, err := tuchuang.UploadImage(imageData)
					if err == nil {
						imageAddress = u
					}
				}
			}

			result := &OcrResult{
				ImageURL:       imageURL,
				ImageURLHash:   resp.ImageURLHash,
				Content:        resp.Content,
				SensitiveWords: sensitiveWords,
				IsNew:          resp.IsNew,
				ImageAddress:   imageAddress,
			}
			ocrResults = append(ocrResults, result)
		}
		results = append(results, &OcrSensitiveWordResult{
			URL:         page.URL,
			Results:     ocrResults,
			ContentHash: page.ContentHash(),
		})
	}
	return results
}

func (detecter *WSDetecter) DetectOCRSensitivewordsAsSW(page *common.Webpage) []*SensitiveWordResult {
	results := []*SensitiveWordResult{}

	if len(page.Images) > 0 {
		sw := &SensitiveWordResult{
			URL:        page.URL,
			URLHash:    page.URLHash(),
			FromImage:  true,
			IsOuterURL: detecter.isOuterURL(page.EffectiveURL),
		}

		for j := range page.Images {

			imageURL := page.Images[j]

			resp, err := detecter.doOcrRequest(imageURL)
			if err != nil {
				log.Errorln("doOcrRequest error,", page.URL, imageURL, err)
				continue
			}

			if resp.Status != common.SUCCESS {
				log.Errorln("doOcrRequest failed,", page.URL, imageURL, resp.Message)
			}

			log.Infoln("detect orc url", imageURL, resp.Content, detecter.Options.SensitiveMatcher)
			if len(resp.Content) == 0 {
				log.Infoln("no content", imageURL)
				continue
			}

			matches := detecter.searchWordsNotJieba(detecter.Options.SensitiveMatcher, []byte(resp.Content))

			if len(resp.Content) != 0 && len(matches) == 0 {
				newWords := []string{"弹轮", "德国586", "毒品", "唐服", "失意药", "冰毒价格", "火星"}

				log.Infoln(strings.Index(resp.Content, "弹轮"), detecter.searchWordsNotJieba(detecter.Options.SensitiveMatcher,
					[]byte(strings.Join(newWords, ","))))

			}
			log.Infoln(imageURL, matches)
			sensitiveWords := make([]WordResult, len(matches))
			for k := range matches {
				sensitiveWords[k].Word = matches[k].Word
				sensitiveWords[k].Position = matches[k].Position
				sensitiveWords[k].Context = string(matches[k].Context)
			}

			if len(sensitiveWords) == 0 {
				continue
			}

			sw.SensitiveWords = append(sw.SensitiveWords, sensitiveWords...)

		}
		if len(sw.SensitiveWords) > 0 {
			results = append(results, sw)
		}
	}
	return results
}

func (detecter *WSDetecter) doOcrRequest(u string) (*common.OCRResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	err := detecter.Options.OcrSema.Acquire(ctx, 1)
	if err != nil {
		return nil, err
	}
	defer detecter.Options.OcrSema.Release(1)

	request := common.OCRRequest{
		ImgURL: u,
	}
	requestData, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	httpRequest := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(httpRequest)
	httpResponse := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(httpResponse)

	header := &httpRequest.Header
	header.SetMethod(http.MethodPost)
	header.SetContentType("application/json")
	httpRequest.SetBody(requestData)
	httpRequest.SetRequestURI(detecter.Options.OcrAPIAddress)

	err = detecter.ocrClient.DoTimeout(httpRequest, httpResponse, 60*time.Second)
	if err != nil {
		return nil, err
	}

	body := httpResponse.Body()
	if len(body) == 0 {
		return nil, errors.New("nil ocr response")
	}

	var resp common.OCRResponse
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

func (detecter *WSDetecter) detectFailedPages(page *common.Webpage) []FailedPageResult {
	linkResult := []FailedPageResult{}
	log.Infoln("the page", page.URL, "statuscode is", page.StatusCode)
	if page.StatusCode >= 400 || page.StatusCode == 0 {
		failedPage := FailedPageResult{
			Referer:    page.Referer,
			StatusCode: page.StatusCode,
			URL:        page.URL,
		}
		linkResult = append(linkResult, failedPage)
	}

	referer := page.URL
	for i := range page.NetworkWebpages {
		np := &page.NetworkWebpages[i]
		log.Infoln("the page", np.URL, "statuscode is", np.StatusCode)
		if np.StatusCode >= 400 || page.StatusCode == 0 {
			failedPage := FailedPageResult{
				Referer:    referer,
				StatusCode: np.StatusCode,
				URL:        np.URL,
			}
			linkResult = append(linkResult, failedPage)
		}
	}

	failedPagesMap := make(map[string]int)

	for j := range linkResult {
		failedPagesMap[linkResult[j].URL] = 1
	}

	log.Infoln("the page", page.URL, "statuscode is", page.StatusCode)
	if page.StatusCode >= 400 || page.StatusCode == 0 {
		if _, ok := failedPagesMap[page.URL]; !ok {
			failedPage := FailedPageResult{
				Referer:    referer,
				StatusCode: page.StatusCode,
				URL:        page.URL,
			}
			linkResult = append(linkResult, failedPage)
			failedPagesMap[page.URL] = 1
		}
	}

	for j := range page.OuterWebpages {
		op := page.OuterWebpages[j]
		log.Infoln("the page", op.URL, "statuscode is", op.StatusCode)
		if op.StatusCode >= 400 || page.StatusCode == 0 {
			if _, ok := failedPagesMap[op.URL]; !ok {
				failedPage := FailedPageResult{
					Referer:    referer,
					StatusCode: op.StatusCode,
					URL:        op.URL,
				}
				linkResult = append(linkResult, failedPage)
				failedPagesMap[op.URL] = 1
			}
		} // end if op
	} // end for  j := range frame.OuterWebpages

	return linkResult
}

func (detecter *WSDetecter) detectBlackLinksBy3Party(domainSet []string) []string {
	d := detecter.Options.WangdunBlackLinkDetecter
	if d == nil {
		return []string{}
	}
	return d.Detect(domainSet)
}

func getTLDPlusOneOfURL(rawURL string) (string, error) {
	u, err := url.Parse(rawURL)
	if err != nil {
		return "", err
	}
	return publicsuffix.EffectiveTLDPlusOne(u.Hostname())
}

func (detecter *WSDetecter) detectBlackLinks(page *common.Webpage) []*BlackLinkResult {
	if page.GQDocument == nil {
		node, err := html.Parse(bytes.NewReader(page.Content))
		if err != nil {
			log.Errorln("failed to parse html:", err, page.URL)
		} else {
			page.GQDocument = goquery.NewDocumentFromNode(node)
		}
	}

	outDomainSet := utils.StringSet{}
	result := make([]*BlackLinkResult, 0)
	if page.GQDocument != nil {
		page.GQDocument.Find("a").Each(func(_ int, s *goquery.Selection) {
			href := s.AttrOr("href", "")
			if shallIgnore, _ := detecter.isIgnored(href); shallIgnore {
				return
			}

			if detecter.isOuterURL(href) {
				text := s.Text()
				oldLen := len(result)
				result = append(result, detecter.searchBlackWords(href, []byte(href), false)...)
				result = append(result, detecter.searchBlackWords(href, []byte(text), false)...)
				found := len(result)-oldLen > 0
				if !found {
					outDomainSet.Add(href)
				}
			}
		})
		page.GQDocument.Find("img").Each(func(_ int, s *goquery.Selection) {
			src := s.AttrOr("src", "")
			if shallIgnore, _ := detecter.isIgnored(src); shallIgnore {
				return
			}
			if detecter.isOuterURL(src) {
				alt := s.AttrOr("alt", "")
				oldLen := len(result)
				result = append(result, detecter.searchBlackWords(src, []byte(src), false)...)
				result = append(result, detecter.searchBlackWords(src, []byte(alt), false)...)
				found := len(result)-oldLen > 0
				if !found {
					outDomainSet.Add(src)
				}
			}
		})
		page.GQDocument.Find("script").Each(func(_ int, s *goquery.Selection) {
			src := s.AttrOr("src", "")
			if shallIgnore, _ := detecter.isIgnored(src); shallIgnore {
				return
			}
			if detecter.isOuterURL(src) {
				text := s.Text()
				oldLen := len(result)
				result = append(result, detecter.searchBlackWords(src, []byte(src), false)...)
				result = append(result, detecter.searchBlackWords(src, []byte(text), false)...)
				found := len(result)-oldLen > 0
				if !found {
					outDomainSet.Add(src)
				}
			}
		})
	}

	domainLst := outDomainSet.AsStringArray()
	if len(domainLst) > 0 {
		sickDomains := detecter.detectBlackLinksBy3Party(domainLst)
		if len(sickDomains) > 0 {
			for i := range sickDomains {
				domain := sickDomains[i]
				r := &BlackLinkResult{
					URL:     domain,
					Context: "Found By 360 wangdun",
				}
				result = append(result, r)
			}
		}
	}

	foundBlackLinks := utils.StringSet{}
	for j := range result {
		foundBlackLinks.Add(result[j].URL)
	}

	for _, page := range page.OuterWebpages {
		log.Infoln("detectBlackLinks: going to detect black links outer webpage", page.URL)
		outDomainSet := utils.StringSet{}
		for _, op := range page.Frames {
			if !foundBlackLinks.Contains(op.URL) {
				if shallIgnore, _ := detecter.isIgnored(op.URL); shallIgnore {
					continue
				}
				outerResults := detecter.searchBlackWords(op.URL, op.Content, true)

				if len(outerResults) > 0 {
					log.Infoln("found black links in: ", op.URL)
				} else {
					outDomainSet.Add(op.URL)
				}

				for l := range outerResults {
					outerResults[l].OutPage = page
					outerResults[l].OutPageFrameURL = page.URL
					outerResults[l].IsOuterURL = true
				}
				result = append(result, outerResults...)
			}
		}

		domainLst := outDomainSet.AsStringArray()
		if len(domainLst) > 0 {
			sickDomains := detecter.detectBlackLinksBy3Party(domainLst)
			if len(sickDomains) > 0 {
				for i := range sickDomains {
					domain := sickDomains[i]
					r := &BlackLinkResult{
						URL:             domain,
						OutPage:         page,
						OutPageFrameURL: page.URL,
						IsOuterURL:      true,
						Context:         "Found By 360 wangdun",
					}
					result = append(result, r)
				}
			}
		}
	}

	return result
}

func (detecter *WSDetecter) isOuterURL(testurl string) bool {
	return isOuterURL(detecter.rootDomain, testurl)
}

func (detecter *WSDetecter) detectSensitiveWords(page *common.Webpage) []*SensitiveWordResult {
	result := make([]*SensitiveWordResult, 0, 1)

	// Determine the maximum scan depth between page.ExternalScanDepth and 3
	var maxScanDepth int32
	if page.ExternalScanDepth < 3 {
		maxScanDepth = 3
	} else {
		maxScanDepth = int32(page.ExternalScanDepth)
	}

	matches := detecter.searchWordsAndContext(detecter.Options.SensitiveMatcher, page.Content)
	matches = append(matches, detecter.searchSensitiveWordGroups(page.Content)...)
	if len(matches) > 0 {
		sw := &SensitiveWordResult{
			URL:            page.URL,
			URLHash:        page.URLHash(),
			IsOuterURL:     detecter.isOuterURL(page.EffectiveURL),
			SensitiveWords: make([]WordResult, len(matches)),
		}

		for j := range matches {
			sw.SensitiveWords[j].Word = matches[j].Word
			sw.SensitiveWords[j].Position = matches[j].Position
			sw.SensitiveWords[j].Context = string(matches[j].Context)
		}

		result = append(result, sw)
	}

	for _, v := range page.NetworkWebpages {
		if detecter.isOuterURL(v.URL) && page.Depth > maxScanDepth {
			continue
		}

		matches := detecter.searchWordsAndContext(detecter.Options.SensitiveMatcher, v.Content)
		if len(matches) == 0 {
			continue
		}
		swr := &SensitiveWordResult{
			URL:            v.URL,
			URLHash:        v.URLHash(),
			SensitiveWords: make([]WordResult, len(matches)),
		}

		for j := range matches {
			swr.SensitiveWords[j].Word = matches[j].Word
			swr.SensitiveWords[j].Position = matches[j].Position
			swr.SensitiveWords[j].Context = string(matches[j].Context)
		}
		result = append(result, swr)
	}

	log.Infoln("page.Depth, page.ExternalScanDepth", page.Depth, page.ExternalScanDepth, maxScanDepth)

	if page.Depth < maxScanDepth {
		for _, op := range page.OuterWebpages {
			for _, v := range op.Frames {
				matches := detecter.searchWordsAndContext(detecter.Options.SensitiveMatcher, v.Content)
				if len(matches) == 0 {
					continue
				}
				swr := &SensitiveWordResult{
					URL:            v.URL,
					URLHash:        v.URLHash(),
					SensitiveWords: make([]WordResult, len(matches)),
					Content:        v.Content,
					ContentHash:    v.ContentHash(),
				}

				for j := range matches {
					swr.SensitiveWords[j].Word = matches[j].Word
					swr.SensitiveWords[j].Position = matches[j].Position
					swr.SensitiveWords[j].Context = string(matches[j].Context)
				}
				result = append(result, swr)
			}
		}
	}

	return result
}

func checkBySafeBrowsing(serverURL string, URL string) ([]*TrojanResult, error) {
	threats, err := SBLookupURLS(serverURL, []string{URL})
	if err != nil {
		return nil, err
	}

	length := len(threats)
	results := make([]*TrojanResult, 0, length)
	if length > 0 {
		for _, threat := range threats {
			results = append(results, &TrojanResult{
				URL:             URL,
				PlatformType:    threat.PlatformType,
				ThreatType:      threat.ThreatType,
				ThreatEntryType: threat.ThreatEntryType,
				Source:          "Google Safe Browsing",
				Confidence:      "high",
			})
		}
	}

	return results, nil
}

func checkByJSUnpack(jsunpackPath string, URL string) ([]*TrojanResult, error) {
	jsunpackResult, err := JSUnpackDetect(jsunpackPath, URL)
	if err != nil {
		return nil, err
	}
	result := make([]*TrojanResult, 0, 1)
	for _, item := range jsunpackResult.Maliciouses {
		result = append(result, &TrojanResult{
			URL:        URL,
			Confidence: "high",
			Evidence:   strings.Join(item.Matches, "\n"),
			Info:       item.Content,
			Source:     "jsunpack",
		})
	}
	for _, item := range jsunpackResult.Suspiciouses {
		result = append(result, &TrojanResult{
			URL:        URL,
			Confidence: "middle",
			Evidence:   strings.Join(item.Matches, "\n"),
			Info:       item.Content,
			Source:     "jsunpack",
		})
	}
	return result, nil
}

func (detecter *WSDetecter) detectTrojan(page *common.Webpage, fileType string) []*TrojanResult {
	result := make([]*TrojanResult, 0)

	trojanDetecter := detecter.Options.TrojanDetecter
	if trojanDetecter != nil {
		fmt.Println("page", page.URL, string(page.Content))
		matchedRules, found := trojanDetecter.Detect(page.Content, fileType)
		if found {
			for _, matchedRule := range matchedRules {
				trojanResult := &TrojanResult{
					URL:          page.URL,
					Confidence:   "high",
					Evidence:     page.URL,
					Info:         matchedRule.Name + ":" + matchedRule.AffectContent,
					Source:       string(page.Content),
					PlatformType: strings.Join(matchedRule.AffectSystem, ","),
				}
				result = append(result, trojanResult)
			}

		}
		for i := range page.NetworkWebpages {

			np := &page.NetworkWebpages[i]

			contentType := np.Headers.Get("Content-Type")
			var curType string
			if strings.HasPrefix(contentType, "application/javascript") {
				curType = FileScript
			} else if strings.HasPrefix(contentType, "text/") {
				curType = FileText
			}

			fmt.Println("np", np.URL, string(np.Content))
			matchedRules, found := trojanDetecter.Detect(np.Content, curType)
			if found {

				for _, matchedRule := range matchedRules {
					trojanResult := &TrojanResult{
						URL:          page.URL,
						Confidence:   "high",
						Evidence:     np.URL,
						Info:         matchedRule.Name + ":" + matchedRule.AffectContent,
						Source:       string(np.Content),
						PlatformType: strings.Join(matchedRule.AffectSystem, ","),
					}
					result = append(result, trojanResult)
				}

				trojanResult := &TrojanResult{}
				result = append(result, trojanResult)
			}
		}

	}

	suspicious := false
	ignored, err := detecter.isIgnored(page.EffectiveURL)
	if err == nil && !ignored {
		if detecter.isOuterURL(page.EffectiveURL) {

			parts, _ := url.Parse(page.EffectiveURL)
			hostname := parts.Hostname()
			regDomain, _ := publicsuffix.EffectiveTLDPlusOne(hostname)
			if !strings.Contains(regDomain, "gov") && !strings.Contains(regDomain, "edu") {

				suspicious = true
			}
		} else {

			if page.GQDocument == nil {
				node, err := html.Parse(bytes.NewReader(page.Content))
				if err != nil {
					log.Errorln("failed to parse html:", err, page.URL)
				} else {
					page.GQDocument = goquery.NewDocumentFromNode(node)
				}
			}
			if page.GQDocument != nil {
				page.GQDocument.Find("script").Each(func(_ int, s *goquery.Selection) {
					lang := strings.ToLower(s.AttrOr("type", s.AttrOr("language", "")))
					if strings.Contains(lang, "vbscript") {
						evidence := s.Text()
						if evidence != "" {

							suspicious = true
						}
					}
				})
			}
		}
	}

	if suspicious {

		checkResults, err := checkBySafeBrowsing(detecter.Options.SafeBrowsingURL, page.URL)
		if err != nil {
			log.Errorln("failed to check by safe browsing API:", page.URL, err)
		}
		if len(checkResults) == 0 {

			checkResults, err = checkByJSUnpack(detecter.Options.JSUnpackPath, page.URL)
			if err != nil {
				log.Errorln("failed to check by jsunpack:", page.URL, err)
			}
		}
		if len(checkResults) > 0 {
			for _, checkResult := range checkResults {
				result = append(result, checkResult)
			}
		}
	}
	return result
}

func (detecter *WSDetecter) isIgnored(u string) (bool, error) {
	parts, err := url.Parse(u)
	if err != nil {
		return true, err
	}
	regDomain, err := publicsuffix.EffectiveTLDPlusOne(parts.Hostname())
	if err != nil {
		return true, err
	}

	/*
		if strings.HasSuffix(regDomain, ".cn") || strings.HasSuffix(regDomain, ".CN") {
			return true, nil
		}
	*/

	if detecter.Options.IgnoredBlackLinkDomains.IsIgnored(regDomain) {
		return true, nil
	}
	return false, nil
}

var (
	phonePattern       = regexp.MustCompile(`\b(?:[0+]\d{2,3}[\s-]?)?(?:1[34578]\d{9})\b`)
	ipPattern          = regexp.MustCompile(`\b(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|[1-9])\.)(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){2}(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\b`)
	emailPattern       = regexp.MustCompile("^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$")
	idCard18Pattern    = regexp.MustCompile(`\b[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]\b`)
	idCardPrefixTwoMap = map[int]bool{
		11: true, 12: true, 13: true, 14: true, 15: true,
		21: true, 22: true, 23: true,
		31: true, 32: true, 33: true, 34: true, 35: true, 36: true, 37: true,
		41: true, 42: true, 43: true, 44: true, 45: true, 46: true,
		50: true, 51: true, 52: true, 53: true, 54: true,
		61: true, 62: true, 63: true, 64: true, 65: true,
		71: true,
		81: true, 82: true,
	}
)

func shallIPIgnore(ip string) bool {
	return len(ip) < 10
}

func isEmailLegal(email string) (bool, error) {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false, errors.New("not illegal email")
	}
	domain := parts[1]
	_, icann := publicsuffix.PublicSuffix(domain)
	return icann, nil
}

func shallEmailIgnore(email string) bool {
	ignoreNamesMap := map[string]struct{}{
		"service":  struct{}{},
		"master":   struct{}{},
		"admin":    struct{}{},
		"sales":    struct{}{},
		"customer": struct{}{},
	}
	parts := strings.Split(email, "@")
	name := parts[0]
	_, in := ignoreNamesMap[name]
	return in
}

func (detecter *WSDetecter) detectPrivateInfos(page *common.Webpage) []*FoundVul {
	result := make([]*FoundVul, 0, 10)
	phones := map[string]bool{}
	ips := map[string]bool{}
	emails := map[string]bool{}
	idCards := map[string]bool{}

	if atomic.LoadInt32(&detecter.phoneDisclosureCount) < 10 {
		for _, match := range phonePattern.FindAllString(string(page.Content), -1) {
			phones[strings.TrimSpace(match)] = true
		}
	}
	if atomic.LoadInt32(&detecter.ipDisclosureCount) < 10 {
		for _, match := range ipPattern.FindAllString(string(page.Content), -1) {
			ip := strings.TrimSpace(match)
			if !shallIPIgnore(ip) {
				ips[ip] = true
			}
		}
	}
	if atomic.LoadInt32(&detecter.emailDisclosureCount) < 10 {
		for _, match := range emailPattern.FindAllString(string(page.Content), -1) {
			email := strings.TrimSpace(match)
			legal, err := isEmailLegal(email)
			if err != nil {
				continue
			}
			if legal {
				if !shallEmailIgnore(email) {
					emails[email] = true
				}
			}
		}
	}

	for _, match := range idCard18Pattern.FindAllString(string(page.Content), -1) {
		id := strings.TrimSpace(match)
		prefixTwoInt, _ := strconv.Atoi(id[0:2])
		if _, ok := idCardPrefixTwoMap[prefixTwoInt]; ok {
			idCards[id] = true
		}
	}

	if len(phones) > 0 {
		atomic.AddInt32(&detecter.phoneDisclosureCount, 1)
		result = append(result, constructPrivateInfoVul(VulPhoneDisclosure, page.URL, phones))
	}
	if len(ips) > 0 {
		atomic.AddInt32(&detecter.ipDisclosureCount, 1)
		result = append(result, constructPrivateInfoVul(VulIpDisclosure, page.URL, ips))
	}
	if len(emails) > 0 {
		atomic.AddInt32(&detecter.emailDisclosureCount, 1)
		result = append(result, constructPrivateInfoVul(VulEmailDisclosure, page.URL, emails))
	}
	if len(idCards) > 0 {
		result = append(result, constructPrivateInfoVul(VulIdCardDisclosure, page.URL, idCards))
	}

	return result
}

var jenkinsVersion = regexp.MustCompile(`Jenkins ver. ([0-9.]+)`)

const jenkinsVulXML = "CVE-2018-1999001-1999002.xml"

func (detecter *WSDetecter) detectJenkins(page *common.Webpage) []*FoundVul {
	result := []*FoundVul{}
	versions := map[string]bool{}
	match := jenkinsVersion.FindSubmatch(page.Content)
	if len(match) < 1 {
		return result
	}
	version := string(match[1])
	parts := strings.Split(version, ".")
	affected := false
	if len(parts) == 2 {
		firstVersion, _ := strconv.Atoi(parts[0])
		secVersion, _ := strconv.Atoi(parts[1])
		if firstVersion*1000+secVersion <= 2132 {
			affected = true
		}
	} else if len(parts) == 3 {
		firstVersion, _ := strconv.Atoi(parts[0])
		secVersion, _ := strconv.Atoi(parts[1])
		thirdVersion, _ := strconv.Atoi(parts[2])
		if firstVersion*10000+secVersion*10+thirdVersion <= 21211 {
			affected = true
		}
	}

	if affected {
		versions[version] = true
	}

	if len(versions) > 0 {
		result = append(result, &FoundVul{
			Affect:   "text",
			VulXML:   jenkinsVulXML,
			VulURL:   page.URL + "|" + strings.Join(stringSetToArray(versions), ","),
			Severity: "high",
		})
	}
	return result
}

func stringSetToArray(infos map[string]bool) []string {
	keys := make([]string, 0, len(infos))
	for key := range infos {
		keys = append(keys, key)
	}
	return keys
}

func constructPrivateInfoVul(VulXML string, pageURL string, infos map[string]bool) *FoundVul {
	return &FoundVul{
		Affect:   "text",
		VulXML:   VulXML,
		VulURL:   pageURL + "|" + strings.Join(stringSetToArray(infos), ","),
		Severity: "low",
	}
}

var formPattern = regexp.MustCompile(`(?is)<form.[^>]*password.[^>]*>(.*?)</form>|<form.[^>]*login.[^>]*>(.*?)</form>|<form.[^>]*logon.[^>]*>(.*?)</form>`)

func (detecter *WSDetecter) detectUnsafeForms(page *common.Webpage) []*FoundVul {
	if atomic.LoadInt32(&detecter.unsafeFormCount) < 10 && !strings.HasPrefix(page.EffectiveURL, "https://") {
		if formPattern.Match(page.Content) {
			atomic.AddInt32(&detecter.unsafeFormCount, 1)
			return []*FoundVul{
				&FoundVul{
					Affect:   "text",
					VulURL:   page.URL,
					VulXML:   VulUnsafeForms,
					Severity: "info",
				}}
		}
	}
	return nil
}

func (detecter *WSDetecter) detectCSRF(page *common.Webpage) []*FoundVul {
	log.Info("detect csrf page", page.URL)
	reader := bytes.NewReader(page.Content)
	doc, err := goquery.NewDocumentFromReader(reader)
	if err != nil {
		return nil
	}

	foundCSRFToken := true

	doc.Find("form").Each(func(i int, s *goquery.Selection) {
		existsToken := false
		s.Find("input[type='hidden']").Each(func(i int, s *goquery.Selection) {
			inputName := s.AttrOr("name", "")
			if strings.Contains(inputName, "token") || strings.Contains(inputName, "csrf") ||
				strings.Contains(inputName, "__VIEWSTATE") {
				existsToken = true
				return
			}
		})

		if !existsToken {
			foundCSRFToken = false
			return
		}
	})

	if !foundCSRFToken {
		return []*FoundVul{
			&FoundVul{
				Affect:   "text",
				VulURL:   page.URL,
				VulXML:   VulCsrfDisclosure,
				Severity: "low",
			}}
	}

	return nil
}

var (
	apacheDirectoryPattern        = regexp.MustCompile(`(?is)<TITLE>Index of`)
	iisDirectoryPattern           = regexp.MustCompile(`(?is)<title>.*? - .*?</title></head><body><H1>`)
	tomcatDirectoryPattern        = regexp.MustCompile(`(?is)<body><h1>Directory\sListing\sFor\s.*</h1>`)
	tomcatExampleDirectoryPattern = regexp.MustCompile(`(?is)<H3>Apache Tomcat Examples</H3>`)
)

func (detecter *WSDetecter) detectDirectoryList(page *common.Webpage) []*FoundVul {
	if apacheDirectoryPattern.Match(page.Content) ||
		iisDirectoryPattern.Match(page.Content) ||
		tomcatDirectoryPattern.Match(page.Content) ||
		tomcatExampleDirectoryPattern.Match(page.Content) {
		return []*FoundVul{
			&FoundVul{
				Affect:   "text",
				VulURL:   page.URL,
				VulXML:   "Directories_with_LIST_permissions_enabled.xml",
				Severity: "low",
			}}
	}
	return nil
}

var (
	oceanLotusDomains = []string{
		"a.doulbeclick.org",
		"ad.adthis.org",
		"ad.jqueryclick.com",
		"api.querycore.com",
		"browser-extension.jdfkmiabjpfjacifcmihfdjhpnjpiick.com",
		"cdn-js.com",
		"cdn.adsfly.co",
		"cdn.disqusapi.com",
		"cloudflare-api.com",
		"cory.ns.webjzcnd.com",
		"googlescripts.com",
		"health-ray-id.com",
		"hit.asmung.net",
		"jquery.google-script.org",
		"js.ecommer.org",
		"s.jscore-group.com",
		"s1.gridsumcontent.com",
		"s1.jqueryclick.com",
		"ssl.security.akamaihd-d.com",
		"stat.cdnanalytic.com",
		"stats.widgetapi.com",
		"track-google.com",
		"update.security.akamaihd-d.com",
		"update.webfontupdate.com",
		"wiget.adsfly.co",
		"www.googleuserscontent.org",
		"ad.linksys-analytic.com",
		"ads.alternativeads.net",
		"api.2nd-weibo.com",
		"api.analyticsearch.org",
		"api.baiduusercontent.com",
		"api.disquscore.com",
		"api.fbconnect.net",
		"cache.akamaihd-d.com",
		"cloud.corewidget.com",
		"core.alternativeads.net",
		"d3.advertisingbaidu.com",
		"eclick.analyticsearch.org",
		"google-js.net",
		"google-js.org",
		"google-script.net",
		"gs.baidustats.com",
		"linked.livestreamanalytic.com",
		"linksys-analytic.com",
		"live.webfontupdate.com",
		"static.livestreamanalytic.com",
		"stats.corewidget.com",
		"update.akamaihd-d.com",
		"update.webfontupdate.com",
		"upgrade.liveupdateplugins.com",
		"widget.jscore-group.com",
	}
	btcDomains = []string{
		"coinhive.min.js",
		"coinhive.com",
		"projectpoi.min.js",
		"ppoi.org",
		"bitcoinjs.min.js",
		"bitcoinjs.org",
	}
)

func containDomains(testurl string, domains []string) bool {
	for i := range domains {
		if strings.Contains(testurl, domains[i]) {
			return true
		}
	}
	return false
}

func detectPageRequests(page *common.Webpage, vulXML string, domains []string) []*FoundVul {
	vulURLs := []string{}
	if containDomains(page.URL, domains) {
		vulURLs = append(vulURLs, page.URL)
	}

	if page.EffectiveURL != page.URL && containDomains(page.EffectiveURL, domains) {
		vulURLs = append(vulURLs, page.EffectiveURL)
	}

	for i := range page.NetworkWebpages {
		np := &page.NetworkWebpages[i]
		if containDomains(np.URL, domains) {
			vulURLs = append(vulURLs, np.URL)
		}
	}
	if len(vulURLs) > 0 {
		return []*FoundVul{
			&FoundVul{
				Affect:   "script",
				VulURL:   strings.Join(vulURLs, "|"),
				VulXML:   vulXML,
				Severity: "high",
			}}
	}
	return nil
}

func (detecter *WSDetecter) detectOceanLotus(page *common.Webpage) []*FoundVul {
	return detectPageRequests(page, "OceanLotus_Site.xml", oceanLotusDomains)
}

func (detecter *WSDetecter) detectBTC(page *common.Webpage) []*FoundVul {
	return detectPageRequests(page, "Front_end_BTC.xml", btcDomains)
}

const contextSize = 200

type contextRange struct {
	Start int
	End   int
	Word  string
}

type matchedWordContext struct {
	Word        string
	Position    int
	Context     []byte
	URL         string
	ContentHash string
}

func (detecter *WSDetecter) genContextOfMatchedWords(words []acsmx.MatchedWord, content []byte) []matchedWordContext {
	var startIdx, endIdx int
	var word *acsmx.MatchedWord

	sort.SliceStable(words, func(i, j int) bool { return words[i].Position < words[j].Position })

	ranges := make([]contextRange, len(words))

	for i := range words {
		word = &words[i]
		startIdx = word.Position - contextSize
		endIdx = word.Position + len([]byte(word.Word)) + contextSize
		if startIdx < 0 {
			startIdx = 0
		}

		if endIdx >= len(content) {
			endIdx = len(content) - 1
		}

		ranges[i].Start = startIdx
		ranges[i].End = endIdx
		ranges[i].Word = word.Word
	}

	mergeRanges(ranges)

	const delimiter = "__"
	result := make([]matchedWordContext, 0, len(words))
	for i := range words {
		startIdx := getStartIdx(ranges[i].Start, ranges[i].End, content)
		context := content[startIdx : ranges[i].End+1]
		endIdx := bytes.IndexRune(context, utf8.RuneError)
		if endIdx != -1 {
			context = context[:endIdx]
		}

		curWord := words[i].Word
		curWordByte := []byte(curWord)
		position := words[i].Position
		endPosition := position + len(curWordByte)
		if endPosition > len(content) {
			endPosition = len(content)
		}

		fenci := detecter.Options.JiebaFenCi
		fenci = nil
		if bytes.Equal(bytes.ToLower(content[position:endPosition]), bytes.ToLower(curWordByte)) {
			if fenci != nil {

				curContext := *(*string)(unsafe.Pointer(&context))

				fenciContext := delimiter + strings.Join(fenci.CutAll(utils.RemoveNonLetterNumber(curContext)), delimiter) + delimiter
				fenciWord := delimiter + strings.Join(fenci.Cut(words[i].Word, true), delimiter) + delimiter
				if !strings.Contains(strings.ToLower(fenciContext), strings.ToLower(fenciWord)) {
					continue
				}
			}
		} else if utils.IsAlphaNumber(curWord) {
			continue
		}

		if !bytes.Equal(bytes.ToLower(content[position:endPosition]), bytes.ToLower(curWordByte)) &&
			utils.IsAlphaNumber(curWord) {
			continue
		}
		result = append(result, matchedWordContext{
			Word:     words[i].Word,
			Position: words[i].Position,
			Context:  context,
		})
	}
	return result
}

func (detecter *WSDetecter) searchWordsAndContext(matcher *acsmx.Matcher, content []byte) []matchedWordContext {
	if matcher == nil {
		return nil
	}
	_, words := matcher.SearchAll(content)
	return detecter.genContextOfMatchedWords(words, content)
}

func (detecter *WSDetecter) searchWordsNotJieba(matcher *acsmx.Matcher, content []byte) []matchedWordContext {
	if matcher == nil {
		return nil
	}
	_, words := matcher.SearchAll(content)
	var startIdx, endIdx int
	var word *acsmx.MatchedWord

	sort.SliceStable(words, func(i, j int) bool { return words[i].Position < words[j].Position })

	ranges := make([]contextRange, len(words))
	for i := range words {
		word = &words[i]
		startIdx = word.Position - contextSize
		endIdx = word.Position + len([]byte(word.Word)) + contextSize
		if startIdx < 0 {
			startIdx = 0
		}

		if endIdx >= len(content) {
			endIdx = len(content) - 1
		}

		ranges[i].Start = startIdx
		ranges[i].End = endIdx
		ranges[i].Word = word.Word
	}

	mergeRanges(ranges)

	result := make([]matchedWordContext, 0, len(words))
	for i := range words {
		startIdx := getStartIdx(ranges[i].Start, ranges[i].End, content)
		context := content[startIdx : ranges[i].End+1]

		endIdx := bytes.IndexRune(context, utf8.RuneError)
		if endIdx != -1 {
			context = context[:endIdx]
		}

		result = append(result, matchedWordContext{
			Word:     words[i].Word,
			Position: words[i].Position,
			Context:  context,
		})
	}
	return result
}

func (detecter *WSDetecter) searchSensitiveWordGroups(content []byte) []matchedWordContext {
	matchedWords := []acsmx.MatchedWord{}

	for _, group := range detecter.Options.SensitiveMatcherGroups {
		_, groupWords := group.SearchAll(content)
		matchedWords = append(matchedWords, groupWords...)
	}
	return detecter.genContextOfMatchedWords(matchedWords, content)
}

func getStartIdx(startIdx, endIdx int, content []byte) int {
	start := startIdx
	for i := startIdx; i < endIdx; i++ {
		if utf8.RuneStart(content[i]) {
			start = i
			break
		}
	}
	return start
}

func mergeRanges(ranges []contextRange) {
	var mergeHappened = false
	for {
		mergeHappened = false
		var a, b *contextRange
		for i := 0; i < len(ranges)-1; i++ {
			a = &ranges[i]
			b = &ranges[i+1]
			if a.Word == b.Word && (a.Start != b.Start || a.End != b.End) && a.End >= b.Start+contextSize {
				mergeHappened = true
				a.End = b.End
				b.Start = a.Start
			}
		}
		if !mergeHappened {
			break
		}
	}
}

func (detecter *WSDetecter) searchBlackWords(testurl string, content []byte, IsOuter bool) []*BlackLinkResult {
	result := []*BlackLinkResult{}
	matches := detecter.searchWordsAndContext(detecter.Options.BlackMatcher, content)
	for k := range matches {
		position := -1
		if IsOuter {
			position = matches[k].Position
		}
		result = append(result, &BlackLinkResult{
			URL:      testurl,
			Word:     matches[k].Word,
			Position: position,
			Context:  string(matches[k].Context),
		})
	}
	return result
}

func (detecter *WSDetecter) GetStats() *common.DetectStats {
	stats := &common.DetectStats{
		PageCount:    detecter.detectPageCount,
		ErrorCount:   detecter.detectErrorCount,
		ErrorReasons: detecter.errorReasons,
	}
	return stats
}

func (detecter *WSDetecter) ClearUp() error {
	return nil
}

func (detecter *WSDetecter) downloadImage(imageURL string) ([]byte, error) {
	log.Infoln("start download image", imageURL)
	request, err := http.NewRequest(http.MethodGet, imageURL, nil)
	if err != nil {
		log.Errorln("downloadImage new request error.", err)
		return []byte{}, err
	}

	request.Header.Add("Referer", "site")
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	resp, err := client.Do(request)
	if err != nil {
		log.Errorln("downloadImage error.", err)
		return []byte{}, err
	}

	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorln("read image resp body error.", err)
		return []byte{}, err
	}
	return body, nil
}
