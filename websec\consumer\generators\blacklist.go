package generators

import (
	"websec/common"
	"websec/common/schema"
	"websec/detect"
	"websec/utils/log"
)

func (generator *Generator) getBlackListResult(page *common.Webpage, result *detect.DetectResult, versionTime int64) *schema.RawBlackLinkResult {
	// Check if it's an outer URL
	if !page.IsOuterURL {
		// Process original black list logic for non-outer URLs
		linkResultMap := make(map[string]schema.FoundBlackLink)
		for _, item := range result.BlackLinks {
			linkResult, ok := linkResultMap[item.URL]
			if !ok {
				linkResult = schema.FoundBlackLink{
					URL:        item.URL,
					Words:      make(map[string][]int),
					IsOuterURL: item.IsOuterURL,
				}
				linkResultMap[item.URL] = linkResult
			}
			linkResult.Words[item.Word] = append(linkResult.Words[item.Word], item.Position)
		}

		if len(linkResultMap) == 0 || versionTime == 0 {
			return nil
		}

		rawBlackLinkResult := new(schema.FoundBlackLinksDoc)
		rawBlackLinkResult.Host = page.Host
		rawBlackLinkResult.URL = page.URL
		rawBlackLinkResult.MainframeURL = page.MainFrameURL
		rawBlackLinkResult.URLHash = page.URLHash()
		rawBlackLinkResult.FoundAt = page.CrawledAt
		rawBlackLinkResult.VersionTime = versionTime
		rawBlackLinkResult.Results = make([]schema.FoundBlackLink, 0, len(linkResultMap))

		for _, v := range linkResultMap {
			rawBlackLinkResult.Results = append(rawBlackLinkResult.Results, v)
		}

		err := generator.dbConnection.SaveSourceCode(page.URL, page.URLHash(), versionTime, page.Content)
		if err != nil {
			log.Errorln("save sourcecode err:", err)
		}
		page.SaveToMongo = true
		return rawBlackLinkResult
	}

	/*
		// Process outer URL
		parsedURL, err := url.Parse(page.URL)
		if err != nil {
			log.Errorf("Failed to parse URL %s: %v", page.URL, err)
			return nil
		}

		// Get IP addresses from host
		ips, err := net.LookupIP(parsedURL.Host)
		if err != nil {
			log.Errorf("Failed to lookup IP for host %s: %v", parsedURL.Host, err)
			return nil
		}

		// get xapikey from config
		if XApiKey == "" {
			settings, err := config.ParseConfig()
			if err != nil {
				log.Errorln("failed to parse config:", err)
				return nil
			}
			XApiKey = settings.BaizeXapikey
		}
		// Check each IP
		for _, ip := range ips {
			// Skip non-IPv4 addresses
			if ipv4 := ip.To4(); ipv4 != nil {
				isMalicious, err := CheckIPMalicious(ipv4.String(), XApiKey)
				if err != nil {
					log.Errorf("Failed to check IP %s: %v", ipv4.String(), err)
					continue
				}

				if isMalicious {
					// Create black link result for malicious IP
					rawBlackLinkResult := new(schema.FoundBlackLinksDoc)
					rawBlackLinkResult.Host = page.Host
					rawBlackLinkResult.URL = page.URL
					rawBlackLinkResult.MainframeURL = page.MainFrameURL
					rawBlackLinkResult.URLHash = page.URLHash()
					rawBlackLinkResult.FoundAt = page.CrawledAt
					rawBlackLinkResult.VersionTime = versionTime
					rawBlackLinkResult.Results = []schema.FoundBlackLink{
						{
							URL:        ipv4.String(),
							Words:      make(map[string][]int),
							IsOuterURL: true,
						},
					}

					err = generator.dbConnection.SaveSourceCode(page.URL, page.URLHash(), versionTime, page.Content)
					if err != nil {
						log.Errorln("save sourcecode err:", err)
					}
					page.SaveToMongo = true
					return rawBlackLinkResult
				}
			}
		}
	*/

	return nil
}
