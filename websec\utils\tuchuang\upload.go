package tuchuang

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"errors"
	"io/ioutil"
	"net/http"
	"net/url"
	"time"

	"github.com/ugorji/go/codec"
)

type uploadResponse struct {
	Message string             `codec:"MSG"`
	Data    uploadResponseData `codec:"DATA"`
}

type uploadResponseData struct {
	URL []string `codec:"URL"`
}

const tuchuangURL = "http://upload.qhimg.com/tasks"

var (
	mAccessKey    = []byte("fed5bad465091a3257d104e4acf693b1")
	mAccessKeyLen = len(mAccessKey)
	mPrivateKey   = []byte("8ded6b551e52b473137effd3902c604e")
)

var uploadClient = http.Client{
	Timeout: 4 * time.Second,
}

func signBytes(data []byte) []byte {

	mac := hmac.New(sha1.New, mPrivateKey)

	mac.Write(data)
	macSum := mac.Sum(nil)

	sign := make([]byte, mAccessKeyLen+1+base64.URLEncoding.EncodedLen(len(macSum)))
	copy(sign, mAccessKey)
	sign[mAccessKeyLen] = ':'

	base64.URLEncoding.Encode(sign[mAccessKeyLen+1:], macSum)
	return sign
}

func signMap(values map[string]string) ([]byte, error) {
	var jh codec.JsonHandle
	jh.Canonical = true

	jsonData := []byte{}
	enc := codec.NewEncoderBytes(&jsonData, &jh)
	err := enc.Encode(values)
	if err != nil {
		return nil, err
	}
	return signBytes(jsonData), nil
}

func encodeMap(data map[string]string) string {
	values := url.Values{}
	for key, value := range data {
		values[key] = []string{value}
	}
	return url.Values(values).Encode()
}

func Upload(filename string) (string, error) {
	imgData, err := ioutil.ReadFile(filename)
	if err != nil {
		return "", err
	}
	return UploadImage(imgData)
}

func UploadImage(imgData []byte) (string, error) {
	if len(imgData) == 0 {
		return "", errors.New("img data is empty")
	}

	var jh codec.JsonHandle
	jh.Canonical = true

	params := map[string]string{
		"data":       base64.URLEncoding.EncodeToString(imgData),
		"key_prefix": "",
		"policy":     "{}",
		"tag":        "put",
		"suffix":     "jpg",
	}
	sign, err := signMap(params)
	if err != nil {
		return "", nil
	}
	params["sign"] = string(sign)

	values := url.Values{}
	for key, value := range params {
		values[key] = []string{value}
	}

	var dec *codec.Decoder
	var resp *http.Response
	var jsonResp = uploadResponse{}
	for i := 0; i < 3; i++ {
		if i > 0 {
			time.Sleep(1 * time.Second)
		}
		resp, err = uploadClient.PostForm(tuchuangURL, values)
		if err != nil {
			continue
		}
		if resp.StatusCode == 200 {
			dec = codec.NewDecoder(resp.Body, &jh)
			err = dec.Decode(&jsonResp)
			resp.Body.Close()
			if err != nil {
				continue
			}
			if jsonResp.Message == "success" && len(jsonResp.Data.URL) != 0 {
				return jsonResp.Data.URL[0], nil
			}
		}
	}
	return "", nil
}
