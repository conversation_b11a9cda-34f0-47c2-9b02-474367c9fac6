package main

import (
	"context"
	"encoding/csv"
	"flag"
	"net/url"
	"os"
	"regexp"
	"strings"
	"time"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/config"
	"websec/tools/phish/hawk"
	"websec/utils/log"
	"websec/utils/stream"

	"github.com/scylladb/go-set/strset"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	keyWord  = flag.String("name", "", "asset keyword")
	duration = flag.Int("duration", 7, "duration")

	fishSrc = map[string]*strset.Set{}
)

func main() {
	settings, err := config.ParseConfig()
	if err != nil {
		log.Errorln(err)
		return
	}

	dbConnection, err := db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis))
	if err != nil {
		log.Errorln(err)
		return
	}

	file, err := os.Open("fishing.csv")
	if err != nil {
		panic(err)
	}
	defer file.Close()
	reader := csv.NewReader(file)

	reader.FieldsPerRecord = -1

	record, err := reader.ReadAll()
	if err != nil {
		panic(err)
	}

	fishSrc = make(map[string]*strset.Set)
	for _, item := range record {
		if len(item) < 2 {
			continue
		}

		u, err := url.Parse(item[0])
		if err != nil {
			log.Errorln("err name", item[0])
			continue
		}

		var name string
		if u.Scheme == "https" {
			name = u.Host + ":443"
		} else {
			name = u.Host
		}

		phishingURL := item[1]

		if v, exists := fishSrc[name]; exists {
			v.Add(phishingURL)
			fishSrc[name] = v
		} else {
			fishSrc[name] = strset.New(phishingURL)
		}
		log.Infoln(name, fishSrc[name].List())
	}

	run(dbConnection, settings)

}

func run(dbConnection *db.DBConnection, settings *config.Config) {
	producer, err := stream.NewProducer(&stream.ProducerOptions{
		Brokers:         settings.Kafka.Brokers,
		MessageMaxBytes: settings.Kafka.MessageMaxBytes,
	})
	if err != nil {
		log.Errorln(err)
		return
	}
	producer.Go()
	defer producer.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var batchSize int32 = 5000
	cursor, err := dbConnection.GetMongoDatabase().Collection(consts.CollectionAssets).Find(ctx,
		bson.M{
			"status":             1,
			"options.expired_at": bson.M{"$gt": time.Now()},
		},
		options.Find().SetSort(bson.M{"_id": 1}).SetBatchSize(batchSize))

	if err != nil {
		return
	}
	defer cursor.Close(ctx)

	var sets *strset.Set
	var exists bool
	for cursor.Next(ctx) {
		var result schema.Asset
		err := cursor.Decode(&result)
		if err != nil {
			log.Errorln("failed to decode:", err)
			continue
		}

		if sets, exists = fishSrc[result.Host]; !exists {
			continue
		}

		for _, v := range sets.List() {
			curHosts := strings.Split(result.Host, ":")
			if len(curHosts) == 0 {
				continue
			}
			doc := &schema.FoundPhishingDoc{
				ID:      primitive.NewObjectID(),
				Pu:      schema.FoundPhishingURL{URL: v},
				Ou:      schema.FoundPhishingURL{URL: v},
				Host:    v,
				FoundAt: time.Now(),
				AssetID: result.ID.Hex(),
			}
			log.Infoln("related website:", result)

			producer.Produce("phishing-results", doc)
		}
	}
	time.Sleep(60 * time.Second)

}

func getRelatedWebsite(asset *schema.Asset, producer *stream.Producer, dbConnection *db.DBConnection) {
	if asset.Name == "" || asset.Name == "无" {
		return
	}

	log.Debugln("asset Name", asset.Name)

	lastSearchTime, err := dbConnection.HGetInt64(consts.RedisHawkTime, asset.Name)
	if err != nil {
		lastSearchTime = time.Now().Unix() - int64(*duration*86400)
	}

	page := 1
	httpsPattern := regexp.MustCompile(`https`)

	for {
		var resp *hawk.Response
		for i := 0; i < 3; i++ {
			resp, err = hawk.GetRelatedWebSite(asset.Host, lastSearchTime, time.Now().Unix(), page)
			if err != nil || resp.Status != 200 {
				log.Errorln(err)
				time.Sleep(1 * time.Second)
				continue
			} else {
				break
			}
		}

		if err != nil {
			log.Errorln("retry failed", err)
			break
		}

		for _, v := range resp.Data.List {
			site := httpsPattern.ReplaceAllString(v, "http")
			doc := &schema.FoundPhishingDoc{
				Pu:      schema.FoundPhishingURL{Host: asset.Host},
				Ou:      schema.FoundPhishingURL{Host: asset.Host},
				Host:    site,
				FoundAt: time.Now(),
			}
			log.Infoln("related website:", site)
			producer.Produce(consts.TopicRawPhishingResults, doc)
		}

		if page < resp.Data.TotalPage {
			page++
		} else {
			dbConnection.HSet(consts.RedisHawkTime, asset.Name, time.Now().Unix())
			break
		}
	}
}
