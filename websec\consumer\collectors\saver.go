package collectors

import (
	"context"
	"runtime/debug"
	"sync"
	"time"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	elastic "gopkg.in/olivere/elastic.v5"
)

const (
	maxBulkSize       = int(20)
	maxHBaseBulkSize  = int(50)
	saveInterval      = time.Second * 5
	hBaseSaveInterval = time.Second * 2
)

type Saver struct {
	dbConnection *db.DBConnection
	waitGroup    sync.WaitGroup
	closeOnce    sync.Once

	pageEsChannel    chan *schema.PageArchiveES
	pageHBaseChannel chan *schema.PageArchiveHBase
	pageMongoChannel chan *schema.PageArchiveHBase
}

func NewSaver(connection *db.DBConnection) *Saver {
	return &Saver{
		dbConnection:     connection,
		pageEsChannel:    make(chan *schema.PageArchiveES, 100),
		pageHBaseChannel: make(chan *schema.PageArchiveHBase, 100),
		pageMongoChannel: make(chan *schema.PageArchiveHBase, 100),
	}
}

func (saver *Saver) Go() {
	saver.waitGroup.Add(3)
	go saver.saveToElastic()
	go saver.saveToHBase()
	go saver.saveToMongo()
}

func (saver *Saver) Close() {
	saver.closeOnce.Do(func() {
		close(saver.pageEsChannel)
		close(saver.pageHBaseChannel)
		close(saver.pageMongoChannel)
		saver.waitGroup.Wait()
	})
}

func (saver *Saver) AddPageArchiveToEs(doc *schema.PageArchiveES) {
	saver.pageEsChannel <- doc
}

func (saver *Saver) AddPageArchiveToHBase(doc *schema.PageArchiveHBase) {
	saver.pageHBaseChannel <- doc
}

func (saver *Saver) AddPageArchiveToMongo(doc *schema.PageArchiveHBase) {
	saver.pageMongoChannel <- doc
}

func (saver *Saver) saveToElastic() {
	defer func() {
		if err := recover(); err != nil {
			log.Error(err, string(debug.Stack()))
		}
		saver.waitGroup.Done()
	}()

	var (
		esBulkCount  int
		esLastSaveAt time.Time
	)
	bulk := saver.dbConnection.GetESClient().Bulk().Retrier(elastic.NewBackoffRetrier(
		elastic.NewExponentialBackoff(100*time.Millisecond, 30*time.Second)))
Saving:
	for {
		t := time.NewTimer(5 * time.Second)
		select {
		case doc, ok := <-saver.pageEsChannel:
			if !ok {
				break Saving
			}
			req := elastic.NewBulkIndexRequest().Index(consts.ESYunJianCeIndex).Type(consts.EsWebarchiveType).Doc(doc)
			bulk.Add(req)
			esBulkCount++
		case <-t.C:
		}

		if esBulkCount > 0 && (esBulkCount >= maxBulkSize || time.Since(esLastSaveAt) >= saveInterval) {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)

			rsp, err := bulk.Do(ctx)
			if err != nil {
				log.Errorf("saving docs failed %d %v", esBulkCount, rsp)
				bulk.Reset()
			} else {
				log.Infoln("saving docs to elasticsearch done... ")
			}

			esBulkCount = 0
			esLastSaveAt = time.Now()
			cancel()
		}
	}

	if esBulkCount > 0 {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)

		rsp, err := bulk.Do(ctx)
		if err != nil {
			log.Errorf("saving docs failed %d %v", esBulkCount, rsp)
		}
		cancel()
	}
}

func (saver *Saver) saveToHBase() {
	ticker := time.NewTicker(hBaseSaveInterval)

	defer func() {
		if err := recover(); err != nil {
			log.Error(err, string(debug.Stack()))
		}
		ticker.Stop()
		saver.waitGroup.Done()
	}()

	var (
		count      = 0
		lastSaveAt = time.Now()
	)
	contentCache := make([]*schema.PageArchiveHBase, 0, maxHBaseBulkSize)

Saving:
	for {
		select {
		case doc, ok := <-saver.pageHBaseChannel:
			if !ok {
				break Saving
			}
			contentCache = append(contentCache, doc)
			count++
		case <-ticker.C:
		}

		if count > 0 && (count >= maxHBaseBulkSize || time.Since(lastSaveAt) >= hBaseSaveInterval) {
			saver.saveHBaseContent(contentCache)
			count = 0
			lastSaveAt = time.Now()
			contentCache = make([]*schema.PageArchiveHBase, 0, maxHBaseBulkSize)
		}
	}

	if count > 0 {
		saver.saveHBaseContent(contentCache)
	}
}

func (saver *Saver) saveHBaseContent(contentCache []*schema.PageArchiveHBase) {
	err := saver.dbConnection.SetHBaseContent(contentCache)
	if err != nil {

		for _, v := range contentCache {
			log.Errorf("save to hbase error:%v, rowkey:%s", err, v.RowKey)
		}
	}
}

func (saver *Saver) saveToMongo() {
	ticker := time.NewTicker(saveInterval)

	defer func() {
		if err := recover(); err != nil {
			log.Error(err, string(debug.Stack()))
		}
		ticker.Stop()
		saver.waitGroup.Done()
	}()

	var (
		count      = 0
		lastSaveAt = time.Now()
	)
	contentCache := make([]*schema.PageArchiveHBase, 0, maxBulkSize)

Saving:
	for {
		select {
		case doc, ok := <-saver.pageMongoChannel:
			if !ok {
				break Saving
			}
			contentCache = append(contentCache, doc)
			count++
		case <-ticker.C:
		}

		if count > 0 && (count >= maxBulkSize || time.Since(lastSaveAt) >= saveInterval) {
			saver.saveContentToMongo(contentCache)
			count = 0
			lastSaveAt = time.Now()
			contentCache = make([]*schema.PageArchiveHBase, 0, maxBulkSize)
		}
	}

	if count > 0 {
		saver.saveContentToMongo(contentCache)
	}
}

func (saver *Saver) saveContentToMongo(contents []*schema.PageArchiveHBase) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	models := make([]mongo.WriteModel, len(contents))
	for k, v := range contents {
		models[k] = mongo.NewInsertOneModel().SetDocument(v)
	}

	res, err := saver.dbConnection.GetMongoDatabase().Collection(consts.CollectionHBaseContent).BulkWrite(ctx, models,
		options.BulkWrite().SetOrdered(false))
	if err != nil {
		log.Errorln("saveContentToMongo error:", err)
		return err
	}
	if res.InsertedCount < int64(len(contents)) {
		log.Errorln("some documents are not saved: ", res.InsertedCount, " ", len(contents))
	}

	log.Infoln("saveContentToMongo success:", res.InsertedCount)

	return nil
}
