package utils

import (
	"container/list"
	"sync"
)

type queueItem interface {
	Clear()
}

type Queue struct {
	m    *sync.Mutex
	data *list.List

	closed bool
}

func NewQueue() *Queue {
	return &Queue{
		m:      &sync.Mutex{},
		data:   list.New(),
		closed: false,
	}
}

func (queue *Queue) Push(item queueItem) {
	queue.m.Lock()
	defer queue.m.Unlock()

	if !queue.closed {
		queue.data.PushBack(item)
	} else {
		item.Clear()
	}
}

func (queue *Queue) Pop() interface{} {
	queue.m.Lock()
	defer queue.m.Unlock()

	if queue.closed {
		return nil
	}
	return queue.pop()
}

func (queue *Queue) pop() interface{} {
	iter := queue.data.Front()
	if iter == nil {
		return nil
	}
	v := iter.Value
	queue.data.Remove(iter)
	return v
}

func (queue *Queue) Len() int {
	queue.m.Lock()
	defer queue.m.Unlock()

	return queue.data.Len()
}

func (queue *Queue) Close() {
	queue.m.Lock()
	defer queue.m.Unlock()

	for {
		v := queue.pop()
		if v == nil {
			break
		}
		v.(queueItem).Clear()
	}

	queue.closed = true
}

func (queue *Queue) IsClosed() bool {
	queue.m.Lock()
	defer queue.m.Unlock()

	return queue.closed
}
