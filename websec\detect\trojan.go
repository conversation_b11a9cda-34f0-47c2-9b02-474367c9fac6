package detect

import (
	"bytes"
	"encoding/json"
	"fmt"
	"os"
	"regexp"
	"strings"
	"time"
)

type TrojanTime time.Time

func (tt *TrojanTime) UnmarshalJSON(bytes []byte) error {
	t, err := time.Parse(`"2006-01-02"`, string(bytes))
	if err != nil {
		return err
	}
	*tt = TrojanTime(t)
	return err
}

type TrojanRule struct {
	Name          string         `json:"name"`
	FileType      string         `json:"file_type"`
	Description   string         `json:"description"`
	PrefixRegex   string         `json:"prefix_regex"`
	Features      []string       `json:"features"`
	AffectSystem  []string       `json:"affect_system"`
	AffectBrowser string         `json:"affect_browser"`
	CVE           string         `json:"cve"`
	Time          TrojanTime     `json:"time"`
	AffectContent string         `json:"affect_content,omitempty"`
	Regex         *regexp.Regexp `json:"regex,omitempty"`
}

func (rule TrojanRule) Matchs(page []byte) bool {
	fmt.Println(rule.Features)
	if len(rule.Features) == 0 || len(page) == 0 {
		return false
	}

	for _, feature := range rule.Features {
		words := strings.Split(feature, ",")

		for _, word := range words {
			if !bytes.Contains(page, []byte(word)) {
				break
			}
			return true
		}

	}
	return false
}

type TrojanDetecter struct {
	Rules []TrojanRule
}

var flysnowRegexp = regexp.MustCompile(`<iframe.*`)

func (td TrojanDetecter) Detect(page []byte, fileType string) (r []TrojanRule, exists bool) {

	for _, rule := range td.Rules {
		if fileType != rule.FileType {
			continue
		}

		if rule.Regex != nil {
			l := rule.Regex.FindAllString(string(page), -1)
			fmt.Println(l)
			if len(l) == 0 {
				continue
			}

			for _, curContent := range l {
				if rule.Matchs([]byte(curContent)) {
					rule.AffectContent = curContent
					fmt.Println("-------", rule)
					r = append(r, rule)
					exists = true
				}
			}
		} else {
			if rule.Matchs(page) {
				r = append(r, rule)
				exists = true
			}
		}

	}

	return
}

func NewTrojanDetecter(rulePath string) (*TrojanDetecter, error) {
	var err error
	f, err := os.Open(rulePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	var rules []TrojanRule
	decoder := json.NewDecoder(f)

	err = decoder.Decode(&rules)
	if err != nil {
		return nil, err
	}

	for index, item := range rules {
		if item.PrefixRegex != "" {
			item.Regex = regexp.MustCompile(item.PrefixRegex)
		}
		rules[index] = item
	}

	detecter := &TrojanDetecter{
		Rules: rules,
	}

	for _, item := range rules {
		fmt.Println(item.PrefixRegex)
	}

	return detecter, nil
}
