package scan

import (
	"context"
	"fmt"
	"io/ioutil"
	"net/url"
	"os"
	"os/exec"
	"strings"
	"time"
	"websec/scan/rules"
	"websec/utils/log"
	"websec/utils/semaphore"
)

const nmapBinLocation = "/usr/bin/nmap"

var nmapScripts = []string{
	"rdp-vuln-ms12-020",
	"ssl-dh-params",
	"ssl-known-key",
	"smb-vuln-ms06-025",
	"smb-double-pulsar-backdoor",
	"smb-vuln-ms08-067",
	"smb-vuln-ms10-061",
	"ssl-cert-intaddr",
	"sslv2-drown",
	"ssl-ccs-injection",
	"samba-vuln-cve-2012-1182",
	"smb-vuln-cve-2017-7494",
	"smtp-vuln-cve2011-1720",
	"ssl-heartbleed",
	"mysql-vuln-cve2012-2122",
	"smb-vuln-cve2009-3103",
	"smb-vuln-ms07-029",
	"smb-vuln-ms10-054",
	"smtp-vuln-cve2010-4344",
	"smtp-vuln-cve2011-1764",
	"smb-vuln-ms17-010",
	"ssl-poodle",
	"rmi-vuln-classloader",
	"tls-ticketbleed",
	"ftp-proftpd-backdoor",
	"ftp-vsftpd-backdoor",
	"smb-vuln-conficker",
	"ftp-libopie",
	"smb-vuln-ms17-010",
	"ssl-enum-ciphers",
	"http-websphere-console",
}
var scriptStr = strings.Join(nmapScripts, ",")
var nmapSema = semaphore.NewWeighted(8)
var nmapSemaContext = context.Background()

func (scanner *WSScanner) scanNmapWrap(linkOrg *AffectLink) {

	if scanner.specificVulXMLs != nil {
		if _, ok := scanner.specificVulXMLs[XMLWebSphereRCE]; !ok {
			return
		}
	}

	link := linkOrg.DeepCopy()
	result := scanner.scanNmap(link)
	if result != nil {
		scanner.outputResult(result)
	}
}

func (scanner *WSScanner) scanNmap(link *AffectLink) *ScanResult {
	err := nmapSema.Acquire(nmapSemaContext, 1)

	if err != nil {
		log.Errorln("failed to acquire nmaplinSema for:", link)
		return &ScanResult{ErrorCount: 1}
	}
	defer nmapSema.Release(1)

	f, err := ioutil.TempFile("", "nmap-scan-result")
	if err != nil {
		log.Errorln("failed to create temp file for store nmap scan result,", err)
		return &ScanResult{ErrorCount: 1}
	}
	f.Close()

	defer os.Remove(f.Name())
	return nmap(scanner.Options.Ctx, link, f.Name(), false)
}

func nmap(parent context.Context, link *AffectLink, filename string, useSyn bool) *ScanResult {
	var hostname string
	if strings.HasPrefix(link.URL, "http://") || strings.HasPrefix(link.URL, "https://") {
		parts, err := url.Parse(link.URL)
		if err != nil {
			return &ScanResult{ErrorCount: 1}
		}
		hostname = parts.Hostname()
	} else {
		hostname = link.URL
	}

	ctx, cancel := context.WithTimeout(parent, 300*time.Second)
	defer cancel()

	var params []string
	if useSyn {
		params = []string{
			"-sV", "-sS", "-Pn", hostname, "--open",
			"--script", scriptStr, "-n", "-oX", filename,
		}
	} else {
		params = []string{
			"-sV", "-Pn", hostname, "--open",
			"--script", scriptStr, "-n", "-oX", filename,
		}
	}

	if err := exec.CommandContext(ctx, nmapBinLocation, params...).Run(); err != nil {

		log.Errorln("failed to execute nmap:", err)
		return &ScanResult{ErrorCount: 1}
	}

	nmapResult, err := rules.ParseNmapResult(filename)
	if err != nil {
		log.Errorln("failed to parse nmap xml,", filename, err)
		return &ScanResult{ErrorCount: 1}
	}
	result := &ScanResult{}

	addr := nmapResult.Host.Address.Addr
	ports := nmapResult.Host.Ports

	for i := range ports {
		port := &ports[i]
		for j := range port.Scripts {
			script := &port.Scripts[j]
			scriptName := script.ID
			if scriptName != "ssl-known-key" &&
				scriptName != "ssl-cert-intaddr" &&
				scriptName != "smtp-vuln-cve2010-4344" &&
				scriptName != "ssl-enum-ciphers" &&
				scriptName != "ftp-proftpd-backdoor" {
				state := strings.ToLower(script.State())
				if state == "" || strings.Contains(state, "not ") {
					continue
				}
			}
			var xmlName string

			if scriptName == "http-websphere-console" {
				version := port.Service.Version
				if version != "9.0" && version != "8.5" && version != "8.0" && version != "7.0" {
					continue
				} else {
					xmlName = XMLWebSphereRCE
				}
			}
			vulContext := VulContext{
				"addr":    addr,
				"port":    port.PortID,
				"service": port.Service.Name,
				"product": port.Service.Product,
				"version": port.Service.Version,
				"body":    nmapResult.Content,
			}
			if scriptName == "" {
				xmlName = "Open_Port.xml"
			} else if xmlName == "" {
				vulContext["output"] = script.Output
				xmlName = scriptName
			}
			vul := rules.Vulnerability{
				Name:        "nmap",
				Description: "Nmap Scan",
				VulXML:      xmlName,
			}
			foundVul := &FoundVul{
				Link:     link,
				Vul:      &vul,
				VulURL:   fmt.Sprintf("%s:%d", addr, port.PortID),
				Severity: "info",
				Context:  vulContext,
			}
			result.FoundVuls = append(result.FoundVuls, foundVul)
		}
	}
	return result
}
