package ocrv1

import (
	"bufio"
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"image"
	_ "image/gif"
	"image/jpeg"
	_ "image/png"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
	"websec/utils/acsmx"

	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/stream"
	"websec/utils/tuchuang"

	"go.mongodb.org/mongo-driver/bson"
)

var imageFormat = map[string]bool{
	"png": true, "jpeg": true, "gif": true,
}

const (
	imageWidthLimit  int = 50
	imageHeightLimit     = 50
	jpegQuality          = 30
)

func URLHash(u string) string {
	hash := md5.New()
	hash.Write([]byte(u))
	return hex.EncodeToString(hash.Sum(nil))
}

func ImageHash(imageData []byte) string {
	hash := md5.New()
	hash.Write(imageData)
	return hex.EncodeToString(hash.Sum(nil))
}

func getFilename(u string) (string, error) {
	parts, err := url.Parse(u)
	if err != nil {
		return "", err
	}
	pathParts := strings.Split(parts.Path, "/")
	return pathParts[len(pathParts)-1], err
}

func (processor *Processor) processImages(msg *stream.Message) error {
	var crawledImageURLS = new(schema.CrawledImageURLS)
	err := json.Unmarshal(msg.Value, crawledImageURLS)
	if err != nil {
		log.Errorln("Unmarshal failed:", err)
		return err
	}

	log.Infoln("---------------- processImages from ocrv1", crawledImageURLS.JobID, crawledImageURLS.URL)

	assetID := crawledImageURLS.AssetID

	ocrResultDoc := &schema.FoundOcrSensitiveWordDoc{
		URL:     crawledImageURLS.URL,
		Host:    crawledImageURLS.Host,
		AssetID: assetID,
		JobID:   crawledImageURLS.JobID,
		FoundAt: crawledImageURLS.FoundAt,
	}

	results := make([]*schema.SensitiveImageResult, 0, 10)
	for _, imageURL := range crawledImageURLS.Images {
		result, err := processor.processImage(crawledImageURLS, imageURL)
		if err != nil {
			log.Errorln(err)
			continue
		}
		if result != nil {
			results = append(results, result)
		}
	}
	ocrResultDoc.Results = results
	processor.producer.Produce(consts.TopicFinalOcrSensitiveWordResults, ocrResultDoc)
	return nil
}

func (processor *Processor) processImage(crawledImageURLS *schema.CrawledImageURLS, imageURL string) (*schema.SensitiveImageResult, error) {
	imageData, err := processor.downloadImage(imageURL)
	if err != nil {
		log.Errorf("download image error, %v %v", imageURL, err)
		return nil, err
	}

	effective, err := processor.checkImage(imageData)
	if err != nil || !effective {
		log.Errorf("image is not effective, %v %v", imageURL, err)
		return nil, err
	}

	imageMD5 := ImageHash(imageData)
	imageURLHash := URLHash(imageURL)
	imageDoc := processor.getImageFromMongo(crawledImageURLS.Host, imageURLHash)
	if imageDoc != nil {
		if imageDoc.ImageMD5 == imageMD5 {
			content := imageDoc.Content
			wordResults := processor.detect([]byte(content))
			if len(wordResults) > 0 {
				sensitiveImageResult := schema.SensitiveImageResult{
					ImageURL:     imageURL,
					ImageURLHash: imageURLHash,
					ImageAddress: imageDoc.ImageAddress,
					Content:      content,
					ResultDetail: wordResults,
				}
				return &sensitiveImageResult, nil
			}
		}
	}

	//content, err := processor.doOCR(imageURL, imageData, ModelGPU)
	r, err := doOCRV1(imageData)
	var content string
	for _, word := range r.Ret {
		content += word.Word + "\n"
	}
	log.Debugf("%v %v %v", imageURL, len(imageData), len(content))
	if err != nil {
		log.Errorf("Failed to do ocr, %v %v", imageURL, err)
		return nil, err
	}

	var sensitiveImageResult *schema.SensitiveImageResult
	var wordResults []schema.WordResult
	if len(content) > 0 {
		wordResults = processor.detect([]byte(content))
	}
	imageAddress := ""
	if len(wordResults) > 0 {

		compressData, err := processor.compressImage(imageData)
		if err != nil {
			log.Errorln("conpress image error, ", err)
			compressData = imageData
		}
		u, err := tuchuang.UploadImage(compressData)
		if err == nil {
			imageAddress = u
		} else {
			log.Errorf("upload image error, %v %v", imageURL, err)
		}

		sensitiveImageResult = &schema.SensitiveImageResult{
			ImageURL:     imageURL,
			ImageURLHash: imageURLHash,
			ImageAddress: imageAddress,
			Content:      content,
			ResultDetail: wordResults,
		}
	}

	if imageDoc != nil {

		imageDoc.ImageMD5 = imageMD5
		imageDoc.Content = content
		imageDoc.LastCheckedAt = time.Now()
		imageDoc.ImageAddress = imageAddress
		processor.updateImage(imageDoc)
	} else {

		imageDoc := &schema.OcrImageDoc{
			AssetID:       crawledImageURLS.AssetID,
			Host:          crawledImageURLS.Host,
			ImageURL:      imageURL,
			ImageURLHash:  imageURLHash,
			ImageMD5:      imageMD5,
			ImageAddress:  imageAddress,
			Content:       content,
			FoundAt:       time.Now(),
			LastCheckedAt: time.Now(),
		}
		processor.insertImage(imageDoc)
	}

	return sensitiveImageResult, nil
}

func (processor *Processor) detect(content []byte) []schema.WordResult {
	_, matches := processor.options.SensitiveMatcher.SearchAll(content)
	if matches == nil {
		matches = []acsmx.MatchedWord{}
	}
	_, fghkMatches := processor.options.FghkMatcher.SearchAll(content)
	if len(fghkMatches) > 0 {
		matches = append(matches, fghkMatches...)
	}

	results := make([]schema.WordResult, len(matches))
	for i := range matches {
		results[i].Word = matches[i].Word
		results[i].Position = matches[i].Position
	}
	return results
}

func (processor *Processor) downloadImage(imageURL string) ([]byte, error) {
	log.Infoln("start download image", imageURL)
	request, err := http.NewRequest(http.MethodGet, imageURL, nil)
	if err != nil {
		log.Errorln("downloadImage new request error.", err)
		return []byte{}, err
	}

	request.Header.Add("Referer", "site")
	resp, err := processor.client.Do(request)
	if err != nil {
		log.Errorln("downloadImage error.", err)
		return []byte{}, err
	}

	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorln("read image resp body error.", err)
		return []byte{}, err
	}
	return body, nil
}

func (processor *Processor) compressImage(imageData []byte) ([]byte, error) {
	img, _, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return []byte{}, err
	}

	buffer := bytes.NewBuffer(make([]byte, 0))
	bufferWriter := bufio.NewWriter(buffer)
	err = jpeg.Encode(bufferWriter, img, &jpeg.Options{Quality: jpegQuality})
	if err != nil {
		return []byte{}, err
	}
	bufferWriter.Flush()
	return buffer.Bytes(), nil
}

func (processor *Processor) checkImage(imageData []byte) (bool, error) {
	conf, format, err := image.DecodeConfig(bytes.NewReader(imageData))
	if err != nil {
		return false, err
	}

	if conf.Height <= imageHeightLimit && conf.Width <= imageWidthLimit {
		return false, nil
	}

	if _, ok := imageFormat[format]; !ok {
		return false, nil
	}

	return true, nil
}

func (processor *Processor) getImageFromMongo(host string, imageURLHash string) *schema.OcrImageDoc {
	mdb := processor.dbConnection.GetMongoDatabase()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var doc schema.OcrImageDoc
	err := mdb.Collection(consts.CollectionOcrImages).FindOne(ctx, bson.M{"host": host, "image_url_hash": imageURLHash}).Decode(&doc)
	if err != nil {
		log.Errorf("get image from mongo error, %v %v", host, imageURLHash)
		return nil
	}

	return &doc
}

func (processor *Processor) getAccessAuthToken() (string, string) {
	processor.MuAcc.RLock()
	defer processor.MuAcc.RUnlock()
	accessToken := "" //processor.Acc.AccessToken
	authToken := ""   // processor.Acc.AuthToken
	return accessToken, authToken
}

func (processor *Processor) updateImage(imageDoc *schema.OcrImageDoc) {
	data := bson.M{
		"$set": bson.M{
			"image_md5":       imageDoc.ImageMD5,
			"content":         imageDoc.Content,
			"last_checked_at": imageDoc.LastCheckedAt,
		},
	}

	mdb := processor.dbConnection.GetMongoDatabase()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	for retry := 0; retry < 3; retry++ {
		_, err := mdb.Collection(consts.CollectionOcrImages).UpdateOne(
			ctx, bson.M{"_id": imageDoc.ID}, data)
		if err != nil {
			log.Errorln("update ocr_images error: %v %v", err, data)
		} else {
			break
		}
	}
}

func (processor *Processor) insertImage(imageDoc *schema.OcrImageDoc) {
	mdb := processor.dbConnection.GetMongoDatabase()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	for retry := 0; retry < 3; retry++ {
		_, err := mdb.Collection(consts.CollectionOcrImages).InsertOne(
			ctx, imageDoc)
		if err != nil {
			log.Errorln("insert ocr_images error: %v %v", err, imageDoc)
		} else {
			break
		}
	}
}
