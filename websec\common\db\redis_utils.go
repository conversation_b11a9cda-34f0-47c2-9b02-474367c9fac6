package db

import (
	"encoding/json"
	"time"
	"websec/utils"
	"websec/utils/log"

	"github.com/go-redis/redis"
)

func (d *DBConnection) HGetBytes(key, field string) ([]byte, error) {
	return d.redisClient.HGet(key, field).Bytes()
}

func (d *DBConnection) HGetObject(key, field string, val interface{}) error {
	res, err := d.redisClient.HGet(key, field).Bytes()
	if err != nil && err != redis.Nil {
		return err
	}

	if err == redis.Nil {
		return nil
	} else {
		return json.Unmarshal(res, val)
	}
}

func (d *DBConnection) HGetString(key, field string) (string, error) {
	return d.redisClient.HGet(key, field).Result()
}

func (d *DBConnection) HSrem(key, field string) (int64, error) {
	return d.redisClient.SRem(key, field).Result()
}

func (d *DBConnection) HSadd(key, field string) (int64, error) {
	return d.redisClient.SAdd(key, field).Result()
}

func (d *DBConnection) HGetInt64(key, field string) (int64, error) {
	return d.redisClient.HGet(key, field).Int64()
}

func (d *DBConnection) HGetAll(key string) (map[string]string, error) {
	return d.redisClient.HGetAll(key).Result()
}

func (d *DBConnection) HSet(key, field string, value interface{}) error {
	return d.redisClient.HSet(key, field, value).Err()
}

func (d *DBConnection) HSetObject(key, field string, value interface{}) error {
	res, err := json.Marshal(value)
	if err != nil {
		return err
	}

	return d.HSet(key, field, res)
}

func (d *DBConnection) HExists(key, field string) (bool, error) {
	return d.redisClient.HExists(key, field).Result()
}

var expireLua = "if redis.call('TTL', KEYS[1]) == -1 then redis.call('expire', KEYS[1], ARGV[1]) end"

func (d *DBConnection) HSetNx(key, field string, value interface{}, expire int64) error {

	res, err := d.redisClient.HSetNX(key, field, value).Result()
	if err != nil {
		log.Errorln("hsetnx error:", err)
	}
	if res && expire > 0 {
		err = d.redisClient.Eval(expireLua, []string{key}, expire).Err()

	}
	return err
}

func (d *DBConnection) HSetEx(key, filed string, value interface{}, expire int64) error {
	res, err := d.redisClient.HSet(key, filed, value).Result()
	if err != nil {
		log.Errorln("hsetex error:", err)
	}
	if res && expire > 0 {
		err = d.redisClient.Eval(expireLua, []string{key}, expireLua).Err()
	}
	return err
}

func (d *DBConnection) HIncrBy(key, field string, value int64) error {
	return d.redisClient.HIncrBy(key, field, value).Err()
}

func (d *DBConnection) GetRedisBytes(key string) ([]byte, error) {
	return d.redisClient.Get(key).Bytes()
}

func (d *DBConnection) GetRedisString(key string) (string, error) {
	return d.redisClient.Get(key).Result()
}

func (d *DBConnection) GetRedisInt64(key string) (int64, error) {
	return d.redisClient.Get(key).Int64()
}

func (d *DBConnection) RedisSet(key string, value interface{}) error {
	return d.redisClient.Set(key, value, 0).Err()
}

func (d *DBConnection) RedisSetEx(key string, value interface{}, expire int64) error {
	expire += int64(utils.RandBetweenInt32(1, 100))
	return d.redisClient.Set(key, value, time.Duration(expire)*time.Second).Err()
}

func (d *DBConnection) RedisSetNx(key string, value interface{}, expire int64) error {
	return d.redisClient.SetNX(key, value, time.Duration(expire)*time.Second).Err()
}

func (d *DBConnection) Incr(key string) error {
	return d.redisClient.Incr(key).Err()
}

func (d *DBConnection) PFCount(key string) int64 {
	return d.redisClient.PFCount(key).Val()
}

func (d *DBConnection) PFAdd(key, value string) (int64, error) {
	return d.redisClient.PFAdd(key, value).Result()
}

func (d *DBConnection) AddHyperLogLog(key, value string) bool {
	reply, err := d.PFAdd(key, value)
	if err != nil {
		log.Errorln("AddHyperLogLog error:", err)
		return false
	}
	if reply == 1 {
		return true
	}
	return false
}

func (d *DBConnection) RedisDo(cmd string, args ...interface{}) (interface{}, error) {
	return d.redisClient.Do(cmd, args).Result()
}

func (d *DBConnection) SAdd(key string, filed interface{}) (int64, error) {
	return d.redisClient.SAdd(key, filed).Result()
}

func (d *DBConnection) DelKey(key string) error {
	return d.redisClient.Del(key).Err()
}

func (d *DBConnection) HDel(key, field string) error {
	return d.redisClient.HDel(key, field).Err()
}
