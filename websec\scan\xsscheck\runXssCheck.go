package xsscheck

import (
	"errors"
)

var XSSXMLList = []string{"Cross_Site_Scripting_in_path.xml", "File_Cross_Site_Scripting.xml",
	"Cross_Site_Scripting.xml"}

func RunXssCheck(url string, method string, cookie string, userAgent string, data string,
	affect string, xssPayLoadPath string) (result []*XSSPayloadResult, requestString string, err error) {

	xssChecker, err := CreateXSSChecker(false, true, xssPayLoadPath)
	if err != nil {
		return
	}

	xssChecker.Cookie = cookie
	xssChecker.UserAgent = userAgent
	if affect == "file" || affect == "directory" {

		result, requestString, err = xssChecker.CheckDirectoryURL(url)

		return
	}
	if affect == "parameter" {
		result, requestString, err = xssChecker.CheckURL(url, method, data)
		return
	}
	err = errors.New("not support affect")
	return
}
