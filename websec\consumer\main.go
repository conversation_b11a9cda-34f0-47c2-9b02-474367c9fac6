package main

import (
	// stdlog "log" // Removed: No longer using standard log directly here
	"net/http"
	_ "net/http/pprof"
	"os"
	"websec/common/db"
	"websec/common/logger"
	"websec/common/resource"
	"websec/config"
	"websec/consumer/collectors"
	"websec/consumer/filters"
	"websec/consumer/generators"
	ocr "websec/consumer/ocrv1"
	"websec/consumer/sensitiveimage"
	"websec/consumer/snapshot"
	"websec/consumer/swf"
	"websec/consumer/swf/jpex"
	"websec/detect"
	log "websec/utils/log" // Application's primary logger
	"websec/utils/stream"

	"go.uber.org/zap/zapcore"
)

var settings *config.Config
var version = "Not Set"
var XApiKey string

func main() {
	// Start pprof server in a goroutine
	// Changed port to 6061 due to "address already in use" error on 6060.
	go StartPProfServer("localhost:6060")

	log.Infoln(`

	--------------------- start -------------------

	`)
	log.Infoln("running version: ", version)
	var err error

	settings, err = config.ParseConfig()
	if err != nil {
		log.Error("failed to parse config:", err)
		return
	}

	XApiKey = settings.BaizeXapikey

	logger.NewLogger(settings.Log.Dir, zapcore.InfoLevel, 128, 100, 7, true, settings.Log.FileName)

	switch settings.RunMode {
	case ModeGenerator:
		runAsGenerator()
	case ModeFilter:
		runAsFilter()
	case ModeCollector:
		runAsCollector()
	case ModeSnapshot:
		runAsSnapshot()
	case ModeOcr:
		runAsOcr()
	case ModeSensitiveImage:
		runAsSensitiveImagePreProcess()
	case ModeSwfURL:
		runAsSwfProcess()
	default:
		log.Warnln("unknown run mode:", settings.RunMode)
		log.Infoln("current supported mode:")
		for _, mode := range []RunMode{
			ModeGenerator, ModeFilter, ModeCollector,
		} {
			log.Infoln("    - ", mode)
		}
		return
	}

	// Signal handling logs will use the application's logger
	log.Infoln("Application main logic configured. Setting up signal handling...")
	setupSignal(reloadNull, func() {
		log.Infoln("Received termination signal. Closing application...")
		os.Exit(0)
	})

	log.Infoln("Application running. Waiting for signals...")
	select {}
}

func runAsGenerator() {
	openDebug()

	var err error

	dbConnection, err := db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis),
		db.WithHBaseConfig(settings.HBase),
		db.WithAssetCache(),
		db.WithSourceCodeTime(settings.Consumer.SourceCodeSaveTime),
	)
	if err != nil {
		log.Errorln("failed to create db connection: ", err)
		return
	}

	err = dbConnection.ScriptInit()
	if err != nil {
		log.Errorln("failed to ScriptInit: ", err)
		return
	}

	if err = resource.LoadDefaultBlackSensitiveWords(dbConnection.GetMongoDatabase()); err != nil {
		return
	}
	if err = resource.LoadEmphasizeWords(dbConnection.GetMongoDatabase(), settings.Jieba); err != nil {
		return
	}
	if err = resource.LoadSensitiveWordGroups(dbConnection.GetMongoDatabase()); err != nil {
		return
	}
	if err = resource.LoadIgnoredBlackLinkDomains(dbConnection.GetMongoDatabase()); err != nil {
		return
	}
	if err = resource.LoadHostURLSum(dbConnection.GetMongoDatabase()); err != nil {
		return
	}

	resource.LoadWordsPeriodically(dbConnection.GetMongoDatabase(), settings.Jieba, settings.Period.BlackSensitiveWords)
	resource.LoadIgnoredBlackLinkDomainsPeriodically(dbConnection.GetMongoDatabase(), settings.Period.IgnoredBlackLinkDomains)

	consumer, producer, err := newConsumerProducer(
		&stream.ConsumerOptions{
			Brokers:              settings.Kafka.Brokers,
			Group:                GroupGenerator,
			FetchMessageMaxBytes: settings.Kafka.FetchMessageMaxBytes,
		},
		&stream.ProducerOptions{
			Brokers:         settings.Kafka.Brokers,
			MessageMaxBytes: settings.Kafka.MessageMaxBytes,
		},
	)
	if err != nil {
		log.Errorln("failed to create kafka consumer or producer:", err)
		return
	}

	// print current working directory
	dir, err := os.Getwd()
	if err != nil {
		log.Errorln("failed to get current working directory:", err)
		return
	}
	log.Infoln("---------------- current working directory", dir)
	log.Infoln("---------------- settings.Ocr.OcrAPIAddress", settings.Ocr.OcrAPIAddress)

	generator, err := generators.NewGenerator(
		consumer,
		producer,
		generators.WithDetectConfig(settings.Detecter),
		generators.WithDBConnection(dbConnection),
		generators.WithConsumeConcurrency(settings.Consumer.GeneratorConcurrency),
		generators.WithSaveContentDepth(settings.Consumer.SaveDepth),
		generators.WithUseHBase(settings.HBase.ClientType),
		generators.WithNewURL(settings.Consumer.NewURL),
		generators.WithDetectOcrPath(settings.Ocr.OcrAPIAddress),
	)
	if err != nil {
		log.Errorln("failed to create generator instance:", err)
		return
	}

	setupSignal(func() {
		resource.LoadSensitiveWordGroups(dbConnection.GetMongoDatabase())
		resource.LoadEmphasizeWords(dbConnection.GetMongoDatabase(), settings.Jieba)
		resource.LoadSensitiveWordGroups(dbConnection.GetMongoDatabase())
	}, func() { generator.Stop() })

	generator.Run()
}

func newConsumerProducer(consumerConfig *stream.ConsumerOptions, producerConfig *stream.ProducerOptions) (*stream.Consumer, *stream.Producer, error) {
	consumer, err := stream.NewConsumer(consumerConfig)
	if err != nil {
		return nil, nil, err
	}

	producer, err := stream.NewProducer(producerConfig)
	if err != nil {
		consumer.Close()
		return nil, nil, err
	}
	return consumer, producer, nil
}

func runAsFilter() {
	openDebug()

	consumer, producer, err := newConsumerProducer(
		&stream.ConsumerOptions{
			Brokers:              settings.Kafka.Brokers,
			Group:                GroupFilter,
			FetchMessageMaxBytes: settings.Kafka.FetchMessageMaxBytes,
		},
		&stream.ProducerOptions{
			Brokers:         settings.Kafka.Brokers,
			MessageMaxBytes: settings.Kafka.MessageMaxBytes,
		},
	)
	if err != nil {
		log.Errorln("failed to create consumer or producer:", err)
		return
	}

	dbConnection, err := db.NewDBConnection(
		db.WithRedisConfig(settings.Redis),
		db.WithMongoConfig(settings.MongoDB),
	)

	if err != nil {
		log.Errorln("failed to initialize db connection:", err)
		return
	}

	err = dbConnection.ScriptInit()
	if err != nil {
		log.Errorln("failed to ScriptInit:", err)
		return
	}

	filter, err := filters.NewFilter(consumer, producer,
		filters.WithConsumeConcurrency(settings.Consumer.FilterConcurrency),
		filters.WithDBConnection(dbConnection),
	)
	if err != nil {
		log.Errorln("failed to create filter instance:", err)
		return
	}
	setupSignal(reloadNull, func() { filter.Stop() })

	filter.Run()
}

func runAsCollector() {
	openDebug()

	consumer, producer, err := newConsumerProducer(
		&stream.ConsumerOptions{
			Brokers:              settings.Kafka.Brokers,
			Group:                GroupCollector,
			FetchMessageMaxBytes: settings.Kafka.FetchMessageMaxBytes,
		},
		&stream.ProducerOptions{
			Brokers:         settings.PlatformKafka.Brokers,
			MessageMaxBytes: settings.PlatformKafka.MessageMaxBytes,
		},
	)
	if err != nil {
		log.Errorln("failed to create kafka consumer or producer:", err)
		return
	}

	var dbConnection *db.DBConnection
	if settings.CollectorType == "all" || settings.CollectorType == "hbase" {
		settings.HBase.ClientType = 2
		dbConnection, err = db.NewDBConnection(
			db.WithMongoConfig(settings.MongoDB),
			db.WithHBaseConfig(settings.HBase),
		)
	} else {
		dbConnection, err = db.NewDBConnection(
			db.WithMongoConfig(settings.MongoDB),
		)
	}

	if err != nil {
		log.Errorln("failed to create db connection:", err)
		return
	}

	collector, err := collectors.NewCollector(
		consumer,
		producer,
		collectors.WithDBConnection(dbConnection),
		collectors.WithConsumeConcurrency(settings.Consumer.CollectorConcurrency),
		collectors.WithNewURL(settings.Consumer.NewURL),
	)
	if err != nil {
		log.Errorln("failed to create collector instance:", err)
		return
	}
	setupSignal(reloadNull, func() { collector.Stop() })

	collector.Run(settings.CollectorType)

	// 添加图片静态文件服务
	fs := http.FileServer(http.Dir("/home/<USER>"))
	http.Handle("/images/", http.StripPrefix("/images/", fs))
}

func runAsSnapshot() {
	openDebug()

	consumer, producer, err := newConsumerProducer(
		&stream.ConsumerOptions{
			Brokers:              settings.Kafka.Brokers,
			Group:                GroupSnapshot,
			FetchMessageMaxBytes: settings.Kafka.FetchMessageMaxBytes,
			MaxMessageKBytes:     settings.Snapshot.MaxMessageKBytes,
			QueuedMinMessages:    settings.Snapshot.QueuedMinMessages,
		},
		&stream.ProducerOptions{
			Brokers:         settings.Kafka.Brokers,
			MessageMaxBytes: settings.Kafka.MessageMaxBytes,
		},
	)
	if err != nil {
		log.Errorln("failed to create consumer or producer:", err)
		return
	}

	dbConnection, err := db.NewDBConnection(
		db.WithHBaseConfig(settings.HBase),
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis),
	)
	if err != nil {
		log.Errorln("failed to initialize db connection:", err)
	}

	processor, err := snapshot.NewProcessor(
		consumer,
		producer,
		snapshot.WithDBConnection(dbConnection),
		snapshot.WithConsumeConcurrency(int64(settings.Snapshot.Parallel)),
		snapshot.WithSnapshotConfig(&settings.Snapshot),
		snapshot.WithImgUploader(&settings.S3Bucket, settings.API.Addr),
		snapshot.WithIgnoredHosts(&settings.Snapshot),
	)
	if err != nil {
		log.Errorln("failed to create snapshot processor: ", err)
		return
	}

	setupSignal(reloadNull, func() { processor.Stop() })
	processor.Run()
}

func openDebug() {
	if settings.DebugHost != "" {
		go func() {
			log.Infoln(http.ListenAndServe(settings.DebugHost, nil))
		}()
	}
}

func runAsOcr() {
	consumer, producer, err := newConsumerProducer(
		&stream.ConsumerOptions{
			Brokers:              settings.Kafka.Brokers,
			Group:                GroupOcr,
			FetchMessageMaxBytes: settings.Kafka.FetchMessageMaxBytes,
		},
		&stream.ProducerOptions{
			Brokers:         settings.Kafka.Brokers,
			MessageMaxBytes: settings.Kafka.MessageMaxBytes,
		},
	)
	if err != nil {
		log.Errorln("failed to create consumer or producer:", err)
		return
	}

	dbConnection, err := db.NewDBConnection(
		db.WithHBaseConfig(settings.HBase),
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis),
	)
	if err != nil {
		log.Errorln("failed to initialize db connection:", err)
	}

	if err = resource.LoadDefaultBlackSensitiveWords(dbConnection.GetMongoDatabase()); err != nil {
		return
	}

	options := &ocr.Options{
		Consumer:         consumer,
		Producer:         producer,
		DBConnection:     dbConnection,
		SensitiveMatcher: resource.DefaultSensitiveWordsMatcher(),
		FghkMatcher:      detect.NewFghkMatcher(),
		Concurrency:      settings.Ocr.Concurrency,
		OcrAPIAddress:    settings.Ocr.OcrAPIAddress,
		OcrAPIKey:        settings.Ocr.OcrAPIKey,
		OcrAppKey:        settings.Ocr.OcrAppKey,
	}
	processor, err := ocr.NewProcessor(options)
	if err != nil {
		log.Errorln("failed to create ocr processor:", err)
		return
	}
	setupSignal(reloadNull, func() { processor.Stop() })
	processor.Run()
}

func runAsSensitiveImagePreProcess() {
	consumer, producer, err := newConsumerProducer(
		&stream.ConsumerOptions{
			Brokers:              settings.Kafka.Brokers,
			Group:                GroupSensitiveImage,
			FetchMessageMaxBytes: settings.Kafka.FetchMessageMaxBytes,
		},
		&stream.ProducerOptions{
			Brokers:         settings.Kafka.Brokers,
			MessageMaxBytes: settings.Kafka.MessageMaxBytes,
		},
	)
	if err != nil {
		log.Errorln("failed to create consumer or producer:", err)
		return
	}

	dbConnection, err := db.NewDBConnection(
		db.WithHBaseConfig(settings.HBase),
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis),
	)
	if err != nil {
		log.Errorln("failed to initialize db connection:", err)
	}

	options := &sensitiveimage.Options{
		Consumer:     consumer,
		Producer:     producer,
		DBConnection: dbConnection,
		Concurrency:  settings.SensitiveImage.Concurrency,
	}
	processor, err := sensitiveimage.NewProcessor(options)
	if err != nil {
		log.Errorln("failed to create ocr processor:", err)
		return
	}
	setupSignal(reloadNull, func() { processor.Stop() })
	processor.Run()
}

func runAsSwfProcess() {
	consumer, producer, err := newConsumerProducer(
		&stream.ConsumerOptions{
			Brokers:              settings.Kafka.Brokers,
			Group:                GroupSensitiveImage,
			FetchMessageMaxBytes: settings.Kafka.FetchMessageMaxBytes,
		},
		&stream.ProducerOptions{
			Brokers:         settings.Kafka.Brokers,
			MessageMaxBytes: settings.Kafka.MessageMaxBytes,
		},
	)
	if err != nil {
		log.Errorln("failed to create consumer or producer:", err)
		return
	}

	dbConnection, err := db.NewDBConnection(
		db.WithHBaseConfig(settings.HBase),
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis),
	)
	if err != nil {
		log.Errorln("failed to initialize db connection:", err)
	}

	options := &swf.Options{
		Consumer:     consumer,
		Producer:     producer,
		DBConnection: dbConnection,
		Concurrency:  settings.SwfCheck.Concurrency,
		JpexClient:   jpex.NewJpexClient(settings.SwfCheck.JpexURL),
	}
	processor, err := swf.NewProcessor(options)
	if err != nil {
		log.Errorln("failed to create ocr processor:", err)
		return
	}
	setupSignal(reloadNull, func() { processor.Stop() })
	processor.Run()
}
