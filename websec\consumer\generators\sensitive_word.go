package generators

import (
	"websec/common"
	"websec/common/resource"
	"websec/common/schema"
	"websec/detect"
	"websec/utils/log"
)

func (generator *Generator) getSensitiveResult(page *common.Webpage, result *detect.DetectResult,
	customSensitiveWords []string, versionTime int64) []*schema.RawSensitiveWordResult {

	if len(result.SensitiveWords) == 0 {
		return nil
	}

	results := make([]*schema.RawSensitiveWordResult, 0, 10)
	customWordMap := make(map[string]bool)
	for _, v := range customSensitiveWords {
		customWordMap[v] = true
	}

	for _, v := range result.SensitiveWords {
		wordResult := new(schema.RawSensitiveWordResult)

		if v.URL != page.URL {
			wordResult.Host = v.GetHost()
			wordResult.MainframeURL = page.MainFrameURL
			wordResult.URL = v.URL
			wordResult.URLHash = v.URLHash
			wordResult.FoundAt = page.CrawledAt
			wordResult.VersionTime = versionTime
			wordResult.Content = page.Content
			wordResult.ContentHash = page.ContentHash()
			wordResult.Results = make([]schema.FoundSensitiveWord, 0, len(v.SensitiveWords))

			sensitiveWords := make([]string, len(v.SensitiveWords))

			for k, sw := range v.SensitiveWords {
				sensitiveWords[k] = sw.Word
				_, ok := customWordMap[sw.Word]
				wr := schema.FoundSensitiveWord{
					Word:         sw.Word,
					Position:     sw.Position,
					Context:      sw.Context,
					ContextHash:  "", // FIXME
					IsCustomized: ok,
				}

				if v.FromImage {
					wr.FromOcr = true
				}
				wordResult.Results = append(wordResult.Results, wr)
			}

			wordResult.Strength = resource.GetSensitiveWordStrength(sensitiveWords, customSensitiveWords)

			if wordResult.Strength != -1 {
				results = append(results, wordResult)
				err := generator.dbConnection.SaveSourceCode(page.URL, page.URLHash(), versionTime, page.Content)
				if err != nil {
					log.Errorln("save sourcecode err:", err)
				}
				page.SaveToMongo = true
			}
		} else {
			wordResult.Host = page.Host
			wordResult.MainframeURL = page.MainFrameURL
			wordResult.URL = v.URL
			wordResult.URLHash = v.URLHash
			wordResult.FoundAt = page.CrawledAt
			wordResult.VersionTime = versionTime
			wordResult.Content = page.Content
			wordResult.ContentHash = page.ContentHash()
			wordResult.Results = make([]schema.FoundSensitiveWord, 0, len(v.SensitiveWords))

			sensitiveWords := make([]string, len(v.SensitiveWords))

			for k, sw := range v.SensitiveWords {
				sensitiveWords[k] = sw.Word
				_, ok := customWordMap[sw.Word]
				wr := schema.FoundSensitiveWord{
					Word:         sw.Word,
					Position:     sw.Position,
					Context:      sw.Context,
					ContextHash:  "", // FIXME
					IsCustomized: ok,
				}

				if v.FromImage {
					wr.FromOcr = true
				}
				wordResult.Results = append(wordResult.Results, wr)
			}

			wordResult.Strength = resource.GetSensitiveWordStrength(sensitiveWords, customSensitiveWords)

			if wordResult.Strength != -1 {
				results = append(results, wordResult)
				err := generator.dbConnection.SaveSourceCode(page.URL, page.URLHash(), versionTime, page.Content)
				if err != nil {
					log.Errorln("save sourcecode err:", err)
				}
				page.SaveToMongo = true
			}
		}
	}

	return results
}

func (generator *Generator) getOcrSensitiveWordResult(page *common.Webpage, result *detect.DetectResult, customSensitiveWords []string) []*schema.RawOcrSensitiveWordResult {
	ocrSensitiveDocList := make([]*schema.RawOcrSensitiveWordResult, 0, 2)

	// Create a map for faster lookup of custom sensitive words
	customWordMap := make(map[string]bool)
	for _, v := range customSensitiveWords {
		customWordMap[v] = true
	}

	for _, item := range result.OcrSensitiveWords {
		if len(item.Results) == 0 {
			continue
		}

		ocrResult := &schema.RawOcrSensitiveWordResult{
			Mainframe:   page.URL,
			URL:         item.URL,
			ContentHash: item.ContentHash,
			FoundAt:     page.CrawledAt,
			Results:     make([]*schema.OcrWordResult, 0, 2),
		}

		for _, result := range item.Results {
			detail := make([]*schema.WordResult, 0, 2)

			for _, word := range result.SensitiveWords {
				_, isCustom := customWordMap[word.Word]
				wr := &schema.WordResult{
					Word:         word.Word,
					Position:     word.Position,
					Context:      word.Context,
					IsCustomized: isCustom,
				}
				detail = append(detail, wr)
			}

			imageResult := &schema.OcrWordResult{
				ImageURL:     result.ImageURL,
				ImageURLHash: result.ImageURLHash,
				ImageAddress: result.ImageAddress,
				Content:      result.Content,
				Results:      detail,
			}

			ocrResult.Results = append(ocrResult.Results, imageResult)
		}

		ocrSensitiveDocList = append(ocrSensitiveDocList, ocrResult)
	}

	return ocrSensitiveDocList
}
