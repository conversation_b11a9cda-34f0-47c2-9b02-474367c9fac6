#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifndef ACSMX2S_H
#define ACSMX2S_H

#define MAX_ALPHABET_SIZE 256

#define AC32

#ifdef AC32

typedef unsigned int acstate_t;
#define ACSM_FAIL_STATE2 0xffffffff

#else

typedef unsigned short acstate_t;
#define ACSM_FAIL_STATE2 0xffff

#endif

typedef struct _acsm_pattern2
{
  struct _acsm_pattern2 *next;

  unsigned char *patrn;
  unsigned char *casepatrn;
  int n;
  int nocase;
  int offset;
  int depth;
  int negative;
  void *udata;
  int iid;
  void *rule_option_tree;
  void *neg_list;

} ACSM_PATTERN2;

typedef struct trans_node_s
{

  acstate_t key;

  acstate_t next_state;
  struct trans_node_s *next;

} trans_node_t;

enum
{
  ACF_FULL,
  ACF_SPARSE,
  ACF_BANDED,
  ACF_SPARSEBANDS,
  ACF_FULLQ
};

enum
{
  FSA_TRIE,
  FSA_NFA,
  FSA_DFA
};

#define AC_MAX_INQ 32
typedef struct
{
  unsigned inq;
  unsigned inq_flush;
  void *q[AC_MAX_INQ];
} PMQ;

typedef struct
{

  int acsmMaxStates;
  int acsmNumStates;

  ACSM_PATTERN2 *acsmPatterns;
  acstate_t *acsmFailState;
  ACSM_PATTERN2 **acsmMatchList;

  trans_node_t **acsmTransTable;

  acstate_t **acsmNextState;
  int acsmFormat;
  int acsmSparseMaxRowNodes;
  int acsmSparseMaxZcnt;

  int acsmNumTrans;
  int acsmAlphabetSize;
  int acsmFSA;
  int numPatterns;
  void (*userfree)(void *p);
  void (*optiontreefree)(void **p);
  void (*neg_list_free)(void **p);
  PMQ q;
  int sizeofstate;
  int compress_states;

} ACSM_STRUCT2;

ACSM_STRUCT2 *acsmNew2(void (*userfree)(void *p),
                       void (*optiontreefree)(void **p),
                       void (*neg_list_free)(void **p));
int acsmAddPattern2(ACSM_STRUCT2 *p, unsigned char *pat, int n,
                    int nocase, int offset, int depth, int negative, void *id, int iid);
int acsmCompile2(ACSM_STRUCT2 *acsm,
                 int (*build_tree)(void *id, void **existing_tree),
                 int (*neg_list_func)(void *id, void **list));
struct _SnortConfig;
int acsmCompile2WithSnortConf(struct _SnortConfig *, ACSM_STRUCT2 *acsm,
                              int (*build_tree)(struct _SnortConfig *, void *id, void **existing_tree),
                              int (*neg_list_func)(void *id, void **list));
int acsmSearch2(ACSM_STRUCT2 *acsm, unsigned char *T, int n,
                int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
                void *data, int *current_state);
int acsmSearchAll2(ACSM_STRUCT2 *acsm, unsigned char *T, int n,
                   int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
                   void *data, int *current_state);
void acsmFree2(ACSM_STRUCT2 *acsm);
int acsmPatternCount2(ACSM_STRUCT2 *acsm);

void acsmCompressStates(ACSM_STRUCT2 *, int);

int acsmSelectFormat2(ACSM_STRUCT2 *acsm, int format);
int acsmSelectFSA2(ACSM_STRUCT2 *acsm, int fsa);

void acsmSetMaxSparseBandZeros2(ACSM_STRUCT2 *acsm, int n);
void acsmSetMaxSparseElements2(ACSM_STRUCT2 *acsm, int n);
int acsmSetAlphabetSize2(ACSM_STRUCT2 *acsm, int n);
void acsmSetVerbose2(void);

void acsmPrintInfo2(ACSM_STRUCT2 *p);

int acsmPrintDetailInfo2(ACSM_STRUCT2 *);
int acsmPrintSummaryInfo2(void);
void acsmx2_print_qinfo(void);
void acsm_init_summary(void);

#endif
