package filters

import (
	"encoding/json"
	"websec/common/schema"
	"websec/utils/stream"
)

func (filter *Filter) processSensitiveWord(msg *stream.Message) error {
	var doc = new(schema.RawSensitiveWordResult)
	err := json.Unmarshal(msg.Value, doc)
	if err != nil {
		return err
	}

	results := make([]schema.FoundSensitiveWord, 0, 1)
	for _, v := range doc.Results {
		results = append(results, v)
	}

	if len(results) > 0 {
		doc.Results = results
		filter.AddFinalSensitiveWord(doc)
	}

	return nil
}
