package generators

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"
	"websec/common"
	"websec/common/consts"
	"websec/common/schema"
	"websec/consumer/generators/contentdiff"
	"websec/distributed/protocol"
	"websec/utils/acsmx"
	"websec/utils/log"
	"websec/utils/stream"

	"github.com/agnivade/levenshtein"
)

const md5HeaderLen = 6

func (generator *Generator) getContentChangeFromRedis(host, urlHash string) (string, int64, error) {
	key := consts.RedisChangePagePrefix + host
	str, err := generator.dbConnection.HGetString(key, urlHash)
	if err != nil {
		return "", 0, err
	}

	strVec := strings.Split(str, ":")
	if len(strVec) != 2 {
		return "", 0, errors.New("format error, len != 2")
	}
	version, err := strconv.ParseInt(strVec[1], 10, 64)
	if err != nil {
		return "", 0, err
	}

	if len(strVec[0]) > md5HeaderLen {
		return strVec[0][0:md5HeaderLen], version, nil
	} else {
		return strVec[0], version, nil
	}
}

func (generator *Generator) saveContentChangeToRedis(host, urlHash, contentHash string, version int64) error {
	key := consts.RedisChangePagePrefix + host
	return generator.dbConnection.HSet(key, urlHash, fmt.Sprintf("%s:%d", contentHash, version))
}

func (generator *Generator) checkPageChanges(page *common.Webpage, asset *schema.Asset,
	blackMatcher *acsmx.Matcher) (int64, []*schema.RawContentChangeResult) {
	return generator.checkPageChange(page, asset, blackMatcher)
}

func (generator *Generator) checkPageChange(page *common.Webpage, asset *schema.Asset, blackMatcher *acsmx.Matcher) (int64, []*schema.RawContentChangeResult) {
	newContentHash := page.ContentHash()
	hasChangeMonitor := detectContentChange(asset)
	pageChangeResult := make([]*schema.RawContentChangeResult, 0)

	log.Debugln("========== checkPageChange =========== URL:", page.URL, "len(Content):", len(page.Content), "OldVersionTime:", page.OldVersionTime, "VersionTime:", page.VersionTime(), "SnapshotType:", asset.Options.ContentChange.SnapshotType)

	if !hasChangeMonitor || newContentHash == "" {
		return 0, nil
	}

	newContentHashHeader := newContentHash[0:md5HeaderLen]

	pageArchive := &schema.FoundPageArchiveDoc{
		AssetID:        page.AssetID,
		JobID:          page.JobID,
		Host:           page.Host,
		URL:            page.URL,
		URLHash:        page.URLHash(),
		MainFrameURL:   page.MainFrameURL,
		StatusCode:     int16(page.StatusCode),
		Header:         page.Headers,
		VersionTime:    page.VersionTime(),
		OldVersionTime: page.OldVersionTime,
		ContentHash:    newContentHash,
		Content:        page.Content,
	}

	oldContentHashHeader, versionTime, err := generator.getContentChangeFromRedis(page.JobID, page.URLHash())

	// // 处理首次扫描或Redis错误的情况
	if err != nil {
		page.SaveToMongo = true
		if err := generator.saveContentChangeToRedis(page.JobID, page.URLHash(), newContentHashHeader, page.VersionTime()); err != nil {
			log.Errorln("save content change to redis error:", err)
		}
		generator.addFoundPageArchive(pageArchive)
		return page.VersionTime(), pageChangeResult
	}

	// // 检查内容是否发生变化
	// contentChanged := newContentHashHeader != oldContentHashHeader
	// timeChanged := versionTime != pageArchive.VersionTime
	// if contentChanged || timeChanged {
	pageArchive.OldVersionTime = versionTime
	page.OldVersionTime = versionTime

	// 只有当存在旧内容时才进行差异比较
	if oldContentHashHeader != "" {
		exceptionType, exceptionContent, diff, diffPercent, err := generator.getExceptionAndDiff(page, asset, versionTime, blackMatcher)

		log.Infoln("-------- content change diff --------", page.URL, exceptionType, diffPercent, versionTime, page.VersionTime())

		if err != nil {
			log.Errorln("failed to get exception and diff:", err)
		} else if exceptionType != consts.NoException {
			pageChange := &schema.RawContentChangeResult{
				Host:             page.Host,
				MainframeURL:     page.MainFrameURL,
				URL:              page.URL,
				URLHash:          page.URLHash(),
				NewVersionTime:   page.VersionTime(),
				OldVersionTime:   versionTime,
				Diff:             diff,
				DiffPercent:      diffPercent,
				ExceptionType:    exceptionType,
				ExceptionContent: []byte(exceptionContent),
				FoundAt:          page.CrawledAt,
				AssetID:          page.AssetID,
				JobID:            page.JobID,
			}

			if err := generator.dbConnection.SaveSourceCode(page.URL, page.URLHash(), page.VersionTime(), page.Content); err != nil {
				log.Errorln("save sourcecode err:", err)
			}
			page.SaveToMongo = true
			pageChangeResult = append(pageChangeResult, pageChange)
		}
	}

	// 更新Redis中的内容
	if err := generator.saveContentChangeToRedis(page.JobID, page.URLHash(), newContentHashHeader, page.VersionTime()); err != nil {
		log.Errorln("save content change to redis error:", err)
	}
	generator.addFoundPageArchive(pageArchive)
	// }

	return page.VersionTime(), pageChangeResult
}

func (generator *Generator) getExceptionAndDiff(page *common.Webpage, asset *schema.Asset, versionTime int64, blackMatcher *acsmx.Matcher) (string, string, []byte, float64, error) {
	var exceptionContent string
	var exceptionType = consts.NoException
	var useStaticSnapshot = isStaticSnapshot(asset.Options.ContentChange.SnapshotType)
	var oldContent []byte = []byte{}
	var err error = nil

	if isImportantURL(page.URL, asset) {
		exceptionType = consts.ImportantURLContentChange
	}
	// using static snapshot
	if useStaticSnapshot {
		oldContent, err = generator.dbConnection.GetWebpageSnapshotContent(page.JobID, page.URL)
		if err != nil {
			oldContent = []byte{}
		}
	} else { // using dynamic snapshot, always compare with the recently snapshot
		oldContent, err = generator.dbConnection.GetHBaseContent(page.JobID, page.URL, page.URLHash(), versionTime)

		if err != nil {
			log.Errorf("Get webpage snapshot content error:%v url:%s urlhash:%s versiontime:%d", err, page.JobID, page.AssetID, page.URL, page.URLHash(), versionTime)
			return "", "", nil, 0, err
		}
	}

	{
		pattern := `^document\.write\s*\(\s*["']\s*\d+\s*["']\s*\)\s*;{0,1}\s*$`
		re := regexp.MustCompile(pattern)
		if re.Match(oldContent) && re.Match(page.Content) {
			return "", "", nil, 0, errors.New("ignore fool text content change")
		}
	}

	diff, err := generator.getContentDiff(oldContent, page.Content)
	if err != nil {
		log.Error(err)
		return "", "", nil, 0, err
	}

	changedContent, err := contentdiff.GetChangedContent(diff)
	if err != nil {
		log.Error(err)
		return "", "", nil, 0, err
	}

	diffPercent := 0.00

	if len(changedContent) < 1 {
		exceptionType = consts.NoException
	} else {
		t1, t2 := string(oldContent), string(page.Content)
		l1, l2 := len(t1), len(t2)
		distance := levenshtein.ComputeDistance(t1, t2)

		if l1 == 0 || l2 == 0 {
			diffPercent = 0.00
		} else {
			diffPercent = float64(distance) / float64(max(l1, l2)) * 100
		}

		if exceptionType == consts.NoException {
			exceptionType, exceptionContent = contentdiff.GetExceptionTypeAndExceptionContent(
				page.URL, diff, changedContent, oldContent, page.Content, blackMatcher)

			if exceptionType == consts.NoException && diffPercent > 0 {
				exceptionType = consts.NormalContentChangeException
			}
		}
	}
	return exceptionType, exceptionContent, diff, diffPercent, nil
}

func (generator *Generator) getImageDiff(page *common.Webpage, asset *schema.Asset, oldVersionTime int64) []*schema.RawContentChangeResult {
	var useStaticSnapshot = isStaticSnapshot(asset.Options.ContentChange.SnapshotType)
	var images []string = page.Images
	var imgChangeResult = make([]*schema.RawContentChangeResult, 0)
	var diffPercent = 0.00
	var diff = []byte{}

	// using static snapshot
	if useStaticSnapshot {
		for _, img := range images {
			imgHash := common.URLSha1(img)
			oldHash, err := generator.dbConnection.GetWebpageContentHash(page.JobID, img)

			// If error fetching old hash or it's the first time, skip change detection for this image, but still save the new one later
			isFirstScan := err != nil || oldHash == ""

			imgData, fetchErr := generator.fetchImage(img)
			if fetchErr != nil {
				log.Errorf("Failed to fetch image %s: %v", img, fetchErr)
				continue // Skip this image if fetching failed
			}

			newHash := generator.getContentHash(imgData)
			if newHash == "" {
				continue // Skip if content is empty
			}

			// Prepare document for saving, regardless of change detection result
			imgDoc := &schema.PageArchiveHBase{
				AssetID:        page.AssetID,
				JobID:          page.JobID,
				Host:           page.Host, // TODO: Consider if Host should be parsed from img URL if external
				MainFrameURL:   page.MainFrameURL,
				URL:            img,
				URLHash:        imgHash,
				StatusCode:     http.StatusOK, // Assuming OK if fetch succeeded
				Header:         "",            // Image headers might be useful but aren't fetched currently
				VersionTime:    page.VersionTime(),
				OldVersionTime: oldVersionTime, // Use the passed oldVersionTime
				ContentHash:    newHash,
				Content:        imgData,
			}
			// Save the fetched image data and hash for future comparisons
			// Consider if SaveHBaseImageContent is the right place for static snapshot data
			generator.dbConnection.SaveHBaseImageContent(imgDoc)

			// Only create a change record if it's not the first scan and hashes differ
			if !isFirstScan && oldHash != newHash {
				diffPercent = 100 // Assuming 100% diff if hashes change

				imgChange := &schema.RawContentChangeResult{
					Host:              imgDoc.Host, // Use host from imgDoc
					MainframeURL:      page.MainFrameURL,
					URL:               img,
					URLHash:           imgHash,
					NewVersionTime:    page.VersionTime(),
					OldVersionTime:    oldVersionTime, // Use the correct old version time
					Diff:              diff,           // Diff is empty for images, hash difference implies change
					DiffPercent:       diffPercent,
					ExceptionType:     consts.NormalImageChangeException,
					ExceptionContent:  []byte{},
					FoundAt:           page.CrawledAt,
					AssetID:           page.AssetID,
					JobID:             page.JobID,
					OldSnapshot:       oldHash,
					NewSnapshot:       newHash,
					UseStaticSnapshot: useStaticSnapshot,
				}

				imgChangeResult = append(imgChangeResult, imgChange)
			}
		}
	} else { // using dynamic snapshot, always compare with the recently snapshot
		for _, img := range images {
			imgHash := common.URLSha1(img)

			// TODO: Ideally, GetHBaseContentHash should accept oldVersionTime to fetch the specific previous hash.
			// Assuming current implementation fetches the *latest* hash before the current one.
			oldHash, err := generator.dbConnection.GetHBaseContentHash(page.JobID, img)

			// If error fetching old hash or it's the first time, skip change detection
			isFirstScan := err != nil || oldHash == ""

			imgData, fetchErr := generator.fetchImage(img)
			if fetchErr != nil {
				log.Errorf("Failed to fetch image %s: %v", img, fetchErr)
				continue // Skip this image if fetching failed
			}

			newHash := generator.getContentHash(imgData)
			if newHash == "" {
				continue // Skip if content is empty
			}

			// Prepare document for saving
			imgDoc := &schema.PageArchiveHBase{
				AssetID:        page.AssetID,
				JobID:          page.JobID,
				Host:           page.Host, // TODO: Consider if Host should be parsed from img URL if external
				MainFrameURL:   page.MainFrameURL,
				URL:            img,
				URLHash:        imgHash,
				StatusCode:     http.StatusOK,
				Header:         "",
				VersionTime:    page.VersionTime(),
				OldVersionTime: oldVersionTime, // Use the passed oldVersionTime
				ContentHash:    newHash,
				Content:        imgData,
			}
			// Always save the new version
			generator.dbConnection.SaveHBaseImageContent(imgDoc)

			// Only create a change record if it's not the first scan and hashes differ
			if !isFirstScan && oldHash != newHash {
				diffPercent = 100
				imgChange := &schema.RawContentChangeResult{
					Host:              imgDoc.Host, // Use host from imgDoc
					MainframeURL:      page.MainFrameURL,
					URL:               img,
					URLHash:           imgHash,
					NewVersionTime:    page.VersionTime(),
					OldVersionTime:    oldVersionTime, // Use the correct old version time
					Diff:              diff,
					DiffPercent:       diffPercent,
					ExceptionType:     consts.NormalImageChangeException,
					ExceptionContent:  []byte{},
					FoundAt:           page.CrawledAt,
					AssetID:           page.AssetID,
					JobID:             page.JobID,
					OldSnapshot:       oldHash,
					NewSnapshot:       newHash,
					UseStaticSnapshot: useStaticSnapshot,
				}

				imgChangeResult = append(imgChangeResult, imgChange)
			}
		}
	}

	return imgChangeResult
}

func (generator *Generator) fetchImage(url string) ([]byte, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch image: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to fetch image: status code %d", resp.StatusCode)
	}

	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read image data: %w", err)
	}

	return imageData, nil
}

func (generator *Generator) getContentHash(content []byte) string {
	if len(content) > 0 {
		hash := md5.New()
		hash.Write(content)
		return hex.EncodeToString(hash.Sum(nil))
	}
	return ""
}

func (generator *Generator) getContentDiff(oldContent, newContent []byte) ([]byte, error) {
	prettyOld, err := contentdiff.PrettifyHTML(oldContent, "  ")
	if err != nil { //actully, prettifyHTML always err = nil
		log.Errorln("Failed to pretty oldContent", err)
		return nil, err
	}

	prettyNew, err := contentdiff.PrettifyHTML(newContent, " ")
	if err != nil { //actully, prettifyHTML always err = nil
		log.Errorln("Failed to pretty oldContent", err)
		return nil, err
	}

	return contentdiff.GetContentDiff(prettyOld, prettyNew, "Old", "New"), nil
}

func (generator *Generator) checkNetworkWebPageChange(page *common.Webpage) {
	for _, v := range page.NetworkWebpages {
		newContentHash := v.ContentHash()
		if newContentHash == "" {
			continue
		}

		host := page.Host
		if v.IsOuterURL {
			parts, err := url.Parse(v.URL)
			if err != nil {
				log.Errorln("host not found ", v.URL)
				continue
			}
			host = parts.Host
		}

		pageArchive := &schema.FoundPageArchiveDoc{
			AssetID:        page.AssetID,
			JobID:          page.JobID,
			Host:           host,
			MainFrameURL:   page.MainFrameURL,
			URL:            v.URL,
			URLHash:        v.URLHash(),
			StatusCode:     int16(v.StatusCode),
			Header:         v.Headers,
			VersionTime:    page.VersionTime(),
			OldVersionTime: page.VersionTime(),
			ContentHash:    v.ContentHash(),
			Content:        v.Content,
		}

		newContentHashHeader := newContentHash[0:md5HeaderLen]
		oldContentHashHeader, versionTime, err := generator.getContentChangeFromRedis(page.JobID, v.URLHash())
		if err != nil || newContentHashHeader != oldContentHashHeader {
			if err == nil && newContentHashHeader != oldContentHashHeader {
				pageArchive.OldVersionTime = versionTime
			}
			generator.saveContentChangeToRedis(page.JobID, v.URLHash(), newContentHashHeader, page.VersionTime())
			generator.addFoundPageArchive(pageArchive)
		}
	}
}

func (generator *Generator) checkOuterPageChanges(page *common.Webpage, asset *schema.Asset, blackMatcher *acsmx.Matcher) (int64, []*schema.RawContentChangeResult) {
	useStaticSnapshot := isStaticSnapshot(asset.Options.ContentChange.SnapshotType)
	pageChangeResult := make([]*schema.RawContentChangeResult, 0)
	hasChangeMonitor := detectContentChange(asset)
	versionTime := int64(0)

	if !hasChangeMonitor {
		return 0, nil
	}

	for _, outer := range page.OuterWebpages {
		for _, v := range outer.Frames {
			urlHash := v.URLHash()
			newContentHash := v.ContentHash()

			if newContentHash == "" {
				continue
			}

			parts, err := url.Parse(v.URL)
			if err != nil {
				log.Errorln("checkOuterPageChanges url.Parse error", err)
				continue
			}

			pageArchive := &schema.FoundPageArchiveDoc{
				AssetID:        page.AssetID,
				JobID:          page.JobID,
				Host:           parts.Host,
				MainFrameURL:   v.MainFrameURL,
				URL:            v.URL,
				URLHash:        urlHash,
				StatusCode:     int16(v.StatusCode),
				Header:         outer.Headers,
				VersionTime:    page.VersionTime(),
				OldVersionTime: page.VersionTime(),
				ContentHash:    newContentHash,
				Content:        v.Content,
			}

			generator.addHotPageArchive(pageArchive)

			newContentHashHeader := newContentHash[0:md5HeaderLen]
			oldContentHashHeader, versionTime, err := generator.getContentChangeFromRedis(page.JobID, urlHash)

			if err != nil || newContentHashHeader != oldContentHashHeader {
				if err == nil && newContentHashHeader != oldContentHashHeader {
					pageArchive.OldVersionTime = versionTime
				}
				generator.saveContentChangeToRedis(page.JobID, urlHash, newContentHashHeader, page.VersionTime())
				generator.addFoundPageArchive(pageArchive)

				if useStaticSnapshot {
					log.Debugln("checkOuterPageChanges: use static snapshot")
				} else {
					log.Debugln("checkOuterPageChanges: dynamic snapshot comparison")
				}
			}

			if newContentHashHeader != oldContentHashHeader || versionTime != pageArchive.OldVersionTime {
				pageArchive.OldVersionTime = versionTime
				page.OldVersionTime = versionTime

				if oldContentHashHeader != "" && newContentHashHeader != "" {
					outerPage := common.Webpage{
						URL:     v.URL,
						Content: v.Content,
						JobID:   page.JobID,
						AssetID: page.AssetID,
						Host:    parts.Host,
					}
					exceptionType, exceptionContent, diff, diffPercent, err := generator.getExceptionAndDiff(&outerPage, asset, versionTime, blackMatcher)
					if err != nil {
						log.Debugln("checkOuterPageChanges outer link failed to get exception and diff: ", exceptionType, err)
					}

					if diffPercent > 0 && exceptionType != consts.NoException {
						pageChange := &schema.RawContentChangeResult{
							Host:             parts.Host,
							MainframeURL:     v.MainFrameURL,
							URL:              v.URL,
							URLHash:          urlHash,
							NewVersionTime:   page.VersionTime(),
							OldVersionTime:   versionTime,
							Diff:             diff,
							DiffPercent:      diffPercent,
							ExceptionType:    exceptionType,
							ExceptionContent: []byte(exceptionContent),
							FoundAt:          page.CrawledAt,
							AssetID:          page.AssetID,
							JobID:            page.JobID,
						}

						err = generator.dbConnection.SaveSourceCode(v.URL, urlHash, page.VersionTime(), v.Content)
						if err != nil {
							log.Errorln("checkOuterPageChanges save sourcecode err:", err)
						}
						page.SaveToMongo = true
						pageChangeResult = append(pageChangeResult, pageChange)
					}
				}

				generator.saveContentChangeToRedis(page.JobID, urlHash, newContentHashHeader, page.VersionTime())
				generator.addFoundPageArchive(pageArchive)
			}
		}
	}

	return versionTime, pageChangeResult
}

func (generator *Generator) processFinishedTask(msg *stream.Message) error {
	finish := protocol.TaskFinishBody{}
	err := json.Unmarshal(msg.Value, &finish)

	if err != nil {
		return err
	}

	timer := time.After(5 * time.Second)

	<-timer

	go func() {
		delete(generator.processedURLCounter, finish.AssetID.Hex())

		redisClient := generator.dbConnection.GetRedisPool()
		storedData, err := redisClient.Get("total_link_counter_" + finish.AssetID.Hex()).Bytes()
		if err != nil {
			fmt.Println("Error retrieving data from Redis:", err)
		}
		var detail TotalLinkDetail
		err = json.Unmarshal([]byte(storedData), &detail)
		if err != nil {
			fmt.Println("Error decoding JSON:", err)
		}

		task, err := generator.dbConnection.GetTaskByID(finish.TaskID.Hex(), true)

		if err != nil {
			return
		}

		loc, err := time.LoadLocation("Asia/Shanghai")
		if err != nil {
			log.Errorln(err)
		}

		summary := protocol.TaskSummaryBody{
			AssetID:    finish.AssetID,
			TaskID:     finish.TaskID,
			StartTime:  task.StartAt.In(loc).Format("2006-01-02 15:04:05"),
			EndTime:    task.FinishedAt.In(loc).Format("2006-01-02 15:04:05"),
			TotalLinks: detail.TotalLinks,
			TotalFail:  detail.TotalFail,
		}

		generator.producer.ProduceBytes(consts.TopicTaskSummary, &summary)
	}()

	log.Infoln("consumer.generators.process_webpage.processFinishedTask - AssetID - TaskID ", finish.AssetID.Hex(), finish.TaskID.Hex())

	return nil
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func isStaticSnapshot(snapshotType int) bool {
	return snapshotType == 0 || snapshotType == -1
}

func detectContentChange(asset *schema.Asset) bool {
	for _, v := range asset.MonitorTypes {
		if v == consts.MonitorTypeContentChange {
			return true
		}
	}
	return false
}

func isImportantURL(rawURL string, asset *schema.Asset) bool {
	for _, v := range asset.ImportantURLS {
		if v == rawURL {
			return true
		}
	}
	return false
}
