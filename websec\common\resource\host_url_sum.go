package resource

import (
	"context"
	"sync"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	urlSumMap map[string]*schema.HostURLSum
	lock      sync.RWMutex
	minNum    int32 = 10000
	maxNum    int32 = 1000000
)

func LoadHostURLSum(mongodb *mongo.Database) error {
	tmpURLSumMap := make(map[string]*schema.HostURLSum)

	cursor, err := mongodb.Collection(consts.CollectionHostURLSum).Find(context.Background(), bson.M{})
	if err != nil {
		log.Errorln("failed to load sensitive words.", err)
		return err
	}
	defer cursor.Close(context.Background())

	for cursor.Next(context.Background()) {
		doc := new(schema.HostURLSum)
		err = cursor.Decode(doc)
		if err != nil {
			log.Errorln(err)
			continue
		}
		
		if doc.Sum == 0 {
			doc.Sum = minNum
		}

		if doc.Sum > maxNum {
			doc.Sum = maxNum
		}

		doc.Precision = getPrecisionBySum(doc.Sum)
		tmpURLSumMap[doc.Host] = doc
	}

	log.Infoln("load hosturlsum total:", len(tmpURLSumMap))
	urlSumMap = tmpURLSumMap
	return nil
}

func GetHostURLSum(host string) *schema.HostURLSum {
	v := getHostURLSum(host)
	if v != nil {
		return v
	}

	return addHostURLSum(host)
}

func getHostURLSum(host string) *schema.HostURLSum {
	lock.RLock()
	defer lock.RUnlock()
	if v, ok := urlSumMap[host]; ok {
		return v
	}
	return nil
}

func addHostURLSum(host string) *schema.HostURLSum {
	doc := &schema.HostURLSum{
		Host:      host,
		Sum:       minNum,
		Precision: getPrecisionBySum(minNum),
	}

	lock.Lock()
	defer lock.Unlock()
	urlSumMap[host] = doc

	return doc
}

func getPrecisionBySum(sum int32) float32 {
	if sum > 100000 {
		return 0.00001
	} else if sum >= 10000 {
		return 0.0001
	} else if sum >= 1000 {
		return 0.001
	} else {
		return 0.01
	}
}
