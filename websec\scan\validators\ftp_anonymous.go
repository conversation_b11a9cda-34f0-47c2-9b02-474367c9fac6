package validators

import (
	"bytes"
	"context"
	"net/url"
	"os/exec"
	"regexp"
	"time"
)

var ftpVulPattern = regexp.MustCompile(`\b230\b`)

func ValidateFTPAnonymous(args *ValidationArgs) (*ValidationResult, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	u, err := url.Parse(args.URL)
	if err != nil {
		return nil, err
	}

	hostname := u.Hostname()
	params := []string{"-nv", hostname}

	cmd := exec.CommandContext(ctx, "ftp", params...)
	cmd.Stdin = bytes.NewReader([]byte("user anonymous anonymous\nls -al\nbye\n"))
	output, err := cmd.CombinedOutput()
	if ftpVulPattern.Match(output) {
		return &ValidationResult{
			Status:       VulIsValid,
			Command:      "ftp -nv " + hostname + "<<<$'user anonymous test\nls -al\nbye'",
			Output:       string(output),
			Highlight:    "230",
			NeedSnapshot: true,
		}, nil
	}
	return &ValidationResult{Status: VulIsInvalid, Output: string(output)}, nil
}
