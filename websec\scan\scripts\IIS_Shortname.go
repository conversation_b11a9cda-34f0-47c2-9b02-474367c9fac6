package scripts

import (
	"strings"
	"time"

	"github.com/valyala/fasthttp"
)

var iisMagicFinalPartList = []string{"/a.aspx", "/a.shtml", "/a.asp", "/a.asmx", "/a.ashx", "/a.config", "/a.php", "/a.jpg", "/a.xxx", ""}
var iisRequestHeader = map[string]string{
	"User-Agent":      "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:54.0) Gecko/20100101 Firefox/54.0",
	"Accept":          "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
	"Accept-Language": "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3",
	"Accept-Encoding": "gzip, deflate",
}

func IISShortname(args *ScriptScanArgs) (*ScriptScanResult, error) {
	rawurl := constructURL(args, "/")

	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.Header.SetMethod("GET")
	for key, value := range iisRequestHeader {
		request.Header.Set(key, value)
	}

	var err error
	for _, path := range iisMagicFinalPartList {

		request.SetRequestURI(rawurl + "*~1*" + path)
		err = httpClient.DoTimeout(request, response, 5*time.Second)
		if err != nil || response.StatusCode() != 404 {
			continue
		}

		request.SetRequestURI(rawurl + "/webscan360noThisFile*~1*" + path)
		err = httpClient.DoTimeout(request, response, 5*time.Second)
		if err != nil || response.StatusCode() != 400 || strings.Contains(string(response.Body()), "System.Web.HttpRequest.ValidateInputIfRequiredByConfig()") {
			continue
		}

		request.SetRequestURI(rawurl + "/eliFsihTon063nacsbew*~1*" + path)
		err = httpClient.DoTimeout(request, response, 5*time.Second)
		if err != nil || response.StatusCode() != 400 {
			continue
		}
		return &ScriptScanResult{Vulnerable: true, Output: rawurl + "webscan360noThisFile*~1*" + path}, nil
	}
	if err != nil {
		return nil, err
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("Server_ShortName.xml", IISShortname)
}
