package scripts

import (
	"bufio"
	"bytes"
	"net"
	"net/http"
	"strconv"
	"time"

	"github.com/valyala/fasthttp"
)

func JetLeakRemoteLeakageOfBuffers(args *ScriptScanArgs) (*ScriptScanResult, error) {
	rawurl := constructURL(args, "/")

	request := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(request)

	request.SetRequestURI(rawurl)
	request.Header.SetMethod(http.MethodPost)
	request.Header.SetUserAgent(scriptUserAgent)
	request.Header.SetBytesV("Referer", []byte{0x00})

	conn, err := net.DialTimeout("tcp", args.Host+":"+strconv.Itoa(int(args.Port)), 5*time.Second)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	err = conn.SetWriteDeadline(time.Now().Add(5 * time.Second))
	if err != nil {
		return nil, err
	}

	_, err = request.WriteTo(conn)
	if err != nil {
		return nil, err
	}

	err = conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	if err != nil {
		return nil, err
	}

	line, _, err := bufio.NewReader(conn).ReadLine()
	if err != nil {
		return nil, err
	}

	if bytes.HasSuffix(line, []byte("400 Illegal character 0x0 in state")) {
		return &ScriptScanResult{Vulnerable: true, Output: rawurl, Body: line}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("jetleak_remote_leakage_of_buffers_vul.xml", JetLeakRemoteLeakageOfBuffers)
}
