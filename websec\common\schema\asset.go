package schema

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Asset struct {
	ID            primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty" codec:"_id,omitempty"`
	Host          string             `json:"host" bson:"host" codec:"host"`
	URL           string             `json:"url" bson:"url" codec:"url"`
	JobID         string             `json:"job_id" bson:"job_id" codec:"job_id"`
	Name          string             `json:"name" bson:"name" codec:"name"`
	Entries       []string           `json:"entries" bson:"entries" codec:"entries"`
	ImportantURLS []string           `json:"important_urls,omitempty" bson:"important_urls" codec:"important_urls"`
	MonitorTypes  []string           `json:"monitor_types" bson:"monitor_types" codec:"monitor_types"`
	Status        int32              `json:"status" bson:"status" codec:"status"`
	Options       assetOptions       `json:"options" bson:"options" codec:"options"`
	CreatedAt     time.Time          `json:"created_at,omitempty" bson:"created_at,omitempty" codec:"created_at,omitempty"`
	UpdatedAt     time.Time          `json:"updated_at,omitempty" bson:"updated_at,omitempty" codec:"updated_at,omitempty"`
}

type ScheduleRange struct {
	Start       string `json:"start" bson:"start" codec:"start"`
	End         string `json:"end" bson:"end" codec:"end"`
	Concurrency int32  `json:"concurrency,omitempty" bson:"concurrency,omitempty" codec:"concurrency,omitempty"`
}

type ScheduleTimeRange struct {
	Start       time.Time `json:"start" bson:"start" codec:"start"`
	End         time.Time `json:"end" bson:"end" codec:"end"`
	Concurrency int32     `json:"concurrency" bson:"concurrency" codec:"concurrency"`
}

func genRangeTime(t time.Time, s string) time.Time {
	year, month, day := t.Date()
	parts := strings.Split(s, ":")
	if len(parts) != 2 {
		return time.Time{}
	}
	hour, err := strconv.Atoi(parts[0])
	minute, err := strconv.Atoi(parts[1])
	if err != nil {
		return time.Time{}
	}
	return time.Date(year, month, day, hour, minute, 0, 0, t.Location())
}

func GenScheduleTime(s string, countHours int64) time.Time {
	loc := time.FixedZone("CST", 8*3600)
	monitorAt, err := time.ParseInLocation("2006-01-02 15:04:05", s, loc)
	if err != nil {
		return time.Time{}
	}
	now := time.Now()

	subTimeCount := math.Ceil(now.Sub(monitorAt).Hours() / float64(countHours))
	if subTimeCount <= 0 {
		return monitorAt
	}

	return monitorAt.Add(time.Duration(float64(countHours)*subTimeCount) * time.Hour)
}

func (ran ScheduleRange) ToTimeRange(t time.Time) ScheduleTimeRange {
	start := genRangeTime(t, ran.Start)
	end := genRangeTime(t, ran.End)
	if start.After(end) {
		end = end.AddDate(0, 0, 1)
	}
	return ScheduleTimeRange{start, end, ran.Concurrency}
}

func min(a, b int32) int32 {
	if a < b {
		return a
	}
	return b
}

func (ranA ScheduleTimeRange) Merge(ranB ScheduleTimeRange) []ScheduleTimeRange {

	if ranA.Start.After(ranB.Start) {

		ranA, ranB = ranB, ranA
	}
	if ranA.End.Before(ranB.Start) {

		return []ScheduleTimeRange{ranA, ranB}
	}

	if ranA.End == ranB.Start {
		if ranA.Concurrency == ranB.Concurrency {
			return []ScheduleTimeRange{
				ScheduleTimeRange{ranA.Start, ranB.End, ranA.Concurrency},
			}
		}
		return []ScheduleTimeRange{ranA, ranB}
	}

	if ranA.Concurrency == ranB.Concurrency {

		if !ranA.End.After(ranB.End) {
			return []ScheduleTimeRange{
				ScheduleTimeRange{ranA.Start, ranB.End, ranA.Concurrency},
			}
		}
		return []ScheduleTimeRange{ranA}
	}

	if ranA.End.After(ranB.Start) && ranA.End.Before(ranB.End) {

		if ranA.Concurrency < ranB.Concurrency {

			return []ScheduleTimeRange{
				ScheduleTimeRange{ranA.Start, ranA.End, ranA.Concurrency},
				ScheduleTimeRange{ranA.End, ranB.End, ranB.Concurrency},
			}
		}

		return []ScheduleTimeRange{
			ScheduleTimeRange{ranA.Start, ranB.Start, ranA.Concurrency},
			ScheduleTimeRange{ranB.Start, ranB.End, ranB.Concurrency},
		}
	}

	if ranA.Concurrency < ranB.Concurrency {
		return []ScheduleTimeRange{
			ranA,
		}
	}
	return []ScheduleTimeRange{
		ScheduleTimeRange{ranA.Start, ranB.Start, ranA.Concurrency},
		ScheduleTimeRange{ranB.Start, ranB.End, ranB.Concurrency},
		ScheduleTimeRange{ranB.End, ranA.End, ranA.Concurrency},
	}
}

type ScheduleRanges []ScheduleRange
type ScheduleTimeRanges []ScheduleTimeRange

func (ranges ScheduleRanges) ToTimeRange(t time.Time) ScheduleTimeRanges {
	result := make(ScheduleTimeRanges, len(ranges))
	for i := range ranges {
		result[i] = ranges[i].ToTimeRange(t)
	}
	return result
}

func toDayMinute(s string) int {

	parts := strings.Split(s, ":")
	if len(parts) != 2 {
		return -1
	}
	hour, err := strconv.Atoi(parts[0])
	minute, err := strconv.Atoi(parts[1])
	if err != nil {
		return -1
	}
	return hour*60 + minute
}

func toMinuteString(dayMinute int) string {

	return fmt.Sprintf("%02d:%02d", dayMinute/60, dayMinute%60)
}

func (ranges ScheduleRanges) Merge() ScheduleRanges {

	concurrencies := make([]int8, 1440*2+1)
	for i := range ranges {
		start := toDayMinute(ranges[i].Start)
		end := toDayMinute(ranges[i].End)
		concurrency := int8(ranges[i].Concurrency)

		if end < start {

			for j := 0; j < end; j++ {
				if concurrencies[j] == 0 || concurrencies[j] > concurrency {
					concurrencies[j] = concurrency
				}
			}
			end += 1440
		}
		for j := start; j < end; j++ {
			if concurrencies[j] == 0 || concurrencies[j] > concurrency {
				concurrencies[j] = concurrency
				if j < 1440 {

					concurrencies[j+1440] = concurrency
				}
			}
		}
	}

	result := make(ScheduleRanges, 0, 1)
	var (
		lastConcurrency int32
		start           = -1
	)

	for i := range concurrencies {
		concurrency := int32(concurrencies[i])
		if concurrency != lastConcurrency {
			if lastConcurrency != 0 {
				result = append(result, ScheduleRange{toMinuteString(start), toMinuteString(i), lastConcurrency})
			}
			start = i
			lastConcurrency = concurrency
		}
	}
	return result
}

func (ranges ScheduleTimeRanges) PauseTime(startAt time.Time) time.Time {

	var start, end time.Time
	pauseTime := startAt
	for i := range ranges {
		start = ranges[i].Start
		end = ranges[i].End
		if end.Before(startAt) {
			continue
		}
		if pauseTime.Before(start) && start.Sub(pauseTime) > time.Minute*5 {

			break
		}
		pauseTime = end
	}
	return pauseTime
}

func (ranges ScheduleTimeRanges) StartTime(currentTime time.Time) time.Time {
	var start, end time.Time
	startAt := currentTime

	for i := range ranges {
		start = ranges[i].Start
		end = ranges[i].End
		if end.Before(currentTime) {
			continue
		}
		if start.Before(currentTime) {
			startAt = currentTime
			break
		}
		startAt = start
		break
	}
	return startAt
}

type assetOptions struct {
	Priority               int32                     `json:"priority" bson:"priority" codec:"priority"`
	Concurrency            int32                     `json:"concurrency" bson:"concurrency" codec:"concurrency"`
	MaxDepth               int32                     `json:"max_depth,omitempty" bson:"max_depth" codec:"max_depth"`
	MaxLinkNum             uint64                    `json:"max_link_num,omitempty" bson:"max_link_num" codec:"max_link_num"`
	ExpiredAt              time.Time                 `json:"expired_at" bson:"expired_at" codec:"expired_at"`
	Periods                assetPeriods              `json:"periods,omitempty" bson:"periods" codec:"periods"`
	Vul                    assetVulOptions           `json:"vul,omitempty" bson:"vul,omitempty" codec:"vul,omitempty"`
	BlackLink              assetBlackLinkOptions     `json:"black_link,omitempty" bson:"black_link,omitempty" codec:"black_link,omitempty"`
	SensitiveWord          assetSensitiveWordOptions `json:"sensitive_word,omitempty" bson:"sensitive_word,omitempty" codec:"sensitive_word,omitempty"`
	ContentChange          assetContentChangeOptions `json:"content_change,omitempty" bson:"content_change,omitempty" codec:"content_change,omitempty"`
	ExternalScanPolicy     externalScanPolicy        `json:"external_scan_policy,omitempty" bson:"external_scan_policy,omitempty" codec:"external_scan_policy,omitempty"`
	ScheduleTag            string                    `json:"schedule_tag,omitempty" bson:"schedule_tag,omitempty" codec:"schedule_tag,omitempty"`
	Headers                string                    `json:"headers,omitempty" bson:"headers,omitempty" codec:"headers,omitempty"`
	CrawlScheduleAt        time.Time                 `json:"crawl_schedule_at,omitempty" bson:"crawl_schedule_at,omitempty" codec:"crawl_schedule_at,omitempty"`
	ScanScheduleAt         time.Time                 `json:"scan_schedule_at,omitempty" bson:"scan_schedule_at,omitempty" codec:"scan_schedule_at,omitempty"`
	ScheduleRanges         ScheduleRanges            `json:"schedule_ranges,omitempty" bson:"schedule_ranges,omitempty" codec:"schedule_ranges,omitempty"`
	QuickMonitorHomepage   int64                     `json:"quick_monitor_homepage,omitempty" bson:"quick_monitor_homepage,omitempty" codec:"quick_monitor_homepage,omitempty"`
	QuickMonitorSecondpage int64                     `json:"quick_monitor_secondpage,omitempty" bson:"quick_monitor_secondpage,omitempty" codec:"quick_monitor_secondpage,omitempty"`
}

type assetPeriods struct {
	Vul           int32 `json:"vul,omitempty" bson:"vul,omitempty" codec:"vul,omitempty"`
	BlackLink     int32 `json:"black_link,omitempty" bson:"black_link,omitempty" codec:"black_link,omitempty"`
	SensitiveWord int32 `json:"sensitive_word,omitempty" bson:"sensitive_word,omitempty" codec:"sensitive_word,omitempty"`
	ContentChange int32 `json:"content_change,omitempty" bson:"content_change,omitempty" codec:"content_change,omitempty"`
	Phishing      int32 `json:"phishing,omitempty" bson:"phishing,omitempty" codec:"phishing,omitempty"`
	Trojan        int32 `json:"trojan,omitempty" bson:"trojan,omitempty" codec:"trojan,omitempty"`
}

type assetVulOptions struct {
	Specific assetVulSpecific `json:"specific,omitempty" bson:"specific,omitempty" codec:"specific,omitempty"`
}

type assetVulSpecific struct {
	XMLs      []string `json:"xmls,omitempty" bson:"xmls,omitempty" codec:"xmls,omitempty"`
	Affects   []string `json:"affects,omitempty" bson:"affects,omitempty" codec:"affects,omitempty"`
	FilterReg string   `json:"filter_reg,omitempty" bson:"filter_reg,omitempty" codec:"filter_reg,omitempty"`
}

type assetBlackLinkOptions struct {
	Customized []string `json:"customized,omitempty" bson:"customized,omitempty" codec:"customized,omitempty"`
}

type assetSensitiveWordOptions struct {
	Customized            []string `json:"customized,omitempty" bson:"customized,omitempty" codec:"customized,omitempty"`
	CheckImage            bool     `json:"check_image" bson:"check_image" codec:"check_image"`
	UseSensitiveWordGroup bool     `json:"use_sensitive_word_group" bson:"use_sensitive_word_group" codec:"use_sensitive_word_group"`
}

type assetContentChangeOptions struct {
	Customized   map[string][]string `json:"customized,omitempty" bson:"customized,omitempty" codec:"customized,omitempty"`
	MaxDepth     int32               `json:"max_depth,omitempty" bson:"max_depth,omitempty" codec:"max_depth,omitempty"`
	SnapshotType int                 `json:"snapshot_type"  bson:"snapshot_type,omitempty" codec:"snapshot_type,omitempty"`
}

type externalScanPolicy struct {
	ScanDepth int `json:"scan_depth"  bson:"scan_depth,omitempty" codec:"scan_depth,omitempty"`
}
