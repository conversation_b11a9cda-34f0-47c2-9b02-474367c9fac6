package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"sync"
	"time"
	"websec/common"
	"websec/common/consts"
	"websec/common/resource"
	"websec/common/schema"
	"websec/crawl"
	"websec/distributed/protocol"
	"websec/utils"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type TotalLinkDetail struct {
	TotalFail  int64 `json:"totalFail"`
	TotalLinks int64 `json:"totalLinks"`
}

var sensitivePaths = []string{
	"/admin/", "/manage/", "/css/", "/images/", "/js/", "/icons/",
	"/icons/small/", "/templates/", "/uploads/", "/extensions/", "/views/",
	"/test/", "/web/", "/website/", "/备份/", "/beifen/", "/www/", "/vera_Mobile/",
	"/zuixin/", "/最新/", "/最新备份/", "/wwwroot/", "/ftp/", "/账号/", "/密码/",
	"/bf/", "/zhanghao/", "/mima/", "/zhanhaomima/", "/1/", "/1111/", "/aaa/",
	"/999/", "/新建文本文档/", "/111/", "/以前/", "/源码/", "/ziliao/", "/资料/",
	"/123/", "/11/", "/数据/", "/数据库/", "/data/", "/database/", "/daochu/",
	"/导出/", "/用户/", "/user/", "/pass/", "/passwd/", "/password/", "cms", "file_inclusion",
}

func (processor *Processor) feedCrawler(assetID string, jobID string, host string, depth int32, once bool, linksChan chan<- *common.Link) {
	for _, v := range processor.asset.Entries {
		linksChan <- common.GenLink(v, http.MethodGet, "", "", 0)
		for j := range sensitivePaths {
			newURL, _ := utils.URLJoin(v, sensitivePaths[j])
			if newURL != "" {
				linksChan <- common.GenLink(newURL, http.MethodGet, "", "", 1)
				log.Debugln("load sensitive paths ", newURL)
			}
		}
	}

	for _, v := range processor.asset.ImportantURLS {
		linksChan <- common.GenLink(v, http.MethodGet, "", "", 0)
	}

	retry := 0
	maxRetry := 3
	for retry < maxRetry {
		err := processor.loadFoundLinks(jobID, host, depth, once, linksChan)
		if err != nil {
			retry++
		} else {
			break
		}
	}
}

func (processor *Processor) feedCrawlerWithImpoartantURLS(linksChan chan<- *common.Link) {
	for _, v := range processor.asset.Entries {
		linksChan <- common.GenLink(v, http.MethodGet, "", "", 0)

	}

	for _, v := range processor.asset.ImportantURLS {
		linksChan <- common.GenLink(v, http.MethodGet, "", "", 0)

	}
}

func (processor *Processor) loadFoundLinks(jobID string, host string, depth int32, once bool, linksChan chan<- *common.Link) error {
	urlCount := int64(0)

	findOption := options.Find().SetBatchSize(10000)
	if once {
		findOption.SetLimit(18000)
	} else {
		findOption.SetLimit(100000)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	cursor, err := processor.GetDBConnection().GetMongoDatabase().Collection(consts.CollectionURLs).Find(ctx, bson.M{
		"job_id":     jobID,
		"host":       host,
		"methods":    bson.M{"$bitsAnySet": consts.MethodBitGet},
		"depth":      bson.M{"$lte": depth},
		"media_type": bson.M{"$in": []string{"", "text/html"}},
	}, findOption)

	if err != nil {
		log.Errorln("failed to load urls:", err)
		cancel()
		return err
	}

	for cursor.Next(ctx) {
		var u schema.SiteURL
		err := cursor.Decode(&u)
		if err != nil {
			log.Errorln("failed to decode url:", err)
			continue
		}
		urlCount++

		{
			v := &u
			link := common.GenLink(v.String(), http.MethodGet, "", "", v.Depth)
			link.IsNew = false

			if link.MediaType == "" {

			}
			linksChan <- link
		}
	}
	if err = cursor.Err(); err != nil {
		cursor.Close(ctx)
		cancel()
		return err
	}
	cursor.Close(ctx)
	cancel()

	log.Debugln("load crawl urls finished, total found: ", urlCount, " ", host)
	return nil
}

func (processor *Processor) createCrawler(task *schema.Task) (crawl.Crawler, error) {
	var linksChan = make(chan *common.Link, 10)
	go func() {
		defer close(linksChan)
		processor.feedCrawler(task.AssetID.Hex(), task.JobID, task.Host, task.Crawl.MaxDepth, true, linksChan)
	}()

	offset := task.Crawl.Offset
	if offset == "" {
		offset = processor.getProgress(task.ID.Hex())
	}

	customeHeaders := task.CustomHeaders()
	if _, ok := customeHeaders["Accept-Language"]; !ok {
		customeHeaders.Add("Accept-Language", "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7")
	}

	crawlOptions := &crawl.Options{
		ByChrome:                true,
		Task:                    task,
		Host:                    task.Host,
		AssetID:                 task.AssetID.Hex(),
		JobID:                   task.JobID,
		UserAgent:               settings.UserAgent,
		Semaphores:              processor.createSemaphores(task.Host),
		ChromeHost:              settings.Chrome.Host,
		ChromePort:              settings.Chrome.Port,
		Headers:                 customeHeaders,
		IgnoredBlackLinkDomains: resource.DefaultIgnoredBlackLinkDomains(),
		Timeout:                 time.Duration(task.Crawl.Timeout) * time.Second,
		Concurrency:             int64(processor.asset.Options.Concurrency),
		MaxDepth:                task.Crawl.MaxDepth,
		MaxLinkNum:              task.Crawl.MaxLinkNum,
		ExternalScanDepth:       int32(task.ExternalScanDepth),
		Offset:                  offset,
		LinksChan:               linksChan,
		GetLastGetAt:            processor.getLastGetAt,
		SetLastGetAt:            processor.setLastGetAt,
		ExtractEntryImages:      processor.asset.Options.SensitiveWord.CheckImage,
		ExtractEntrySwfs:        false, // TODO: from assets options
		ShouldCrawlFoundLinks:   true,
		FilterReg:               task.Crawl.FilterReg,
		SpecificXMLs:            task.Scan.SpecificXMLs,
		CrawlOuterLinks:         true,
	}

	if crawlOptions.MaxLinkNum == 0 {
		crawlOptions.MaxLinkNum = processor.asset.Options.MaxLinkNum
	}

	if task.ByChrome == NotUseChrome {
		crawlOptions.ByChrome = false
		log.Infoln("crawler not used chrome")
	}

	return crawl.NewCrawler(crawlOptions, processor)
}

func (processor *Processor) createSemaphores(host string) *sync.Map {
	requestSemaphore := &crawl.RequestSemaphore{
		Sema: processor.sema,
		Ctx:  processor.ctx,
	}
	semaphores := &sync.Map{}
	semaphores.Store(host, requestSemaphore)
	return semaphores
}

func (processor *Processor) watchCrawler(task *schema.Task, crawler crawl.Crawler, future chan<- *protocol.TaskFinishBody) {
	runAt := time.Now()

	result := &protocol.TaskFinishBody{
		TaskID:  task.ID,
		AssetID: task.AssetID,
	}

	defer func() {
		log.Infof("watchCrawler finish taskID:%s result:%v", task.ID.Hex(), result)

		future <- result
		processor.crawlerMap.Delete(task.ID.Hex())
		processor.wg.Done()

		stat := crawler.GetStats()
		totalKey := "total_link_counter_" + task.AssetID.Hex()
		redisClient := processor.GetDBConnection().GetRedisPool()
		errCount := stat.ErrorCount
		reqCount := stat.RequestCount - int64(len(sensitivePaths))
		if reqCount < 1 {
			reqCount = stat.RequestCount
		}
		if errCount > reqCount {
			errCount = reqCount
		}
		data := TotalLinkDetail{
			TotalFail:  errCount,
			TotalLinks: reqCount,
		}

		jsonData, err := json.Marshal(data)

		if err != nil {
			fmt.Println("Error encoding JSON:", err)
			return
		}

		redisClient.Set(totalKey, jsonData, 0)
	}()

	crawlChan, err := crawler.Go()
	if err != nil {
		result.Status = consts.TaskResultError
		result.Reason = err.Error()
		return
	}

	var handler *crawl.DirHandler

	if task.Schedule.Tag == consts.TaskTagCrawlAll {
		handler, err = crawl.InitDirHandler(crawler.(*crawl.WSCrawler))
		if err != nil {
			log.Errorln("init handler err", err)
			return
		}
		go handler.DefaultDirScanner()
	}

	interval := 0
	log.Debugln("begin to consume crawlChan ...")

	assetID := task.AssetID.Hex()

	for webPage := range crawlChan {
		if webPage.URL == "" {
			continue
		}

		// set the task id
		webPage.AssetID = assetID
		webPage.JobID = task.JobID
		webPage.ExternalScanDepth = task.ExternalScanDepth

		if task.Schedule.Tag == consts.TaskTagCrawlAll &&
			(webPage.StatusCode >= 200 && webPage.StatusCode < 300) {
			u, err := url.Parse(webPage.URL)
			if err != nil {
				continue
			} else {
				dir, _ := filepath.Split(u.Path)
				rawurl := fmt.Sprintf("%v://%v%v", u.Scheme, u.Host, dir)

				crawler.RunDirScanner(
					rawurl,
					"HEAD",
					[]string{"php", "html"},
					"Mozilla/5.0 (Windows NT 6.1; WOW64; rv:40.0) Gecko/20100101 Firefox/40.1",
					"",
					false,
					handler,
					webPage.Depth,
				)
			}
		}

		if webPage.StatusCode >= 200 && webPage.StatusCode < 300 {
			log.Infof(" %s %d", webPage.URL, webPage.Depth)
			processor.AddWebpage(webPage)
			processor.AddImage(webPage)
			processor.AddSwf(webPage)
		}
		interval++
		if interval == 5 {
			processor.setProgress(task.ID.Hex(), crawler.GetOffset())
			interval = 0
		}
		webPage.Reset()

	}

	stats := crawler.GetStats()
	result.Status = int64(stats.TaskStatus)

	taskStats := new(schema.TaskStats)
	taskStats.TaskID = task.ID.Hex()
	taskStats.Host = task.Host
	taskStats.AssetID = assetID
	taskStats.RunAt = runAt
	taskStats.Stats.RequestCount = stats.RequestCount
	taskStats.Stats.FoundURLsCount = stats.FoundURLsCount
	taskStats.Stats.OuterURLsCount = stats.OuterURLsCount
	taskStats.Stats.ErrorCount = stats.ErrorCount
	taskStats.Stats.ErrorReason = stats.ErrorReason
	processor.AddTaskStats(taskStats)

	processor.delProgress(task.ID.Hex())

	crawler.(*crawl.WSCrawler).Die()
}

func (processor *Processor) getLastGetAt(host string, URLHash string) *common.PageLastGetAt {
	key := consts.RedisLastWebPagePrefix + host
	raw, err := processor.GetDBConnection().HGetBytes(key, URLHash)
	if err != nil {
		return nil
	}
	var lastGetAt common.PageLastGetAt
	err = json.Unmarshal(raw, &lastGetAt)
	if err != nil {
		return nil
	}
	return &lastGetAt
}

func (processor *Processor) setLastGetAt(host string, URLHash string, lastGetAt *common.PageLastGetAt) bool {
	key := consts.RedisLastWebPagePrefix + host
	raw, err := json.Marshal(lastGetAt)
	if err != nil {
		return false
	}
	err = processor.GetDBConnection().HSetEx(key, URLHash, raw, 300)
	if err != nil {
		return false
	}
	return true
}

var (
	fileInjectBeforeLoad = "inject_before_load.js"
	fileInjectAfterLoad  = "inject_after_load.js"
)

func LoadInjectJS(filePath string) *common.InjectJavaScript {
	injectJS := new(common.InjectJavaScript)

	filename := filePath + fileInjectBeforeLoad
	injectJS.InjectBeforeLoad, _ = loadInjectJS(filename)

	filename = filePath + fileInjectAfterLoad
	injectJS.InjectAfterLoad, _ = loadInjectJS(filename)

	return injectJS
}

func loadInjectJS(filename string) (string, error) {
	f, err := os.Open(filename)
	if err != nil {
		log.Errorln("Failed to open file:", filename, err)
		return "", err
	}

	defer f.Close()
	buf, err := io.ReadAll(f)
	if err != nil {
		log.Errorln("Filed to read file:", filename, err)
		return "", err
	}

	return string(buf), nil
}
