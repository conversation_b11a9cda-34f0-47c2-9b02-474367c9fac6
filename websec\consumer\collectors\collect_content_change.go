package collectors

import (
	"encoding/json"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/stream"
)

func (collector *Collector) processContentChange(msg *stream.Message) error {
	var doc = new(schema.FinalContentChangeResult)
	err := json.Unmarshal(msg.Value, doc)
	if err != nil {
		log.Errorln("collect_content_change.go processContentChange Error:", err)
		return err
	}

	collector.AddContentChange(doc)
	return nil
}
