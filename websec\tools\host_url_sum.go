package main

import (
	"context"
	"fmt"
	"sync"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/config"
	"websec/utils/log"
	"websec/utils/semaphore"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	dbConnection *db.DBConnection
)

func main() {
	settings, err := config.ParseConfig()
	if err != nil {
		log.Errorln(err)
		return
	}

	dbConnection, err = db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis))
	if err != nil {
		log.Errorln(err)
		return
	}

	var lock sync.Mutex
	var wg sync.WaitGroup
	sem := semaphore.NewWeighted(40)

	assets := make([]string, 0, 50000)
	cursor, err := dbConnection.GetMongoDatabase().Collection(consts.CollectionAssets).Find(context.Background(),
		bson.M{},
		options.Find().SetBatchSize(10000))
	if err != nil {
		log.Errorln(err)
		return
	}

	for cursor.Next(context.Background()) {
		doc := new(schema.Asset)
		err = cursor.Decode(doc)
		if err != nil {
			log.Errorln(err)
			continue
		}

		assets = append(assets, doc.ID.Hex())
	}
	cursor.Close(context.Background())

	domains := make(map[string]int64)
	for _, v := range assets {
		if err = sem.Acquire(context.Background(), 1); err == nil {
			wg.Add(1)
			go func(assetID string) {
				defer func() {
					sem.Release(1)
					wg.Done()
				}()
				count, err := dbConnection.GetMongoDatabase().Collection(consts.CollectionURLs).CountDocuments(context.Background(),
					bson.M{"asset_id": assetID})
				if err != nil {
					log.Errorln(err)
					return
				}

				lock.Lock()
				domains[assetID] = count
				lock.Unlock()
				log.Infof("assetID %s sum: %d", assetID, count)
			}(v)
		}
	}

	wg.Wait()
	log.Infoln("domain:", len(domains))

	for _, v := range assets {
		if err = sem.Acquire(context.Background(), 1); err == nil {
			wg.Add(1)
			go func(assetID string) {
				defer func() {
					sem.Release(1)
					wg.Done()
				}()
				dbConnection.DelKey(fmt.Sprintf("%s{%s}", assetID, assetID))
				dbConnection.DelKey(fmt.Sprintf("%s{%s}bit:1", assetID, assetID))
			}(v)
		}
	}
	wg.Wait()

	err = dbConnection.GetMongoDatabase().Collection(consts.CollectionAssetURLSum).Drop(context.Background())
	if err != nil {
		log.Error(err)
		return
	}

	updateAssetURLSum(domains)
}

func updateAssetURLSum(domains map[string]int64) {
	result := make([]interface{}, 0, 50)
	resultCount := 0
	for k, v := range domains {
		hs := &schema.AssetURLSum{
			AssetID: k,
			Sum:     int32(v),
		}

		result = append(result, hs)
		resultCount++
		if resultCount == 50 {
			res, err := dbConnection.GetMongoDatabase().Collection(consts.CollectionAssetURLSum).InsertMany(context.Background(), result,
				options.InsertMany().SetOrdered(false))
			if err == nil {
				log.Infoln("res insert:", len(res.InsertedIDs))
			}
			result = make([]interface{}, 0, 50)
			resultCount = 0
		}
	}

	if resultCount > 0 {
		res, err := dbConnection.GetMongoDatabase().Collection(consts.CollectionAssetURLSum).InsertMany(context.Background(), result,
			options.InsertMany().SetOrdered(false))
		if err == nil {
			log.Infoln("res insert:", len(res.InsertedIDs))
		}
	}
}
