package crawl

import (
	"bufio"
	"os"
)

func LineReader(filename string, noff int64) (chan string, error) {
	fp, err := os.Open(filename)
	if err != nil {
		return nil, err
	}

	if noff > 0 {

		b := make([]byte, 1)
		for b[0] != '\n' {
			noff--
			fp.Seek(noff, os.SEEK_SET)
			fp.Read(b)
		}
		noff++
	}

	out := make(chan string)
	go func() {
		defer fp.Close()
		defer close(out)
		scanner := bufio.NewScanner(fp)
		scanner.Split(bufio.ScanLines)
		for scanner.Scan() {
			noff, _ = fp.Seek(0, os.SEEK_CUR)
			out <- scanner.Text()
		}
	}()

	return out, nil
}

func LineReaderList(Listfilename string, noff int64) (strList []string, err error) {
	fp, err := os.Open(Listfilename)
	if err != nil {
		return
	}

	if noff > 0 {

		b := make([]byte, 1)
		for b[0] != '\n' {
			noff--
			fp.Seek(noff, os.SEEK_SET)
			fp.Read(b)
		}
		noff++
	}

	defer fp.Close()

	scanner := bufio.NewScanner(fp)
	scanner.Split(bufio.ScanLines)
	for scanner.Scan() {
		noff, _ = fp.Seek(0, os.SEEK_CUR)
		strList = append(strList, scanner.Text())
	}

	return
}
