package stream

import (
	"encoding/json"
	"runtime/debug"
	"sync"
	"websec/utils/log"

	"github.com/confluentinc/confluent-kafka-go/kafka"
)

type ProducerOptions struct {
	Brokers         string
	MessageMaxBytes int
}

type Producer struct {
	options *ProducerOptions

	messages      chan *ProduceMessage
	kafkaProducer *kafka.Producer
	messagesBytes chan *ProduceMessageBytes

	waitGroup sync.WaitGroup
	closeOnce sync.Once
}

func (producer *Producer) Produce(topic string, content interface{}) {
	producer.messages <- &ProduceMessage{
		Topic: topic,
		Value: content,
	}
}

func (producer *Producer) ProduceBytes(topic string, content interface{}) {
	bin, err := json.Marshal(content)
	if err != nil {
		log.Errorln("failed to marshal content for topic:", topic, err)
		return
	}

	kafkaMsg := &kafka.Message{
		TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
		Value:          bin,
	}

	producer.kafkaProducer.ProduceChannel() <- kafkaMsg

}

func (producer *Producer) watchKafka() {
	defer producer.waitGroup.Done()

	var jsonMap map[string]interface{}
	for ev := range producer.kafkaProducer.Events() {
		switch e := ev.(type) {
		case *kafka.Message:
			if e.TopicPartition.Error != nil {
				json.Unmarshal(e.Value, &jsonMap)
				log.Errorf("producer delivery failed: %v %s", e.TopicPartition, jsonMap["url"])
			} else {
				log.Infof("delivered message to %v\n", e.TopicPartition)
			}
		default:
			log.Infof("ignored event: %s\n", e)
		}
	}

}

func (producer *Producer) sendToKafka() {
	defer func() {
		if err := recover(); err != nil {
			log.Errorln("recover error ", err, string(debug.Stack()))
		}

		producer.kafkaProducer.Flush(5000)
		producer.kafkaProducer.Close()
		producer.waitGroup.Done()
	}()

	for msg := range producer.messages {

		bin, err := json.Marshal(msg.Value)
		if err != nil {
			log.Errorln("failed to marshal content for topic:", msg.Topic, err)
			continue
		}

		kafkaMsg := &kafka.Message{
			TopicPartition: kafka.TopicPartition{Topic: &msg.Topic, Partition: kafka.PartitionAny},
			Value:          bin,
		}

		producer.kafkaProducer.ProduceChannel() <- kafkaMsg
	}

	log.Infof("all messages have been sent")
}

func (producer *Producer) Close() {
	producer.closeOnce.Do(func() {
		close(producer.messages)
		producer.waitGroup.Wait()
	})

}

func (producer *Producer) Go() {
	producer.waitGroup.Add(2)
	go producer.sendToKafka()
	go producer.watchKafka()
}

func newKafkaProducer(options *ProducerOptions) (*kafka.Producer, error) {
	return kafka.NewProducer(&kafka.ConfigMap{
		"bootstrap.servers":            options.Brokers,
		"compression.codec":            "gzip",
		"message.max.bytes":            options.MessageMaxBytes,
		"message.send.max.retries":     3,    // 默认值 2
		"retry.backoff.ms":             500,  // 默认值100
		"queue.buffering.max.ms":       50,   // 默认值 0
		"queue.buffering.max.messages": 1000, //默认值 100000
		"batch.num.messages":           1000, // 默认值 10000
		"go.produce.channel.size":      200,  // 最多缓存10000条数据

	})
}

func NewProducer(options *ProducerOptions) (*Producer, error) {
	p, err := newKafkaProducer(options)
	if err != nil {
		return nil, err
	}

	producer := &Producer{
		waitGroup:     sync.WaitGroup{},
		messages:      make(chan *ProduceMessage, 100),
		kafkaProducer: p,
		messagesBytes: make(chan *ProduceMessageBytes, 100),
	}
	return producer, nil
}
