package scripts

import (
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"
)

func runConnect(address string, c chan string, key string, value int) {
	conn, err := net.DialTimeout("tcp", address, 3*time.Second)
	if err != nil {
		c <- ""
	} else {
		defer conn.Close()
		result := "|" + strconv.Itoa(value) + "(" + key + ")"
		c <- result
	}
}

func SimplePortScan(args *ScriptScanArgs) (*ScriptScanResult, error) {
	ports := map[string]int{
		"ftp":           21,
		"ssh":           22,
		"telnet":        23,
		"smtp":          25,
		"nameserver":    42,
		"DNS":           53,
		"tftp":          69,
		"pop3":          110,
		"mssql":         1433,
		"oracle":        1521,
		"mysql":         3306,
		"radmin":        4899,
		"vnc":           5800,
		"teamviwer":     5938,
		"rdp":           3389,
		"redis":         6379,
		"elasticsearch": 9200,
		"memcached":     11211,
		"db2":           50000,
	}
	c := make(chan string, len(ports))

	var results = ""
	for key, value := range ports {
		addr := args.Host + ":" + strconv.Itoa(value)
		go runConnect(addr, c, key, value)
	}

	for i := 0; i < len(ports); i++ {
		results += <-c
	}
	if results != "" && !(strings.Contains(results, "22(ssh)") &&
		strings.Contains(results, "3389(rdp)")) {
		results = strings.Replace(results, "|", "", 1)
		return &ScriptScanResult{Vulnerable: true, Output: fmt.Sprintf("%v", results)}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("SimplePortScan.xml", SimplePortScan)
}
