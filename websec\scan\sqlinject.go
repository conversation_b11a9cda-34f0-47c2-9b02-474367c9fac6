package scan

import (
	"bytes"
	"context"
	"encoding/binary"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"sync/atomic"
	"time"
	"unsafe"
	"websec/scan/rules"
	"websec/utils"
	"websec/utils/log"
	"websec/utils/semaphore"
)

var dbTypeMapping = map[uint32]string{
	2:  "MSSQL 2000",
	3:  "MSSQL 2000",
	12: "MSSQL 2005",
	13: "MSSQL 2005",
	14: "MSSQL 2005",
	15: "MSSQL 2005",
	5:  "MySQL",
	4:  "Oracle",
	6:  "Sybase",
	7:  "DB2",
	8:  "Access",
	9:  "Informix",
	10: "PostgreSQL",
	11: "Sqlite",
}

var injectTypeMapping = map[uint32]string{
	0:  "none",
	1:  "Int",
	12: "OR-Int",
	34: "OR-Int-KH",
	5:  "Int-KH",
	6:  "Int-Calc",
	19: "Int-Case",
	31: "Int-Rep-Case",
	45: "Int-Rep-db-Case",
	20: "Int-Addi-Case",
	23: "Int_ms-Case",
	24: "Int-ms_Case-Addi",
	25: "Int-Rlike",
	26: "Int-Addi-Rlike",
	2:  "String",
	7:  "String-KH",
	4:  "String-DQ",
	8:  "String-KH-DQ",
	9:  "Str-M-Encode",
	13: "OR-String",
	14: "OR-DQ-String",
	37: "OR-Like-String",
	29: "String-KH-Rlike",
	30: "String-Addi-Rlike",
	33: "String_Rlike",
	38: "String-like",
	39: "Str-like-KH",
	42: "Makeset",
	43: "Elt",
	44: "SS",
	46: "ORA-Case",
	47: "Case-GO",
	48: "Case-DB-GO",
	49: "ORA-Case-GO",
	50: "ESCAPE",
	3:  "Search",
	10: "Search-KH",
	11: "Search-DQ-KH",
	15: "Time",
	41: "HeavyQuery",
	16: "Error",
	32: "ErrorReplace",
	17: "Php-Pdo",
	21: "access-multi",
	22: "access-multi-addi",
	27: "ms-multi",
	28: "ms-multi-addi",
	36: "ms-multi-addi-1",
	18: "Array",
	35: "Cookie",
	40: "Weak Pass",
}

type webscanHeader struct {
	Version   uint32
	CMD       uint32
	SubCMD    uint32
	Direction uint32
	Cookie    uint32
	BodySize  uint32
	GlobalID  uint64
	SessionID uint32
	Padding   uint32
}

type requestBodyHeader struct {
	Method      uint32
	QPS         uint32
	Verify      uint32
	CheckType   uint32
	HarmfulTest uint32
	PayloadLen  uint32
}

type responseBodyHeader struct {
	Result     uint32
	InjectType uint32
	DBType     uint32
	PayloadLen uint32
}

type webscanUnpackResponse struct {
	GlobalID   uint64
	SessionID  uint32
	InjectType string
	DBType     string
	Payload    string
}

type sqlinjectURL struct {
	URL   string
	Field string
}

var pangolinSema = semaphore.NewWeighted(100)
var pangolinSemaContext = context.Background()

func constructSQLInjectUrls(link *AffectLink) []sqlinjectURL {
	result := []sqlinjectURL{}

	var parts *url.URL
	var qs = url.Values{}
	var err error
	var keys = []string{}

	if link.Method == http.MethodPost {
		qs, err = url.ParseQuery(link.Data)
		if err != nil {
			return result
		}
	} else {
		parts, err = url.Parse(link.URL)
		if err != nil {
			return result
		}
		if parts.RawQuery != "" {
			qs = parts.Query()
		}
	}

	for key := range qs {
		keys = append(keys, key)
	}
	var value []string
	var query string
	for _, key := range keys {
		value = qs[key]
		qs.Del(key)
		query = qs.Encode()
		if query != "" {
			query += "&"
		}
		query += key + "=" + value[0]

		if link.Method == http.MethodPost {
			result = append(result, sqlinjectURL{URL: link.URL + "?" + query, Field: key})
		} else {
			parts.RawQuery = query
			result = append(result, sqlinjectURL{URL: utils.ToString(parts), Field: key})
		}
		qs[key] = value
	}
	return result
}

const webscanVersion uint32 = 1
const sqlinjectCMD uint32 = 0x1001
const sqlinjectSubCMD uint32 = 0x1011
const sqlinjectCookie uint32 = 0x12FAABCD

func packSQLInject(globalID uint64, sessionID uint32, method string, rawurl string) []byte {
	var header webscanHeader
	var requestHeader requestBodyHeader
	var err error

	bodySize := uint32(unsafe.Sizeof(requestHeader)) + uint32(len(rawurl))
	header = webscanHeader{
		Version:   webscanVersion,
		CMD:       sqlinjectCMD,
		SubCMD:    sqlinjectSubCMD,
		Direction: 0,
		Cookie:    sqlinjectCookie,
		BodySize:  bodySize,
		GlobalID:  globalID,
		SessionID: sessionID,
		Padding:   0,
	}
	var requestMethod uint32
	if method == "POST" {
		requestMethod = 1
	} else {
		requestMethod = 0
	}

	requestHeader = requestBodyHeader{
		Method:      requestMethod,
		QPS:         5,
		Verify:      0,
		CheckType:   0,
		HarmfulTest: 1,
		PayloadLen:  uint32(len(rawurl)),
	}
	buf := &bytes.Buffer{}
	err = binary.Write(buf, binary.BigEndian, header)
	if err != nil {
		return nil
	}
	err = binary.Write(buf, binary.BigEndian, requestHeader)
	if err != nil {
		return nil
	}
	_, err = buf.WriteString(rawurl)
	if err != nil {
		return nil
	}
	return buf.Bytes()
}

func unpackSQLInject(buf []byte) (*webscanUnpackResponse, error) {
	var header = &webscanHeader{}

	reader := bytes.NewReader(buf)

	err := binary.Read(reader, binary.BigEndian, header)
	if err != nil {
		return nil, err
	}
	if header.CMD != sqlinjectCMD {
		return nil, fmt.Errorf("header.CMD error: %v", header.CMD)
	}
	if header.SubCMD != sqlinjectSubCMD {
		return nil, fmt.Errorf("header.SubCMD error: %v", header.SubCMD)
	}
	if header.Cookie != sqlinjectCookie {
		return nil, fmt.Errorf("header.Cookie error: %v", header.Cookie)
	}

	if header.BodySize == 4 {
		return nil, nil
	}

	var bodyHeader = &responseBodyHeader{}
	err = binary.Read(reader, binary.BigEndian, bodyHeader)
	if err != nil {
		return nil, err
	}

	if bodyHeader.InjectType <= 0 {
		return nil, nil
	}

	var injectType, dbType string
	var ok bool

	injectType, ok = injectTypeMapping[bodyHeader.InjectType]
	if !ok {
		injectType = fmt.Sprintf("Unknown %v", bodyHeader.InjectType)
	}
	dbType, ok = dbTypeMapping[bodyHeader.DBType]
	if !ok {
		dbType = fmt.Sprintf("Unknown %v", bodyHeader.DBType)
	}
	var payload = make([]byte, bodyHeader.PayloadLen)
	_, err = reader.Read(payload)
	if err != nil {
		log.Errorln("error happens when reading sqlinject payload:", err.Error())
	}
	return &webscanUnpackResponse{
		GlobalID:   header.GlobalID,
		SessionID:  header.SessionID,
		InjectType: injectType,
		DBType:     dbType,
		Payload:    string(payload),
	}, nil
}

var webscanGlobalID uint64
var sqlinjectVul = rules.Vulnerability{
	Name:     "SQL Injection",
	VulXML:   "Sql_Injection(inside_check).xml",
	Severity: "high",
}

func (scanner *WSScanner) scanSQLInjectWrap(link *AffectLink) {
	if scanner.specificVulXMLs != nil {
		if _, ok := scanner.specificVulXMLs["Sql_Injection(inside_check).xml"]; !ok {
			return
		}
	}
	result := scanner.scanSQLInjectWithSQLMap(link)
	if result != nil {
		scanner.outputResult(result)
	}
}

func (scanner *WSScanner) scanSQLInjectWithSQLMap(link *AffectLink) *ScanResult {
	foundVuls := []*FoundVul{}
	injectURLs := constructSQLInjectUrls(link)
	for i := range injectURLs {
		injectURL := injectURLs[i]
		rawurl := injectURL.URL
		log.Infoln("sqlinject scan with sqlmap:", link, rawurl)

		context := VulContext{
			"severity":    "high",
			"method":      link.Method,
			"inject_type": "",
			"db_type":     "unknown_db",
			"field":       injectURL.Field,
			"payload":     "",
		}
		foundVul := &FoundVul{
			Link:     link,
			Vul:      &sqlinjectVul,
			Severity: "high",
			VulURL:   rawurl,
			Context:  context,
		}

		cookie := scanner.Options.Headers.Clone().Get("Cookie")
		scanner.validateSQLInjectVulWithScreenshot(link, &sqlinjectVul, foundVul, []byte(cookie))
		if isValid, ok := foundVul.Context["is_valid"]; ok {
			if vulValid, yes := isValid.(bool); yes {
				if vulValid {
					foundVuls = append(foundVuls, foundVul)
				}
			}

		}
	}
	if len(foundVuls) == 0 {
		return nil
	}
	return &ScanResult{FoundVuls: foundVuls, RequestCount: 0, ErrorCount: 0}
}

func (scanner *WSScanner) scanSQLInject(link *AffectLink) *ScanResult {
	err := pangolinSema.Acquire(pangolinSemaContext, 1)
	if err != nil {
		log.Errorln("failed to acquire pangolinSema for:", link)
		return &ScanResult{ErrorCount: 1}
	}
	defer pangolinSema.Release(1)

	conn, err := net.DialTimeout("tcp", scanner.Options.PangolinAddress, 5*time.Second)
	if err != nil {
		return &ScanResult{ErrorCount: 1}
	}
	defer conn.Close()

	var buf []byte
	var errorCount int64

	globalID := atomic.AddUint64(&webscanGlobalID, 1)
	foundVuls := []*FoundVul{}
	for i, item := range constructSQLInjectUrls(link) {
		sessionID := uint32(i)
		rawurl := item.URL
		log.Infoln("sqlinject scan:", link, rawurl)

		conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
		request := packSQLInject(globalID, sessionID, link.Method, rawurl)
		if len(request) == 0 {
			continue
		}
		_, err = conn.Write(request)
		if err != nil {
			log.Errorln("failed to write:", err)
			errorCount++
			continue
		}

		conn.SetReadDeadline(time.Now().Add(time.Minute * 10))
		buf = make([]byte, 1024*4)
		_, err = conn.Read(buf)
		if err != nil {
			log.Errorln("failed to read:", err)
			errorCount++
			continue
		}
		response, err := unpackSQLInject(buf)
		if err != nil {
			errorCount++
			continue
		}

		log.Infoln("sqlinject response:", response)
		if response == nil || response.SessionID != sessionID {
			continue
		}

		context := VulContext{
			"severity":    "high",
			"method":      link.Method,
			"inject_type": response.InjectType,
			"db_type":     response.DBType,
			"field":       item.Field,
			"payload":     string(response.Payload),
		}

		foundVul := &FoundVul{
			Link:     link,
			Vul:      &sqlinjectVul,
			Severity: "high",
			VulURL:   rawurl,
			Context:  context,
		}

		if link.Method == http.MethodGet {
			foundVul.VulURL = rawurl + string(response.Payload)
		}

		cookie := scanner.Options.Headers.Clone().Get("Cookie")
		scanner.validateSQLInjectVulWithScreenshot(link, &sqlinjectVul, foundVul, []byte(cookie))
		foundVuls = append(foundVuls, foundVul)
	}

	return &ScanResult{FoundVuls: foundVuls, RequestCount: 0, ErrorCount: errorCount}
}

func (scanner *WSScanner) scanCookieSQLInjectWith(link *AffectLink) *ScanResult {
	err := pangolinSema.Acquire(pangolinSemaContext, 1)
	if err != nil {
		log.Errorln("failed to acquire pangolinSema for:", link)
		return &ScanResult{ErrorCount: 1}
	}
	defer pangolinSema.Release(1)

	var errorCount int64

	foundVuls := []*FoundVul{}
	for _, item := range constructSQLInjectUrls(link) {

		rawurl := item.URL
		log.Infoln("sqlinject scan:", link, rawurl)

		context := VulContext{
			"severity": "high",
			"method":   link.Method,
			"field":    item.Field,
		}
		foundVul := &FoundVul{
			Link: link,
			Vul:  &sqlinjectVul,

			Severity: "high",
			Context:  context,
		}

		foundVuls = append(foundVuls, foundVul)
	}
	return &ScanResult{FoundVuls: foundVuls, RequestCount: 0, ErrorCount: errorCount}
}
