package acsmx

// #include <stdlib.h>
// #include <stdio.h>
// #include <string.h>
// #include "acsmx2.h"
//
// typedef struct {
//     int position;
// 	   char* word;
// } MatchedWord;
//
// typedef struct {
//     size_t count;
//     size_t capacity;
// 	   MatchedWord* words;
// } MatchedList;
//
// MatchedList* new_matched_list() {
//     size_t capacity = 8;
//     MatchedList* matched_list = (MatchedList*)malloc(sizeof(MatchedList));
//     if (matched_list == NULL) {
//         return NULL;
//     }
//     MatchedWord* word_list= (MatchedWord*)malloc(capacity * sizeof(MatchedWord));
//     if (word_list == NULL) {
//         free(matched_list);
//         return NULL;
//     }
//     matched_list->capacity = capacity;
//     matched_list->count = 0;
//     matched_list->words = word_list;
//     return matched_list;
// }
//
// void reset_matched_list(MatchedList* matched_list) {
//     matched_list->count = 0;
//     memset(&(matched_list->words), 0, matched_list->count * sizeof(MatchedWord));
// }
//
// MatchedList* expand_matched_list(MatchedList* matched_list) {
//     int new_capacity = 0;
//     if (matched_list->capacity < 256) {
//         new_capacity = matched_list->capacity * 2;
//     } else {
//         new_capacity = matched_list->capacity * 1.2;
//     };
//     MatchedWord* new_words = (MatchedWord*)realloc(matched_list->words, new_capacity * sizeof(MatchedWord));
//     if (new_words == NULL) {
//         return NULL;
//     }
//     matched_list->capacity = new_capacity;
//     matched_list->words = new_words;
//     return matched_list;
// }
//
// MatchedWord* get_matched_word(MatchedList* matched_list, int index) {
//     if (index >= matched_list->count) {
//         return NULL;
//     }
//     return &(matched_list->words[index]);
// }
//
// void free_matched_list(MatchedList* matched_list) {
//     if (matched_list != NULL) {
//         free(matched_list->words);
//         free(matched_list);
//     }
// }
// int min(int a, int b) {
//     if (a <= b) {
//         return a;
//     }
//     return b;
// }
//
//
// int match_found(void * _id, void *tree, int index, void *matched_list, void *neg_list) {
//     char* word = (char*)_id;
//     MatchedList* list = (MatchedList*)matched_list;
//     if (list->capacity <= list->count) {
//         if (expand_matched_list(list) == NULL) {
//             // no enough memory, stop searching
//             return 1;
//         };
//     }
//     MatchedWord* mword = &(list->words[list->count]);
//     mword->word = word;
//     mword->position = index;
//     list->count++;
//     return 0;
// }
//
// typedef struct {
//     ACSM_STRUCT2 *acsm;
//     int currentIID;
//     int compiled;
// } Matcher;
//
// Matcher* new_matcher() {
//     Matcher *matcher = (Matcher*)malloc(sizeof(Matcher));
//     if (matcher == NULL) {
//         return NULL;
//     }
//     ACSM_STRUCT2 *acsm = acsmNew2(free, NULL, NULL);
//     if (acsm == NULL) {
//         free(matcher);
//         return NULL;
//     }
//     acsmCompressStates(acsm, 1);
//     matcher->acsm = acsm;
//     matcher->currentIID = 0;
//     matcher->compiled = 0;
//     return matcher;
// }
//
// void add_pattern(Matcher *matcher, unsigned char * pat, int length, unsigned char * id, int id_length) {
//     ACSM_STRUCT2 *acsm = matcher->acsm;
//     unsigned char* copied_id = (unsigned char*)malloc((id_length+1) * sizeof(unsigned char));
//     int iid = matcher->currentIID;
//     if (copied_id == NULL) {
//         exit(1);
//     }
//     memcpy(copied_id, id, id_length);
//     copied_id[id_length] = '\0';
//     acsmAddPattern2(acsm, pat, length, 1, 0, 0, 0, (void*)copied_id, matcher->currentIID++);
// }
//
// // WARN: Don't use this function.
// void add_patterns(Matcher *matcher, char** patterns, int length) {
//     int i = 0;
//     size_t pat_len = 0;
//     char* pat = NULL;
//     char* copied_pat = NULL;
//     for (i = 0; i < length; i++) {
//         pat = patterns[i];
//         pat_len = strlen(pat);
//         copied_pat = (char*)malloc((pat_len+1) * sizeof(char));
//         strcpy(copied_pat, pat);
//         acsmAddPattern2(matcher->acsm, (unsigned char*)copied_pat, pat_len, 1, 0, 0, 0, (void*)copied_pat, matcher->currentIID++);
//     }
// }
// void compile(Matcher *matcher) {
//     if (matcher->compiled == 0) {
//         acsmCompile2(matcher->acsm, NULL, NULL);
//         matcher->compiled = 1;
//     }
// }
//
// MatchedList* search(Matcher *matcher, unsigned char* text, size_t length, int full_search) {
//     if (matcher->compiled == 0) {
//         compile(matcher);
//     }
//     MatchedList *matched_list = new_matched_list();
//     if (matched_list == NULL) {
//         return NULL;
//     }
//     ACSM_STRUCT2 *acsm = matcher->acsm;
//     int start_state = 0;
//     int count = 0;
//
//     if (full_search == 0) {
//         count = acsmSearch2(acsm, text, length, match_found, (void*)matched_list, &start_state);
//     } else {
//         count = acsmSearchAll2(acsm, text, length, match_found, (void*)matched_list, &start_state);
//     }
//     return matched_list;
// }
//
// void free_matcher(Matcher *matcher) {
//     if (matcher == NULL) {
//         return;
//     }
//     if (matcher->acsm != NULL) {
//         acsmFree2(matcher->acsm);
//         matcher->acsm = NULL;
//     }
//     free(matcher);
// }
//
import "C"
import (
	"runtime"
	"unicode/utf8"
	"unsafe"
)

type Matcher struct {
	m *C.Matcher
}

type MatchedWord struct {
	Position int
	Word     string
}

func NewMatcher() *Matcher {
	m := C.new_matcher()
	if m == nil {
		return nil
	}
	matcher := &Matcher{m: m}
	runtime.SetFinalizer(matcher, freeMatcher)
	return matcher
}

func (matcher *Matcher) AddPatternString(pattern string) {
	patternBytes := []byte(pattern)
	matcher.AddPattern(patternBytes)
}

func (matcher *Matcher) AddPattern(pattern []byte) {
	numReplacedPattern := replaceNums(pattern, false)
	C.add_pattern(matcher.m, (*C.uchar)(unsafe.Pointer(&numReplacedPattern[0])), C.int(len(numReplacedPattern)), (*C.uchar)(unsafe.Pointer(&pattern[0])), C.int(len(pattern)))
}

func (matcher *Matcher) search(text []byte, fullSearch bool) (int, []MatchedWord) {
	if len(text) == 0 {
		return 0, nil
	}

	var intFullSearch int
	if fullSearch {
		intFullSearch = 1
	}
	matchedList := C.search(matcher.m, (*C.uchar)(unsafe.Pointer(&text[0])), C.size_t(len(text)), C.int(intFullSearch))
	results := []MatchedWord{}
	for i := 0; i < int(matchedList.count); i++ {
		word := C.get_matched_word(matchedList, C.int(i))
		if word == nil {
			continue
		}
		results = append(results, MatchedWord{Word: C.GoString(word.word), Position: int(word.position)})
	}
	C.free_matched_list(matchedList)
	return len(results), results
}

func (matcher *Matcher) Compile() {
	C.compile(matcher.m)
}

func (matcher *Matcher) Search(text []byte) (int, []MatchedWord) {
	return matcher.search(text, false)
}

func (matcher *Matcher) SearchAll(text []byte) (int, []MatchedWord) {
	return matcher.search(text, true)
}

func freeMatcher(matcher *Matcher) {
	if matcher.m != nil {
		C.free_matcher(matcher.m)
		matcher.m = nil
	}
}

var chineseNumMap = map[rune][]byte{
	'零': []byte{'0'},
	'一': []byte{'1'},
	'二': []byte{'2'},
	'三': []byte{'3'},
	'四': []byte{'4'},
	'五': []byte{'5'},
	'六': []byte{'6'},
	'七': []byte{'7'},
	'八': []byte{'8'},
	'九': []byte{'9'},
	'十': []byte{'1', '0'},
}

func mappingChineseToNum(r rune, padding bool) []byte {
	if v, ok := chineseNumMap[r]; ok {
		if padding {
			paddingV := []byte{' ', ' ', ' '}
			copy(paddingV[len(paddingV)-len(v):], v)
			return paddingV
		}
		return v
	}
	return []byte(string(r))
}

func replaceNums(s []byte, padding bool) []byte {
	nb := make([]byte, len(s))
	offset := 0
	for i := 0; i < len(s); {
		wid := 1
		r := rune(s[i])
		if r >= utf8.RuneSelf {
			r, wid = utf8.DecodeRune(s[i:])
		}
		w := mappingChineseToNum(r, padding)
		copy(nb[offset:], w)
		i += wid
		offset += len(w)
	}
	return nb[:offset]
}
