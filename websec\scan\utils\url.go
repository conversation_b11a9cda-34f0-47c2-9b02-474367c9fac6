package utils

import (
	"bytes"
	"fmt"
	"net/url"
	"strings"
)

func ToString(u *url.URL) string {
	var buf bytes.Buffer
	if u.Scheme != "" {
		buf.WriteString(u.Scheme)
		buf.WriteByte(':')
	}
	if u.Opaque != "" {
		buf.WriteString(u.Opaque)
	} else {
		if u.Scheme != "" || u.Host != "" || u.User != nil {
			buf.WriteString("//")
			if ui := u.User; ui != nil {
				buf.WriteString(ui.String())
				buf.WriteByte('@')
			}
			if h := u.Host; h != "" {
				buf.WriteString(h)
			}
		}
		path := u.RawPath
		if path == "" {
			path = u.Path
		}
		if path != "" && path[0] != '/' && u.Host != "" {
			buf.WriteByte('/')
		}
		if buf.Len() == 0 {

			if i := strings.IndexByte(path, ':'); i > -1 && strings.IndexByte(path[:i], '/') == -1 {
				buf.WriteString("./")
			}
		}
		buf.WriteString(path)
	}
	if u.ForceQuery || u.RawQuery != "" {
		buf.WriteByte('?')
		buf.WriteString(u.RawQuery)
	}
	if u.Fragment != "" {
		buf.WriteByte('#')
		buf.WriteString(u.Fragment)
	}
	return buf.String()
}

func URLJoin(baseURL string, ref string) (string, error) {
	part, err := url.Parse(baseURL)
	if err != nil {
		return "", err
	}
	refPart, err := url.Parse(ref)
	if err != nil {
		return "", err
	}
	return part.ResolveReference(refPart).String(), nil
}

func URLParse(rawURL string) (*url.URL, error) {
	part, err := url.Parse(rawURL)
	if err != nil {
		return nil, err
	}

	if !strings.Contains(part.Host, ":") {
		scheme := strings.ToLower(part.Scheme)
		if scheme == "http" {
			part.Host = fmt.Sprintf("%s:%d", part.Host, 80)
		} else if scheme == "https" {
			part.Host = fmt.Sprintf("%s:%d", part.Host, 443)
		}
	}

	return part, nil
}

func HostFilter(scheme, host string) string {
	strVec := strings.Split(host, ":")
	if len(strVec) == 2 {
		if scheme == "http" && strVec[1] == "80" {
			return strVec[0]
		} else if scheme == "https" && strVec[1] == "443" {
			return strVec[0]
		}
	}

	return host
}
