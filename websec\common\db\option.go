package db

import (
	"errors"
	"websec/config"

	"github.com/jinzhu/gorm"
)

type OptionFn func(*DBConnection) error

const (
	HBaseOriginal int = iota
	HBaseThrift
)

func WithRedisConfig(redisConfig config.RedisConfig) OptionFn {
	return func(connection *DBConnection) error {
		pool, err := GetRedisPool(redisConfig)
		if err != nil {
			return err
		}
		connection.redisConfig = redisConfig
		connection.redisClient = pool
		return nil
	}
}

func WithMongoConfig(mongoConfig config.MongoConfig) OptionFn {
	return func(connection *DBConnection) error {
		mClient, err := GetMongo(mongoConfig)
		if err != nil {
			return err
		}
		connection.mongoConfig = mongoConfig
		connection.mongoClient = mClient
		return nil
	}
}

func WithESConfig(esConfig config.ElasticsearchConfig) OptionFn {
	return func(connection *DBConnection) error {
		es, err := GetES(esConfig)
		if err != nil {
			return err
		}
		connection.esConfig = esConfig
		connection.esClient = es
		return nil
	}
}

func WithHBaseConfig(hBaseConfig config.HBaseConfig) OptionFn {
	return func(connection *DBConnection) error {
		connection.hBaseConfig = hBaseConfig
		if hBaseConfig.ClientType == HBaseOriginal {
			connection.hBaseClient = GetHBaseOriginal(hBaseConfig)
		} else if hBaseConfig.ClientType == HBaseThrift {
			client, err := GetHBaseThrift(hBaseConfig)
			if err != nil {
				return err
			}
			connection.hBaseThriftClient = client
		}

		return nil
	}
}

func WithAssetCache() OptionFn {
	return func(connection *DBConnection) error {
		if connection.redisClient == nil {
			return errors.New("redis not init")
		}
		if connection.mongoClient == nil {
			return errors.New("mongo not init")
		}
		return nil
	}
}

func WithSourceCodeTime(sec int) OptionFn {
	return func(connection *DBConnection) error {
		connection.sourceCodeSaveTime = int64(sec)
		return nil
	}
}

func WithMySQLDB(db *gorm.DB) OptionFn {
	return func(connection *DBConnection) error {
		connection.MySQLDB = db
		return nil
	}
}
