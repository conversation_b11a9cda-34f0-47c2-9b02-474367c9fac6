package db

import (
	"context"
	"encoding/json"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/config"
	"websec/utils"
	"websec/utils/log"

	"github.com/go-redis/redis"
	"github.com/jinzhu/gorm"
	"github.com/tsuna/gohbase"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/x/network/connstring"
	elastic "gopkg.in/olivere/elastic.v5"
)

type DBConnection struct {
	mongoConfig config.MongoConfig
	mongoClient *mongo.Client

	redisConfig config.RedisConfig
	redisClient *redis.ClusterClient

	esConfig config.ElasticsearchConfig
	esClient *elastic.Client

	hBaseConfig       config.HBaseConfig
	hBaseClient       gohbase.Client
	hBaseThriftClient *ThriftHBaseClient

	MySQLDB *gorm.DB

	sourceCodeSaveTime int64
}

func NewDBConnection(options ...OptionFn) (*DBConnection, error) {
	connection := &DBConnection{}

	var err error

	for _, opt := range options {
		err = opt(connection)
		if err != nil {
			return nil, err
		}
	}
	return connection, err
}

func (d *DBConnection) GetMongoDatabase() *mongo.Database {
	cs, _ := connstring.Parse(d.mongoConfig.URI)
	return d.mongoClient.Database(cs.Database)
}

func (d *DBConnection) GetMongoClient() *mongo.Client {
	return d.mongoClient
}

func (d *DBConnection) GetRedisPool() *redis.ClusterClient {
	return d.redisClient
}

func (d *DBConnection) GetESClient() *elastic.Client {
	return d.esClient
}

func (d *DBConnection) GetHBaseClient() gohbase.Client {
	return d.hBaseClient
}

func (d *DBConnection) GetHBaseThriftClient() *ThriftHBaseClient {
	return d.hBaseThriftClient
}

func (d *DBConnection) CloseThriftClient() {
	if d.hBaseThriftClient != nil {
		d.hBaseThriftClient.Close()
	}
}

func (d *DBConnection) GetAsset(host string, useMongo bool) (*schema.Asset, error) {
	bin, err := d.HGetBytes(consts.RedisAssets, host)
	if err != nil && err != redis.Nil {
		return nil, err
	}

	if err == redis.Nil {
		if !useMongo {
			return nil, err
		}
		newAsset, err := d.getAssetFromMongo(host)
		if err != nil {
			return nil, err
		}
		bin, err = json.Marshal(newAsset)
		if err == nil {
			d.HSet(consts.RedisAssets, host, bin)
		}
		return newAsset, nil
	}

	asset := new(schema.Asset)
	err = json.Unmarshal(bin, asset)
	return asset, err
}

func (d *DBConnection) GetAssetByID(assetID string, useMongo bool) (*schema.Asset, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(assetID)
	if err != nil {
		return nil, err
	}

	asset := new(schema.Asset)
	err = d.GetMongoDatabase().Collection(consts.CollectionAssets).FindOne(ctx, bson.M{"_id": objectID}).Decode(asset)
	if err != nil {
		return nil, err
	}

	return asset, nil
}

func (d *DBConnection) GetTaskByID(taskID string, useMongo bool) (*schema.Task, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(taskID)
	if err != nil {
		return nil, err
	}

	task := new(schema.Task)
	err = d.GetMongoDatabase().Collection(consts.CollectionTasks).FindOne(ctx, bson.M{"_id": objectID}).Decode(task)
	if err != nil {
		return nil, err
	}

	return task, nil
}

func (d *DBConnection) getAssetFromMongo(host string) (*schema.Asset, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	asset := new(schema.Asset)
	err := d.GetMongoDatabase().Collection(consts.CollectionAssets).FindOne(ctx, bson.M{"host": host}).Decode(asset)
	if err != nil {
		return nil, err
	}

	return asset, nil
}

func (d *DBConnection) GetSensitiveWordContent(sourceID primitive.ObjectID) ([]byte, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var doc schema.FoundSensitiveWordsDoc

	filter := bson.M{
		"_id": sourceID,
	}
	opts := options.FindOne()
	err := d.GetMongoDatabase().Collection(consts.CollectionFoundSensitiveWords).FindOne(ctx, filter, opts).Decode(&doc)
	if err != nil {
		log.Errorln("getSensitiveWordContent error:", err, sourceID)
		return nil, err
	}

	return doc.Content, nil
}

func (d *DBConnection) SaveSourceCode(rawURL, urlHash string, timestamp int64, content []byte) error {
	gzipContent, err := utils.Zip(content)
	if err != nil {
		return err
	}

	key := GenerateHBaseContentKey(rawURL, urlHash, timestamp)

	return d.RedisSetNx(key, gzipContent, d.sourceCodeSaveTime)
}

func (d *DBConnection) GetSourceCode(key string) ([]byte, error) {
	bin, err := d.GetRedisBytes(key)
	if err != nil {
		return nil, err
	}

	return utils.Unzip(bin)
}
