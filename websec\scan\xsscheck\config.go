package xsscheck

import "github.com/lestrrat/go-libxml2/parser"

const XsscheckSimpleString = "8bb799fa7d0e9753bc451a95ec0bb78e"
const XSSPayloadPlaceholder = "{payload}"
const XSSPAYLOADPATH = "/home/<USER>/engine/go/src/websec/scan/xsscheck/payload.json"

var KeySymbols = []string{
	"<", ">", "[", "]", "{", "}", "(", ")", "'", "\"", "`", "\\",
}

type XSSPayloadStruct struct {
	Tag        string            `json:"tag"`  //标签
	Attr       map[string]string `json:"attr"` //属性
	Text       string            `json:"text"` //文本内容
	TagSupport []string          `json:"tagSupport"'`
}

type XSSPayloadResult struct {
	Vulnerable bool   `json:"vulnerable"`
	Url        string `json:"url"`
	Method     string `json:"method"`
	Data       string `json:"data"`
}

var invulnerableResult = XSSPayloadResult{Vulnerable: false}

var HTMLParserOption parser.HTMLOption = parser.DefaultHTMLOptions | parser.HTMLParseNoNet | parser.HTMLParseNoError | parser.HTMLParseIgnoreEnc

var FunctionList []string = []string{
	"alert(42873)",
	"alert`42873`",
	"top['al'+'ert'](42873)",
	"top['al'+'ert']`42873`",
	"\\u0061lert(42873)", "\\u006142873", "top['al'+'ert']`42873`",
	"top[(false+[])[1]+(false+[])[2]+(true+[])[3]+(true+[])[1]+(true+[])[0]](42873)",
	"top[(false+[])[1]+(false+[])[2]+(true+[])[3]+(true+[])[1]+(true+[])[0]]`42873`",
}
