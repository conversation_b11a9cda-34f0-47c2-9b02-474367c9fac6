package collectors

import (
	"context"
	"encoding/json"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/stream"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (collector *Collector) processTaskStats(msg *stream.Message) error {
	var doc = new(schema.TaskStats)
	err := json.Unmarshal(msg.Value, doc)
	if err != nil {
		return err
	}

	id, err := primitive.ObjectIDFromHex(doc.TaskID)
	if err != nil {
		return err
	}

	return collector.updateTask(id, bson.M{"track.stats": doc.Stats})
}

func (collector *Collector) updateTask(taskID primitive.ObjectID, updates bson.M) error {
	ctx, ctxCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer ctxCancel()

	var err error
	var result *mongo.UpdateResult

Retry:
	for retry := 0; retry < 3; retry++ {

		for _, coll := range []string{
			consts.CollectionTasks,
			consts.CollectionTasksArchive,
		} {
			result, err = collector.dbConnection.GetMongoDatabase().Collection(coll).UpdateOne(ctx,
				bson.M{"_id": taskID},
				bson.M{"$set": updates, "$currentDate": bson.M{"updated_at": true}},
			)
			if err != nil {
				log.Errorln("failed to update task:", coll, taskID, err)
			} else {
				if result.ModifiedCount != 0 {

					break Retry
				}
			}
		}
		time.Sleep(time.Second * 1)
	}
	return err
}
