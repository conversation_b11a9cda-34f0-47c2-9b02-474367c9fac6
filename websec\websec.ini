#jinja2: trim_blocks:False

[scheduler]
host = ***********
port = 18080
max_taskcount = 400
tag = default

[mongodb]
addrs = 127.0.0.1:27017
username = mongo
password = #$F#$@F#Fdadfasfa
database = websec0 
replica_set = 

[redis]
host = ***********:16051
port = 16051
password =
database = 0

[hbase]
addr = hbase.websec.cn:9090
client_type = 2

[period]
black_sensitive = 86400
ignored_blacklink_domains = 86400

[detecter]
safe_browsing_url = http://safe.websec.cn:8686/v4/threatMatches:find
jsunpack_path = /opt/projects/jsunpack-n/
ocr_server_port = 19004
ocr_address = https://ocr.websec.cn
ocr_username = ocr
ocr_api_address = ""

[scanner]
blueprint_path = /opt/projects/websec/xml/All.xml
vuls_xml_dir = /opt/projects/websec/xml/vuls/
sqlmap_path = /opt/projects/sqlmap/
sqlmap_screenshot_path = /opt/projects/websec/js/shell.js
xss_js_screenshot_path = /opt/projects/websec/js/xss4.js
captcha_service = http://captcha.websec.cn:8090/predict
xss_payload_path = /opt/projects/websec/config/payload.json


[jieba]
dict_path = /opt/projects/websec/jieba/jieba.dict.utf8
hmm_path = /opt/projects/websec/jieba/hmm_model.utf8
user_dict_path = /opt/projects/websec/jieba/user.dict.utf8
idf_path = /opt/projects/websec/jieba/idf.utf8
stop_words_path = /opt/projects/websec/jieba/stop_words.utf8

[webscan]
host = 127.0.0.1
pangolin_port = 45500

[crawler]
# chrome_client_ports = 9225,9226,9227,9228,9229,9230
chrome_client_ports = 9225,9226,9227
chrome_concurrency = 50
chrome_server_port = 19222
inject_javascript_path = /opt/projects/websec/chrome/


[kafka]
brokers = ***********:9092,***********:9092,***********:9092
message_max_bytes = 5120000
fetch_message_max_bytes = 5242880

[out_kafka]
brokers = ***********:9092,***********:9092,***********:9092
message_max_bytes = 5120000

[chrome]
host = 127.0.0.1
port = 19222

[consumer]
generator_concurrency = 40
filter_concurrency = 40
collector_concurrency = 40
save_depth = 10
sourcecode_savetime = 600

[snapshot]
chrome_endpoint = http://127.0.0.1:9888/json
phantomjs_config = /opt/projects/websec/js/phantomjs/config.json
phantomjs_script = /opt/projects/websec/js/phantomjs/render_singlepage.js
task_timeout = 120
parallel = 70
chromesema = 50
phantomjssema = 80
uploadimgsema = 50

[api]
addr = 0.0.0.0:9003

[ocr]
concurrency = 3

[s3bucket]
endpoint = s3.websec.cn
ssl = false
region = custom-signing-region
access_key = key 
secret_key = 1234 
bucket_name = websec
acl = public-read
url_prefix = /media/
max_retry = 3
bucket_type = 0

