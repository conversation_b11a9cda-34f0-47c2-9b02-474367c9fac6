package schema

import (
	"fmt"
	"math/rand"
	"net/http"
	"time"
	"websec/common/consts"
	"websec/utils"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type taskCrawl struct {
	Offset     string `codec:"offset,omitempty" json:"offset,omitempty" bson:"offset"`
	MaxDepth   int32  `codec:"max_depth,omitempty" json:"max_depth,omitempty" bson:"max_depth"`
	MaxLinkNum uint64 `codec:"max_link_num,omitempty" json:"max_link_num,omitempty" bson:"max_link_num"`
	Timeout    int32  `codec:"timeout,omitempty" json:"timeout,omitempty" bson:"timeout"`
	FilterReg  string `codec:"filter_reg,omitempty" json:"filter_reg,omitempty" bson:"filter_reg,omitempty"`
}

type taskScan struct {
	Offset  string `codec:"offset,omitempty" json:"offset,omitempty" bson:"offset"`
	Timeout int32  `codec:"timeout,omitempty" json:"timeout,omitempty" bson:"timeout"`

	FilterReg       string   `codec:"filter_reg,omitempty" json:"filter_reg,omitempty" bson:"filter_reg,omitempty"`
	SpecificAffects []string `codec:"specific_affects" json:"specific_affects" bson:"specific_affects"`
	SpecificXMLs    []string `codec:"specific_xmls" json:"specific_xmls" bson:"specific_xmls"`
}

type taskSchedule struct {
	Tag          string    `codec:"tag,omitempty" json:"tag,omitempty" bson:"tag,omitempty"`
	Plan         time.Time `codec:"plan,omitempty" json:"plan,omitempty" bson:"plan"`
	OriginalPlan time.Time `codec:"original_plan,omitempty" json:"original_plan,omitempty" bson:"original_plan"`
	Period       int32     `codec:"period,omitempty" json:"period,omitempty" bson:"period,omitempty"`
	Priority     int32     `codec:"priority,omitempty" json:"priority,omitempty" bson:"priority"`
	Retry        int32     `codec:"retry,omitempty" json:"retry,omitempty" bson:"retry,omitempty"`
}

type taskOptionChrome struct {
	Host string `codec:"host,omitempty" json:"host,omitempty" bson:"host,omitempty"`
	Port uint16 `codec:"port,omitempty" json:"port,omitempty" bson:"port,omitempty"`
}

type taskExtraOptions struct {
	Headers string `codec:"headers" json:"headers" bson:"headers"`
}

type taskStats struct {
	RequestCount   int64  `codec:"request_count,omitempty" json:"request_count,omitempty" bson:"request_count,omitempty"`
	FailedCount    int64  `codec:"failed_count,omitempty" json:"failed_count,omitempty" bson:"failed_count,omitempty"`
	ScanCount      int64  `codec:"scan_count,omitempty" json:"scan_count,omitempty" bson:"scan_count,omitempty"`
	FoundVulsCount int64  `codec:"found_vuls_count,omitempty" json:"found_vuls_count,omitempty" bson:"found_vuls_count,omitempty"`
	FoundURLsCount int64  `codec:"found_urls_count,omitempty" json:"found_urls_count,omitempty" bson:"found_urls_count,omitempty"`
	OuterURLsCount int64  `codec:"outer_urls_count,omitempty" json:"outer_urls_count,omitempty" bson:"outer_urls_count,omitempty"`
	ErrorCount     int64  `codec:"error_count,omitempty" json:"error_count,omitempty" bson:"error_count,omitempty"`
	ErrorReason    string `codec:"error_reason,omitempty" json:"error_reason,omitempty" bson:"error_reason,omitempty"`
}

type taskTrack struct {
	ClientID   string             `codec:"client_id,omitempty" json:"client_id,omitempty" bson:"client_id,omitempty"`
	Address    string             `codec:"address" json:"address" bson:"address"`
	Stats      taskStats          `codec:"stats,omitempty" json:"stats,omitempty" bson:"stats,omitempty"`
	LastTaskID primitive.ObjectID `codec:"last_task_id,omitempty" json:"last_task_id,omitempty" bson:"last_task_id,omitempty"`
}

type Task struct {
	ID                primitive.ObjectID `codec:"id" json:"id" bson:"_id,omitempty"`
	Status            int64              `codec:"status" json:"status" bson:"status"`
	ByChrome          int64              `codec:"by_chrome,omitempty" json:"by_chrome,omitempty" bson:"by_chrome"`
	Host              string             `codec:"host" json:"host" bson:"host"`
	AssetID           primitive.ObjectID `codec:"asset_id" json:"asset_id" bson:"asset_id"`
	JobID             string             `codec:"job_id" json:"job_id" bson:"job_id"`
	Type              string             `codec:"type" json:"type" bson:"type"`
	Schedule          taskSchedule       `codec:"schedule,omitempty" json:"schedule,omitempty" bson:"schedule"`
	Crawl             taskCrawl          `codec:"crawl,omitempty" json:"crawl,omitempty" bson:"crawl"`
	Scan              taskScan           `codec:"scan,omitempty" json:"scan,omitempty" bson:"scan"`
	ExtraOptions      taskExtraOptions   `codec:"extra_options,omitempty" json:"extra_options,omitempty" bson:"extra_options,omitempty"`
	Track             taskTrack          `codec:"track,omitempty" json:"track,omitempty" bson:"track,omitempty"`
	CreatedAt         time.Time          `codec:"created_at,omitempty" json:"created_at" bson:"created_at"`
	StartAt           time.Time          `codec:"start_at,omitempty" json:"start_at,omitempty" bson:"start_at,omitempty"`
	UpdatedAt         time.Time          `codec:"updated_at,omitempty" json:"updated_at,omitempty" bson:"updated_at,omitempty"`
	FinishedAt        time.Time          `codec:"finished_at,omitempty" json:"finished_at,omitempty" bson:"finished_at,omitempty"`
	FinishedStatus    int64              `codec:"finished_status,omitempty" json:"finished_status,omitempty" bson:"finished_status"`
	ExternalScanDepth int                `codec:"external_scan_depth" json:"external_scan_depth" bson:"external_scan_depth"`
	Headers           http.Header        `codec:"headers" json:"headers" bson:"headers"`
}

func (task *Task) String() string {
	return fmt.Sprintf(
		"_id: %v, Host: %v, Type: %v, Scheduler: %v, Crawl: %v, Scan: %v",
		task.ID, task.Host, task.Type, task.Schedule, task.Crawl, task.Scan,
	)
}

func (task *Task) CustomHeaders() http.Header {
	if task.Headers == nil {
		task.Headers = utils.ParseHeader(task.ExtraOptions.Headers)
	}
	return task.Headers
}

func (task *Task) SetHeaders(headers string) {
	task.ExtraOptions = taskExtraOptions{
		Headers: headers,
	}
}

func NewTask(asset *Asset, typ string) *Task {
	now := time.Now()
	return &Task{
		Status:  consts.TaskStatusNew,
		Host:    asset.Host,
		AssetID: asset.ID,
		JobID:   asset.JobID,
		Type:    typ,
		Schedule: taskSchedule{
			Tag:  typ, // default value, needs to be changed
			Plan: now,
		},
		Crawl:             taskCrawl{},
		CreatedAt:         now,
		ExternalScanDepth: asset.Options.ExternalScanPolicy.ScanDepth,
	}
}

type BreakScanAction struct {
	Action   int `json:"action"`
	Done     int `json:"done,omitempty"` //0没有处理 1处理
	ExpireAt int `json:"at,omitempty"`   //失效时间
}

func NewSmartScanTask(asset *Asset) *Task {
	now := time.Now()
	return &Task{
		Status:  0,
		AssetID: asset.ID,
		JobID:   asset.JobID,
		Host:    asset.Host,
		Type:    consts.TaskTypeScan,
		Schedule: taskSchedule{
			Tag:    consts.TaskTagScanSmart,
			Plan:   now.Add(time.Second * time.Duration(rand.Int63n(60*5))),
			Period: 0,
		},
		Scan: taskScan{
			Timeout: 60,
		},
		CreatedAt: now,
	}
}
