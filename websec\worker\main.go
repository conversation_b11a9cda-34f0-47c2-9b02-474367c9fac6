package main

import (
	"context"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"net/url"
	"runtime"
	"strconv"
	"time"
	"websec/api"
	"websec/chrome"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/logger"
	"websec/common/resource"
	"websec/common/schema"
	"websec/config"
	_ "websec/health"
	"websec/scan"
	"websec/scan/rules"
	"websec/schedule"
	"websec/search"
	"websec/standalone"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/stream"
	"websec/utils/tuchuang"

	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	"go.uber.org/zap/zapcore"
)

var version = "Not Set"

var (
	settings *config.Config
)

func main() {
	log.Infoln(`

	--------------------- start -------------------

	`)
	log.Infoln("running version: ", version)

	var err error
	settings, err = config.ParseConfig()
	if err != nil {
		log.Errorln("failed to parse config:", err)
		return
	}

	logger.NewLogger(settings.Log.Dir, zapcore.InfoLevel, 128, 100, 7, true, settings.Log.FileName)

	switch settings.RunMode {
	case ModeWorker:
		runAsWorker()
	case ModeScheduler:
		runAsScheduler()
	case ModeStandalone:
		runStandalone()
	case ModeCrawler:
		runAsCrawler()
	case ModeScanner:
		runAsScanner()
	case ModeChromeServer:
		runAsChromeServer()
	case ModeAPI:
		runAsAPI()
	case ModeSearchCrawler:
		runAsSearchCrawler()
	case ModeStandaloneCrawler:
		runAsStandaloneCrawler()
	case ModeScanSingle:
		runAsScannerSingleLink()
	default:
		log.Warnln("unknown run mode:", settings.RunMode)
		log.Infoln("current supported mode:")
		for _, mode := range []RunMode{
			ModeWorker, ModeScheduler, ModeStandalone, ModeCrawler, ModeScanner,
		} {
			log.Infoln("    - ", mode)
		}
		return
	}
}

func printMemStats(interval time.Duration) {
	m := runtime.MemStats{}
	ticker := time.NewTicker(interval)
	for range ticker.C {
		runtime.ReadMemStats(&m)
		log.Infoln("-------- mem stat ---------")
		log.Infof("    %20s: %v\n", "Alloc", m.Alloc)
		log.Infof("    %20s: %v\n", "TotalAlloc", m.TotalAlloc)
		log.Infof("    %20s: %v\n", "Sys", m.Sys)
		log.Infof("    %20s: %v\n", "Mallocs", m.Mallocs)
		log.Infof("    %20s: %v\n", "Frees", m.Frees)
		log.Infof("    %20s: %v\n", "HeapAlloc", m.HeapAlloc)
		log.Infof("    %20s: %v\n", "HeapSys", m.HeapSys)
		log.Infof("    %20s: %v\n", "HeapIdle", m.HeapIdle)
		log.Infof("    %20s: %v\n", "HeapInuse", m.HeapInuse)
		log.Infof("    %20s: %v\n", "HeapReleased", m.HeapReleased)
		log.Infof("    %20s: %v\n", "HeapObjects", m.HeapObjects)
		log.Infof("    %20s: %v\n", "StackInuse", m.StackInuse)
		log.Infof("    %20s: %v\n", "StackSys", m.StackSys)
		log.Infof("    %20s: %v\n", "GCSys", m.GCSys)
		log.Infof("    %20s: %v\n", "OtherSys", m.OtherSys)
		log.Infof("    %20s: %v\n", "NextGC", m.NextGC)
		log.Infof("    %20s: %v\n", "LastGC", time.Unix(0, (int64)(m.LastGC)))
		log.Infof("    %20s: %v\n", "PauseTotalNs", m.PauseTotalNs)

		log.Infof("    %20s: %v\n", "NumGC", m.NumGC)
		log.Infof("    %20s: %v\n", "NumForcedGC", m.NumForcedGC)
	}
}

func runAsWorker() {
	log.Infoln(`

	--------------------- start (worker mode) -------------------

	`)

	go printMemStats(time.Second * 5)

	producer, err := stream.NewProducer(&stream.ProducerOptions{
		Brokers:         settings.Kafka.Brokers,
		MessageMaxBytes: settings.Kafka.MessageMaxBytes,
	})
	if err != nil {
		log.Fatalln("failed to setup producer:", err)
		return
	}
	producer.Go()

	dbConnection, err := db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis),
		db.WithAssetCache(),
	)
	if err != nil {
		log.Errorln("failed to create db connection:", err)
		return
	}

	bucketSelect := tuchuang.NewBucketSelect(&settings.S3Bucket, settings.API.Addr, settings.S3Bucket.BucketType)
	worker, err := NewWorker(producer, dbConnection, bucketSelect)
	if err != nil {
		log.Errorln("failed to create worker:", err)
		return
	}

	if err = resource.LoadIgnoredBlackLinkDomains(dbConnection.GetMongoDatabase()); err != nil {
		log.Errorln("failed to load ignored blacklink domains:", err)
		return
	}

	resource.LoadIgnoredBlackLinkDomainsPeriodically(dbConnection.GetMongoDatabase(), settings.Period.IgnoredBlackLinkDomains)

	setupSignal(func() {
		worker.Stop()
	})

	if !worker.Register() {
		log.Errorln("failed to register:", err)
		return
	}

	if settings.DebugHost != "" {
		go func() {
			setupHTTPServer(worker)
			log.Infoln(http.ListenAndServe(settings.DebugHost, nil))
		}()
	}

	worker.Run()
}

func runAsScheduler() {
	openDebug()
	{
		conn := settings.Scheduler.MySQL
		sqlDB, err := gorm.Open("mysql", conn)
		if err != nil {
			log.Errorln("failed to connect to mysql:", err)
			return
		}
		opt := &schedule.CollectOpt{
			DB: sqlDB,
		}
		schedule.InitWorkerStatCollect(opt)
	}

	dbConnection, err := db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis),
	)
	if err != nil {
		log.Errorln("failed to create db connection:", err)
		return
	}

	mongodb := dbConnection.GetMongoDatabase()

	serverAddr := fmt.Sprintf("%v:%v", settings.Scheduler.Host, settings.Scheduler.Port)
	scheduler, err := schedule.NewScheduler(
		serverAddr,
		schedule.WithMongo(mongodb),
		schedule.WithMaxTaskCount(settings.Scheduler.MaxTaskCount),
		schedule.WithTag(settings.Scheduler.Tag),
		schedule.WithDBConnection(dbConnection),
		schedule.WithDispatchConcurrency(settings.Scheduler.DispatchConcurrency),
	)
	if err != nil {
		log.Errorln("failed to create scheduler")
		return
	}
	err = scheduler.Restore()
	if err != nil {
		log.Errorln("failed to restore:", err)
		return
	}
	log.Infoln("running on:", serverAddr)

	scheduler.Run()
}

func runStandalone() {
	log.Infoln(`

	--------------------- start (standalone mode) -------------------

	`)
}

func runAsCrawler() {
}

func runAsScanner() {
	var err error
	blueprint, err := rules.LoadBlueprint(settings.Scanner.BlueprintPath, settings.Scanner.VulsPath)
	if err != nil {
		log.Errorln("error while runAsScanner when load blueprint", err)
		return
	}
	bucketSelect := tuchuang.NewBucketSelect(&settings.S3Bucket, settings.API.Addr, settings.S3Bucket.BucketType)
	scannerOptions := &scan.Options{
		Blueprint:            &blueprint,
		Entry:                canonicalizeSite(settings.Target),
		SpecificXMLs:         settings.Scanner.XMLs,
		SpecificAffects:      settings.Scanner.Affects,
		UserAgent:            settings.UserAgent,
		ValidateSQLInjection: false,
		ValidateXSS:          false,
		SqlmapPath:           settings.Scanner.SqlmapPath,
		SqlmapScreenshotPath: settings.Scanner.SqlmapScreenshotPath,
		XSSJSPath:            settings.Scanner.XSSJSPath,
		XSSJSScreenshotPath:  settings.Scanner.XSSJSScreenshotPath,
		PangolinAddress:      settings.Webscan.Host + ":" + strconv.Itoa(int(settings.Webscan.SQLInjectPort)),
		BucketSelect:         bucketSelect,
	}
	scanner, err := scan.NewScanner(scannerOptions)
	if err != nil {
		log.Errorln("failed to create new scanner:", err)
		return
	}
	resultChan, err := scanner.Go()
	if err != nil {
		log.Errorln("failed to run scanner:", err)
		return
	}

	scanner.Add(&scan.ScanLink{
		Method: http.MethodGet,
		URL:    scannerOptions.Entry,
	})
	scanner.AddDone()

	log.Infoln("------------- scanning -----------")
	for result := range resultChan {
		log.Infoln("scan result::", result.RequestCount, " error count:", result.ErrorCount)
		for i := range result.FoundVuls {
			vul := result.FoundVuls[i]
			log.Infoln("  found vul:", vul.Vul.VulXML, vul.Link, vul.VulURL)
		}
	}
}

func runAsScannerSingleLink() {
	var err error
	blueprint, err := rules.LoadBlueprint(settings.Scanner.BlueprintPath, settings.Scanner.VulsPath)
	if err != nil {
		log.Errorln("error while runAsScannerSingleLink when load blueprint", err)
		return
	}
	producer, err := stream.NewProducer(&stream.ProducerOptions{
		Brokers:         settings.Kafka.Brokers,
		MessageMaxBytes: settings.Kafka.MessageMaxBytes,
	})
	if err != nil {
		log.Fatalln("failed to setup producer:", err)
		return
	}
	producer.Go()
	defer producer.Close()

	hh, _ := time.ParseDuration("2h")
	ctx, _ := context.WithCancel(context.Background())

	bucketSelect := tuchuang.NewBucketSelect(&settings.S3Bucket, settings.API.Addr, settings.S3Bucket.BucketType)
	scannerOptions := &scan.Options{
		Blueprint: &blueprint,
		Entry:     canonicalizeSite(settings.Target),

		UserAgent:            settings.UserAgent,
		ValidateSQLInjection: false,
		ValidateXSS:          false,
		Concurrency:          10,
		Sema:                 semaphore.NewWeighted(10),
		ExpiredAt:            time.Now().Add(hh),
		SqlmapPath:           settings.Scanner.SqlmapPath,
		SqlmapScreenshotPath: settings.Scanner.SqlmapScreenshotPath,
		XSSJSPath:            settings.Scanner.XSSJSPath,
		XSSJSScreenshotPath:  settings.Scanner.XSSJSScreenshotPath,
		PangolinAddress:      settings.Webscan.Host + ":" + strconv.Itoa(int(settings.Webscan.SQLInjectPort)),
		BucketSelect:         bucketSelect,
		SpecificXMLs:         settings.SpecificXml,
		Ctx:                  ctx,
	}

	scanner, err := scan.NewScanner(scannerOptions)
	if err != nil {
		log.Errorln("failed to create new scanner:", err)
		return
	}
	link := &scan.AffectLink{
		Method: settings.UrlMethod,
		URL:    settings.ScanUrl,
		Affect: settings.LinkAffect,
	}
	u, err := url.Parse(settings.ScanUrl)
	if err != nil {
		log.Errorln("failed to parse URL:")
		return
	}

	log.Infoln("------------- scanning -----------")

	scanChan, err := scanner.ScanSingleLink(link)
	if err != nil {
		log.Error("scansinglelink err", settings.ScanUrl, err)
		return
	}

Watching:
	for {
		timer := time.NewTimer(time.Second * 1)
		select {

		case scanResult, ok := <-scanChan:
			timer.Stop()
			if ok {

				for i := range scanResult.FoundVuls {
					vul := scanResult.FoundVuls[i]
					if vul == nil {
						continue
					}
					xmlName := vul.Vul.VulXML

					doc := schema.FoundVulDoc{
						Host: u.Host,
						Scan: schema.FoundVulsScan{
							Affect:      vul.Link.Affect,
							Method:      vul.Link.Method,
							URL:         vul.Link.URL,
							Data:        vul.Link.Data,
							Headers:     vul.Link.Headers,
							Fingerprint: vul.Link.Fingerprint(),
						},
						Vul: schema.FoundVulsVul{
							VulXML:   xmlName,
							Severity: vul.Severity,
							VulURL:   vul.VulURL,
							From:     "暂无相关信息",
						},
						Context: vul.Context,
						FoundAt: time.Now(),
						AssetID: settings.AssetIDStr,
					}
					log.Info("vuls", doc)
					producer.Produce(consts.TopicFinalVulResults, &doc)
				}

			} else {
				break Watching
			}
		case <-timer.C:
			log.Info("wait", u.Host)
		}
	}

}

func runAsChromeServer() {
	log.Infoln(`

		--------------------- start (chromeServer mode) -------------------
	
		`)

	openDebug()

	injectJS := LoadInjectJS(settings.Crawler.InjectJSPath)

	options := &chrome.ChromeServerOptions{
		ClientPorts:      settings.Crawler.ChromeClientPorts,
		Concurrency:      settings.Crawler.ChromeConcurrency,
		ServerPort:       settings.Crawler.ChromeServerPort,
		InjectJavaScript: injectJS,
	}
	chromeServer, err := chrome.NewChromeServer(options)
	if err != nil {
		log.Errorln("failed to create chromeServer:", err)
		return
	}
	chromeServer.Go()
}

func runAsAPI() {
	openDebug()
	log.Infoln(`

		--------------------- start (api mode) -------------------
	
		`)

	dbConnection, err := db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
		db.WithHBaseConfig(settings.HBase),
		db.WithRedisConfig(settings.Redis),
	)
	if err != nil {
		log.Errorln("failed to create db connection:", err)
		return
	}

	producer, err := stream.NewProducer(&stream.ProducerOptions{
		Brokers:         settings.Kafka.Brokers,
		MessageMaxBytes: settings.Kafka.MessageMaxBytes,
	})
	if err != nil {
		log.Errorln("failed to create kafka producer")
		return
	}
	producer.Go()
	defer producer.Close()

	options := &api.Options{
		Address:      settings.API.Addr,
		DBConnection: dbConnection,
		Producer:     producer,
	}

	myAPI := api.NewAPI(options, settings)
	myAPI.StartServer()
}

func openDebug() {
	if settings.DebugHost != "" {
		go func() {
			log.Infoln(http.ListenAndServe(settings.DebugHost, nil))
		}()
	}
}

func runAsSearchCrawler() {
	log.Infoln(`

		--------------------- start (search crawler mode) -------------------
	
		`)

	producer, err := stream.NewProducer(&stream.ProducerOptions{
		Brokers:         settings.Kafka.Brokers,
		MessageMaxBytes: settings.Kafka.MessageMaxBytes,
	})
	if err != nil {
		log.Fatalln("failed to setup producer:", err)
		return
	}
	consumer, err := stream.NewConsumer(&stream.ConsumerOptions{
		Brokers:              settings.Kafka.Brokers,
		Group:                "search",
		FetchMessageMaxBytes: settings.Kafka.FetchMessageMaxBytes,
	})
	if err != nil {
		log.Fatalln("failed to setup consumer:", err)
		return
	}

	options := &search.Options{
		Consumer:    consumer,
		Producer:    producer,
		Concurrency: 10,
		ChromeHost:  settings.Chrome.Host,
		ChromePort:  settings.Chrome.Port,
		UserAgent:   settings.UserAgent,
	}

	processor, err := search.NewProcessor(options)
	if err != nil {
		log.Fatalln(err)
		return
	}

	processor.Run()
}

func runAsStandaloneCrawler() {
	log.Infoln(`

		--------------------- start (search crawler mode) -------------------

		`)

	openDebug()

	dbConnection, err := db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
	)
	if err != nil {
		log.Errorln("failed to create db connection:", err)
		return
	}

	producer, err := stream.NewProducer(&stream.ProducerOptions{
		Brokers:         settings.Kafka.Brokers,
		MessageMaxBytes: settings.Kafka.MessageMaxBytes,
	})
	if err != nil {
		log.Fatalln("failed to setup producer:", err)
		return
	}

	options := &standalone.Options{
		Producer:           producer,
		DBConnection:       dbConnection,
		Concurrency:        settings.Crawler.StandaloneConcurrency,
		RequestConcurrency: settings.Crawler.StandaloneRequestConcurrency,
		ChromeHost:         settings.Chrome.Host,
		ChromePort:         settings.Chrome.Port,
		UserAgent:          settings.UserAgent,
		Depth:              10,
		Cycle:              60,
		ScheduleTag:        settings.Scheduler.Tag,
		AssetOffset:        settings.CrawlerOffset,
	}

	sdaCrawler, err := standalone.NewStandaloneCrawler(options)
	if err != nil {
		log.Errorln(err)
		return
	}

	sdaCrawler.Run()
}
