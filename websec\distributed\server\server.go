package server

import (
	"errors"
	"net"
	"websec/distributed/endpoint"
	"websec/utils/log"
)

type OnNewFunc func(*endpoint.Endpoint) error

type Server struct {
	retries  int
	listener net.Listener

	arrivedClients chan *endpoint.Endpoint

	onLostFuncs []endpoint.OnLostFunc
}

func (server *Server) ArrivedClients() chan *endpoint.Endpoint {
	return server.arrivedClients
}
func (server *Server) setRetry(retryCount int) error {
	server.retries = retryCount
	return nil
}

func (server *Server) AddLostHandle(f endpoint.OnLostFunc) error {
	server.onLostFuncs = append(server.onLostFuncs, f)
	return nil
}

func (server *Server) processNewClient(conn net.Conn) error {
	log.Infoln("New Client:", conn)

	var err error

	handler, err := endpoint.NewEndpoint(
		endpoint.WithConnection(conn),
	)
	if err != nil {
		log.Errorln("failed to create endpoint:", err)
		conn.Close()
		return err
	}

	for _, f := range server.onLostFuncs {
		handler.AddLostHandle(f)
	}

	server.arrivedClients <- handler

	return nil
}

func (server *Server) Run() {
	log.Infoln("server running...")
	for {
		conn, err := server.listener.Accept()
		if err != nil {
			log.Fatalf("accept err: %v", err)
			continue
		}

		go server.processNewClient(conn)
	}
}

func NewServer(network string, addr string, options ...optionFn) (*Server, error) {
	var listener net.Listener
	var err error

	switch network {
	case "tcp", "udp":
		listener, err = net.Listen(network, addr)
	case "quic":
		err = errors.New("not supported yet")
	}
	if err != nil {
		return nil, err
	}

	server := &Server{
		listener:       listener,
		retries:        3,
		arrivedClients: make(chan *endpoint.Endpoint),
	}

	for _, opt := range options {
		err := opt(server)
		if err != nil {
			return nil, err
		}
	}
	return server, nil
}
