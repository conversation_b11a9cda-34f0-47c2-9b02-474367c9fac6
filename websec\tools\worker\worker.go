package main

import (
	"fmt"
	"log"
	"net/http"
	_ "net/http/pprof"
	"os"
	"time"
	"websec/distributed/client"
	"websec/distributed/protocol"
)

var logger = log.New(os.Stdout, "", log.Lshortfile|log.Ldate|log.Ltime)

func send(cli *client.Client) {
	request := &protocol.Message{
		Action: 1,
		Body: map[string]string{
			"hello": "world",
		},
	}
	resp, err := cli.Request(request)
	if err != nil {
		fmt.Println("failed to request:", err)
	} else {
		fmt.Println("response:", resp)
	}
}

func register(cli *client.Client) bool {
	request := &protocol.Message{
		Action: protocol.ActionRegister,
	}
	resp, err := cli.Request(request)
	if err != nil {
		logger.Println("failed to register:", err)
		return false
	}
	if resp.Status != protocol.StatusOK {
		logger.Println("failed to register, status:", resp.Status)
		return false
	}
	return true
}

func main() {
	go func() {
		fmt.Println(http.ListenAndServe("0.0.0.0:6060", nil))
	}()

	cli, err := client.NewClient("127.0.0.1:8088")
	if err != nil {
		fmt.Println("failed to create client", err)
		return
	}
	cli.Run()

	if !register(cli) {
		return
	}
	time.Sleep(time.Second * 5)
	for {
		send(cli)
	}
}
