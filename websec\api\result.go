package api

import (
	"context"
	"net/http"
	"reflect"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"

	"github.com/gorilla/mux"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	TypeSensitiveWord = "sensitive-word"
	TypeBlacklink     = "blacklink"
	TypeContentChange = "content-change"
	TypeVul           = "vul"
)

type resultResponse struct {
	Result interface{} `json:"result"`
	Page   int         `json:"page,omitempty"`
	Size   int         `json:"size,omitempty"`
	Total  int         `json:"total,omitempty"`
}

func (api *API) resultHandler(rw http.ResponseWriter, req *http.Request) {
	vars := mux.Vars(req)
	resultType := vars["type"]

	var response *schema.Response
	var err error

	switch resultType {
	case TypeSensitiveWord:
		var docType schema.FoundSensitiveWordsDoc
		response, err = api.querySensitiveWordResult(req, consts.CollectionFoundSensitiveWords, docType)
	case TypeBlacklink:
		var docType schema.FoundBlackLinksDoc
		response, err = api.queryBlacklinkResult(req, consts.CollectionFoundBlackLinks, docType)
	case TypeContentChange:
		var docType schema.FoundContentChangeDoc
		response, err = api.queryContentChangeResult(req, consts.CollectionFoundContentChange, docType)
	case TypeVul:
		var docType schema.FoundVulDoc
		response, err = api.queryVulResult(req, consts.CollectionFoundVuls, docType)
	}
	if err != nil {
		log.Errorln(err)
		api.writeResponse(rw, newFailResponse("failed to get result"))
	}
	api.writeResponse(rw, response)
}

func (api *API) querySensitiveWordResult(req *http.Request, collection string, docType interface{}) (*schema.Response, error) {
	query := req.URL.Query()
	host := query.Get("host")
	rawURL := query.Get("url")
	queryOpts := bson.M{}
	if host != "" {
		queryOpts["host"] = host
	}
	if rawURL != "" {
		queryOpts["url"] = rawURL
	}
	log.Debugln(queryOpts)
	response, err := api.queryResult(req, collection, docType, queryOpts)
	return response, err
}

func (api *API) queryBlacklinkResult(req *http.Request, collection string, docType interface{}) (*schema.Response, error) {
	query := req.URL.Query()
	host := query.Get("host")
	rawURL := query.Get("url")
	queryOpts := bson.M{}
	if host != "" {
		queryOpts["host"] = host
	}
	if rawURL != "" {
		queryOpts["url"] = rawURL
	}
	log.Debugln(queryOpts)
	response, err := api.queryResult(req, collection, docType, queryOpts)
	return response, err
}

func (api *API) queryContentChangeResult(req *http.Request, collection string, docType interface{}) (*schema.Response, error) {
	query := req.URL.Query()
	host := query.Get("host")
	rawURL := query.Get("url")
	queryOpts := bson.M{}
	if host != "" {
		queryOpts["host"] = host
	}
	if rawURL != "" {
		queryOpts["url"] = rawURL
	}
	log.Debugln(queryOpts)
	response, err := api.queryResult(req, collection, docType, queryOpts)
	return response, err
}

func (api *API) queryVulResult(req *http.Request, collection string, docType interface{}) (*schema.Response, error) {
	query := req.URL.Query()
	xmlName := query.Get("xml_name")
	vulURL := query.Get("vul_url")
	assetID := query.Get("asset_id")
	queryOpts := bson.M{}
	if xmlName != "" {
		queryOpts["vul.xml_name"] = xmlName
	}
	if vulURL != "" {
		queryOpts["vul.vul_url"] = vulURL
	}
	if assetID != "" {
		queryOpts["asset_id"] = assetID
	}
	log.Debugln(queryOpts)
	response, err := api.queryResult(req, collection, docType, queryOpts)
	return response, err
}

func (api *API) queryResult(req *http.Request, collection string, docType interface{}, filter interface{}) (*schema.Response, error) {
	query := req.URL.Query()
	page := query.Get("page")
	size := query.Get("size")
	if size == "" {
		size = query.Get("per_page")
	}
	sort := query.Get("sort")
	sortOpts := api.genSortOpts(sort)

	pageNum, sizeNum, err := api.checkPageAndSize(page, size)
	if err != nil {
		log.Errorln(err)
		return newFailResponse("page or size error."), err
	}

	skipNum := int64((pageNum - 1) * sizeNum)
	sortOpts = sortOpts.SetSkip(skipNum).SetLimit(int64(sizeNum))
	if _, ok := docType.(schema.FoundVulDoc); ok {
		sortOpts.SetProjection(bson.M{"context.body": 0})
	}
	if _, ok := docType.(schema.FoundContentChangeDoc); ok {
		sortOpts.SetProjection(bson.M{"diff": 0, "exception_content": 0})
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	mdb := api.options.DBConnection.GetMongoDatabase()
	cursor, err := mdb.Collection(collection).Find(
		ctx, filter, sortOpts)
	if err != nil {
		log.Errorln(err)
		return newFailResponse("query mongodb error."), err
	}

	docs := make([]interface{}, 0, 10)
	for cursor.Next(ctx) {
		doc := reflect.New(reflect.TypeOf(docType)).Interface()
		err := cursor.Decode(doc)
		if err != nil {
			log.Error(err)
			continue
		}
		docs = append(docs, doc)
	}

	var total int64
	if len(filter.(primitive.M)) == 0 {
		total, err = mdb.Collection(collection).EstimatedDocumentCount(ctx)
	} else {
		total, err = mdb.Collection(collection).CountDocuments(ctx, filter, nil)
	}
	if err != nil {
		log.Errorln("failed to get total count:", err)
		return newFailResponse("failed to get total count."), err
	}

	data := resultResponse{
		Result: docs,
		Page:   pageNum,
		Size:   sizeNum,
		Total:  int(total),
	}
	return newSuccessResponse(data), nil
}
