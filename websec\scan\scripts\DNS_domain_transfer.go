package scripts

import (
	"bytes"
	"context"
	"fmt"
	"net"
	"os/exec"
	"regexp"
	"time"

	"golang.org/x/net/publicsuffix"
)

var dnsNameServerPattern = regexp.MustCompile(`nameserver = ([\w\.]+)`)

func DNSDomainTransfer(args *ScriptScanArgs) (*ScriptScanResult, error) {

	var rawurl = constructURL(args, "/")
	rootDomain, err := publicsuffix.EffectiveTLDPlusOne(args.Host)
	if err != nil {
		return nil, err
	}

	nameServers, err := net.LookupNS(rootDomain)
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()

	var cmd *exec.Cmd
	var output []byte
	for i := range nameServers {
		server := nameServers[i].Host
		if len(server) < 5 {
			server += rootDomain
		}
		cmd = exec.CommandContext(ctx, "sh", "-c", fmt.Sprintf("`which dig` @%v axfr %v", server, rootDomain))
		output, err = cmd.CombinedOutput()
		if err != nil {
			continue
		}
		if !bytes.Contains(output, []byte("Transfer failed.")) && !bytes.Contains(output, []byte("connection timed out")) && bytes.Contains(output, []byte("XFR size")) {
			return &ScriptScanResult{Vulnerable: true, Output: fmt.Sprintf("%v", rawurl), Body: output}, nil
		}
	}
	if err != nil {
		return nil, err
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("DNS_domain_transfer.xml", DNSDomainTransfer)
}
