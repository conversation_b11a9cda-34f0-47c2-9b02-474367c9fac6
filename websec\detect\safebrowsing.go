package detect

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
)

type ThreatType = string
type PlatformType = string
type ThreatEntryType = string

type ThreatEntry struct {
	URL string `json:"url"`
}

type ThreatInfo struct {
	ThreatTypes      []ThreatType      `json:"threatTypes"`
	PlatformTypes    []PlatformType    `json:"platformTypes"`
	ThreatEntryTypes []ThreatEntryType `json:"threatEntryTypes"`
	ThreatEntries    []ThreatEntry     `json:"threatEntries"`
}

type ThreatMatch struct {
	Threat          ThreatEntry     `json:"threat"`
	PlatformType    PlatformType    `json:"platformType"`
	ThreatType      ThreatType      `json:"threatType"`
	ThreatEntryType ThreatEntryType `json:"threatEntryType"`
}

const (
	ThreatType_Unspecified                   = "THREAT_TYPE_UNSPECIFIED"
	ThreatType_Malware                       = "MALWARE"
	ThreatType_SocialEngineering             = "SOCIAL_ENGINEERING"
	ThreatType_UnwantedSoftware              = "UNWANTED_SOFTWARE"
	ThreatType_PotentiallyHarmfulApplication = "POTENTIALLY_HARMFUL_APPLICATION"
)

const (
	PlatformType_AnyPlatform  = "ANY_PLATFORM"
	PlatformType_AllPlatforms = "ALL_PLATFORMS"

	PlatformType_Windows = "WINDOWS"
	PlatformType_Linux   = "LINUX"
	PlatformType_Android = "ANDROID"
	PlatformType_OSX     = "OSX"
	PlatformType_iOS     = "IOS"
	PlatformType_Chrome  = "CHROME"
)

const (
	ThreatEntryType_Unspecified = "THREAT_ENTRY_TYPE_UNSPECIFIED"
	ThreatEntryType_URL         = "URL"
	ThreatEntryType_Executable  = "EXECUTABLE"
	ThreatEntryType_IPRange     = "IP_RANGE"
)

type SBRequest struct {
	ThreatInfo ThreatInfo `json:"threatInfo"`
}

type SBResponse struct {
	Matches []ThreatMatch `json:"matches"`
}

func SBLookupURLS(serverURL string, urls []string) ([]ThreatMatch, error) {
	entries := []ThreatEntry{}
	for _, url := range urls {
		entries = append(entries, ThreatEntry{
			URL: url,
		})
	}

	request := SBRequest{
		ThreatInfo: ThreatInfo{
			ThreatTypes:      []ThreatType{},
			PlatformTypes:    []PlatformType{PlatformType_AllPlatforms},
			ThreatEntryTypes: []ThreatEntryType{},
			ThreatEntries:    entries,
		},
	}
	body, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}
	resp, err := http.Post(serverURL, "application/json", bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("safebrowsing: unexpected server response code: %d", resp.StatusCode)
	}
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	sbResponse := SBResponse{}
	err = json.Unmarshal(respBody, &sbResponse)
	if err != nil {
		return nil, err
	}
	return sbResponse.Matches, nil
}
