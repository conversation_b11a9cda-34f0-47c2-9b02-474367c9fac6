package api

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"
	"websec/common"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/gorilla/mux"
)

type sourceCodeResponse struct {
	SourceCode string `json:"source_code"`
	URL        string `json:"url,omitempty"`
}

type vulSourceCodeRequest struct {
	VulIDs []string `json:"vul_ids"`
}

var (
	reSourceID      = regexp.MustCompile(`^[0-9a-f]{24}$`)
	reSourceIDIndex = regexp.MustCompile(`^[0-9a-f]{24}_[0-9]+$`)
)

func (api *API) sensitiveWordSourceCodeHandler(rw http.ResponseWriter, req *http.Request) {
	vars := mux.Vars(req)
	sourceID := vars["source_id"]

	sensitiveWordDoc, err := api.getSensitiveWordDoc(sourceID)
	if err != nil {
		log.Errorln("get sensitiveword error:", err)
		api.writeResponse(rw, newFailResponse("get sensitiveword error."))
		return
	}

	jobID := sensitiveWordDoc.JobID
	rawURL := sensitiveWordDoc.URL
	urlHash := sensitiveWordDoc.URLHash
	versionTime := sensitiveWordDoc.VersionTime

	content, err := api.options.DBConnection.GetHBaseContent(jobID, rawURL, urlHash, versionTime)
	if err != nil {
		log.Errorln("Get content from hbase error.", err)
		api.writeResponse(rw, newFailResponse("Get content from hbase error."))
		return
	}

	highlightContent, err := api.highlightObuscateWords(sensitiveWordDoc.Results, content)
	if err != nil {
		log.Errorln(err)
		highlightContent = content
	}

	data := sourceCodeResponse{
		SourceCode: string(highlightContent),
		URL:        rawURL,
	}
	log.Debugln("finish get sourcecode:", rawURL)
	api.writeResponse(rw, newSuccessResponse(data))
}

func (api *API) getSensitiveWordDoc(sourceID string) (*schema.FoundSensitiveWordsDoc, error) {
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(sourceID)
	if err != nil {
		return nil, err
	}

	cursor, err := mdb.Collection(consts.CollectionFoundSensitiveWords).Find(
		ctx, bson.M{"_id": objectID}, nil)
	if err != nil {
		return nil, err
	}

	var doc schema.FoundSensitiveWordsDoc
	for cursor.Next(ctx) {
		err := cursor.Decode(&doc)
		if err != nil {
			return nil, err
		}
	}
	return &doc, nil
}

var chineseNumMap = map[rune][]byte{
	'零': []byte{'0'},
	'一': []byte{'1'},
	'二': []byte{'2'},
	'三': []byte{'3'},
	'四': []byte{'4'},
	'五': []byte{'5'},
	'六': []byte{'6'},
	'七': []byte{'7'},
	'八': []byte{'8'},
	'九': []byte{'9'},
	'十': []byte{'1', '0'},
}

func mappingChineseToNum(r rune, padding bool) []byte {
	if v, ok := chineseNumMap[r]; ok {
		if padding {
			paddingV := []byte{' ', ' ', ' '}
			copy(paddingV[len(paddingV)-len(v):], v)
			return paddingV
		}
		return v
	}
	return []byte(string(r))
}

func replaceNums(s []byte, padding bool) []byte {
	nb := make([]byte, len(s))
	offset := 0
	for i := 0; i < len(s); {
		wid := 1
		r := rune(s[i])
		if r >= utf8.RuneSelf {
			r, wid = utf8.DecodeRune(s[i:])
		}
		w := mappingChineseToNum(r, padding)
		copy(nb[offset:], w)
		i += wid
		offset += len(w)
	}
	return nb[:offset]
}

func (api *API) highlightObuscateWords(matchedWords []schema.FoundSensitiveWord, page []byte) ([]byte, error) {
	insertPositions := make(map[int]struct{})
	for _, matchedWord := range matchedWords {
		position := matchedWord.Position
		word := []byte(matchedWord.Word)
		wordDigit := replaceNums(word, false)
		endPosition := position + len(wordDigit)
		if len(page) < endPosition {
			err := errors.New("page content does not match which shall be with message")
			log.Error(err)
			return []byte{}, err
		}
		tmpWord := page[endPosition-len(word) : endPosition]
		if !bytes.Equal(bytes.ToLower(tmpWord), bytes.ToLower(word)) {
			insertPositions[endPosition] = struct{}{}
		}
	}

	if len(insertPositions) > 0 {
		var obtext = []byte("（混淆敏感词）")
		var outPage bytes.Buffer
		orderedPositions := make([]int, 0, len(insertPositions))
		for k := range insertPositions {
			orderedPositions = append(orderedPositions, k)
		}
		sort.Ints(orderedPositions)

		pre, post := 0, 0
		for _, v := range orderedPositions {
			pre, post = post, v
			outPage.Write(page[pre:post])
			outPage.Write(obtext)
		}
		outPage.Write(page[post:])
		return outPage.Bytes(), nil
	}
	return page, nil
}

func (api *API) blacklinkSourceCodeHandler(rw http.ResponseWriter, req *http.Request) {
	vars := mux.Vars(req)
	rawSourceID := vars["source_id"]

	var response *schema.Response
	switch {
	case reSourceID.MatchString(rawSourceID):
		response = api.getAffectedURLSourceCode(rawSourceID)
	case reSourceIDIndex.MatchString(rawSourceID):
		response = api.getBlacklinkSourceCode(rawSourceID)
	default:
		response = newFailResponse("source id error.")
	}

	api.writeResponse(rw, response)
}

func (api *API) getBlacklinkSourceCode(rawSourceID string) *schema.Response {

	parts := strings.Split(rawSourceID, "_")
	sourceID := parts[0]
	rawIndex := parts[1]
	index, err := strconv.Atoi(rawIndex)
	if err != nil {
		log.Errorln("convert index error:", err)
		return newFailResponse("convert index error.")
	}

	blacklinkDoc, err := api.getBlacklinkDoc(sourceID)
	if err != nil {
		log.Errorln("get blacklink error:", err)
		return newFailResponse("get blacklink error.")
	}

	if index >= len(blacklinkDoc.Results) {
		log.Errorln("index out of range ", rawSourceID)
		return newFailResponse("index out of range.")
	}
	blacklink := blacklinkDoc.Results[index]
	if !blacklink.IsOuterURL {

		data := sourceCodeResponse{
			SourceCode: "",
		}
		return newSuccessResponse(data)
	}

	jobID := blacklinkDoc.JobID
	versionTime := blacklinkDoc.VersionTime
	rawURL := blacklink.URL
	urlHash := common.URLSha1(rawURL)

	content, err := api.options.DBConnection.GetHBaseContent(jobID, rawURL, urlHash, versionTime)
	if err != nil {
		log.Errorln("Get content from hbase error.", err)
		return newFailResponse("Get content from hbase error.")
	}

	data := sourceCodeResponse{
		SourceCode: string(content),
	}
	log.Debugln("finish get sourcecode:", rawURL)
	return newSuccessResponse(data)
}

func (api *API) getAffectedURLSourceCode(sourceID string) *schema.Response {
	blacklinkDoc, err := api.getBlacklinkDoc(sourceID)
	if err != nil {
		log.Errorln("get blacklink error:", err)
		return newFailResponse("get blacklink error.")
	}

	jobID := blacklinkDoc.JobID
	versionTime := blacklinkDoc.VersionTime
	rawURL := blacklinkDoc.URL
	urlHash := blacklinkDoc.URLHash

	content, err := api.options.DBConnection.GetHBaseContent(jobID, rawURL, urlHash, versionTime)
	if err != nil {
		log.Errorln("Get content from hbase error.", err)
		return newFailResponse("Get content from hbase error.")
	}

	data := sourceCodeResponse{
		SourceCode: string(content),
	}
	log.Debugln("finish get sourcecode:", rawURL)
	return newSuccessResponse(data)
}

func (api *API) getBlacklinkDoc(sourceID string) (*schema.FoundBlackLinksDoc, error) {
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(sourceID)
	if err != nil {
		return nil, err
	}

	cursor, err := mdb.Collection(consts.CollectionFoundBlackLinks).Find(
		ctx, bson.M{"_id": objectID}, nil)
	if err != nil {
		return nil, err
	}

	var doc schema.FoundBlackLinksDoc
	for cursor.Next(ctx) {
		err := cursor.Decode(&doc)
		if err != nil {
			return nil, err
		}
	}
	return &doc, nil
}

func (api *API) vulSourceCodeHandler(rw http.ResponseWriter, req *http.Request) {
	reqData := new(vulSourceCodeRequest)
	err := json.Unmarshal(getHttpBody(req), reqData)
	if err != nil {
		log.Errorln("get request body error:", err)
		api.writeResponse(rw, newFailResponse("get request body error"))
		return
	}

	vulIDS := reqData.VulIDs
	result := make(map[string]string)
	for _, sourceID := range vulIDS {
		body, err := api.getVulBody(sourceID)
		if err != nil {
			log.Errorln("get vul body error:", err, " ", sourceID)
			continue
		}
		result[sourceID] = string(body)
	}

	api.writeResponse(rw, newSuccessResponse(result))
}

func (api *API) getVulBody(sourceID string) ([]byte, error) {
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(sourceID)
	if err != nil {
		return nil, err
	}

	cursor, err := mdb.Collection(consts.CollectionFoundVuls).Find(
		ctx, bson.M{"_id": objectID}, nil)
	if err != nil {
		return nil, err
	}

	var doc schema.FoundVulDoc
	for cursor.Next(ctx) {
		err := cursor.Decode(&doc)
		if err != nil {
			return nil, err
		}
	}

	if body, ok := doc.Context["body"]; ok {
		utf8Body, _ := utils.ForceUtf8([]byte(body.(string)), "utf-8")
		return utf8Body, nil
	}
	return []byte{}, nil
}
