package main

import (
	"context"
	"flag"
	"sync"
	"websec/common/db"
	"websec/config"
	"websec/utils/log"
	"websec/utils/semaphore"
)

var host = flag.String("host", "", "redis host")
var key = flag.String("key", "", "key pattern")

func main() {
	flag.Parse()
	redisClient, err := db.GetRedisPool(config.RedisConfig{
		Host:     *host,
		Password: "tez5EdSoeNml4lf6Dz",
	})

	if err != nil {
		log.Errorln(err)
		return
	}

	startID := uint64(0)
	var wg sync.WaitGroup
	seMa := semaphore.NewWeighted(20)
	for {
		keys, cursor, err := redisClient.Scan(startID, *key, 10000).Result()
		if err != nil {
			log.Errorln(err)
			break
		}

		startID = cursor
		for _, v := range keys {
			if err = seMa.Acquire(context.Background(), 1); err == nil {
				wg.Add(1)
				go func(k string) {
					defer func() {
						seMa.Release(1)
						wg.Done()
					}()
					res, err := redisClient.Del(k).Result()
					log.Infoln(res, err)
				}(v)
			}
		}

		if startID == 0 {
			break
		}
		log.Infoln("del :", len(keys))
	}
	wg.Wait()
}
