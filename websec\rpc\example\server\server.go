package main

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"encoding/pem"
	"math/big"
	"time"
	"websec/rpc/example/genproto"
	"websec/rpc/log"
	"websec/rpc/server"
	"websec/rpc/testdata"
)

type Args struct {
	A int
	B int
}

type Reply struct {
	C int
}

type Arith int

func (t *Arith) Mul(ctx context.Context, args *Args, reply *Reply) error {
	reply.C = args.A * args.B
	return nil
}

func (t *Arith) Add(ctx context.Context, args *Args, reply *Reply) error {
	reply.C = args.A + args.B
	return nil
}

type PBArith int

func (t *PBArith) Mul(ctx context.Context, args *testdata.ProtoArgs, reply *testdata.ProtoReply) error {
	reply.C = args.A * args.B
	return nil
}

func main() {
	s := server.NewServer(server.WithTLSConfig(generateTLSConfig()))
	s.RegisterOn<PERSON>hutdown(func(s *server.Server) {
		log.Info("test onshutdown")
	})
	s.<PERSON>("Arith", new(Arith), "")
	s.RegisterName("PBArith", new(PBArith), "")

	go sendToClient(s)

	s.Serve("tcp", "127.0.0.1:7000")

}

func sendToClient(s *server.Server) {
	defer func() {
		if err := recover(); err != nil {
			log.Error(err)
		}
	}()

	time.Sleep(time.Second)
	for {
		conn := s.RandomActiveConn()
		if conn == nil {
			log.Info("conn is nil")
			time.Sleep(time.Second)
			continue
		}
		args := &Args{
			A: 10,
			B: 20,
		}

		reply := &Reply{}
		err := s.Call(context.Background(), conn, "Arith", "Mul", args, reply)
		if err != nil {
			log.Errorf("failed to call: %v", err)
		}
		log.Info("reply", reply)

		s.SendMessage(conn, "ServerPush", "Test1", &genproto.ServerPush{A: 1, B: 2}, nil)

		time.Sleep(time.Second)
	}
}

func generateTLSConfig() *tls.Config {
	key, err := rsa.GenerateKey(rand.Reader, 1024)
	if err != nil {
		panic(err)
	}
	template := x509.Certificate{SerialNumber: big.NewInt(1)}
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, &key.PublicKey, key)
	if err != nil {
		panic(err)
	}
	keyPEM := pem.EncodeToMemory(&pem.Block{Type: "RSA PRIVATE KEY", Bytes: x509.MarshalPKCS1PrivateKey(key)})
	certPEM := pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: certDER})

	tlsCert, err := tls.X509KeyPair(certPEM, keyPEM)
	if err != nil {
		panic(err)
	}
	return &tls.Config{Certificates: []tls.Certificate{tlsCert}}
}
