package validators

import "regexp"

const (
	VulTypeUnknown string = ""

	GenericSQLInjection                     = "sql_injection"
	GenericXSS                              = "xss"
	GenericDirectoryTraversal               = "dir_traversal"
	GenericCvsSourceLeak                    = "cvs_source_leak"
	GenericSvnSourceLeak                    = "svn_source_leak"
	GenericGitSourceLeak                    = "git_source_leak"
	GenericPHPInfo                          = "phpinfo"
	GenericWebdavWrite                      = "webdav_write"
	GenericFileDownload                     = "file_download"
	GenericErrorMessage                     = "error_message"
	GenericStruts032                        = "struts_032"
	GenericStruts016                        = "struts_016"
	GenericApacheCGI                        = "apache_cgi"
	GenericNginxExecute                     = "nginx_execute"
	GenericPHPErrorsEnabled                 = "php_errors_enabled"
	GenericPHPAllowURLFopen                 = "php_allow_url_fopen"
	GenericPHPAllowURLInclude               = "php_allow_url_include"
	GenericPHPOpenBaseDir                   = "php_open_basedir"
	GenericFTPAnonymous                     = "ftp_anonymous"
	GenericDNSZoneTransfer                  = "dns_zone_transfer"
	GenericShellShock                       = "shell_shock"
	GenericCompressedFile                   = "compressed_file"
	GenericFileBackup                       = "file_backup"
	GenericSensitiveFileExposed             = "sensitive_file_exposed"
	GenericServerPageNotProcessedSourceLeak = "server_page_not_processed_source_leak"
	GenericWeblogicPasswordCracked          = "weblogic_password_cracked"
	GenericWeblogicSsrf                     = "weblogic_ssrf"
	GenericLocalPathDisclosure              = "local_path_disclosure"

	TrivialHTAccessReadable     = "htaccess_readable"
	TrivialApacheServerStatus   = "apache_server_status"
	TrivialJetbrainProjectFiles = "jetbrain_project_files"
	TrivialWebXMLReadable       = "web.xml_readable"
	TrivialResinCauchoStatus    = "resin_caucho_status"
	TrivialFlashCrossDomain     = "flash_crossdomain"
	TrivialWeblogicConsoleFound = "weblogic_console_found"
	TrivialRobotsTxt            = "robots.txt"
	TrivialOpensslHeartbleed    = "openssl_heartbleed"
	TrivialSslCcsInjection      = "ssl_ccs_injection"
	TrivialSslV2Deprecated      = "ssl_v2_deprecated"

	ApplicationSiteServerFiletreeDirTraversal = "siteserver_filetree_dir_traversal"
)

type VulType struct {
	Pattern  *regexp.Regexp
	TypeName string
}

var vulTypes = []VulType{
	VulType{Pattern: regexp.MustCompile(`(?i)\bSql_Injection\b`), TypeName: GenericSQLInjection},
	VulType{Pattern: regexp.MustCompile(`(?i)\bCross_Site_Scripting\b`), TypeName: GenericXSS},
	VulType{Pattern: regexp.MustCompile(`(?i)\bCross_Site_Scripting_in_path\b`), TypeName: GenericXSS},
	VulType{Pattern: regexp.MustCompile(`(?i)\bFile_Cross_Site_Scripting\b`), TypeName: GenericXSS},
	VulType{Pattern: regexp.MustCompile(`(?i)\bDirectories_with_LIST_permissions_enabled\b`), TypeName: GenericDirectoryTraversal},
	VulType{Pattern: regexp.MustCompile(`(?i)\b(Database_Runtime_Error|Application_error|Possible_\.Net_Error_Message|Possible_PHP_Error_Message|Possible_Java_Error_Message)\b`), TypeName: GenericErrorMessage},
	VulType{Pattern: regexp.MustCompile(`(?i)\bhtaccess_readble\b`), TypeName: TrivialHTAccessReadable},
	VulType{Pattern: regexp.MustCompile(`(?i)\bDirectory_backup_check\b`), TypeName: GenericCompressedFile},
	VulType{Pattern: regexp.MustCompile(`(?i)\bcaucho_status\b`), TypeName: TrivialResinCauchoStatus},
	VulType{Pattern: regexp.MustCompile(`(?i)\bFile_backup_check\b`), TypeName: GenericFileBackup},
	VulType{Pattern: regexp.MustCompile(`(?i)\bPossible_backup_dbconfig_file\b`), TypeName: GenericFileBackup},
	VulType{Pattern: regexp.MustCompile(`(?i)\bFlash_security_conf\b`), TypeName: TrivialFlashCrossDomain},
	VulType{Pattern: regexp.MustCompile(`(?i)\bPHP_info_disclosure\b`), TypeName: GenericPHPInfo},
	VulType{Pattern: regexp.MustCompile(`(?i)\bPHP_allow_url_fopen_enabled\b`), TypeName: GenericPHPAllowURLFopen},
	VulType{Pattern: regexp.MustCompile(`(?i)\bPHP_allow_url_include_enabled\b`), TypeName: GenericPHPAllowURLInclude},
	VulType{Pattern: regexp.MustCompile(`(?i)\bPHP_open_basedir_is_not_set\b`), TypeName: GenericPHPOpenBaseDir},
	VulType{Pattern: regexp.MustCompile(`(?i)\bSVN_wc.db_found\b`), TypeName: GenericSvnSourceLeak},
	VulType{Pattern: regexp.MustCompile(`(?i)\bCVS_sensitive_info_disclosure\b`), TypeName: GenericCvsSourceLeak},
	VulType{Pattern: regexp.MustCompile(`(?i)\bWeblogic_console_found\b`), TypeName: TrivialWeblogicConsoleFound},
	VulType{Pattern: regexp.MustCompile(`(?i)\bRobots_text_file_found\b`), TypeName: TrivialRobotsTxt},
	VulType{Pattern: regexp.MustCompile(`(?i)\bSource_code_disclosure\b`), TypeName: GenericServerPageNotProcessedSourceLeak},

	VulType{Pattern: regexp.MustCompile(`(?i)\bopenssl_heartbleed_vuln\b`), TypeName: TrivialOpensslHeartbleed},
	VulType{Pattern: regexp.MustCompile(`(?i)\bssl_ccs_injection\b`), TypeName: TrivialSslCcsInjection},
	VulType{Pattern: regexp.MustCompile(`(?i)\bweblogic_ssrf_vul\b`), TypeName: GenericWeblogicSsrf},
	VulType{Pattern: regexp.MustCompile(`(?i)\blocal_path_disclosure\b`), TypeName: GenericLocalPathDisclosure},
	VulType{Pattern: regexp.MustCompile(`(?i)\bsiteserver_background_fileTree.aspx_directory_traverse\b`), TypeName: ApplicationSiteServerFiletreeDirTraversal},

	VulType{Pattern: regexp.MustCompile(`(?i)\bserver-status_enabled\b`), TypeName: TrivialApacheServerStatus},
}

func getVulTypeName(XMLName string) string {
	for i := range vulTypes {
		vulType := &vulTypes[i]
		if vulType.Pattern.MatchString(XMLName) {
			return vulType.TypeName
		}
	}
	return VulTypeUnknown
}
