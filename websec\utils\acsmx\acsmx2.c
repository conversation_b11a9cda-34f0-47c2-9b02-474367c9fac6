#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>

#include "acsmx2.h"

#define LogMessage printf

#define MEMASSERT(p, s)                     \
    if (!p)                                 \
    {                                       \
        printf("ACSM-No Memory: %s!\n", s); \
    }

static int acsm2_total_memory = 0;
static int acsm2_pattern_memory = 0;
static int acsm2_matchlist_memory = 0;
static int acsm2_transtable_memory = 0;
static int acsm2_dfa_memory = 0;
static int acsm2_dfa1_memory = 0;
static int acsm2_dfa2_memory = 0;
static int acsm2_dfa4_memory = 0;
static int acsm2_failstate_memory = 0;
static int s_verbose = 0;

typedef struct acsm_summary_s
{
    unsigned num_states;
    unsigned num_transitions;
    unsigned num_instances;
    unsigned num_patterns;
    unsigned num_characters;
    unsigned num_match_states;
    unsigned num_1byte_instances;
    unsigned num_2byte_instances;
    unsigned num_4byte_instances;
    ACSM_STRUCT2 acsm;

} acsm_summary_t;

static acsm_summary_t summary;

void acsm_init_summary(void)
{
    summary.num_states = 0;
    summary.num_transitions = 0;
    summary.num_instances = 0;
    summary.num_patterns = 0;
    summary.num_characters = 0;
    summary.num_match_states = 0;
    summary.num_1byte_instances = 0;
    summary.num_2byte_instances = 0;
    summary.num_4byte_instances = 0;
    memset(&summary.acsm, 0, sizeof(ACSM_STRUCT2));
    acsm2_total_memory = 0;
    acsm2_pattern_memory = 0;
    acsm2_matchlist_memory = 0;
    acsm2_transtable_memory = 0;
    acsm2_dfa_memory = 0;
    acsm2_failstate_memory = 0;
}

static unsigned char xlatcase[256];

static void
init_xlatcase()
{
    int i;
    for (i = 0; i < 256; i++)
    {
        xlatcase[i] = (unsigned char)toupper(i);
    }
}

static inline void
ConvertCaseEx(unsigned char *d, unsigned char *s, int m)
{
    int i;
#ifdef XXXX
    int n;
    n = m & 3;
    m >>= 2;

    for (i = 0; i < m; i++)
    {
        d[0] = xlatcase[s[0]];
        d[2] = xlatcase[s[2]];
        d[1] = xlatcase[s[1]];
        d[3] = xlatcase[s[3]];
        d += 4;
        s += 4;
    }

    for (i = 0; i < n; i++)
    {
        d[i] = xlatcase[s[i]];
    }
#else
    for (i = 0; i < m; i++)
    {
        d[i] = xlatcase[s[i]];
    }

#endif
}

#define FILTER_NOISES                                                                                   \
    {                                                                                                   \
        if (total_skipped < 10 && !ps[1])                                                               \
        {                                                                                               \
            if ((*T & 0x80) == 0)                                                                       \
            {                                                                                           \
                if (!isalnum(*T) && *T != '<' && *T != '>')                                             \
                {                                                                                       \
                    total_skipped++;                                                                    \
                    continue;                                                                           \
                }                                                                                       \
            }                                                                                           \
            else if ((*T & 0xc0) == 0x80)                                                               \
            {                                                                                           \
                if (T > Toffset)                                                                        \
                {                                                                                       \
                    total_skipped++;                                                                    \
                    continue;                                                                           \
                }                                                                                       \
            }                                                                                           \
            else if ((*T & 0xe0) == 0xc0)                                                               \
            {                                                                                           \
                T += 1;                                                                                 \
                total_skipped++;                                                                        \
                continue;                                                                               \
            }                                                                                           \
            else if ((*T & 0xf0) == 0xe0)                                                               \
            {                                                                                           \
                if (T + 2 < Tend)                                                                       \
                {                                                                                       \
                    unsigned short code = ((T[0] & 0x0f) << 12) + ((T[1] & 0x3f) << 6) + (T[2] & 0x3f); \
                    if (code < 0x4E00 || code > 0x9FFF)                                                 \
                    {                                                                                   \
                        T += 2;                                                                         \
                        total_skipped++;                                                                \
                        continue;                                                                       \
                    }                                                                                   \
                    Toffset = T + 2;                                                                    \
                }                                                                                       \
                else                                                                                    \
                {                                                                                       \
                    T = Tend;                                                                           \
                    continue;                                                                           \
                }                                                                                       \
            }                                                                                           \
            else if ((*T & 0xf8) == 0xf0)                                                               \
            {                                                                                           \
                T += 3;                                                                                 \
                total_skipped++;                                                                        \
                continue;                                                                               \
            }                                                                                           \
            else                                                                                        \
            {                                                                                           \
                                                                                                        \
                total_skipped++;                                                                        \
                continue;                                                                               \
            }                                                                                           \
        }                                                                                               \
        total_skipped = 0;                                                                              \
                                                                                                        \
        if (T1 == 0)                                                                                    \
        {                                                                                               \
            T0 = T[0];                                                                                  \
            if (!ps[1])                                                                                 \
            {                                                                                           \
                if ((T0 & 0xf0) == 0xe0 && T + 2 < Tend)                                                \
                {                                                                                       \
                    code = (T[0] << 16) + (T[1] << 8) + T[2];                                           \
                    T += 2;                                                                             \
                    switch (code)                                                                       \
                    {                                                                                   \
                    case 15309750:                                                                      \
                        T0 = '0';                                                                       \
                        break;                                                                          \
                    case 14989440:                                                                      \
                    case 15049657:                                                                      \
                        T0 = '1';                                                                       \
                        break;                                                                          \
                    case 14989964:                                                                      \
                    case 15250608:                                                                      \
                        T0 = '2';                                                                       \
                        break;                                                                          \
                    case 14989449:                                                                      \
                    case 15044481:                                                                      \
                        T0 = '3';                                                                       \
                        break;                                                                          \
                    case 15047579:                                                                      \
                    case 15237766:                                                                      \
                        T0 = '4';                                                                       \
                        break;                                                                          \
                    case 14989972:                                                                      \
                    case 14990477:                                                                      \
                        T0 = '5';                                                                       \
                        break;                                                                          \
                    case 15041965:                                                                      \
                    case 15309190:                                                                      \
                        T0 = '6';                                                                       \
                        break;                                                                          \
                    case 14989443:                                                                      \
                    case 15114130:                                                                      \
                        T0 = '7';                                                                       \
                        break;                                                                          \
                    case 15041963:                                                                      \
                    case 15109516:                                                                      \
                        T0 = '8';                                                                       \
                        break;                                                                          \
                    case 14989725:                                                                      \
                    case 15175318:                                                                      \
                        T0 = '9';                                                                       \
                        break;                                                                          \
                    case 15043969:                                                                      \
                    case 15109054:                                                                      \
                        T0 = '1';                                                                       \
                        T1 = '0';                                                                       \
                        T -= 1;                                                                         \
                        break;                                                                          \
                    default:                                                                            \
                        T -= 2;                                                                         \
                    }                                                                                   \
                }                                                                                       \
            }                                                                                           \
        }                                                                                               \
        else                                                                                            \
        {                                                                                               \
            T0 = T1;                                                                                    \
            T1 = 0;                                                                                     \
        }                                                                                               \
    }

void acsmSetVerbose2(void)
{
    s_verbose = 1;
}

typedef enum _Acsm2MemoryType
{
    ACSM2_MEMORY_TYPE__NONE = 0,
    ACSM2_MEMORY_TYPE__PATTERN,
    ACSM2_MEMORY_TYPE__MATCHLIST,
    ACSM2_MEMORY_TYPE__TRANSTABLE,
    ACSM2_MEMORY_TYPE__FAILSTATE

} Acsm2MemoryType;

static void *
AC_MALLOC(
    int n,
    Acsm2MemoryType type)
{
    void *p = calloc(1, n);

    if (p != NULL)
    {
        switch (type)
        {
        case ACSM2_MEMORY_TYPE__PATTERN:
            acsm2_pattern_memory += n;
            break;
        case ACSM2_MEMORY_TYPE__MATCHLIST:
            acsm2_matchlist_memory += n;
            break;
        case ACSM2_MEMORY_TYPE__TRANSTABLE:
            acsm2_transtable_memory += n;
            break;
        case ACSM2_MEMORY_TYPE__FAILSTATE:
            acsm2_failstate_memory += n;
            break;
        case ACSM2_MEMORY_TYPE__NONE:
            break;
        default:
            LogMessage("%s(%d) Invalid memory type\n", __FILE__, __LINE__);
            break;
        }

        acsm2_total_memory += n;
    }

    return p;
}

static void *
AC_MALLOC_DFA(
    int n,
    int sizeofstate)
{
    void *p = calloc(1, n);

    if (p != NULL)
    {
        switch (sizeofstate)
        {
        case 1:
            acsm2_dfa1_memory += n;
            break;
        case 2:
            acsm2_dfa2_memory += n;
            break;
        case 4:
        default:
            acsm2_dfa4_memory += n;
            break;
        }

        acsm2_dfa_memory += n;
        acsm2_total_memory += n;
    }

    return p;
}

static void
AC_FREE(
    void *p,
    int n,
    Acsm2MemoryType type)
{
    if (p != NULL)
    {
        switch (type)
        {
        case ACSM2_MEMORY_TYPE__PATTERN:
            acsm2_pattern_memory -= n;
            break;
        case ACSM2_MEMORY_TYPE__MATCHLIST:
            acsm2_matchlist_memory -= n;
            break;
        case ACSM2_MEMORY_TYPE__TRANSTABLE:
            acsm2_transtable_memory -= n;
            break;
        case ACSM2_MEMORY_TYPE__FAILSTATE:
            acsm2_failstate_memory -= n;
            break;
        case ACSM2_MEMORY_TYPE__NONE:
        default:
            break;
        }

        acsm2_total_memory -= n;
        free(p);
    }
}

static void
AC_FREE_DFA(
    void *p,
    int n,
    int sizeofstate)
{
    if (p != NULL)
    {
        switch (sizeofstate)
        {
        case 1:
            acsm2_dfa1_memory -= n;
            break;
        case 2:
            acsm2_dfa2_memory -= n;
            break;
        case 4:
        default:
            acsm2_dfa4_memory -= n;
            break;
        }

        acsm2_dfa_memory -= n;
        acsm2_total_memory -= n;
        free(p);
    }
}

typedef struct _qnode
{
    int state;
    struct _qnode *next;
} QNODE;

typedef struct _queue
{
    QNODE *head, *tail;
    int count;
} QUEUE;

static void
queue_init(QUEUE *s)
{
    s->head = s->tail = 0;
    s->count = 0;
}

static int
queue_find(QUEUE *s, int state)
{
    QNODE *q;
    q = s->head;
    while (q)
    {
        if (q->state == state)
            return 1;
        q = q->next;
    }
    return 0;
}

static void
queue_add(QUEUE *s, int state)
{
    QNODE *q;

    if (queue_find(s, state))
        return;

    if (!s->head)
    {
        q = s->tail = s->head =
            (QNODE *)AC_MALLOC(sizeof(QNODE), ACSM2_MEMORY_TYPE__NONE);
        MEMASSERT(q, "queue_add");
        q->state = state;
        q->next = 0;
    }
    else
    {
        q = (QNODE *)AC_MALLOC(sizeof(QNODE), ACSM2_MEMORY_TYPE__NONE);
        MEMASSERT(q, "queue_add");
        q->state = state;
        q->next = 0;
        s->tail->next = q;
        s->tail = q;
    }
    s->count++;
}

static int
queue_remove(QUEUE *s)
{
    int state = 0;
    QNODE *q;
    if (s->head)
    {
        q = s->head;
        state = q->state;
        s->head = s->head->next;
        s->count--;

        if (!s->head)
        {
            s->tail = 0;
            s->count = 0;
        }
        AC_FREE(q, sizeof(QNODE), ACSM2_MEMORY_TYPE__NONE);
    }
    return state;
}

static int
queue_count(QUEUE *s)
{
    return s->count;
}

static void
queue_free(QUEUE *s)
{
    while (queue_count(s))
    {
        queue_remove(s);
    }
}

static int List_GetNextState(ACSM_STRUCT2 *acsm, int state, int input)
{
    trans_node_t *t = acsm->acsmTransTable[state];

    while (t)
    {
        if (t->key == (acstate_t)input)
        {
            return t->next_state;
        }
        t = t->next;
    }

    if (state == 0)
        return 0;

    return ACSM_FAIL_STATE2;
}

static int List_GetNextState2(ACSM_STRUCT2 *acsm, int state, int input)
{
    trans_node_t *t = acsm->acsmTransTable[state];

    while (t)
    {
        if (t->key == (acstate_t)input)
        {
            return t->next_state;
        }
        t = t->next;
    }

    return 0;
}
static int List_PutNextState(ACSM_STRUCT2 *acsm, int state, int input, int next_state)
{
    trans_node_t *p;
    trans_node_t *tnew;

    p = acsm->acsmTransTable[state];
    while (p)
    {
        if (p->key == (acstate_t)input)
        {
            p->next_state = next_state;
            return 0;
        }
        p = p->next;
    }

    tnew = (trans_node_t *)AC_MALLOC(sizeof(trans_node_t),
                                     ACSM2_MEMORY_TYPE__TRANSTABLE);
    if (!tnew)
        return -1;

    tnew->key = input;
    tnew->next_state = next_state;
    tnew->next = 0;

    tnew->next = acsm->acsmTransTable[state];
    acsm->acsmTransTable[state] = tnew;

    acsm->acsmNumTrans++;

    return 0;
}
static int
List_FreeTransTable(
    ACSM_STRUCT2 *acsm)
{
    int i;
    trans_node_t *t, *p;

    if (acsm->acsmTransTable == NULL)
        return 0;

    for (i = 0; i < acsm->acsmMaxStates; i++)
    {
        t = acsm->acsmTransTable[i];

        while (t != NULL)
        {
            p = t->next;
            AC_FREE(t, sizeof(trans_node_t), ACSM2_MEMORY_TYPE__TRANSTABLE);
            t = p;
        }
    }

    AC_FREE(acsm->acsmTransTable, sizeof(void *) * acsm->acsmMaxStates,
            ACSM2_MEMORY_TYPE__TRANSTABLE);

    acsm->acsmTransTable = NULL;

    return 0;
}

static int List_PrintTransTable(ACSM_STRUCT2 *acsm)
{
    int i;
    trans_node_t *t;
    ACSM_PATTERN2 *patrn;

    if (!acsm->acsmTransTable)
        return 0;

    printf("Print Transition Table- %d active states\n", acsm->acsmNumStates);

    for (i = 0; i < acsm->acsmNumStates; i++)
    {
        t = acsm->acsmTransTable[i];

        printf("state %3d: ", i);

        while (t)
        {
            if (isascii((int)t->key) && isprint((int)t->key))
                printf("%3c->%-5d\t", t->key, t->next_state);
            else
                printf("%3d->%-5d\t", t->key, t->next_state);

            t = t->next;
        }

        patrn = acsm->acsmMatchList[i];

        while (patrn)
        {
            printf("%.*s ", patrn->n, patrn->patrn);

            patrn = patrn->next;
        }

        printf("\n");
    }
    return 0;
}

static int
List_ConvToFull(
    ACSM_STRUCT2 *acsm,
    acstate_t state,
    acstate_t *full)
{
    int tcnt = 0;
    trans_node_t *t = acsm->acsmTransTable[state];

    memset(full, 0, acsm->sizeofstate * acsm->acsmAlphabetSize);

    if (t == NULL)
        return 0;

    while (t != NULL)
    {
        switch (acsm->sizeofstate)
        {
        case 1:
            *((uint8_t *)full + t->key) = (uint8_t)t->next_state;
            break;
        case 2:
            *((uint16_t *)full + t->key) = (uint16_t)t->next_state;
            break;
        default:
            full[t->key] = t->next_state;
            break;
        }

        tcnt++;
        t = t->next;
    }

    return tcnt;
}

static ACSM_PATTERN2 *
CopyMatchListEntry(ACSM_PATTERN2 *px)
{
    ACSM_PATTERN2 *p;

    p = (ACSM_PATTERN2 *)AC_MALLOC(sizeof(ACSM_PATTERN2),
                                   ACSM2_MEMORY_TYPE__MATCHLIST);
    MEMASSERT(p, "CopyMatchListEntry");

    memcpy(p, px, sizeof(ACSM_PATTERN2));

    p->next = 0;

    return p;
}

static void
AddMatchListEntry(ACSM_STRUCT2 *acsm, int state, ACSM_PATTERN2 *px)
{
    ACSM_PATTERN2 *p;

    p = (ACSM_PATTERN2 *)AC_MALLOC(sizeof(ACSM_PATTERN2),
                                   ACSM2_MEMORY_TYPE__MATCHLIST);

    MEMASSERT(p, "AddMatchListEntry");

    memcpy(p, px, sizeof(ACSM_PATTERN2));

    p->next = acsm->acsmMatchList[state];

    acsm->acsmMatchList[state] = p;
}

static void
AddPatternStates(ACSM_STRUCT2 *acsm, ACSM_PATTERN2 *p)
{
    int state, next, n;
    unsigned char *pattern;

    n = p->n;
    pattern = p->patrn;
    state = 0;

    if (s_verbose)
        printf(" Begin AddPatternStates: acsmNumStates=%d\n", acsm->acsmNumStates);
    if (s_verbose)
        printf("    adding '%.*s', nocase=%d\n", n, p->patrn, p->nocase);

    for (; n > 0; pattern++, n--)
    {
        if (s_verbose)
            printf(" find char='%c'\n", *pattern);

        next = List_GetNextState(acsm, state, *pattern);
        if ((acstate_t)next == ACSM_FAIL_STATE2 || next == 0)
        {
            break;
        }
        state = next;
    }

    for (; n > 0; pattern++, n--)
    {
        if (s_verbose)
            printf(" add char='%c' state=%d NumStates=%d\n", *pattern, state, acsm->acsmNumStates);

        acsm->acsmNumStates++;
        List_PutNextState(acsm, state, *pattern, acsm->acsmNumStates);
        state = acsm->acsmNumStates;
    }

    AddMatchListEntry(acsm, state, p);

    if (s_verbose)
        printf(" End AddPatternStates: acsmNumStates=%d\n", acsm->acsmNumStates);
}

static void
Build_NFA(ACSM_STRUCT2 *acsm)
{
    int r, s, i;
    QUEUE q, *queue = &q;
    acstate_t *FailState = acsm->acsmFailState;
    ACSM_PATTERN2 **MatchList = acsm->acsmMatchList;
    ACSM_PATTERN2 *mlist, *px;

    queue_init(queue);

    for (i = 0; i < acsm->acsmAlphabetSize; i++)
    {
        s = List_GetNextState2(acsm, 0, i);
        if (s)
        {
            queue_add(queue, s);
            FailState[s] = 0;
        }
    }

    while (queue_count(queue) > 0)
    {
        r = queue_remove(queue);

        for (i = 0; i < acsm->acsmAlphabetSize; i++)
        {
            int fs, next;

            s = List_GetNextState(acsm, r, i);

            if ((acstate_t)s != ACSM_FAIL_STATE2)
            {
                queue_add(queue, s);

                fs = FailState[r];

                while ((acstate_t)(next = List_GetNextState(acsm, fs, i)) == ACSM_FAIL_STATE2)
                {
                    fs = FailState[fs];
                }

                FailState[s] = next;

                for (mlist = MatchList[next];
                     mlist;
                     mlist = mlist->next)
                {
                    px = CopyMatchListEntry(mlist);

                    px->next = MatchList[s];
                    MatchList[s] = px;
                }
            }
        }
    }

    queue_free(queue);

    if (s_verbose)
        printf("End Build_NFA: NumStates=%d\n", acsm->acsmNumStates);
}

static void
Convert_NFA_To_DFA(ACSM_STRUCT2 *acsm)
{
    int i, r, s, cFailState;
    QUEUE q, *queue = &q;
    acstate_t *FailState = acsm->acsmFailState;

    queue_init(queue);

    for (i = 0; i < acsm->acsmAlphabetSize; i++)
    {
        s = List_GetNextState(acsm, 0, i);
        if (s != 0)
        {
            queue_add(queue, s);
        }
    }

    while (queue_count(queue) > 0)
    {
        r = queue_remove(queue);

        for (i = 0; i < acsm->acsmAlphabetSize; i++)
        {
            s = List_GetNextState(acsm, r, i);

            if ((acstate_t)s != ACSM_FAIL_STATE2 && s != 0)
            {
                queue_add(queue, s);
            }
            else
            {
                cFailState = List_GetNextState(acsm, FailState[r], i);

                if (cFailState != 0 && (acstate_t)cFailState != ACSM_FAIL_STATE2)
                {
                    List_PutNextState(acsm, r, i, cFailState);
                }
            }
        }
    }

    queue_free(queue);

    if (s_verbose)
        printf("End Convert_NFA_To_DFA: NumStates=%d\n", acsm->acsmNumStates);
}

static int
Conv_List_To_Full(
    ACSM_STRUCT2 *acsm)
{
    acstate_t k;
    acstate_t *p;
    acstate_t **NextState = acsm->acsmNextState;

    for (k = 0; k < (acstate_t)acsm->acsmNumStates; k++)
    {
        p = AC_MALLOC_DFA(acsm->sizeofstate * (acsm->acsmAlphabetSize + 2),
                          acsm->sizeofstate);
        if (p == NULL)
            return -1;

        switch (acsm->sizeofstate)
        {
        case 1:
            List_ConvToFull(acsm, k, (acstate_t *)((uint8_t *)p + 2));
            *((uint8_t *)p) = ACF_FULL;
            *((uint8_t *)p + 1) = 0;
            break;
        case 2:
            List_ConvToFull(acsm, k, (acstate_t *)((uint16_t *)p + 2));
            *((uint16_t *)p) = ACF_FULL;
            *((uint16_t *)p + 1) = 0;
            break;
        default:
            List_ConvToFull(acsm, k, (p + 2));
            p[0] = ACF_FULL;
            p[1] = 0;
            break;
        }

        NextState[k] = p;
    }

    return 0;
}

static int
Conv_Full_DFA_To_Sparse(ACSM_STRUCT2 *acsm)
{
    int cnt, m, k, i;
    acstate_t *p, state, maxstates = 0;
    acstate_t **NextState = acsm->acsmNextState;
    acstate_t full[MAX_ALPHABET_SIZE];

    for (k = 0; k < acsm->acsmNumStates; k++)
    {
        cnt = 0;

        List_ConvToFull(acsm, (acstate_t)k, full);

        for (i = 0; i < acsm->acsmAlphabetSize; i++)
        {
            state = full[i];
            if (state != 0 && state != ACSM_FAIL_STATE2)
                cnt++;
        }

        if (cnt > 0)
            maxstates++;

        if (k == 0 || cnt > acsm->acsmSparseMaxRowNodes)
        {
            p = AC_MALLOC_DFA(sizeof(acstate_t) * (acsm->acsmAlphabetSize + 2),
                              sizeof(acstate_t));
            if (!p)
                return -1;

            p[0] = ACF_FULL;
            p[1] = 0;
            memcpy(&p[2], full, acsm->acsmAlphabetSize * sizeof(acstate_t));
        }
        else
        {
            p = AC_MALLOC_DFA(sizeof(acstate_t) * (3 + 2 * cnt),
                              sizeof(acstate_t));
            if (!p)
                return -1;

            m = 0;
            p[m++] = ACF_SPARSE;
            p[m++] = 0;
            p[m++] = cnt;

            for (i = 0; i < acsm->acsmAlphabetSize; i++)
            {
                state = full[i];
                if (state != 0 && state != ACSM_FAIL_STATE2)
                {
                    p[m++] = i;
                    p[m++] = state;
                }
            }
        }

        NextState[k] = p;
    }

    return 0;
}

static int
Conv_Full_DFA_To_Banded(ACSM_STRUCT2 *acsm)
{
    int first = -1, last;
    acstate_t *p, state, full[MAX_ALPHABET_SIZE];
    acstate_t **NextState = acsm->acsmNextState;
    int cnt, m, k, i;

    for (k = 0; k < acsm->acsmNumStates; k++)
    {
        cnt = 0;

        List_ConvToFull(acsm, (acstate_t)k, full);

        first = -1;
        last = -2;

        for (i = 0; i < acsm->acsmAlphabetSize; i++)
        {
            state = full[i];

            if (state != 0 && state != ACSM_FAIL_STATE2)
            {
                if (first < 0)
                    first = i;
                last = i;
            }
        }

        cnt = last - first + 1;

        p = AC_MALLOC_DFA(sizeof(acstate_t) * (4 + cnt), sizeof(acstate_t));

        if (!p)
            return -1;

        m = 0;
        p[m++] = ACF_BANDED;
        p[m++] = 0;
        p[m++] = cnt;
        p[m++] = first;

        for (i = first; i <= last; i++)
        {
            p[m++] = full[i];
        }

        NextState[k] = p;
    }

    return 0;
}

static int calcSparseBands(acstate_t *next, int *begin, int *end, int asize, int zmax)
{
    int i, nbands, zcnt, last = 0;
    acstate_t state;

    nbands = 0;
    for (i = 0; i < asize; i++)
    {
        state = next[i];
        if (state != 0 && state != ACSM_FAIL_STATE2)
        {
            begin[nbands] = i;
            zcnt = 0;
            for (; i < asize; i++)
            {
                state = next[i];
                if (state == 0 || state == ACSM_FAIL_STATE2)
                {
                    zcnt++;
                    if (zcnt > zmax)
                        break;
                }
                else
                {
                    zcnt = 0;
                    last = i;
                }
            }
            end[nbands++] = last;
        }
    }
    return nbands;
}

static int
Conv_Full_DFA_To_SparseBands(ACSM_STRUCT2 *acsm)
{
    acstate_t *p;
    acstate_t **NextState = acsm->acsmNextState;
    int cnt, m, k, i, zcnt = acsm->acsmSparseMaxZcnt;

    int band_begin[MAX_ALPHABET_SIZE];
    int band_end[MAX_ALPHABET_SIZE];
    int nbands, j;
    acstate_t full[MAX_ALPHABET_SIZE];

    for (k = 0; k < acsm->acsmNumStates; k++)
    {
        cnt = 0;

        List_ConvToFull(acsm, (acstate_t)k, full);

        nbands = calcSparseBands(full, band_begin, band_end, acsm->acsmAlphabetSize, zcnt);

        cnt = 3;
        for (i = 0; i < nbands; i++)
        {
            cnt += 2;
            cnt += band_end[i] - band_begin[i] + 1;
        }

        p = AC_MALLOC_DFA(sizeof(acstate_t) * (cnt), sizeof(acstate_t));

        if (!p)
            return -1;

        m = 0;
        p[m++] = ACF_SPARSEBANDS;
        p[m++] = 0;
        p[m++] = nbands;

        for (i = 0; i < nbands; i++)
        {
            p[m++] = band_end[i] - band_begin[i] + 1;
            p[m++] = band_begin[i];

            for (j = band_begin[i]; j <= band_end[i]; j++)
            {
                if (j >= MAX_ALPHABET_SIZE)
                {
                    AC_FREE_DFA(p, sizeof(acstate_t) * (cnt), sizeof(acstate_t));
                    return -1;
                }

                p[m++] = full[j];
            }
        }

        NextState[k] = p;
    }

    return 0;
}

static void
Print_DFA_MatchList(ACSM_STRUCT2 *acsm, int state)
{
    ACSM_PATTERN2 *mlist;

    for (mlist = acsm->acsmMatchList[state];
         mlist;
         mlist = mlist->next)
    {
        printf("%.*s ", mlist->n, mlist->patrn);
    }
}
static void
Print_DFA(ACSM_STRUCT2 *acsm)
{
    int k, i;
    acstate_t *p, state, n, fmt, index, nb;
    acstate_t **NextState = acsm->acsmNextState;

    printf("Print DFA - %d active states\n", acsm->acsmNumStates);

    for (k = 0; k < acsm->acsmNumStates; k++)
    {
        p = NextState[k];

        if (!p)
            continue;

        fmt = *p++;

        printf("state %3d, fmt=%d: ", k, fmt);

        if (fmt == ACF_SPARSE)
        {
            n = *p++;
            for (; n > 0; n--, p += 2)
            {
                if (isascii((int)p[0]) && isprint((int)p[0]))
                    printf("%3c->%-5d\t", p[0], p[1]);
                else
                    printf("%3d->%-5d\t", p[0], p[1]);
            }
        }
        else if (fmt == ACF_BANDED)
        {

            n = *p++;
            index = *p++;

            for (; n > 0; n--, p++)
            {
                if (isascii((int)p[0]) && isprint((int)p[0]))
                    printf("%3c->%-5d\t", index++, p[0]);
                else
                    printf("%3d->%-5d\t", index++, p[0]);
            }
        }
        else if (fmt == ACF_SPARSEBANDS)
        {
            nb = *p++;
            for (i = 0; (acstate_t)i < nb; i++)
            {
                n = *p++;
                index = *p++;
                for (; n > 0; n--, p++)
                {
                    if (isascii((int)index) && isprint((int)index))
                        printf("%3c->%-5d\t", index++, p[0]);
                    else
                        printf("%3d->%-5d\t", index++, p[0]);
                }
            }
        }
        else if (fmt == ACF_FULL)
        {

            for (i = 0; i < acsm->acsmAlphabetSize; i++)
            {
                state = p[i];

                if (state != 0 && state != ACSM_FAIL_STATE2)
                {
                    if (isascii(i) && isprint(i))
                        printf("%3c->%-5d\t", i, state);
                    else
                        printf("%3d->%-5d\t", i, state);
                }
            }
        }

        Print_DFA_MatchList(acsm, k);

        printf("\n");
    }
}

int acsmSelectFormat2(ACSM_STRUCT2 *acsm, int m)
{
    switch (m)
    {
    case ACF_FULL:
    case ACF_SPARSE:
    case ACF_BANDED:
    case ACF_SPARSEBANDS:
    case ACF_FULLQ:
        acsm->acsmFormat = m;
        break;
    default:
        return -1;
    }

    return 0;
}

void acsmSetMaxSparseBandZeros2(ACSM_STRUCT2 *acsm, int n)
{
    acsm->acsmSparseMaxZcnt = n;
}

void acsmSetMaxSparseElements2(ACSM_STRUCT2 *acsm, int n)
{
    acsm->acsmSparseMaxRowNodes = n;
}

int acsmSelectFSA2(ACSM_STRUCT2 *acsm, int m)
{
    switch (m)
    {
    case FSA_TRIE:
    case FSA_NFA:
    case FSA_DFA:
        acsm->acsmFSA = m;
    default:
        return -1;
    }
}

int acsmSetAlphabetSize2(ACSM_STRUCT2 *acsm, int n)
{
    if (n <= MAX_ALPHABET_SIZE)
    {
        acsm->acsmAlphabetSize = n;
    }
    else
    {
        return -1;
    }
    return 0;
}

ACSM_STRUCT2 *acsmNew2(void (*userfree)(void *p),
                       void (*optiontreefree)(void **p),
                       void (*neg_list_free)(void **p))
{
    ACSM_STRUCT2 *p;

    init_xlatcase();

    p = (ACSM_STRUCT2 *)AC_MALLOC(sizeof(ACSM_STRUCT2), ACSM2_MEMORY_TYPE__NONE);
    MEMASSERT(p, "acsmNew");

    if (p)
    {
        memset(p, 0, sizeof(ACSM_STRUCT2));

        p->acsmFSA = FSA_DFA;
        p->acsmFormat = ACF_FULL; // ACF_BANDED;
        p->acsmAlphabetSize = 256;
        p->acsmSparseMaxRowNodes = 256;
        p->acsmSparseMaxZcnt = 10;
        p->userfree = userfree;
        p->optiontreefree = optiontreefree;
        p->neg_list_free = neg_list_free;
    }

    return p;
}

int acsmAddPattern2(ACSM_STRUCT2 *p, unsigned char *pat, int n, int nocase,
                    int offset, int depth, int negative, void *id, int iid)
{
    ACSM_PATTERN2 *plist;

    plist = (ACSM_PATTERN2 *)
        AC_MALLOC(sizeof(ACSM_PATTERN2), ACSM2_MEMORY_TYPE__PATTERN);
    MEMASSERT(plist, "acsmAddPattern");

    plist->patrn =
        (unsigned char *)AC_MALLOC(n, ACSM2_MEMORY_TYPE__PATTERN);
    MEMASSERT(plist->patrn, "acsmAddPattern");

    ConvertCaseEx(plist->patrn, pat, n);

    plist->casepatrn =
        (unsigned char *)AC_MALLOC(n, ACSM2_MEMORY_TYPE__PATTERN);
    MEMASSERT(plist->casepatrn, "acsmAddPattern");

    memcpy(plist->casepatrn, pat, n);

    plist->n = n;
    plist->nocase = nocase;
    plist->offset = offset;
    plist->depth = depth;
    plist->negative = negative;
    plist->iid = iid;
    plist->udata = id;

    plist->next = p->acsmPatterns;
    p->acsmPatterns = plist;
    p->numPatterns++;

    return 0;
}

int acsmAddKey2(ACSM_STRUCT2 *p, unsigned char *key, int klen, int nocase, void *data)
{
    ACSM_PATTERN2 *plist;

    plist = (ACSM_PATTERN2 *)
        AC_MALLOC(sizeof(ACSM_PATTERN2), ACSM2_MEMORY_TYPE__PATTERN);
    MEMASSERT(plist, "acsmAddPattern");

    plist->patrn =
        (unsigned char *)AC_MALLOC(klen, ACSM2_MEMORY_TYPE__PATTERN);
    MEMASSERT(plist->patrn, "acsmAddPattern");
    memcpy(plist->patrn, key, klen);

    plist->casepatrn =
        (unsigned char *)AC_MALLOC(klen, ACSM2_MEMORY_TYPE__PATTERN);
    MEMASSERT(plist->casepatrn, "acsmAddPattern");
    memcpy(plist->casepatrn, key, klen);

    plist->n = klen;
    plist->nocase = nocase;
    plist->offset = 0;
    plist->depth = 0;
    plist->iid = 0;
    plist->udata = 0;

    plist->next = p->acsmPatterns;
    p->acsmPatterns = plist;

    return 0;
}

static void
acsmUpdateMatchStates(
    ACSM_STRUCT2 *acsm)
{
    acstate_t state;
    acstate_t **NextState = acsm->acsmNextState;
    ACSM_PATTERN2 **MatchList = acsm->acsmMatchList;

    for (state = 0; state < (acstate_t)acsm->acsmNumStates; state++)
    {
        acstate_t *p = NextState[state];

        if (MatchList[state])
        {
            switch (acsm->sizeofstate)
            {
            case 1:
                *((uint8_t *)p + 1) = 1;
                break;
            case 2:
                *((uint16_t *)p + 1) = 1;
                break;
            default:
                p[1] = 1;
                break;
            }

            summary.num_match_states++;
        }
    }
}

static int acsmBuildMatchStateTrees2(ACSM_STRUCT2 *acsm,
                                     int (*build_tree)(void *id, void **existing_tree),
                                     int (*neg_list_func)(void *id, void **list))
{
    int i, cnt = 0;
    ACSM_PATTERN2 **MatchList = acsm->acsmMatchList;
    ACSM_PATTERN2 *mlist;

    for (i = 0; i < acsm->acsmNumStates; i++)
    {
        for (mlist = MatchList[i];
             mlist != NULL;
             mlist = mlist->next)
        {
            if (mlist->udata)
            {
                if (mlist->negative)
                {
                    neg_list_func(mlist->udata, &MatchList[i]->neg_list);
                }
                else
                {
                    build_tree(mlist->udata, &MatchList[i]->rule_option_tree);
                }
            }

            cnt++;
        }

        if (MatchList[i])
        {

            build_tree(NULL, &MatchList[i]->rule_option_tree);
        }
    }

    return cnt;
}

static int acsmBuildMatchStateTrees2WithSnortConf(struct _SnortConfig *sc, ACSM_STRUCT2 *acsm,
                                                  int (*build_tree)(struct _SnortConfig *, void *id, void **existing_tree),
                                                  int (*neg_list_func)(void *id, void **list))
{
    int i, cnt = 0;
    ACSM_PATTERN2 **MatchList = acsm->acsmMatchList;
    ACSM_PATTERN2 *mlist;

    for (i = 0; i < acsm->acsmNumStates; i++)
    {
        for (mlist = MatchList[i];
             mlist != NULL;
             mlist = mlist->next)
        {
            if (mlist->udata)
            {
                if (mlist->negative)
                {
                    neg_list_func(mlist->udata, &MatchList[i]->neg_list);
                }
                else
                {
                    build_tree(sc, mlist->udata, &MatchList[i]->rule_option_tree);
                }
            }

            cnt++;
        }

        if (MatchList[i])
        {

            build_tree(sc, NULL, &MatchList[i]->rule_option_tree);
        }
    }

    return cnt;
}

void acsmCompressStates(
    ACSM_STRUCT2 *acsm,
    int flag)
{
    if (acsm == NULL)
        return;
    acsm->compress_states = flag;
}

static inline int
_acsmCompile2(
    ACSM_STRUCT2 *acsm)
{
    ACSM_PATTERN2 *plist;

    for (plist = acsm->acsmPatterns; plist != NULL; plist = plist->next)
        acsm->acsmMaxStates += plist->n;

    acsm->acsmMaxStates++;

    acsm->acsmTransTable =
        (trans_node_t **)AC_MALLOC(sizeof(trans_node_t *) * acsm->acsmMaxStates,
                                   ACSM2_MEMORY_TYPE__TRANSTABLE);
    MEMASSERT(acsm->acsmTransTable, "acsmCompile");

    if (s_verbose)
    {
        printf("ACSMX-Max Memory-TransTable Setup: %d bytes, %d states, "
               "%d active states\n",
               acsm2_total_memory, acsm->acsmMaxStates, acsm->acsmNumStates);
    }

    acsm->acsmMatchList =
        (ACSM_PATTERN2 **)AC_MALLOC(sizeof(ACSM_PATTERN2 *) * acsm->acsmMaxStates,
                                    ACSM2_MEMORY_TYPE__MATCHLIST);
    MEMASSERT(acsm->acsmMatchList, "acsmCompile");

    if (s_verbose)
    {
        printf("ACSMX-Max Memory- MatchList Table Setup: %d bytes, %d states, "
               "%d active states\n",
               acsm2_total_memory, acsm->acsmMaxStates, acsm->acsmNumStates);
        printf("ACSMX-Max Memory-Table Setup: %d bytes, %d states, %d active "
               "states\n",
               acsm2_total_memory, acsm->acsmMaxStates, acsm->acsmNumStates);
    }

    acsm->acsmNumStates = 0;

    for (plist = acsm->acsmPatterns; plist != NULL; plist = plist->next)
    {
        summary.num_patterns++;
        summary.num_characters += plist->n;
        AddPatternStates(acsm, plist);
    }

    acsm->acsmNumStates++;

    if (acsm->compress_states)
    {
        if (acsm->acsmNumStates < UINT8_MAX)
        {
            acsm->sizeofstate = 1;
            summary.num_1byte_instances++;
        }
        else if (acsm->acsmNumStates < UINT16_MAX)
        {
            acsm->sizeofstate = 2;
            summary.num_2byte_instances++;
        }
        else
        {
            acsm->sizeofstate = 4;
            summary.num_4byte_instances++;
        }
    }
    else
    {
        acsm->sizeofstate = 4;
    }

    acsm->acsmFailState =
        (acstate_t *)AC_MALLOC(sizeof(acstate_t) * acsm->acsmNumStates,
                               ACSM2_MEMORY_TYPE__FAILSTATE);
    MEMASSERT(acsm->acsmFailState, "acsmCompile");

    acsm->acsmNextState =
        (acstate_t **)AC_MALLOC_DFA(acsm->acsmNumStates * sizeof(acstate_t *),
                                    acsm->sizeofstate);
    MEMASSERT(acsm->acsmNextState, "acsmCompile-NextState");

    if (s_verbose)
    {
        printf("ACSMX-Max Trie List Memory : %d bytes, %d states, %d "
               "active states\n",
               acsm2_total_memory, acsm->acsmMaxStates, acsm->acsmNumStates);
        List_PrintTransTable(acsm);
    }

    if ((acsm->acsmFSA == FSA_DFA) || (acsm->acsmFSA == FSA_NFA))
    {

        if (s_verbose)
            printf("Build_NFA\n");

        Build_NFA(acsm);

        if (s_verbose)
        {
            printf("NFA-Trans-Nodes: %d\n", acsm->acsmNumTrans);
            printf("ACSMX-Max NFA List Memory  : %d bytes, %d states / %d "
                   "active states\n",
                   acsm2_total_memory, acsm->acsmMaxStates, acsm->acsmNumStates);
            List_PrintTransTable(acsm);
        }
    }

    if (acsm->acsmFSA == FSA_DFA)
    {

        if (s_verbose)
            printf("Convert_NFA_To_DFA\n");

        Convert_NFA_To_DFA(acsm);

        if (s_verbose)
        {
            printf("DFA-Trans-Nodes: %d\n", acsm->acsmNumTrans);
            printf("ACSMX-Max NFA-DFA List Memory  : %d bytes, %d states / %d "
                   "active states\n",
                   acsm2_total_memory, acsm->acsmMaxStates, acsm->acsmNumStates);
            List_PrintTransTable(acsm);
        }
    }

    if (s_verbose)
    {
        printf("Converting Transition Lists -> Transition table, fmt=%d\n",
               acsm->acsmFormat);
    }

    if (acsm->acsmFormat == ACF_SPARSE)
    {

        if (Conv_Full_DFA_To_Sparse(acsm))
            return -1;

        if (s_verbose)
        {
            printf("ACSMX-Max Memory-Sparse: %d bytes, %d states, %d "
                   "active states\n",
                   acsm2_total_memory, acsm->acsmMaxStates, acsm->acsmNumStates);
            Print_DFA(acsm);
        }
    }
    else if (acsm->acsmFormat == ACF_BANDED)
    {

        if (Conv_Full_DFA_To_Banded(acsm))
            return -1;

        if (s_verbose)
        {
            printf("ACSMX-Max Memory-banded: %d bytes, %d states, %d "
                   "active states\n",
                   acsm2_total_memory, acsm->acsmMaxStates, acsm->acsmNumStates);
            Print_DFA(acsm);
        }
    }
    else if (acsm->acsmFormat == ACF_SPARSEBANDS)
    {

        if (Conv_Full_DFA_To_SparseBands(acsm))
            return -1;

        if (s_verbose)
        {
            printf("ACSMX-Max Memory-sparse-bands: %d bytes, %d states, %d "
                   "active states\n",
                   acsm2_total_memory, acsm->acsmMaxStates, acsm->acsmNumStates);
            Print_DFA(acsm);
        }
    }
    else if ((acsm->acsmFormat == ACF_FULL) || (acsm->acsmFormat == ACF_FULLQ))
    {
        if (Conv_List_To_Full(acsm))
            return -1;

        if (s_verbose)
        {
            printf("ACSMX-Max Memory-Full: %d bytes, %d states, %d active "
                   "states\n",
                   acsm2_total_memory, acsm->acsmMaxStates, acsm->acsmNumStates);
            Print_DFA(acsm);
        }

        AC_FREE(acsm->acsmFailState, sizeof(acstate_t) * acsm->acsmNumStates,
                ACSM2_MEMORY_TYPE__FAILSTATE);
        acsm->acsmFailState = NULL;
    }

    acsmUpdateMatchStates(acsm);

    List_FreeTransTable(acsm);

    if (s_verbose)
    {
        printf("ACSMX-Max Memory-Final: %d bytes, %d states, %d active "
               "states\n",
               acsm2_total_memory, acsm->acsmMaxStates, acsm->acsmNumStates);
    }

    if (s_verbose)
        acsmPrintInfo2(acsm);

    summary.num_states += acsm->acsmNumStates;
    summary.num_transitions += acsm->acsmNumTrans;
    summary.num_instances++;

    memcpy(&summary.acsm, acsm, sizeof(ACSM_STRUCT2));

    return 0;
}

int acsmCompile2(
    ACSM_STRUCT2 *acsm,
    int (*build_tree)(void *id, void **existing_tree),
    int (*neg_list_func)(void *id, void **list))
{
    int rval;

    if ((rval = _acsmCompile2(acsm)))
        return rval;

    if (build_tree && neg_list_func)
    {
        acsmBuildMatchStateTrees2(acsm, build_tree, neg_list_func);
    }

    return 0;
}

int acsmCompile2WithSnortConf(
    struct _SnortConfig *sc,
    ACSM_STRUCT2 *acsm,
    int (*build_tree)(struct _SnortConfig *, void *id, void **existing_tree),
    int (*neg_list_func)(void *id, void **list))
{
    int rval;

    if ((rval = _acsmCompile2(acsm)))
        return rval;

    if (build_tree && neg_list_func)
    {
        acsmBuildMatchStateTrees2WithSnortConf(sc, acsm, build_tree, neg_list_func);
    }

    return 0;
}

static inline acstate_t SparseGetNextStateNFA(acstate_t *ps, acstate_t state, unsigned input)
{
    acstate_t fmt;
    acstate_t n;
    unsigned int index;
    int nb;

    fmt = *ps++;

    ps++;

    switch (fmt)
    {
    case ACF_BANDED:
    {
        n = ps[0];
        index = ps[1];

        if (input < index)
        {
            if (state == 0)
            {
                return 0;
            }
            else
            {
                return (acstate_t)ACSM_FAIL_STATE2;
            }
        }
        if (input >= index + n)
        {
            if (state == 0)
            {
                return 0;
            }
            else
            {
                return (acstate_t)ACSM_FAIL_STATE2;
            }
        }
        if (ps[input - index] == 0)
        {
            if (state != 0)
            {
                return ACSM_FAIL_STATE2;
            }
        }

        return (acstate_t)ps[input - index];
    }

    case ACF_SPARSE:
    {
        n = *ps++;

        for (; n > 0; n--)
        {
            if (ps[0] > input)
            {
                return (acstate_t)ACSM_FAIL_STATE2;
            }
            else if (ps[0] == input)
            {
                return ps[1];
            }
            ps += 2;
        }
        if (state == 0)
        {
            return 0;
        }
        return ACSM_FAIL_STATE2;
    }

    case ACF_SPARSEBANDS:
    {
        nb = *ps++;

        while (nb > 0)
        {
            n = *ps++;
            index = *ps++;

            if (input < index)
            {
                if (state != 0)
                {
                    return (acstate_t)ACSM_FAIL_STATE2;
                }
                return (acstate_t)0;
            }
            if ((input >= index) && (input < (index + n)))
            {
                if (ps[input - index] == 0)
                {
                    if (state != 0)
                    {
                        return ACSM_FAIL_STATE2;
                    }
                }
                return (acstate_t)ps[input - index];
            }
            nb--;
            ps += n;
        }
        if (state != 0)
        {
            return (acstate_t)ACSM_FAIL_STATE2;
        }
        return (acstate_t)0;
    }

    case ACF_FULL:
    case ACF_FULLQ:
    {
        if (ps[input] == 0)
        {
            if (state != 0)
            {
                return ACSM_FAIL_STATE2;
            }
        }
        return ps[input];
    }
    }

    return 0;
}

static inline acstate_t SparseGetNextStateDFA(acstate_t *ps, acstate_t state, unsigned input)
{
    acstate_t n, nb;
    unsigned int index;

    switch (ps[0])
    {

    case ACF_BANDED:
    {

        if (input < ps[3])
            return 0;
        if (input >= (unsigned)(ps[3] + ps[2]))
            return 0;

        return ps[4 + input - ps[3]];
    }

    case ACF_FULL:
    {
        return ps[2 + input];
    }

    case ACF_SPARSE:
    {
        n = ps[2];

        ps += 3;

        for (; n > 0; n--)
        {
            if (input < ps[0])
            {
                return (acstate_t)0;
            }
            else if (ps[0] == input)
            {
                return ps[1];
            }
            ps += 2;
        }
        return (acstate_t)0;
    }

    case ACF_SPARSEBANDS:
    {
        nb = ps[2];

        ps += 3;

        while (nb > 0)
        {
            n = ps[0];
            index = ps[1];
            if (input < index)
            {
                return (acstate_t)0;
            }
            if ((input < (index + n)))
            {
                return (acstate_t)ps[2 + input - index];
            }
            nb--;
            ps += 2 + n;
        }
        return (acstate_t)0;
    }
    }

    return 0;
}

static inline int
acsmSearchSparseDFA(ACSM_STRUCT2 *acsm, unsigned char *Tx, int n,
                    int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
                    void *data, int *current_state)
{
    acstate_t state;
    ACSM_PATTERN2 *mlist;
    unsigned char *Tend;
    int nfound = 0;
    unsigned char *T, *Tc;
    int index;
    acstate_t **NextState = acsm->acsmNextState;
    ACSM_PATTERN2 **MatchList = acsm->acsmMatchList;

    Tc = Tx;
    T = Tx;
    Tend = T + n;

    if (!current_state)
    {
        return 0;
    }

    state = *current_state;

    for (; T < Tend; T++)
    {
        state = SparseGetNextStateDFA(NextState[state], state, xlatcase[*T]);

        if (NextState[state][1])
        {
            mlist = MatchList[state];
            if (mlist)
            {
                index = T - mlist->n - Tc + 1;
                nfound++;
                if (Match(mlist->udata, mlist->rule_option_tree, index, data, mlist->neg_list) > 0)
                {
                    *current_state = state;
                    return nfound;
                }
            }
        }
    }

    *current_state = state;

    return nfound;
}

void acsmx2_print_qinfo(void)
{
#ifdef ACSMX2_TRACK_Q
    if (snort_conf->max_inq)
    {
        LogMessage("mpse: queue size     = %d, max possible = %d\n", snort_conf->max_inq, AC_MAX_INQ);
        LogMessage("mpse: queue flushes  = " STDu64 "\n", snort_conf->tot_inq_flush);
        LogMessage("mpse: queue inserts  = " STDu64 "\n", snort_conf->tot_inq_inserts);
        LogMessage("mpse: queue uinserts = " STDu64 "\n", snort_conf->tot_inq_uinserts);
    }
#endif
}

static inline void
_init_queue(PMQ *b)
{
    b->inq = 0;
    b->inq_flush = 0;
}

static inline int
_add_queue(PMQ *b, void *p)

{
    int i;

#ifdef ACSMX2_TRACK_Q
    snort_conf->tot_inq_inserts++;
#endif

    for (i = (int)(b->inq) - 1; i >= 0; i--)
        if (p == b->q[i])
            return 0;

#ifdef ACSMX2_TRACK_Q
    snort_conf->tot_inq_uinserts++;
#endif

    if (b->inq < AC_MAX_INQ)
    {
        b->q[b->inq++] = p;
    }

    if (b->inq == AC_MAX_INQ)
    {
#ifdef ACSMX2_TRACK_Q
        b->inq_flush++;
#endif
        return 1;
    }
    return 0;
}

static inline unsigned
_process_queue(PMQ *q,
               int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
               void *data)
{
    ACSM_PATTERN2 *mlist;
    unsigned int i;

#ifdef ACSMX2_TRACK_Q
    if (q->inq > snort_conf->max_inq)
        snort_conf->max_inq = q->inq;
    snort_conf->tot_inq_flush += q->inq_flush;
#endif

    for (i = 0; i < q->inq; i++)
    {
        mlist = q->q[i];
        if (mlist)
        {
            if (Match(mlist->udata, mlist->rule_option_tree, 0, data, mlist->neg_list) > 0)
            {
                q->inq = 0;
                return 1;
            }
        }
    }
    q->inq = 0;
    return 0;
}

#define AC_SEARCH_Q                                            \
    for (; T < Tend; T++)                                      \
    {                                                          \
        ps = NextState[state];                                 \
        sindex = xlatcase[T[0]];                               \
        if (ps[1])                                             \
        {                                                      \
            if (MatchList[state])                              \
            {                                                  \
                if (_add_queue(&acsm->q, MatchList[state]))    \
                {                                              \
                    if (_process_queue(&acsm->q, Match, data)) \
                    {                                          \
                        *current_state = state;                \
                        return 1;                              \
                    }                                          \
                }                                              \
            }                                                  \
        }                                                      \
        state = ps[2 + sindex];                                \
    }

static inline int
acsmSearchSparseDFA_Full_q(
    ACSM_STRUCT2 *acsm,
    unsigned char *T,
    int n,
    int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
    void *data,
    int *current_state)
{
    unsigned char *Tend;
    int sindex;
    acstate_t state;
    ACSM_PATTERN2 **MatchList = acsm->acsmMatchList;

    Tend = T + n;

    if (current_state == NULL)
        return 0;

    _init_queue(&acsm->q);

    state = *current_state;

    switch (acsm->sizeofstate)
    {
    case 1:
    {
        uint8_t *ps;
        uint8_t **NextState = (uint8_t **)acsm->acsmNextState;
        AC_SEARCH_Q;
    }
    break;
    case 2:
    {
        uint16_t *ps;
        uint16_t **NextState = (uint16_t **)acsm->acsmNextState;
        AC_SEARCH_Q;
    }
    break;
    default:
    {
        acstate_t *ps;
        acstate_t **NextState = acsm->acsmNextState;
        AC_SEARCH_Q;
    }
    break;
    }

    *current_state = state;

    if (MatchList[state])
        _add_queue(&acsm->q, MatchList[state]);

    _process_queue(&acsm->q, Match, data);

    return 0;
}

#define AC_SEARCH_Q_ALL                                                                       \
    for (; T < Tend; T++)                                                                     \
    {                                                                                         \
        ps = NextState[state];                                                                \
        sindex = xlatcase[T[0]];                                                              \
        if (ps[1])                                                                            \
        {                                                                                     \
            for (mlist = MatchList[state];                                                    \
                 mlist != NULL;                                                               \
                 mlist = mlist->next)                                                         \
            {                                                                                 \
                if (mlist->nocase || (memcmp(mlist->casepatrn, T - mlist->n, mlist->n) == 0)) \
                {                                                                             \
                    if (_add_queue(&acsm->q, mlist))                                          \
                    {                                                                         \
                        if (_process_queue(&acsm->q, Match, data))                            \
                        {                                                                     \
                            *current_state = state;                                           \
                            return 1;                                                         \
                        }                                                                     \
                    }                                                                         \
                }                                                                             \
            }                                                                                 \
        }                                                                                     \
        state = ps[2 + sindex];                                                               \
    }

static inline int
acsmSearchSparseDFA_Full_q_all(
    ACSM_STRUCT2 *acsm,
    const unsigned char *T,
    int n,
    int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
    void *data,
    int *current_state)
{
    const unsigned char *Tend;
    int sindex;
    acstate_t state;
    ACSM_PATTERN2 **MatchList = acsm->acsmMatchList;
    ACSM_PATTERN2 *mlist;

    Tend = T + n;

    if (current_state == NULL)
        return 0;

    _init_queue(&acsm->q);

    state = *current_state;

    switch (acsm->sizeofstate)
    {
    case 1:
    {
        uint8_t *ps;
        uint8_t **NextState = (uint8_t **)acsm->acsmNextState;
        AC_SEARCH_Q_ALL;
    }
    break;
    case 2:
    {
        uint16_t *ps;
        uint16_t **NextState = (uint16_t **)acsm->acsmNextState;
        AC_SEARCH_Q_ALL;
    }
    break;
    default:
    {
        acstate_t *ps;
        acstate_t **NextState = acsm->acsmNextState;
        AC_SEARCH_Q_ALL;
    }
    break;
    }

    *current_state = state;

    for (mlist = MatchList[state];
         mlist != NULL;
         mlist = mlist->next)
    {
        if (mlist->nocase || (memcmp(mlist->casepatrn, T - mlist->n, mlist->n) == 0))
        {
            if (_add_queue(&acsm->q, mlist))
            {
                if (_process_queue(&acsm->q, Match, data))
                {
                    *current_state = state;
                    return 1;
                }
            }
        }
    }

    _process_queue(&acsm->q, Match, data);

    return 0;
}

#define AC_SEARCH                                                                                   \
    for (; T < Tend; T++)                                                                           \
    {                                                                                               \
        ps = NextState[state];                                                                      \
        FILTER_NOISES;                                                                              \
        sindex = xlatcase[T0];                                                                      \
        if (ps[1])                                                                                  \
        {                                                                                           \
            mlist = MatchList[state];                                                               \
            if (mlist)                                                                              \
            {                                                                                       \
                index = T - mlist->n - Tx;                                                          \
                nfound++;                                                                           \
                if (Match(mlist->udata, mlist->rule_option_tree, index, data, mlist->neg_list) > 0) \
                {                                                                                   \
                    *current_state = state;                                                         \
                    return nfound;                                                                  \
                }                                                                                   \
            }                                                                                       \
        }                                                                                           \
        state = ps[2u + sindex];                                                                    \
    }

static inline int
acsmSearchSparseDFA_Full(
    ACSM_STRUCT2 *acsm,
    unsigned char *Tx,
    int n,
    int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
    void *data,
    int *current_state)
{
    ACSM_PATTERN2 *mlist;
    unsigned char *Tend;
    unsigned char *T;
    unsigned char *Toffset;
    unsigned char T0, T1 = 0;
    int index;
    int sindex;
    int nfound = 0;
    int total_skipped = 0;
    int code = 0;
    acstate_t state;
    ACSM_PATTERN2 **MatchList = acsm->acsmMatchList;

    T = Tx;
    Toffset = T;
    Tend = Tx + n;

    if (current_state == NULL)
        return 0;

    state = *current_state;

    switch (acsm->sizeofstate)
    {
    case 1:
    {
        uint8_t *ps;
        uint8_t **NextState = (uint8_t **)acsm->acsmNextState;
        AC_SEARCH;
    }
    break;
    case 2:
    {
        uint16_t *ps;
        uint16_t **NextState = (uint16_t **)acsm->acsmNextState;
        AC_SEARCH;
    }
    break;
    default:
    {
        acstate_t *ps;
        acstate_t **NextState = acsm->acsmNextState;
        AC_SEARCH;
    }
    break;
    }

    mlist = MatchList[state];
    if (mlist)
    {
        index = T - mlist->n - Tx;
        nfound++;
        if (Match(mlist->udata, mlist->rule_option_tree, index, data, mlist->neg_list) > 0)
        {
            *current_state = state;
            return nfound;
        }
    }

    *current_state = state;
    return nfound;
}

#define AC_SEARCH_ALL                                                                                   \
    for (; T < Tend; T++)                                                                               \
    {                                                                                                   \
        ps = NextState[state];                                                                          \
        FILTER_NOISES;                                                                                  \
        sindex = xlatcase[T0];                                                                          \
        if (ps[1])                                                                                      \
        {                                                                                               \
            for (mlist = MatchList[state];                                                              \
                 mlist != NULL;                                                                         \
                 mlist = mlist->next)                                                                   \
            {                                                                                           \
                index = T - mlist->n - Tx;                                                              \
                if (mlist->nocase || (memcmp(mlist->casepatrn, Tx + index, mlist->n) == 0))             \
                {                                                                                       \
                    nfound++;                                                                           \
                    if (Match(mlist->udata, mlist->rule_option_tree, index, data, mlist->neg_list) > 0) \
                    {                                                                                   \
                        *current_state = state;                                                         \
                        return nfound;                                                                  \
                    }                                                                                   \
                }                                                                                       \
            }                                                                                           \
        }                                                                                               \
        state = ps[2u + sindex];                                                                        \
    }

static inline int
acsmSearchSparseDFA_Full_All(
    ACSM_STRUCT2 *acsm,
    const unsigned char *Tx,
    int n,
    int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
    void *data,
    int *current_state)
{
    ACSM_PATTERN2 *mlist;
    const unsigned char *Tend;
    const unsigned char *T;
    const unsigned char *Toffset; // 指向当前字符（包括中文字符）后面的位置，用于标记 T 是否在某个字符内
    unsigned char T0, T1 = 0;
    int index;
    int sindex;
    int nfound = 0;
    int total_skipped = 0;
    int code = 0;
    acstate_t state;
    ACSM_PATTERN2 **MatchList = acsm->acsmMatchList;

    T = Tx;
    Toffset = T;
    Tend = Tx + n;

    if (current_state == NULL)
        return 0;

    state = *current_state;

    switch (acsm->sizeofstate)
    {
    case 1:
    {
        uint8_t *ps;
        uint8_t **NextState = (uint8_t **)acsm->acsmNextState;
        AC_SEARCH_ALL;
    }
    break;
    case 2:
    {
        uint16_t *ps;
        uint16_t **NextState = (uint16_t **)acsm->acsmNextState;
        AC_SEARCH_ALL;
    }
    break;
    default:
    {
        acstate_t *ps;
        acstate_t **NextState = acsm->acsmNextState;
        AC_SEARCH_ALL;
    }
    break;
    }

    for (mlist = MatchList[state];
         mlist != NULL;
         mlist = mlist->next)
    {
        index = T - mlist->n - Tx;

        if (mlist->nocase || (memcmp(mlist->casepatrn, Tx + index, mlist->n) == 0))
        {
            nfound++;
            if (Match(mlist->udata, mlist->rule_option_tree, index, data, mlist->neg_list) > 0)
            {
                *current_state = state;
                return nfound;
            }
        }
    }

    *current_state = state;
    return nfound;
}

static inline int
acsmSearchSparseDFA_Banded(ACSM_STRUCT2 *acsm, unsigned char *Tx, int n,
                           int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
                           void *data, int *current_state)
{
    acstate_t state;
    unsigned char *Tend;
    unsigned char *T;
    int sindex;
    int index;
    acstate_t **NextState = acsm->acsmNextState;
    ACSM_PATTERN2 **MatchList = acsm->acsmMatchList;
    ACSM_PATTERN2 *mlist;
    acstate_t *ps;
    int nfound = 0;

    T = Tx;
    Tend = T + n;

    if (!current_state)
    {
        return 0;
    }

    state = *current_state;

    for (; T < Tend; T++)
    {
        ps = NextState[state];

        sindex = xlatcase[T[0]];

        if (ps[1])
        {
            mlist = MatchList[state];
            if (mlist)
            {
                index = T - mlist->n - Tx;
                nfound++;
                if (Match(mlist->udata, mlist->rule_option_tree, index, data, mlist->neg_list) > 0)
                {
                    *current_state = state;
                    return nfound;
                }
            }
        }

        if ((acstate_t)sindex < ps[3])
            state = 0;
        else if ((acstate_t)sindex >= (ps[3] + ps[2]))
            state = 0;
        else
            state = ps[4u + sindex - ps[3]];
    }

    mlist = MatchList[state];
    if (mlist)
    {
        index = T - mlist->n - Tx;
        nfound++;
        if (Match(mlist->udata, mlist->rule_option_tree, index, data, mlist->neg_list) > 0)
        {
            *current_state = state;
            return nfound;
        }
    }

    return nfound;
}

static inline int
acsmSearchSparseNFA(ACSM_STRUCT2 *acsm, unsigned char *Tx, int n,
                    int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
                    void *data, int *current_state)
{
    acstate_t state;
    ACSM_PATTERN2 *mlist;
    unsigned char *Tend;
    int nfound = 0;
    unsigned char *T;
    int index;
    acstate_t **NextState = acsm->acsmNextState;
    acstate_t *FailState = acsm->acsmFailState;
    ACSM_PATTERN2 **MatchList = acsm->acsmMatchList;
    unsigned char Tchar;

    T = Tx;
    Tend = T + n;

    if (!current_state)
    {
        return 0;
    }

    state = *current_state;

    for (; T < Tend; T++)
    {
        acstate_t nstate;

        Tchar = xlatcase[*T];

        while ((nstate = SparseGetNextStateNFA(NextState[state], state, Tchar)) == ACSM_FAIL_STATE2)
            state = FailState[state];

        state = nstate;

        mlist = MatchList[state];
        if (mlist)
        {
            index = T - mlist->n - Tx;
            nfound++;
            if (Match(mlist->udata, mlist->rule_option_tree, index, data, mlist->neg_list) > 0)
            {
                *current_state = state;
                return nfound;
            }
        }
    }

    return nfound;
}

int acsmSearch2(ACSM_STRUCT2 *acsm, unsigned char *Tx, int n,
                int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
                void *data, int *current_state)
{

    switch (acsm->acsmFSA)
    {
    case FSA_DFA:

        if (acsm->acsmFormat == ACF_FULL)
        {
            return acsmSearchSparseDFA_Full(acsm, Tx, n, Match, data,
                                            current_state);
        }
        else if (acsm->acsmFormat == ACF_FULLQ)
        {
            return acsmSearchSparseDFA_Full_q(acsm, Tx, n, Match, data,
                                              current_state);
        }
        else if (acsm->acsmFormat == ACF_BANDED)
        {
            return acsmSearchSparseDFA_Banded(acsm, Tx, n, Match, data,
                                              current_state);
        }
        else
        {
            return acsmSearchSparseDFA(acsm, Tx, n, Match, data,
                                       current_state);
        }

    case FSA_NFA:

        return acsmSearchSparseNFA(acsm, Tx, n, Match, data,
                                   current_state);

    case FSA_TRIE:

        return 0;
    }
    return 0;
}

int acsmSearchAll2(ACSM_STRUCT2 *acsm, unsigned char *Tx, int n,
                   int (*Match)(void *id, void *tree, int index, void *data, void *neg_list),
                   void *data, int *current_state)
{

    switch (acsm->acsmFSA)
    {
    case FSA_DFA:

        if (acsm->acsmFormat == ACF_FULL)
        {
            return acsmSearchSparseDFA_Full_All(acsm, Tx, n, Match, data,
                                                current_state);
        }
        else if (acsm->acsmFormat == ACF_FULLQ)
        {
            return acsmSearchSparseDFA_Full_q_all(acsm, Tx, n, Match, data,
                                                  current_state);
        }
        else if (acsm->acsmFormat == ACF_BANDED)
        {
            return acsmSearchSparseDFA_Banded(acsm, Tx, n, Match, data,
                                              current_state);
        }
        else
        {
            return acsmSearchSparseDFA(acsm, Tx, n, Match, data,
                                       current_state);
        }

    case FSA_NFA:

        return acsmSearchSparseNFA(acsm, Tx, n, Match, data,
                                   current_state);

    case FSA_TRIE:

        return 0;
    }
    return 0;
}

void acsmFree2(
    ACSM_STRUCT2 *acsm)
{
    int i;
    ACSM_PATTERN2 *mlist, *ilist, *plist;

    for (i = 0; i < acsm->acsmNumStates; i++)
    {
        mlist = acsm->acsmMatchList[i];

        while (mlist)
        {
            ilist = mlist;
            mlist = mlist->next;

            if (ilist->rule_option_tree && acsm->optiontreefree)
                acsm->optiontreefree(&(ilist->rule_option_tree));

            if (ilist->neg_list && acsm->neg_list_free)
                acsm->neg_list_free(&(ilist->neg_list));

            AC_FREE(ilist, 0, ACSM2_MEMORY_TYPE__NONE);
        }

        AC_FREE_DFA(acsm->acsmNextState[i], 0, 0);
    }

    for (plist = acsm->acsmPatterns; plist;)
    {
        ACSM_PATTERN2 *tmpPlist = plist->next;

        if (acsm->userfree && (plist->udata != NULL))
        {
            acsm->userfree(plist->udata);
        }

        AC_FREE(plist->patrn, 0, ACSM2_MEMORY_TYPE__NONE);
        AC_FREE(plist->casepatrn, 0, ACSM2_MEMORY_TYPE__NONE);
        AC_FREE(plist, 0, ACSM2_MEMORY_TYPE__NONE);

        plist = tmpPlist;
    }

    AC_FREE_DFA(acsm->acsmNextState, 0, 0);
    AC_FREE(acsm->acsmFailState, 0, ACSM2_MEMORY_TYPE__NONE);
    AC_FREE(acsm->acsmMatchList, 0, ACSM2_MEMORY_TYPE__NONE);
    AC_FREE(acsm, 0, ACSM2_MEMORY_TYPE__NONE);
}

int acsmPatternCount2(ACSM_STRUCT2 *acsm)
{
    return acsm->numPatterns;
}

void acsmPrintInfo2(ACSM_STRUCT2 *p)
{
    char *sf[] = {
        "Full Matrix",
        "Sparse Matrix",
        "Banded Matrix",
        "Sparse Banded Matrix",
        "Full-Q Matrix"};
    char *fsa[] = {
        "TRIE",
        "NFA",
        "DFA"};

    printf("+--[Pattern Matcher:Aho-Corasick]-----------------------------\n");
    printf("| Alphabet Size    : %d Chars\n", p->acsmAlphabetSize);
    if (p->compress_states)
        printf("| Sizeof State     : %d\n", p->sizeofstate);
    else
        printf("| Sizeof State     : %d bytes\n", (int)(sizeof(acstate_t)));
    printf("| Storage Format   : %s \n", sf[p->acsmFormat]);
    printf("| Sparse Row Nodes : %d Max\n", p->acsmSparseMaxRowNodes);
    printf("| Sparse Band Zeros: %d Max\n", p->acsmSparseMaxZcnt);
    printf("| Num States       : %d\n", p->acsmNumStates);
    printf("| Num Transitions  : %d\n", p->acsmNumTrans);
    printf("| State Density    : %.1f%%\n", 100.0 * (double)p->acsmNumTrans / (p->acsmNumStates * p->acsmAlphabetSize));
    printf("| Finite Automaton : %s\n", fsa[p->acsmFSA]);
    if (acsm2_total_memory < 1024 * 1024)
        printf("| Memory           : %.2fKbytes\n", (float)acsm2_total_memory / 1024);
    else
        printf("| Memory           : %.2fMbytes\n", (float)acsm2_total_memory / (1024 * 1024));
    printf("+-------------------------------------------------------------\n");
}

int acsmPrintDetailInfo2(ACSM_STRUCT2 *p)
{

    return 0;
}

int acsmPrintSummaryInfo2(void)
{
    char *sf[] = {
        "Full",
        "Sparse",
        "Banded",
        "Sparse-Bands",
        "Full-Q"};

    char *fsa[] = {
        "TRIE",
        "NFA",
        "DFA"};

    ACSM_STRUCT2 *p = &summary.acsm;

    if (!summary.num_states)
        return 0;

    LogMessage("+- [ Aho-Corasick Summary ] -------------------------------------\n");
    LogMessage("| Storage Format    : %s \n", sf[p->acsmFormat]);
    LogMessage("| Finite Automaton  : %s\n", fsa[p->acsmFSA]);
    LogMessage("| Alphabet Size     : %d Chars\n", p->acsmAlphabetSize);

    if (summary.acsm.compress_states)
        LogMessage("| Sizeof State      : Variable (1,2,4 bytes)\n");
    else
        LogMessage("| Sizeof State      : %d bytes\n", (int)(sizeof(acstate_t)));

    LogMessage("| Instances         : %u\n", summary.num_instances);

    if (summary.acsm.compress_states)
    {
        LogMessage("|     1 byte states : %u\n", summary.num_1byte_instances);
        LogMessage("|     2 byte states : %u\n", summary.num_2byte_instances);
        LogMessage("|     4 byte states : %u\n", summary.num_4byte_instances);
    }

    LogMessage("| Characters        : %u\n", summary.num_characters);
    LogMessage("| States            : %d\n", summary.num_states);
    LogMessage("| Transitions       : %d\n", summary.num_transitions);
    LogMessage("| State Density     : %.1f%%\n",
               100.0 * (double)summary.num_transitions / (summary.num_states * p->acsmAlphabetSize));
    LogMessage("| Patterns          : %u\n", summary.num_patterns);
    LogMessage("| Match States      : %d\n", summary.num_match_states);

    if (acsm2_total_memory < 1024 * 1024)
    {
        LogMessage("| Memory (KB)       : %.2f\n", (float)acsm2_total_memory / 1024);
        if (acsm2_pattern_memory > 0)
            LogMessage("|   Pattern         : %.2f\n", (float)acsm2_pattern_memory / 1024);
        if (acsm2_matchlist_memory > 0)
            LogMessage("|   Match Lists     : %.2f\n", (float)acsm2_matchlist_memory / 1024);
        if (acsm2_transtable_memory > 0)
            LogMessage("|   Transitions     : %.2f\n", (float)acsm2_transtable_memory / 1024);
        if (acsm2_failstate_memory > 0)
            LogMessage("|   Fail States     : %.2f\n", (float)acsm2_failstate_memory / 1024);
        if (acsm2_dfa_memory > 0)
        {
            if (summary.acsm.compress_states)
            {
                LogMessage("|   DFA\n");
                LogMessage("|     1 byte states : %.2f\n", (float)acsm2_dfa1_memory / 1024);
                LogMessage("|     2 byte states : %.2f\n", (float)acsm2_dfa2_memory / 1024);
                LogMessage("|     4 byte states : %.2f\n", (float)acsm2_dfa4_memory / 1024);
            }
            else
            {
                LogMessage("|   DFA             : %.2f\n", (float)acsm2_dfa_memory / 1024);
            }
        }
    }
    else
    {
        LogMessage("| Memory (MB)       : %.2f\n", (float)acsm2_total_memory / (1024 * 1024));
        if (acsm2_pattern_memory > 0)
            LogMessage("|   Patterns        : %.2f\n", (float)acsm2_pattern_memory / (1024 * 1024));
        if (acsm2_matchlist_memory > 0)
            LogMessage("|   Match Lists     : %.2f\n", (float)acsm2_matchlist_memory / (1024 * 1024));
        if (acsm2_transtable_memory > 0)
            LogMessage("|   Transitions     : %.2f\n", (float)acsm2_transtable_memory / (1024 * 1024));
        if (acsm2_failstate_memory > 0)
            LogMessage("|   Fail States     : %.2f\n", (float)acsm2_failstate_memory / (1024 * 1024));
        if (acsm2_dfa_memory > 0)
        {
            if (summary.acsm.compress_states)
            {
                LogMessage("|   DFA\n");
                LogMessage("|     1 byte states : %.2f\n", (float)acsm2_dfa1_memory / (1024 * 1024));
                LogMessage("|     2 byte states : %.2f\n", (float)acsm2_dfa2_memory / (1024 * 1024));
                LogMessage("|     4 byte states : %.2f\n", (float)acsm2_dfa4_memory / (1024 * 1024));
            }
            else
            {
                LogMessage("|   DFA             : %.2f\n", (float)acsm2_dfa_memory / (1024 * 1024));
            }
        }
    }

    LogMessage("+----------------------------------------------------------------\n");

    return 0;
}

#ifdef ACSMX2S_MAIN

unsigned char text[512];

int MatchFound(void *id, int index, void *data)
{
    fprintf(stdout, "%s\n", (char *)id);
    return 0;
}

int main(int argc, char **argv)
{
    int i, nc, nocase = 0;
    ACSM_STRUCT2 *acsm;
    char *p;

    if (argc < 3)

    {
        fprintf(stderr, "Usage: %s search-text pattern +pattern... [flags]\n", argv[0]);
        fprintf(stderr, "  flags: -nfa -nocase -full -sparse -bands -sparsebands -z zcnt (sparsebands) -sparsetree -v\n");
        exit(0);
    }

    acsm = acsmNew2();
    if (!acsm)
    {
        printf("acsm-no memory\n");
        exit(0);
    }

    strncpy(text, argv[1], sizeof(text) - 1);
    text[sizeof(text) - 1] = '\0';

    acsm->acsmFormat = ACF_FULL;

    for (i = 1; i < argc; i++)
    {
        if (strcmp(argv[i], "-nocase") == 0)
        {
            nocase = 1;
        }
        if (strcmp(argv[i], "-v") == 0)
        {
            s_verbose = 1;
        }

        if (strcmp(argv[i], "-full") == 0)
        {
            acsm->acsmFormat = ACF_FULL;
        }
        if (strcmp(argv[i], "-fullq") == 0)
        {
            acsm->acsmFormat = ACF_FULLQ;
        }
        if (strcmp(argv[i], "-sparse") == 0)
        {
            acsm->acsmFormat = ACF_SPARSE;
            acsm->acsmSparseMaxRowNodes = 10;
        }
        if (strcmp(argv[i], "-bands") == 0)
        {
            acsm->acsmFormat = ACF_BANDED;
        }

        if (strcmp(argv[i], "-sparsebands") == 0)
        {
            acsm->acsmFormat = ACF_SPARSEBANDS;
            acsm->acsmSparseMaxZcnt = 10;
        }
        if (strcmp(argv[i], "-z") == 0)
        {
            acsm->acsmSparseMaxZcnt = atoi(argv[++i]);
        }

        if (strcmp(argv[i], "-nfa") == 0)
        {
            acsm->acsmFSA = FSA_NFA;
        }
        if (strcmp(argv[i], "-dfa") == 0)
        {
            acsm->acsmFSA = FSA_DFA;
        }
        if (strcmp(argv[i], "-trie") == 0)
        {
            acsm->acsmFSA = FSA_TRIE;
        }
    }

    for (i = 2; i < argc; i++)
    {
        if (argv[i][0] == '-')
            continue;

        p = argv[i];

        if (*p == '+')
        {
            nc = 1;
            p++;
        }
        else
        {
            nc = nocase;
        }

        acsmAddPattern2(acsm, p, strlen(p), nc, 0, 0, (void *)p, i - 2);
    }

    if (s_verbose)
        printf("Patterns added\n");

    Print_DFA(acsm);

    acsmCompile2(acsm);

    Write_DFA(acsm, "acsmx2-snort.dfa");

    if (s_verbose)
        printf("Patterns compiled--written to file.\n");

    acsmPrintInfo2(acsm);

    acsmSearch2(acsm, text, strlen(text), MatchFound, (void *)0);

    acsmFree2(acsm);

    printf("normal pgm end\n");

    return (0);
}
#endif
