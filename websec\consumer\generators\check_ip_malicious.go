package generators

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

// Response 结构体保持不变
type Response struct {
	ErrorCode    int    `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	Data         struct {
		MaliciousInfo struct {
			IsBruteForce  bool `json:"is_brute_force"`
			IsDdos        bool `json:"is_ddos"`
			IsHijacked    bool `json:"is_hijacked"`
			IsMalicious   bool `json:"is_malicious"`
			IsScanner     bool `json:"is_scanner"`
			IsSpam        bool `json:"is_spam"`
			IsWebAttacker bool `json:"is_web_attacker"`
		} `json:"malicious_info"`
	} `json:"data"`
}

func CheckIPMalicious(ip, xapikey string) (bool, error) {

	if xapikey == "" {
		return false, fmt.Errorf("xapikey not found in configuration")
	}

	// 构建API URL
	url := fmt.Sprintf("https://api.baize.qianxin.com/open/v3/agent/ti?ip=%s", ip)

	// 创建新的请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return false, fmt.Errorf("failed to create request: %v", err)
	}

	// 添加请求头
	req.Header.Add("xapikey", xapikey)

	// 发起请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return false, fmt.Errorf("HTTP request failed: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return false, fmt.Errorf("API request failed with status code: %d", resp.StatusCode)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, fmt.Errorf("failed to read response body: %v", err)
	}

	// 解析JSON响应
	var response Response
	if err := json.Unmarshal(body, &response); err != nil {
		return false, fmt.Errorf("failed to parse JSON: %v", err)
	}

	// 检查恶意行为标记
	maliciousInfo := response.Data.MaliciousInfo
	return maliciousInfo.IsBruteForce ||
		maliciousInfo.IsDdos ||
		maliciousInfo.IsHijacked ||
		maliciousInfo.IsMalicious ||
		maliciousInfo.IsScanner ||
		maliciousInfo.IsSpam ||
		maliciousInfo.IsWebAttacker, nil
}
