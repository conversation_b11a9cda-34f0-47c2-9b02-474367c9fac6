package scripts

import (
	"net"
	"regexp"
	"strconv"
	"strings"
	"time"
)

var memcacheVersionPattern = regexp.MustCompile("version ([0-9]+.[0-9]+.[0-9]+)")

func checkMemcachedVersion(version string) bool {

	var err error
	parts := strings.Split(version, ".")
	versionA, err := strconv.Atoi(parts[0])
	if err != nil {
		return false
	}
	versionB, err := strconv.Atoi(parts[1])
	if err != nil {
		return false
	}
	versionC, err := strconv.Atoi(parts[2])
	if err != nil {
		return false
	}
	return versionA < 1 || (versionA == 1 && versionB < 4) || (versionA == 1 && versionB == 4 && versionC < 33)
}

func MemcachedDOS(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addr := args.Host + ":11211"
	conn, err := net.DialTimeout("tcp", addr, time.Second*10)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
	conn.Write([]byte("stats\n"))

	response := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(time.Second * 5))
	_, err = conn.Read(response)
	if err != nil {
		return nil, err
	}
	groups := memcacheVersionPattern.FindSubmatch(response)
	if len(groups) > 1 {
		version := string(groups[1])
		if checkMemcachedVersion(version) {
			return &ScriptScanResult{Vulnerable: true, Output: addr + "|Memecached Version:" + version, Body: response}, nil
		}
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("memcached_dos.xml", MemcachedDOS)
}
