package detect

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"time"
	"websec/utils/log"
)

type Req struct {
	DomainSet []string `json:"domain_set"`
}

type Resp struct {
	Code uint   `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		DomainSet []string `json:"domain_set"`
	} `json:"data"`
}

type WangdunBlackLinkDetecter struct {
	Api     string
	Enabled bool
}

func NewWangBlackLinkDetecter(api string) *WangdunBlackLinkDetecter {
	return &WangdunBlackLinkDetecter{
		Api:     api,
		Enabled: len(api) > 0,
	}
}

func (d *WangdunBlackLinkDetecter) Detect(domainSet []string) []string {
	sickDomains := make([]string, 0)
	if !d.Enabled {
		return sickDomains
	}

	max := 20
	timeout := time.Second * 5
	req := &Req{
		DomainSet: make([]string, 0),
	}
	for index := range domainSet {
		domain := domainSet[index]
		req.DomainSet = append(req.DomainSet, domain)

		if len(req.DomainSet) >= max || index == len(domainSet)-1 {
			ctx, cancel := context.WithTimeout(context.TODO(), timeout)
			defer cancel() // emmm ...
			resp, err := d.doRequest(ctx, req)
			if err != nil {
				log.Infoln(
					"failed to do requset wangdun black api",
					d.Api,
					req.DomainSet,
					err,
				)
				req.DomainSet = req.DomainSet[:0]
				continue
			}
			if len(resp.Data.DomainSet) > 0 {
				sickDomains = append(sickDomains, resp.Data.DomainSet...)
			}
			req.DomainSet = req.DomainSet[:0]
		}
	}
	return sickDomains
}

func (d *WangdunBlackLinkDetecter) doRequest(ctx context.Context, req *Req) (*Resp, error) {
	raw, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	log.Infoln("wangdun:do request to wangdun for blacklink detecting", req.DomainSet)
	reader := bytes.NewReader(raw)
	httpReq, err := http.NewRequest("POST", d.Api, reader)
	if err != nil {
		return nil, err
	}
	httpResp, err := http.DefaultClient.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer httpResp.Body.Close()
	body, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, err
	}

	resp := &Resp{}
	err = json.Unmarshal(body, resp)
	if err != nil {
		return nil, err
	}

	if resp.Code == 200 && resp.Msg == "success" {
		return resp, nil
	}

	return nil, errors.New(resp.Msg)
}
