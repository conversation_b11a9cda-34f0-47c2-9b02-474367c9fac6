package scripts

import (
	"io/ioutil"
	"net/http"
)

func UnlimitHostnameVul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	userAgent := "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.63 Safari/537.36"
	targetURL := constructURL(args, "/")

	req, err := http.NewRequest(http.MethodGet, targetURL, nil)
	if err != nil {
		return nil, err
	}
	req.Close = true
	req.Header.Set("User-Agent", userAgent)
	respA, err := goHTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer respA.Body.Close()
	bodyA, err := ioutil.ReadAll(respA.Body)
	if err != nil {
		return nil, err
	}

	req, err = http.NewRequest(http.MethodGet, targetURL, nil)
	if err != nil {
		return nil, err
	}
	req.Close = true
	req.Host = "webscantest"
	req.Header.Set("User-Agent", userAgent)
	respB, err := goHTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer respB.Body.Close()
	bodyB, err := ioutil.ReadAll(respB.Body)
	if err != nil {
		return nil, err
	}

	if respA.StatusCode == respB.StatusCode && textSimilarity(string(bodyA), string(bodyB)) > 0.9 {
		return &ScriptScanResult{Vulnerable: true, Output: targetURL}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("unlimit_hostname_vul.xml", UnlimitHostnameVul)
}
