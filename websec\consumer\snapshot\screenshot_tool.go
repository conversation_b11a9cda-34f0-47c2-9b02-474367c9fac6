package snapshot

import (
	"context"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"os"
	"path"
	"time"
	"websec/common"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/utils/chrome"
	"websec/utils/log"
	"websec/utils/semaphore"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"gopkg.in/mgo.v2/bson"
)

type ScreenshotTool struct {
	client       *chrome.Client
	dbConnection *db.DBConnection
}

var injectJS = &common.InjectJavaScript{
	InjectAfterLoad:  `window.alert = function () { return false; };window.prompt = function (msg, input) { return input; };window.confirm = function () { return true; };window.close = function () { return false; };console.log("inject after load.");`,
	InjectBeforeLoad: `console.log("inject after load.");`,
}

func (s *ScreenshotTool) SetDBConnection(dbConnection *db.DBConnection) {
	s.dbConnection = dbConnection
}

func NewScreenshotTool(address string) *ScreenshotTool {
	return &ScreenshotTool{
		client: chrome.NewClient(&chrome.Options{
			Addr:   address,
			Script: injectJS,
		}),
	}
}

func (s *ScreenshotTool) AddSensitiveWordChrome(msg *SensitiveWordChrome) {
	sema := semaphore.NewWeighted(20)
	var err error

	if err = sema.Acquire(context.Background(), 1); err == nil {
		s.doSensitiveWord(msg, sema, true)
	} else {
		log.Errorln(err)
		s.doSensitiveWord(msg, sema, false)
	}

}

func (s *ScreenshotTool) AddContentChangeChrome(msg *ContentChangeChrome) {
	sema := semaphore.NewWeighted(20)
	var err error
	if err = sema.Acquire(context.Background(), 1); err == nil {
		s.doContentChange(msg, sema, true)
	} else {
		log.Errorln(err)
		s.doContentChange(msg, sema, false)
	}
}

func (s *ScreenshotTool) doContentChange(msg *ContentChangeChrome, sema *semaphore.Weighted, acquire bool) {
	if acquire {
		defer func() {
			sema.Release(1)
		}()
	}

	defer func() {
		err := os.Remove(msg.OldFilePath)
		if err != nil {
			log.Errorln("remove old file faild:", msg.OldFilePath)
		}

		err = os.Remove(msg.NewFilePath)
		if err != nil {
			log.Errorln("remove new file faild:", msg.NewFilePath)
		}
	}()

	log.Debugf("filename: old %s new %s", msg.OldFilePath, msg.NewFilePath)

	if _, err := os.Stat(msg.OldFilePath); os.IsNotExist(err) {
		log.Errorln("file not found:", msg.OldFilePath)
		return
	}

	oldData, oldTool, oldJS, oldErr := s.genScreenshot(msg.OldFilePath)

	if oldErr != nil {
		log.Errorln("doContentChange oldErr", oldErr)
		return
	}

	if _, err := os.Stat(msg.NewFilePath); os.IsNotExist(err) {
		log.Errorln("file not found:", msg.NewFilePath)
		return
	}
	newData, newTool, newJS, newErr := s.genScreenshot(msg.NewFilePath)

	if newErr != nil {
		log.Errorln("doContentChange newErr", newErr)
		return
	}

	oldImgPath, err := saveImageFile(oldData)
	if err != nil {
		log.Errorln("save old image file faild:", err)
		return
	}

	newImgPath, err := saveImageFile(newData)
	if err != nil {
		log.Errorln("save new image file faild:", err)
		return
	}

	// @TODO later process
	result := &schema.FinalSnapshotContentChangeResult{
		ID:             msg.ID,
		Host:           msg.Host,
		AssetID:        msg.AssetID,
		OldTool:        oldTool,
		OldJS:          oldJS,
		OldSnapshotURL: oldImgPath,
		NewTool:        newTool,
		NewJS:          newJS,
		NewSnapshotURL: newImgPath,
		Status:         consts.SnapshotFinished,
		Err:            "",
	}

	s.updateContentChangeSnapshot(result)
}

func (s *ScreenshotTool) doSensitiveWord(msg *SensitiveWordChrome, sema *semaphore.Weighted, acquire bool) {
	if acquire {
		defer func() {
			sema.Release(1)
		}()
	}

	defer func() {
		err := os.Remove(msg.FilePath)
		if err != nil {
			log.Errorln("remove file faild:", msg.FilePath)
		}
	}()

	start := time.Now()

	// First, check that the file exists.
	if _, err := os.Stat(msg.FilePath); os.IsNotExist(err) {
		log.Errorln("file not found:", msg.FilePath)
		return
	}

	data, tool, js, err := s.genScreenshot(msg.FilePath)

	log.Infof("Screenshot data length: %d bytes from %s", len(data), msg.FilePath)

	hash := sha1sum(data)

	log.Infof("Screenshot data hash: %s", hash)

	log.Infof("sensitive %s tool: %s, js: %v, err: %v", msg.SnapshotID, tool, js, err)

	log.Infof("doSensitiveWord %s %f", msg.SnapshotID, time.Since(start).Seconds())

	if len(data) == 0 {
		log.Errorln("img data len == 0")
		return
	}

	imgName, err := saveImageFile(data)

	if err != nil {
		log.Errorln("save snapshot file faild:", err)
		return
	}

	if imgName == "" {
		log.Errorln("image name is empty")
		return
	}

	snapshotUrl := "/media/" + imgName

	// 将 msg.SnapshotID 转换为 bson.ObjectId
	snapshotID, err := primitive.ObjectIDFromHex(msg.SnapshotID)
	if err != nil {
		log.Errorln("invalid snapshot id:", msg.SnapshotID)
		return
	}

	// 使用 snapshotID 作为过滤条件，更新 snapshot_url 和 status
	_, err = s.dbConnection.GetMongoDatabase().Collection(consts.CollectionSnapshotSensitiveWord).UpdateOne(context.Background(), bson.M{
		"_id": snapshotID,
	}, bson.M{
		"$set": bson.M{
			"snapshot_url": snapshotUrl,
			"status":       consts.SnapshotFinished,
		},
	})
	if err != nil {
		log.Errorln("update snapshot_sensitiveword collection failed:", err)
	}

	// @TODO later process
	// s.processor.uploadService.AddSensitiveWordUpload(res)
}

func (s *ScreenshotTool) genScreenshot(filePath string) ([]byte, string, bool, error) {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, "", false, fmt.Errorf("file not found: %s", filePath)
	}

	// Copy file to nginx directory
	fileName := path.Base(filePath)
	targetDir := "/data/websec_nginx/system/snapshot"
	targetPath := path.Join(targetDir, fileName)

	// Create target directory if not exists
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return nil, "", false, fmt.Errorf("failed to create target directory: %v", err)
	}

	// Copy the file
	input, err := os.ReadFile(filePath)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to read source file: %v", err)
	}
	if err = os.WriteFile(targetPath, input, 0644); err != nil {
		return nil, "", false, fmt.Errorf("failed to write target file: %v", err)
	}

	// Clean up function
	defer func() {
		if err := os.Remove(targetPath); err != nil {
			log.Errorln("failed to remove temporary file:", targetPath, err)
		}
	}()

	chromeURL := fmt.Sprintf("http://127.0.0.1/snapshot/%s", fileName)
	tool := "chrome"
	js := true

	log.Infof("generating screenshot with chrome js with URL: %s", chromeURL)

	data, err := s.client.ScreenShot(chromeURL)

	if err != nil {
		return data, tool, js, err
	} else {
		if len(data) > 10240 {
			return data, tool, js, nil
		}
	}

	// Handle case where we need to try without special tags
	withoutSpecialTagPath, err := RemoveScriptTagFromFile(filePath)
	if err != nil {
		log.Infof("failed to remove js tag from page %s", filePath)
	}

	withoutSpecialTagPath, err = RemoveStyleLinkTagFromFile(withoutSpecialTagPath)
	if err != nil {
		log.Infof("failed to remove stylesheet link tag from page %s", filePath)
	}

	// Copy the modified file
	fileName = path.Base(withoutSpecialTagPath)
	targetPath = path.Join(targetDir, fileName)
	input, err = os.ReadFile(withoutSpecialTagPath)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to read modified file: %v", err)
	}
	if err = os.WriteFile(targetPath, input, 0644); err != nil {
		return nil, "", false, fmt.Errorf("failed to write modified target file: %v", err)
	}

	defer func() {
		if err := os.Remove(targetPath); err != nil {
			log.Errorln("failed to remove temporary modified file:", targetPath, err)
		}
	}()

	js = false
	tool = "chrome"
	chromeURL = fmt.Sprintf("http://127.0.0.1/snapshot/%s", fileName)
	data, err = s.client.ScreenShot(chromeURL)
	return data, tool, js, err
}

func (s *ScreenshotTool) updateContentChangeSnapshot(val *schema.FinalSnapshotContentChangeResult) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	id, err := primitive.ObjectIDFromHex(val.ID)
	if err != nil {
		log.Errorln("add snapshot contentchange error:", err)
		return
	}

	res, err := s.dbConnection.GetMongoDatabase().Collection(consts.CollectionFoundContentChange).UpdateOne(ctx,
		bson.M{"_id": id},
		bson.M{"$set": bson.M{
			"new_snapshot": val.NewSnapshotURL,
			"old_snapshot": val.OldSnapshotURL,
			"status":       val.Status,
			"old_tool":     val.OldTool,
			"new_tool":     val.NewTool,
			"oldjs":        val.OldJS,
			"newjs":        val.NewJS,
			"snapshot_err": val.Err},
		})
	if err != nil || res.ModifiedCount != 1 {
		log.Errorln("update snapshot contentchange error:", err)
	}

}

func sha1sum(data []byte) string {
	s := sha1.Sum(data)
	return hex.EncodeToString(s[:])
}

// saveImageFile saves image data to disk and returns the file path
func saveImageFile(data []byte) (string, error) {
	dir := "/home/<USER>"
	imageName := sha1sum(data) + ".jpg"
	filePath := path.Join(dir, imageName)

	if err := os.WriteFile(filePath, data, os.ModePerm); err != nil {
		log.Errorln("write snapshot file failed:", err)
		return "", err
	}

	return imageName, nil
}
