package main

import (
	"net"
	"sync"
	"websec/common/db"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/stream"
	"websec/utils/tuchuang"
)

type processorManager struct {
	mu           *sync.RWMutex
	processors   map[string]*Processor
	muSema       *sync.Mutex
	semaphores   map[string]*semaphore.Weighted
	dbConnection *db.DBConnection
	producer     *stream.Producer
	resolver     *net.Resolver
	bucketSelect *tuchuang.BucketSelect
}

func (manager *processorManager) Get(task *schema.Task) (*Processor, error) {
	log.Debugf("get processor host %s address %s", task.Host, task.Track.Address)
	assetID := task.AssetID.Hex()
	processor := manager.get(assetID)
	if processor != nil {
		return processor, nil
	}

	asset, err := manager.dbConnection.GetAssetByID(assetID, true)
	if err != nil {
		return nil, err
	}

	processor = NewProcessor(asset, manager, task.Track.Address)
	manager.add(assetID, processor)
	return processor, nil
}

func (manager *processorManager) Delete(assetID string) {
	manager.mu.Lock()
	defer manager.mu.Unlock()

	if v, ok := manager.processors[assetID]; ok {
		v.Clear()
		delete(manager.processors, assetID)
	}
}

func (manager *processorManager) get(assetID string) *Processor {
	manager.mu.RLock()
	defer manager.mu.RUnlock()

	if processor, ok := manager.processors[assetID]; ok {
		return processor
	}
	return nil
}

func (manager *processorManager) add(assetID string, processor *Processor) {
	manager.mu.Lock()
	defer manager.mu.Unlock()

	manager.processors[assetID] = processor
}

func (manager *processorManager) Count() int {
	manager.mu.Lock()
	defer manager.mu.Unlock()
	return len(manager.processors)
}

func (manager *processorManager) StopAllProcessor() {
	manager.mu.Lock()
	defer manager.mu.Unlock()
	for _, v := range manager.processors {
		v.Stop()
	}
}

func (manager *processorManager) Dump() {
	manager.mu.RLock()
	defer manager.mu.RUnlock()

	for _, v := range manager.processors {
		v.Dump()
	}
}

func (manager *processorManager) getSemaphore(address string, concurrency int64) *semaphore.Weighted {
	manager.muSema.Lock()
	defer manager.muSema.Unlock()

	if sema, ok := manager.semaphores[address]; ok {
		sema.SetSize(concurrency)
		return sema
	}
	sema := semaphore.NewWeighted(concurrency)
	manager.semaphores[address] = sema
	return sema
}

func newProcessorManager(producer *stream.Producer, dbConnection *db.DBConnection,
	bucketSelect *tuchuang.BucketSelect) (*processorManager, error) {

	manager := &processorManager{
		mu:           &sync.RWMutex{},
		processors:   make(map[string]*Processor),
		muSema:       &sync.Mutex{},
		semaphores:   make(map[string]*semaphore.Weighted),
		producer:     producer,
		dbConnection: dbConnection,
		resolver: &net.Resolver{
			PreferGo: true,
		},
		bucketSelect: bucketSelect,
	}
	return manager, nil
}
