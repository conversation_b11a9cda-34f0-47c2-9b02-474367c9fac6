package scripts

import (
	"bytes"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
)

var phpVersionPattern = regexp.MustCompile(`PHP[ /]([0-9\.]+)`)

func PHPVersionOlderThan(args *ScriptScanArgs) (*ScriptScanResult, error) {
	targetURL := constructURL(args, "/")

	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.Header.SetMethod(http.MethodGet)
	request.Header.Set("Accept", "*/*")
	request.SetRequestURI(targetURL)
	err := headerNormalizingHTTPClient.DoTimeout(request, response, time.Second*3)
	if err != nil {
		return nil, err
	}
	var version string
	response.Header.VisitAll(func(key, value []byte) {
		key = bytes.ToLower(key)
		if bytes.Equal(key, []byte("server")) || bytes.Equal(key, []byte("x-powered-by")) {
			match := phpVersionPattern.FindSubmatch(value)
			if match != nil {
				version = string(match[1])
			}
		}
	})
	if version == "" {
		_, body, err := httpGetTimeout(constructURL(args, "/noteverexist1gdlx"), time.Second*3)
		if err == nil {
			match := phpVersionPattern.FindSubmatch(body)
			if match != nil {
				version = string(match[1])
			}
		}
	}
	if version != "" {
		parts := strings.Split(version, ".")
		found := false
		if parts[0] == "5" {

			if len(parts) == 3 {
				versionB, err := strconv.Atoi(parts[1])
				if err == nil {
					if versionB < 3 {
						found = true
					} else if versionB == 3 {
						versionC, err := strconv.Atoi(parts[2])
						if err == nil && versionC < 4 {
							found = true
						}
					}
				}
			}
		} else if intValue, err := strconv.Atoi(parts[0]); err == nil && intValue < 5 {

			found = true
		}
		if found {
			return &ScriptScanResult{Vulnerable: true, Output: targetURL + "|PHP Version: " + version}, nil
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("PHP_version_older_than.xml", PHPVersionOlderThan)
}
