package xsscheck

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"
	"websec/common/logger"
	"websec/scan/utils"

	"github.com/lestrrat/go-libxml2"
	"github.com/lestrrat/go-libxml2/types"
	"github.com/lestrrat/go-libxml2/xpath"
	log "github.com/sirupsen/logrus"
)

type XSSChecker struct {
	TIMEOUT        int
	advancedMode   bool
	isMemoryOpen   bool
	symbolAccept   map[rune]bool
	httpClient     *http.Client
	lastPayload    XSSPayloadStruct
	hasLastPayload bool
	RequestCount   int
	UserAgent      string
	Cookie         string
	xssPayload     []XSSPayloadStruct
}

var displayRe = regexp.MustCompile(`(?i)display\s*:\s*none`)

func LoadXssPayload(xssPayloadFile string) ([]XSSPayloadStruct, error) {
	var xssPayload []XSSPayloadStruct
	filePtr, err := os.Open(xssPayloadFile)
	if err != nil {
		logger.Error("Open file failed [Err:", err)
		return xssPayload, err

	}
	defer filePtr.Close()

	decoder := json.NewDecoder(filePtr)
	err = decoder.Decode(&xssPayload)
	if err != nil {

		logger.Error("JSON Decode Error  Err:", err)
		return xssPayload, err
	}
	log.Debug(fmt.Sprintf("Load %d Payloads", len(xssPayload)))
	return xssPayload, nil
}

func CreateXSSChecker(advancedMode bool, verbose bool, xssPayLoadPath string) (*XSSChecker, error) {

	payload, err := LoadXssPayload(xssPayLoadPath)
	if err != nil {
		return nil, err
	}
	var xssChecker = new(XSSChecker)
	xssChecker.xssPayload = payload

	xssChecker.TIMEOUT = 5
	xssChecker.advancedMode = advancedMode

	xssChecker.isMemoryOpen = true

	xssChecker.symbolAccept = make(map[rune]bool, len(KeySymbols))
	xssChecker.httpClient = utils.CreateHttpClient(false)

	defer xssChecker.httpClient.CloseIdleConnections()
	xssChecker.httpClient.Timeout = 5 * time.Second
	xssChecker.RequestCount = 0
	if verbose {
		log.SetLevel(log.DebugLevel)
	}
	log.SetFormatter(&log.TextFormatter{ForceColors: true})
	return xssChecker, nil
}

func reverseStack(data []rune) []rune {
	var charList [][]rune = [][]rune{
		[]rune{
			rune('<'), rune('>'),
		}, {
			rune('['), rune(']'),
		}, {
			rune('{'), rune('}'),
		}, {
			rune('('), rune(')'),
		}, {
			rune('\''), rune('\''),
		}, {
			rune('"'), rune('"'),
		},
	}
	var _ret []rune = make([]rune, len(data))
	pos := 0
reverseLoopLabel:
	for i := len(data) - 1; i >= 0; i-- {
		for _, iChar := range charList {
			if data[i] == iChar[0] {

				_ret[pos] = iChar[1]
				pos += 1
				continue reverseLoopLabel
			} else if data[i] == iChar[1] {
				_ret[pos] = iChar[0]
				pos += 1
				continue reverseLoopLabel
			}
		}
	}
	return _ret
}
func (xssCheck *XSSChecker) addHeaders(req *http.Request) {
	if len(xssCheck.Cookie) != 0 {
		req.Header.Add("Cookie", xssCheck.Cookie)
	}
	if len(xssCheck.UserAgent) != 0 {
		req.Header.Add("User-Agent", xssCheck.UserAgent)
	}

	req.Header.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3")
}

func (xssCheck *XSSChecker) sendRequests(requestUrl string, method string, data string,
	payload string) (body []byte, requestString string, err error) {
	var resp *http.Response
	var req *http.Request

	payload = url.QueryEscape(payload)
	xssCheck.RequestCount += 1
	startTime := time.Now().UnixNano()

	if method == "GET" {

		requestUrl = strings.Replace(requestUrl, XSSPayloadPlaceholder, payload, 1)
		log.Debug("Send Request:" + requestUrl)
		req, _ = http.NewRequest(method, requestUrl, nil)
		xssCheck.addHeaders(req)
		resp, err = xssCheck.httpClient.Do(req)
	} else if method == "POST" {

		requestUrl = strings.Replace(requestUrl, XSSPayloadPlaceholder, payload, 1)
		data = strings.Replace(data, XSSPayloadPlaceholder, payload, 1)
		bodyReader := strings.NewReader(data)
		req, _ = http.NewRequest(method, requestUrl, bodyReader)
		log.WithField("data", data).Debug("Send Request:" + requestUrl)
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		xssCheck.addHeaders(req)
		resp, err = xssCheck.httpClient.Do(req)
	} else {

		err = errors.New(fmt.Sprintf("Unkown Method:%s", method))
		return
	}
	if err != nil {
		return
	}
	var requestB []byte
	requestB, err = httputil.DumpRequest(req, true)
	if err != nil {
		requestString = string(requestB)
		return
	}

	respContentLength := resp.Header.Get("content-length")
	if respContentLength != "" {
		respContentLengthNum, err := strconv.Atoi(respContentLength)
		if err != nil {

		} else {
			if respContentLengthNum > 200*1024 {
				err = errors.New("Response Body is too long \n")
			}
		}
	}
	defer resp.Body.Close()

	body, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return
	}

	if (time.Now().UnixNano()-startTime)/1e6 < 500 {
		time.Sleep(time.Duration(500-(time.Now().UnixNano()-startTime)/1e6) * time.Millisecond)
	}

	return

}
func (xssCheck *XSSChecker) processBalanceStack(tagText string, inText bool) (before []rune, after []rune) {

	var charList [][]rune = [][]rune{
		[]rune{
			rune('<'), rune('>'),
		}, {
			rune('['), rune(']'),
		}, {
			rune('{'), rune('}'),
		}, {
			rune('('), rune(')'),
		},
	}
	if inText {
		charList = charList[1:4]
	}
	log.Debug("Process Balance Stack")
	split := strings.SplitN(tagText, XsscheckSimpleString, 2)

	symbolStack := utils.CreateStack()

	isFlagInstr := false
	var flagInstr rune = rune(' ')

	flagEscape := false

	if len(split) == 2 {

		codeSegStart := []rune(split[0])

		for pos := 0; pos < len(codeSegStart); {
			if isFlagInstr {

				if !flagEscape {
					if codeSegStart[pos] == flagInstr {

						isFlagInstr = false
						symbolStack.Pop()
					} else if codeSegStart[pos] == rune('\\') {
						flagEscape = true
					}
				} else {
					flagEscape = false
				}
			} else {
				if codeSegStart[pos] == '\'' || codeSegStart[pos] == '"' {
					isFlagInstr = true
					flagInstr = codeSegStart[pos]
					symbolStack.Push(flagInstr)
				} else if inText && codeSegStart[pos] == '/' {

					nowPlace := pos + 2
					if nowPlace >= len(codeSegStart) {
						break
					}
					if codeSegStart[pos+1] == '*' {
						nowPlace = pos + 2
						for {
							if nowPlace+1 < len(codeSegStart) && (codeSegStart[nowPlace] != '*' || codeSegStart[nowPlace+1] != '/') {
								nowPlace += 1
							} else {
								break
							}
						}
						pos = nowPlace + 1
						continue
					} else if codeSegStart[pos+1] == '/' {
						nowPlace = pos + 2
						for {
							if nowPlace < len(codeSegStart) && (codeSegStart[nowPlace] != '\r' && codeSegStart[nowPlace] != '\n') {
								nowPlace += 1
							} else {
								break
							}
						}
						pos = nowPlace + 1
						continue
					}

				} else {
					for _, iChar := range charList {
						nowChar := codeSegStart[pos]
						if nowChar == iChar[0] {
							symbolStack.Push(nowChar)
						} else if nowChar == iChar[1] {
							if symbolStack.GetLength() > 0 && symbolStack.GetTop() == iChar[0] {
								symbolStack.Pop()
							} else {
								symbolStack.Push(nowChar)
							}
						}
					}
				}
			}
			pos++
		}
	}

	return reverseStack(symbolStack.GetData()[:symbolStack.GetLength()]), symbolStack.GetData()[:symbolStack.GetLength()]
}

func RemoveRepeatedElement(arr []rune) (newArr []rune) {
	newArr = make([]rune, 0)
	for i := 0; i < len(arr); i++ {
		repeat := false
		for j := i + 1; j < len(arr); j++ {
			if arr[i] == arr[j] {
				repeat = true
				break
			}
		}
		if !repeat {
			newArr = append(newArr, arr[i])
		}
	}
	return newArr
}

func (xssCheck *XSSChecker) mixString(symbolNeed []rune) (string, []string) {
	_ret := ""
	_parts := make([]string, 2*len(symbolNeed)+1)

	var partLen int = len(XsscheckSimpleString) / (len(symbolNeed) + 1)
	for i := 0; i < len(symbolNeed)+1; i++ {
		if i != len(symbolNeed) {
			_ret += XsscheckSimpleString[i*partLen:(i+1)*partLen] + string(symbolNeed[i])
			_parts[2*i] = XsscheckSimpleString[i*partLen : (i+1)*partLen]
			_parts[2*i+1] = string(symbolNeed[i])
		} else {
			_ret += XsscheckSimpleString[i*partLen : (i+1)*partLen]
			_parts[2*i] = XsscheckSimpleString[i*partLen : (i+1)*partLen]
		}
	}
	return _ret, _parts
}

func (xssCheck *XSSChecker) checkNecessaryChars(url string, method string, data string, char_list []rune, memory bool) bool {
	if len(char_list) == 0 {
		return true
	}
	symbolNeeded := RemoveRepeatedElement(char_list)
	symbolSend := make([]rune, 0)
	log.WithFields(log.Fields{"necessary_char": string(symbolNeeded)}).Debug("Checking Necessary Chars")
	if xssCheck.isMemoryOpen && memory {

		for _, _char := range symbolNeeded {
			v, ok := xssCheck.symbolAccept[_char]
			if ok {
				if v {

				} else {

					return false
				}
			} else {

				symbolSend = append(symbolSend, _char)
			}
		}
		if len(symbolSend) == 0 {
			log.Debug("Find all Necessary Chars in Memory")
			return true
		}
	} else {

		symbolSend = symbolNeeded
	}
	testString, _ := xssCheck.mixString(symbolSend)
	log.Debug(testString)
	testR, _, err := xssCheck.sendRequests(url, method, data, testString)

	if err != nil {
		log.WithField("request_url", url).WithField("test_string", testString).Error("Test Failed:", err)
		return false
	}

	if !bytes.Contains(testR, []byte(testString)) &&
		!(strings.Contains(testString, "'") &&
			bytes.Contains(testR, []byte(strings.Replace(testString, "'", "\\'", 1)))) {
		log.Debug("not found all necessary chars")

		return false
	} else {
		if xssCheck.isMemoryOpen {
			for _, _char := range symbolSend {
				xssCheck.symbolAccept[_char] = true
			}
		}
	}
	log.Debug("Find all necessary Chars in request")
	return true

}

func (xssCheck *XSSChecker) checkNecessaryString(targetUrl string, method string, data string, str string) bool {
	mixString := XsscheckSimpleString[:len(XsscheckSimpleString)/2] + str + XsscheckSimpleString[len(XsscheckSimpleString)/2:]
	resp, _, err := xssCheck.sendRequests(targetUrl, method, data, mixString)
	if err != nil {
		return false
	}
	return bytes.Contains(resp, []byte(mixString))
}

func (xssCheck *XSSChecker) createPayload(payloadInfo XSSPayloadStruct, inAttr bool) string {

	split := "\n"
	var payload string
	if len(payloadInfo.Tag) != 0 {
		payload = fmt.Sprintf("<%s{attr}>", payloadInfo.Tag)
	} else {
		payload = "{attr}"
	}
	if len(payloadInfo.Attr) != 0 {
		for k, v := range payloadInfo.Attr {

			if len(v) == 0 {

				payload = strings.Replace(payload, "{attr}",
					fmt.Sprintf("%s%s{attr}", split, k), 1)
			} else {

				payload = strings.Replace(payload, "{attr}",
					fmt.Sprintf("%s%s\x09=%s{attr}", split, k, v), 1)
			}

		}
	}

	payload = strings.Replace(payload, "{attr}", "", 1)
	if len(payloadInfo.Text) != 0 {
		payload += payloadInfo.Text

		if len(payloadInfo.Tag) != 0 {
			payload += fmt.Sprintf("</%s\n", payloadInfo.Tag)
		}

	} else {
		if inAttr && payload[len(payload)-1] == '>' {
			payload = payload[:len(payload)-1] + "\n"
		}
	}
	log.WithField("payload", payload).Debug("Create payload Finish")

	return payload

}

func (xssCheck *XSSChecker) loadLastPayload() *XSSPayloadStruct {

	if xssCheck.hasLastPayload {
		return &xssCheck.lastPayload
	}
	return nil
}

func (xssCheck *XSSChecker) saveLastPayload(payloadInfo XSSPayloadStruct) {

	xssCheck.lastPayload = payloadInfo
	xssCheck.hasLastPayload = true
}

func (xssCheck *XSSChecker) checkKnowledge(payload string) bool {
	for k, v := range xssCheck.symbolAccept {
		if !v {
			if strings.ContainsRune(payload, k) {
				return false
			}
		}
	}
	return true
}

func (xssCheck *XSSChecker) xpathValue2concat(value string) string {
	value = strings.ReplaceAll(value, "\"", "[\"]")
	value = strings.ReplaceAll(value, "'", "',\"'\",'")
	value = strings.ReplaceAll(value, "[\"]", "','\"','")
	value = "concat('" + value + "')"
	return value
}

func (xssCheck *XSSChecker) findPayload(html []byte, payload XSSPayloadStruct, inAttr bool, textContain bool) bool {
	if doc, err := libxml2.ParseHTML(html, HTMLParserOption); err != nil {
		log.WithField("place", "FindPayload").Error(err)
		return false
	} else {

		xpathStr := "//{tag}"
		_first := true
		if len(payload.Tag) != 0 {
			xpathStr = strings.Replace(xpathStr, "{tag}", payload.Tag, 1)
		} else {
			xpathStr = strings.Replace(xpathStr, "{tag}", "*", 1)
		}
		xpathStr += "[{attr}]"
		if len(payload.Text) != 0 {
			if !textContain {
				xpathStr = strings.Replace(xpathStr, "{attr}", fmt.Sprintf("text()=\"%s\"{attr}", payload.Text), 1)
			} else {

				if strings.Contains(payload.Text, "'") && strings.Contains(payload.Text, "\"") {

					xpathStr = strings.Replace(xpathStr, "{attr}", fmt.Sprintf("text()[contains(.,%s)]{attr}", xssCheck.xpathValue2concat(payload.Text)), 1)
				} else if strings.Contains(payload.Text, "\"") {
					xpathStr = strings.Replace(xpathStr, "{attr}", fmt.Sprintf("text()[contains(.,'%s')]{attr}", payload.Text), 1)
				} else {
					xpathStr = strings.Replace(xpathStr, "{attr}", fmt.Sprintf("text()[contains(.,\"%s\")]{attr}", payload.Text), 1)
				}
			}
			_first = false
		}
		_and := ""
		if len(payload.Attr) != 0 {
			for k, v := range payload.Attr {
				if _first {
					_and = ""
				} else {
					_and = " and "
				}
				xpathStr = strings.Replace(
					xpathStr,
					"{attr}",
					fmt.Sprintf("%s@%s=\"%s\"{attr}", _and, k, strings.ReplaceAll(v, "\"", "\\\"")),
					1)
				_first = false
			}
		}
		xpathStr = strings.Replace(xpathStr, "{attr}", "", 1)
		log.WithField("xpath", xpathStr).Debug("Build XPath Complete")
		nodes, err := doc.Find(xpathStr)

		xpathNodes := xpath.NodeList(nodes, err)
		if len(xpathNodes) != 0 {

			if len(payload.TagSupport) != 0 {

				for _, node := range xpathNodes {
					element, ok := node.(types.Element)
					if ok {

						for _, tagName := range payload.TagSupport {
							if tagName == element.NodeName() {

								return true
							}
						}
					} else {

						log.Error("Cast Node to Element Failed")
					}
				}
				return false
			} else {
				return true
			}
		}
		return false
	}
}
func (xssCheck *XSSChecker) simpleTest(targetUrl string, method string, data string, prefix string,
	suffix string, payloadList []XSSPayloadStruct, inAttr bool) string {
	lastPayload := xssCheck.loadLastPayload()
	if lastPayload != nil {

	}

	iUseTag := xssCheck.checkNecessaryString(targetUrl, method, data, "<")
	if !iUseTag {
		log.Debug("Can't Use Payload With Tag")
	}
	for _, _payload := range payloadList {
		if len(_payload.Tag) != 0 && !iUseTag {

			continue
		}
		if !inAttr && len(_payload.Tag) == 0 {

			continue
		}
		payloadSend := prefix + xssCheck.createPayload(_payload, inAttr)
		log.WithField("Payload_send", payloadSend).Debug("Simple Test Payload Send")
		if !xssCheck.checkKnowledge(payloadSend) {
			continue
		}
		content, _, err := xssCheck.sendRequests(targetUrl, method, data, payloadSend)
		if err != nil {
			log.WithFields(log.Fields{"targetUrl": targetUrl, "payload": payloadSend}).Error("Simple Test Request Error:", err)
			continue
		}
		if xssCheck.findPayload(content, _payload, true, false) {

			log.Debug("Found Payload")
			xssCheck.saveLastPayload(_payload)

			if bytes.Contains(content, []byte(payloadSend)) {
				return payloadSend
			} else if inAttr {

				payloadSendNew := prefix + xssCheck.createPayload(_payload, false) + suffix
				content, _, err = xssCheck.sendRequests(targetUrl, method, data, payloadSendNew)
				if xssCheck.findPayload(content, _payload, true, false) {
					return payloadSendNew
				} else {
					return payloadSend
				}
			} else {
				return payloadSend
			}
		}
	}
	return ""
}
func (xssCheck *XSSChecker) checkHidden(element types.Element) (bool, bool) {
	_type := ""
	if attrType, err := element.GetAttribute("type"); err == nil && attrType != nil {
		if attrType.Value() == "hidden" {
			_type = "type"
		}
	}

	if attrHidden, err := element.GetAttribute("hidden"); err == nil && attrHidden != nil {
		if strings.ToLower(attrHidden.Value()) == "true" {
			_type = "hidden"
		}
	}

	if attrStyle, err := element.GetAttribute("style"); err == nil && attrStyle != nil {
		if displayRe.MatchString(attrStyle.Value()) {
			_type = "style"
		}
	}

	if _type == "" {
		return false, true
	} else {
		iBase := 0
		iHidden := 0
		i := 0
		attribs, err := element.Attributes()
		if err != nil {
			log.Error("can't get Element Attributes")
			return true, false
		}
		for _, attrib := range attribs {
			key := attrib.NodeName()
			if key == _type {
				iHidden = i
			}
			if strings.Contains(attrib.Value(), XsscheckSimpleString) {
				iBase = i
			}
			i += 1
		}
		if iHidden <= iBase {
			return true, false
		} else {
			return true, true
		}
	}

}
func (xssCheck *XSSChecker) processElementInTag(
	document types.Document,
	url string,
	method string,
	data string) string {
	log.Debug("Checking in tag")
	payloadPrefix := ""
	payloadSuffix := ""
	_xpath := fmt.Sprintf("//*[text()[contains(.,\"%s\")]]", XsscheckSimpleString)

	nodes, err := document.Find(_xpath)
	xpathNodes := xpath.NodeList(nodes, err)
	for i := 0; i < len(xpathNodes); i++ {
		element, _ := xpathNodes[i].(types.Element)
		switch strings.ToLower(element.NodeName()) {
		case "script":
			log.Debug("Find in Tag Script")
			if !xssCheck.checkNecessaryString(url, method, data, "</script>") {

				log.Debug("Can not close Script Tag")
			} else {
				res := xssCheck.simpleTest(url, method, data, "</script>", "", xssCheck.xssPayload, false)
				if len(res) != 0 {
					return res
				}
			}
			before, _ := xssCheck.processBalanceStack(element.TextContent(), false)

			log.WithField("Chars", string(before)).Debug("Script Balance Chars")
			if before[0] == '\'' {
				payloadPrefix = "'-"
				payloadSuffix = "-'"
			} else if before[0] == '"' {
				payloadPrefix = "\"-"
				payloadSuffix = "-\""
			} else {

				index := strings.Index(element.TextContent(), XsscheckSimpleString)
				payloadPrefix = "-"

				if index+1 < len(element.TextContent()) && (element.TextContent()[index+1] == ')' || element.TextContent()[index+1] == ',') {
					payloadSuffix = "-"
				}

			}
			if xssCheck.checkNecessaryString(url, method, data, payloadPrefix) {
				log.Debug("Tap into Script Content Insert")
				for _, functionPayload := range FunctionList {
					payload := payloadPrefix + functionPayload + payloadSuffix
					content, _, err := xssCheck.sendRequests(url, method, data, payload)
					if err == nil {
						if (xssCheck.findPayload(content, XSSPayloadStruct{
							Tag:  "script",
							Text: payload,
						}, false, true)) {
							return payload
						} else {
							continue
						}
					}
				}
				return ""
			} else {
				return ""
			}

		case "textarea":
			fallthrough
		case "noscript":
			log.Debug("Find in Tag " + element.NodeName())
			payloadPrefix = fmt.Sprintf("</%s>", element.NodeName())
			if !xssCheck.checkNecessaryString(url, method, data, payloadPrefix) {
				return ""
			}
		default:

		}
		res := xssCheck.simpleTest(url, method, data, payloadPrefix, "", xssCheck.xssPayload, false)

		if len(res) != 0 {
			log.WithField("Result Tag Payload", res).Debug("find Payload Success")
		}
		return res
	}
	return ""

}

var escapeChars []rune = []rune{
	'.', '\\', '?', '(', ')', '+', '[', ']', '^', '$', '|',
}
var entityStr map[rune]string = map[rune]string{
	'&':  "(&|&amp;)",
	'"':  "(\"|&quot;)",
	'<':  "(<|&lt;)",
	'>':  "(>|&gt;)",
	'\'': "('|&apos;)",
}

func (xssCheck *XSSChecker) writeStringWithEscape(buffer *bytes.Buffer, str string) {
outerLoop:
	for _, ch := range []rune(str) {

		for _, escapeChar := range escapeChars {
			if ch == escapeChar {
				buffer.WriteRune('\\')
				buffer.WriteRune(ch)
				continue outerLoop
			}
		}

		for entityChar, outValue := range entityStr {
			if ch == entityChar {
				buffer.WriteString(outValue)
				continue outerLoop
			}
		}
		buffer.WriteRune(ch)
	}
}

func (xssCheck *XSSChecker) findSourceByAttr(document []byte, element types.Element) string {
	var buffer bytes.Buffer
	buffer.WriteRune('<')
	buffer.WriteString(element.NodeName())
	attributes, _ := element.Attributes()
	log.WithField("value", element.String()).Debug("Source Element")
	for _, attr := range attributes {
		buffer.WriteString("\\s*?")

		xssCheck.writeStringWithEscape(&buffer, attr.NodeName())

		if len(attr.NodeValue()) == 0 {
			buffer.WriteString("\\s*?(=\\s*?([\\'\"]?[\\'\"]+)?)?")
		} else {
			buffer.WriteString("\\s*?=\\s*?([\\'\"]+)?")

			if strings.Contains(attr.NodeValue(), XsscheckSimpleString) {

				buffer.WriteString(".*?")
				buffer.WriteString(XsscheckSimpleString)
				buffer.WriteString(".*?")
			} else {
				buffer.WriteString(".*?")
			}

			buffer.WriteString("([\\'\"]+)?")
		}
	}

	buffer.WriteString("\\s*?/?>")
	regexStr := buffer.String()
	compileRegex, err := regexp.Compile(regexStr)
	if err != nil {
		log.WithField("regex", regexStr).Fatal("Error Regex!!")
		return ""
	}
	log.WithField("regex", regexStr).Debug("Regex")
	find := compileRegex.Find(document)
	if find == nil {
		return ""
	} else {
		findStr := string(find)
		log.WithField("Str", findStr).Debug("Found Source")
		return findStr
	}

}
func (xssCheck *XSSChecker) processElementInAttribute(
	resp []byte,
	document types.Document,
	url string,
	method string,
	data string) string {

	log.Debug("Checking in attribute")
	_xpath := fmt.Sprintf("//*[@*[contains(.,\"%s\")]]", XsscheckSimpleString)

	nodes, err := document.Find(_xpath)
	xpathNodes := xpath.NodeList(nodes, err)
	for i := 0; i < len(xpathNodes); i++ {

		tagText := xpathNodes[i].String()

		if !bytes.Contains(resp, []byte(tagText)) {
			log.WithField("tag_text", tagText).Debug("tag_text can not match in Source Code,go to regex Find")

			tagText = xssCheck.findSourceByAttr(resp, xpathNodes[i].(types.Element))
		}
		if len(tagText) == 0 {
			log.Debug("Can not find Original Source Code")
			continue
		}
		xssCheck.processBalanceStack(tagText, false)
		before, after := xssCheck.processBalanceStack(tagText, false)
		prefix := string(before)
		suffix := string(after)

		simpleRes := ""

		if !xssCheck.checkNecessaryChars(url, method, data, before, true) {

			simpleRes = ""
		} else {
			res := xssCheck.simpleTest(url, method, data, prefix, "", xssCheck.xssPayload, true)
			if len(res) != 0 {
				log.WithField("Result Payload", res).Debug("find Payload Success")
				simpleRes = res
			}
		}
		if len(simpleRes) != 0 {
			return simpleRes
		}

		if len(prefix) > 0 {
			if '>' == prefix[len(prefix)-1] {
				prefix = prefix[:len(prefix)-1]
			}
			if '<' == suffix[0] {
				suffix = suffix[1:]
			}
		}
		if !xssCheck.checkNecessaryChars(url, method, data, []rune(prefix), true) {
			log.Debug("can't close the attribute")
			return ""
		}

		element, _ := xpathNodes[i].(types.Element)
		isHidden, canClosed := xssCheck.checkHidden(element)
		if isHidden {
			if !canClosed {
				log.Debug("is Hidden and Can not Close")
				return ""
			} else {
				prefix = prefix + " style=display:block "
			}
		}
		suffix = " " + suffix
		res := xssCheck.simpleTest(url, method, data, prefix, suffix, xssCheck.xssPayload, true)
		if len(res) != 0 {
			log.WithField("Result Attr Payload", res).Debug("find Payload Success")
		}
		return res

	}

	return ""
}
func (xssCheck *XSSChecker) Check(Url string, method string, data string) (*XSSPayloadResult, string, error) {
	log.Debug("================New Checking==============")
	resp, requestString, err := xssCheck.sendRequests(Url, method, data, XsscheckSimpleString)
	if err != nil {
		return &invulnerableResult, requestString, err
	}
	if !bytes.Contains(resp, []byte(XsscheckSimpleString)) {
		log.Debug("Find No Simple String")
		return &invulnerableResult, requestString, err
	}
	if doc, err := libxml2.ParseHTML(resp, HTMLParserOption); err != nil {
		log.Fatal(err)
		return &invulnerableResult, requestString, err
	} else {
		var payload string

		payload = xssCheck.processElementInAttribute(resp, doc, Url, method, data)
		if payload == "" {
			payload = xssCheck.processElementInTag(doc, Url, method, data)
		}
		if payload != "" {
			return &XSSPayloadResult{
				Vulnerable: true,
				Url:        strings.Replace(Url, XSSPayloadPlaceholder, url.QueryEscape(payload), 1),
				Method:     method,
				Data:       strings.Replace(data, XSSPayloadPlaceholder, url.QueryEscape(payload), 1),
			}, requestString, nil
		}
	}
	return &invulnerableResult, requestString, nil
}
func buildQuery(values url.Values, targetKey string) string {
	var buffer bytes.Buffer
	for k := range values {
		if len(values[k]) == 0 {
			buffer.WriteString(k)
		} else {
			buffer.WriteString(k)
			buffer.WriteRune('=')

			if k != targetKey {
				buffer.WriteString(url.QueryEscape(values[k][0]))
			} else {
				buffer.WriteString(XSSPayloadPlaceholder)
			}
		}
		buffer.WriteRune('&')
	}

	return buffer.String()
}

func (xssCheck *XSSChecker) CheckURL(targetUrl string, method string,
	data string) ([]*XSSPayloadResult, string, error) {
	var result []*XSSPayloadResult

	u, err := url.Parse(targetUrl)
	if err != nil {
		log.WithField("URL", targetUrl).Error("Error Parse URL")
		return nil, "", err
	}

	var requestString string
	m, _ := url.ParseQuery(u.RawQuery)
	for k := range m {

		if len(m[k]) != 0 {
			query := buildQuery(m, k)
			reqUrl := fmt.Sprintf("%s://%s%s?%s", u.Scheme, u.Host, u.Path, query)
			checkRes, reqStr, err := xssCheck.Check(reqUrl, method, data)
			if err == nil && checkRes.Vulnerable == true {
				result = append(result, checkRes)
			}
			requestString = reqStr
		}
	}

	if method == "POST" {
		reqUrl := fmt.Sprintf("%s://%s%s?%s", u.Scheme, u.Host, u.Path, u.RawQuery)
		m, _ = url.ParseQuery(data)
		for k := range m {
			if len(m[k]) != 0 {
				query := buildQuery(m, k)
				checkRes, reqStr, err := xssCheck.Check(reqUrl, method, query)
				if err == nil && checkRes.Vulnerable == true {
					result = append(result, checkRes)
				}
				requestString = reqStr
			}
		}
	}
	return result, requestString, nil
}

func (xssCheck *XSSChecker) CheckUrlWithKey(targetUrl string, method string, data string, key string) ([]*XSSPayloadResult, error) {
	var result []*XSSPayloadResult
	u, err := url.Parse(targetUrl)
	if err != nil {
		log.WithField("URL", targetUrl).Error("Error Parse URL")
		return nil, err
	}

	m, _ := url.ParseQuery(u.RawQuery)
	for k := range m {

		if k == key && len(m[k]) != 0 {
			query := buildQuery(m, k)
			reqUrl := fmt.Sprintf("%s://%s%s?%s", u.Scheme, u.Host, u.Path, query)
			checkRes, _, err := xssCheck.Check(reqUrl, method, data)
			if err == nil && checkRes.Vulnerable == true {
				result = append(result, checkRes)
			}
			break
		}
	}

	if method == "POST" {
		reqUrl := fmt.Sprintf("%s://%s%s?%s", u.Scheme, u.Host, u.Path, u.RawQuery)
		m, _ = url.ParseQuery(data)
		for k := range m {
			if k == key && len(m[k]) != 0 {
				query := buildQuery(m, k)
				checkRes, _, err := xssCheck.Check(reqUrl, method, query)
				if err == nil && checkRes.Vulnerable == true {
					result = append(result, checkRes)
				}
			}
			break
		}
	}
	return result, nil
}

func (xssCheck *XSSChecker) CheckDirectoryURL(targetUrl string) ([]*XSSPayloadResult, string, error) {
	var result []*XSSPayloadResult
	reqUrl := targetUrl + "/" + XSSPayloadPlaceholder
	checkRes, requestString, err := xssCheck.Check(reqUrl, "GET", "")
	if checkRes != nil {
		if checkRes.Vulnerable && err == nil {
			result = append(result, checkRes)
			return result, requestString, nil
		}
	}
	return result, requestString, nil
}
