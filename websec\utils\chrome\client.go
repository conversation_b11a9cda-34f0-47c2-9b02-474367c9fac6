package chrome

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"sync/atomic"
	"time"
	"websec/common"
	"websec/utils/log"
)

type Client struct {
	address          string
	httpClient       *http.Client
	injectJavaScript *common.InjectJavaScript

	CurTabCount int32
}

type Options struct {
	Addr   string
	Script *common.InjectJavaScript
}

func NewClient(option *Options) *Client {
	client := &Client{
		address:          option.Addr,
		httpClient:       new(http.Client),
		injectJavaScript: option.Script,
	}

	return client
}

func (c *Client) NewTab() (*Tab, error) {
	u := fmt.Sprintf("http://%s/json/new", c.address)
	req, err := http.NewRequest(http.MethodPut, u, nil)
	if err != nil {
		log.Errorln("create request:", err)
		return nil, err
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		log.Errorln("send request:", err)
		return nil, err
	}
	defer resp.Body.Close()

	var tab Tab
	if err = decode(resp, &tab); err != nil {
		log.Errorln("decode:", u, err, resp)
		return nil, err
	}
	tab.InjectJavaScript = c.injectJavaScript
	atomic.AddInt32(&c.CurTabCount, 1)
	return &tab, nil
}

func (c *Client) CloseTab(ID string) bool {
	u := fmt.Sprintf("http://%s/json/close/%s", c.address, ID)
	resp, err := c.httpClient.Get(u)
	atomic.AddInt32(&c.CurTabCount, -1)
	if resp != nil {
		defer resp.Body.Close()
	}

	if err != nil || resp.StatusCode != 200 {
		return false
	}
	return true
}

func (c *Client) SyncTabCount() {
	go func() {
		ticker := time.NewTicker(60 * time.Second)
		for t := range ticker.C {
			tabCount := c.getTabCount()
			log.Infof("---- sync chrome tab count ---- at:%s, host:%s, count:%d\n", t.String(), c.address, tabCount)
			atomic.StoreInt32(&c.CurTabCount, tabCount)
		}
	}()
}

func (c *Client) getTabCount() int32 {

	var countWhenFail int32 = 200
	u := fmt.Sprintf("http://%s/json", c.address)
	resp, err := c.httpClient.Get(u)
	if resp != nil {
		defer resp.Body.Close()
	}

	if err != nil || resp.StatusCode != 200 {
		return countWhenFail
	}

	var tabs []Tab
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorln("Read chrome tabcount response error.", err)
		return countWhenFail
	}

	if err = json.Unmarshal(body, &tabs); err != nil {
		log.Errorln("Unmarshal chrome tabcount response error.", err)
		return countWhenFail
	}
	return int32(len(tabs))
}

func (c *Client) ScreenShot(url string) ([]byte, error) {
	tab, err := c.NewTab()
	if err != nil {
		return nil, err
	}
	defer c.CloseTab(tab.ID)

	tab.Init()
	err = tab.Connect()
	if err != nil {
		return nil, err
	}
	defer tab.Close()

	err = tab.InitProtocol()
	if err != nil {
		log.Errorln("chrome init protocol error.", err)
		return nil, err
	}

	err = tab.Navigate(url)
	if err != nil {
		log.Errorln("chrome navigate error.", err)
		return nil, err
	}

	return tab.screenShot("jpeg", 50, true, Viewport{})
}
