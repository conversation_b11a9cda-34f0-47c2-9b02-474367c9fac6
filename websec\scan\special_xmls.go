package scan

const (
	XMLStruts2_046               = "Struts2_S2-046.xml"
	XMLStruts2_046Header         = "Struts2_S2-046(header).xml"
	XMLStruct2_057               = "Struts2_S2-057.xml"
	XMLSQLInjection              = "Sql_Injection.xml"
	XMLSQLInjectionInsideCheck   = "Sql_Injection(inside_check).xml"
	XMLSQLInjectionInPath        = "SQL_injection_in_path.xml"
	XMLPossibleSensitiveDirs     = "Possible_sensitive_directories.xml"
	XMLPossibleSensitiveFiles    = "Possible_sensitive_filePossible_sensitive_filess.xml"
	XMLPossibleSensitiveFiles2   = "Possible_sensitive_files2.xml"
	XMLFileXSS                   = "File_Cross_Site_Scripting.xml"
	XMLWebDavDirWithWritePerm    = "WebDAV_directory_with_Write_Permissions.xml"
	XMLXSS                       = "Cross_Site_Scripting.xml"
	XMLXSSInPath                 = "Cross_Site_Scripting_in_path.xml"
	XMLCrossFrameScripting       = "Cross_frame_scripting.xml"
	XMLSourceCodeDisclosure      = "Source_code_disclosure.xml"
	XMLUTF7BOMXSS                = "UTF7_BOM_XSS.xml"
	XMLWebSphereRCE              = "CVE-2018-1567_IBM_WebSphere_RCE.xml"
	XMLDirectoryBackupCheck      = "Directory_backup_check.xml"
	XMLDirectoryBackupCheckSmall = "Directory_backup_check_small.xml"
	XMLFileBackupCheck           = "File_backup_check.xml"

	XMLXssStorage = "Cross_Site_Scripting_Store.xml"
)
