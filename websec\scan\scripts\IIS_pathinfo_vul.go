package scripts

import (
	"bytes"
	"io/ioutil"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

func checkPathInfoResponse(resp *fasthttp.Response, body []byte, firstStep bool) bool {
	var contentTypeIsMatch = strings.Contains(string(resp.Header.Peek("Content-Type")), "text/html")
	if firstStep {
		contentTypeIsMatch = !contentTypeIsMatch
	}
	return resp.StatusCode() == 200 && contentTypeIsMatch &&
		!bytes.Contains(body, []byte("404 Not Found"))
}

func compairPathInfoRequests(args *ScriptScanArgs, rawurl string, serverName string) (string, error) {

	urlA := rawurl
	urlB := urlA + "/index.php"

	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.Header.SetMethod(http.MethodGet)
	request.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	request.Header.Set("Connection", "keep-alive")
	request.SetRequestURI(urlA)
	err := headerNormalizingHTTPClient.DoTimeout(request, response, time.Second*5)
	if err != nil {
		return "", nil
	}
	bodyA, err := utils.GetOriginalBody(response)
	if err != nil {
		return "", err
	}

	headerServer := string(response.Header.Peek("Server"))
	if strings.Contains(strings.ToLower(headerServer), serverName) {
		if checkPathInfoResponse(response, bodyA, true) {
			responseB := fasthttp.AcquireResponse()
			defer fasthttp.ReleaseResponse(responseB)

			request.SetRequestURI(urlB)
			err = headerNormalizingHTTPClient.DoTimeout(request, responseB, time.Second*5)
			if err != nil {
				return "", nil
			}
			bodyB, err := utils.GetOriginalBody(responseB)
			if err != nil {
				return "", err
			}
			if checkPathInfoResponse(responseB, bodyB, false) && textSimilarity(string(bodyA), string(bodyB)) > 0.9 {
				return urlB, nil
			}
		}
	}
	return "", nil
}

var cssPattern = regexp.MustCompile(`href="(.+?\.css)"`)
var imagePattern = regexp.MustCompile(`src="(.+?\.(jpg|png))"`)

func scanPathInfo(args *ScriptScanArgs, serverName string) (*ScriptScanResult, error) {

	for _, path := range []string{"robots.txt", "favicon.ico"} {
		result, err := compairPathInfoRequests(args, constructURL(args, path), serverName)
		if err != nil {
			return nil, err
		}
		if result != "" {
			return &ScriptScanResult{Vulnerable: true, Output: result}, nil
		}
	}

	rawurl := constructURL(args, "/")
	req, err := http.NewRequest(http.MethodGet, rawurl, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("User-Agent", scriptUserAgent)
	resp, err := goHTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.Request.Host == args.Host {
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}

		urlParts := resp.Request.URL

		var tmpURL, tmpPath string
		groups := [][][]byte{}
		groups = append(groups, cssPattern.FindAllSubmatch(body, -1)...)
		groups = append(groups, imagePattern.FindAllSubmatch(body, -1)...)
		for _, group := range groups {

			urlParts.RawPath = ""
			tmpPath = string(group[1])
			if strings.HasPrefix(tmpPath, "//") || strings.HasPrefix(tmpPath, "http://") || strings.HasPrefix(tmpPath, "https://") {
				tmpParts, err := url.Parse(tmpPath)
				if err == nil && tmpParts.Host == urlParts.Host {

					urlParts.Path = tmpParts.Path
				} else {
					continue
				}
			} else {
				urlParts.Path = tmpPath
			}
			tmpURL = urlParts.String()
			result, err := compairPathInfoRequests(args, tmpURL, serverName)
			if err != nil {
				return nil, err
			}
			if result != "" {
				return &ScriptScanResult{Vulnerable: true, Output: result}, nil
			}

			break
		}
	}

	return &invulnerableResult, nil
}

func IISPathInfoVul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	return scanPathInfo(args, "iis")
}

func init() {
	registerHandler("IIS_php_pathinfo_1.xml", IISPathInfoVul)
}
