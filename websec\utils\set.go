package utils

type StringSet map[string]bool

func StringArrayToSet(elements []string) StringSet {
	s := StringSet{}
	for i := range elements {
		s[elements[i]] = true
	}
	return s
}

func (set StringSet) Add(key string) {
	set[key] = true
}

func (set StringSet) Contains(key string) bool {
	_, ok := set[key]
	return ok
}

func (set StringSet) Remove(key string) {
	if _, ok := set[key]; ok {
		delete(set, key)
	}
}

func (set StringSet) AsStringArray() []string {
	ret := make([]string, 0)
	for k := range set {
		ret = append(ret, k)
	}
	return ret
}
