package crawl

import (
	"bufio"
	"crypto/tls"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"sync"
	"websec/common"
	"websec/utils"
	"websec/utils/log"

	"time"
)

var dirPool sync.Pool

func newDirValue() *DirValue {
	if v := dirPool.Get(); v != nil {
		d := v.(*DirValue)
		d.Reset()
		return d
	}
	return new(DirValue)
}

func PutDirValue(d *DirValue) {
	d.Reset()
	dirPool.Put(d)
}

type DirValue struct {
	ExtUrl  string
	ExtList []string
	Agent   string
	Cookie  string
	Depth   int32
}

func (d *DirValue) Reset() {
	d.ExtUrl = ""
	d.ExtList = nil
	d.Agent = ""
	d.Cookie = ""
	d.Depth = 0
}

func (d *DirValue) Clear() {
	PutDirValue(d)
}

type FileValue struct {
	ExtUrl    string
	Agent     string
	Cookie    string
	ErrorPage []byte
	Depth     int32
}

type DirResult struct {
	ResultUrl string

	Agent     string
	Cookie    string
	ErrorPage []byte
	IsWeak    bool
	Depth     int32
}

type DirHandler struct {
	uriMap map[string]bool

	DirQueue *utils.Queue

	PayloadChan chan DirResult
	ResultChan  chan *DirResult
	Crawler     *WSCrawler
}

var goHTTPClient = http.Client{

	Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}},
	Timeout:   10 * time.Second,

	CheckRedirect: func(req *http.Request, via []*http.Request) error {
		return http.ErrUseLastResponse
	},
}

func readFileToMap(extDictPath string) (map[string]bool, error) {
	uriMap := make(map[string]bool)
	f, err := os.Open(extDictPath)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	scanner := bufio.NewScanner(f)
	for scanner.Scan() {
		lineValue := scanner.Text()
		singleLine := strings.Split(lineValue, ",")
		if len(singleLine) == 2 {
			uri := singleLine[0]
			isWeak, err := strconv.ParseBool(singleLine[1])
			if err != nil {
				log.Info(lineValue)
				log.Info(err)
			}
			uriMap[uri] = isWeak
		} else {
			log.Info(lineValue)
			log.Info("format err in dict" + lineValue)
		}
	}
	return uriMap, nil
}

func InitDirHandler(crawler *WSCrawler) (handler *DirHandler, err error) {

	uriMap, err := readFileToMap("/opt/projects/websec/ext.txt")
	if err != nil {
		return
	}

	handler = &DirHandler{}
	handler.uriMap = uriMap
	handler.DirQueue = utils.NewQueue()
	handler.PayloadChan = make(chan DirResult, 10)
	handler.ResultChan = make(chan *DirResult, 10)
	handler.Crawler = crawler

	crawler.dirHandler = handler
	return
}

func IsDirectory(url string) bool {
	if strings.HasSuffix(url, "/") {
		return true
	}
	return false
}

func IsWafPage(body []byte) bool {
	wafKeywordList := make([]string, 0)
	wafKeywordList = append(wafKeywordList,
		"造成安全威胁",
		"Bot-Block-ID",
		"您访问IP已被管理员限制",
		"本次事件ID",
		"当前访问疑似黑客攻击",
		"safedog",
		"拦截",
		"ValidateInputIfRequiredByConfig",
		"You don't have permission to access",
		"location.href",
	)

	for _, key := range wafKeywordList {
		if strings.Contains(string(body), key) {
			return true
		}
		continue
	}
	return false
}

func UrlParse(baseurl string) (string, string, error) {
	info, err := url.Parse(baseurl)
	if err != nil {
		return "", "", err
	}
	return info.Host, info.Path, nil
}

func constructURL(baseUrl string, uri string) string {
	rawurl := fmt.Sprintf("%s%s", baseUrl, uri)
	return rawurl
}

func DoRequest(url string, data url.Values, method string, agent string, cookie string) (*http.Response, []byte, error) {
	req, err := http.NewRequest(method, url, nil)
	if err != nil {

		return nil, nil, err
	}
	req.Header.Set("User-Agent", agent)
	req.Header.Set("Cookie", cookie)
	resp, errRes := goHTTPClient.Do(req)
	if errRes != nil {

		return nil, nil, errRes
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {

		return nil, nil, err
	}
	return resp, body, nil
}

func GetErrorPage(base string, agent string, cookie string) (*http.Response, []byte, error) {
	url := constructURL(base, "neverexists")
	resp, body, err := DoRequest(url, nil, "GET", agent, cookie)
	if err != nil {
		return resp, nil, err
	}

	if body != nil {
		return resp, body, nil
	}
	return resp, nil, nil
}

func checkBaseUrl(baseUrl string, agent string, cookie string) error {
	_, _, err := DoRequest(baseUrl, nil, "GET", agent, cookie)
	if err != nil {
		return err
	}
	return nil
}

func (handler *DirHandler) DirScanner(dirItem *DirValue) {
	defer func() {
		PutDirValue(dirItem)
		handler.Crawler.afterProcessing()
	}()

	baseUrl := dirItem.ExtUrl
	extList := dirItem.ExtList
	agent := dirItem.Agent
	cookie := dirItem.Cookie
	curDepth := dirItem.Depth

	err := checkBaseUrl(baseUrl, agent, cookie)
	if err != nil {
		var err = errors.New("baseurl不可访问,跳过目录爆破。")
		log.Error(err)
		return
	}

	_, errorPage, err := GetErrorPage(baseUrl, agent, cookie)
	if err != nil {
		log.Error(err)

		return
	}

	payloadItem := DirResult{
		Agent:     agent,
		Cookie:    cookie,
		ErrorPage: errorPage,
		Depth:     curDepth,
	}

	for uri, isWeak := range handler.uriMap {
		payloadItem.IsWeak = isWeak
		generalUrl := constructURL(baseUrl, uri)
		if strings.Contains(uri, "%EXT%") {

			for _, ext := range extList {
				extentUrl := strings.Replace(generalUrl, "%EXT%", ext, -1)
				payloadItem.ResultUrl = extentUrl

				handler.DealRequest(payloadItem)
			}
		} else if strings.Contains(uri, "%BASE%") {

			domain, _, err := UrlParse(baseUrl)
			if err != nil {
				continue
			}
			extentUrl := strings.Replace(generalUrl, "%BASE%", domain, -1)
			payloadItem.ResultUrl = extentUrl
			handler.DealRequest(payloadItem)
		} else {
			payloadItem.ResultUrl = generalUrl
			handler.DealRequest(payloadItem)
		}
	}
}

func (handler *DirHandler) DefaultDirScanner() {

	go func() {
		result, isClosed := <-handler.ResultChan
		if !isClosed {
			return
		}

		handler.Crawler.addLink(common.GenLink(result.ResultUrl, http.MethodGet, "", "", result.Depth+1))

		if result.IsWeak {
			handler.Crawler.ScanDirResults.LoadOrStore(result.ResultUrl, result.Depth)
		}

	}()
	defer close(handler.ResultChan)
Processing:
	for {

		handler.Crawler.waitIfPaused()

		if handler.Crawler.shouldStop() {
			break Processing
		} else if item := handler.DirQueue.Pop(); item != nil {

			if handler.Crawler.beforeProcessing() {
				handler.Crawler.goroutines.Add(1)
				dirItem := item.(*DirValue)
				go handler.DirScanner(dirItem)
			}
		} else {
			time.Sleep(2 * time.Second)
		}
	}
	handler.DirQueue.Close()
}

func (handler *DirHandler) DealRequest(fileItem DirResult) {

	url := fileItem.ResultUrl
	agent := fileItem.Agent
	cookie := fileItem.Cookie
	errorPage := fileItem.ErrorPage
	isWeak := fileItem.IsWeak
	curDepth := fileItem.Depth

	if _, ok := handler.Crawler.ScanDirs.LoadOrStore(url, curDepth); ok {
		log.Info("the url is alread exists", url)
		return
	}

	domain, path, err := UrlParse(url)
	if err != nil {
		log.Error("urlparse err", err)
		return
	}
	if strings.Count(path, "/") > 4 {

		return
	}
	resp, body, err := DoRequest(url, nil, "GET", agent, cookie)
	if err != nil {
		log.Error("do request err", err)
		return
	}

	var singleResult *DirResult
	var is403 bool

	statusCode := resp.StatusCode

	switch {

	case statusCode == 200:

		if string(body) != string(errorPage) && !IsWafPage(body) {
			singleResult = &DirResult{
				ResultUrl: url,
				IsWeak:    isWeak,
				Depth:     curDepth,
			}

			log.Info("foundResult", singleResult)
			handler.ResultChan <- singleResult
		}

	case resp.StatusCode >= 300 && resp.StatusCode < 400:

		jumpUrl := resp.Header.Get("Location")
		jumpDomain, _, errParse := UrlParse(jumpUrl)

		if errParse == nil {
			if jumpDomain == domain {
				newFileItem := DirResult{
					ResultUrl: url,
					Agent:     agent,
					Cookie:    cookie,
					ErrorPage: errorPage,
					Depth:     curDepth,
				}
				handler.DealRequest(newFileItem)
			}
		}

	case resp.StatusCode == 403:
		is403 = true

		if string(body) != string(errorPage) && !IsWafPage(body) {
			singleResult = &DirResult{
				ResultUrl: url,
				IsWeak:    false,
				Depth:     curDepth,
			}

			log.Info(singleResult)
			handler.ResultChan <- singleResult

		}

	}

	if singleResult != nil {
		if IsDirectory(url) {

			if is403 {

				onePageResp, _, err := GetErrorPage(url, agent, cookie)
				if err != nil && onePageResp != nil {

					if onePageResp.StatusCode != 403 {
						item := newDirValue()
						item.ExtUrl = url
						item.ExtList = []string{"php", "html", "asp", "aspx", "jsp"}
						item.Agent = agent
						item.Cookie = cookie
						item.Depth = curDepth
						handler.DirQueue.Push(item)
					}
				}
			} else {
				item := newDirValue()
				item.ExtUrl = url
				item.ExtList = []string{"php", "html", "asp", "aspx", "jsp"}
				item.Agent = agent
				item.Cookie = cookie
				item.Depth = curDepth
				handler.DirQueue.Push(item)

			}
		}
	}

}
