package scripts

import (
	"crypto/tls"
	"fmt"
	"net"
	"regexp"
	"strconv"
	"strings"
	"time"
)

var eximPattern = regexp.MustCompile(`Exim (4\.88|4\.89)`)

func EximUaf(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var output string
	var err error
	var conn net.Conn
	for _, port := range []int{25, 26, 465, 587} {
		rawurl := args.Host + ":" + strconv.Itoa(port)
		conn, err = net.DialTimeout("tcp", rawurl, 3*time.Second)

		if err != nil {
			fmt.Println("conntect port err", port, err)
			continue
		}
		if port == 465 {
			conn = net.Conn(tls.Client(conn, &tls.Config{InsecureSkipVerify: true}))
		}
		defer conn.Close()

		response := make([]byte, 1024)
		conn.SetReadDeadline(time.Now().Add(time.Second * 5))
		_, err = conn.Read(response)

		if err != nil {
			fmt.Println("conntect port first read err", port, err)
			continue
		}

		strResponse := string(response)
		if strings.Contains(strResponse, "Exim") && (strings.Contains(strResponse, "4.88") ||
			strings.Contains(strResponse, "4.89")) && !strings.Contains(strResponse, "4.89.1") &&
			!strings.Contains(strResponse, "4.89_1") {
			groups := eximPattern.Find(response)

			if len(groups) > 0 {
				conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
				conn.Write([]byte("EHLO test\n"))
				time.Sleep(100 * time.Millisecond)
				buffer := make([]byte, 1024)
				conn.SetReadDeadline(time.Now().Add(time.Second * 5))
				reqLen, err := conn.Read(buffer)

				if err != nil {
					continue
				}

				if reqLen > 0 {
					output += fmt.Sprintf("|%v:%v", port, string(groups))
				}

			}
		}

	}
	if output != "" {
		return &ScriptScanResult{Vulnerable: true, Output: args.Host + output}, nil
	}
	if err != nil {
		return nil, err
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("Exim_UAF.xml", EximUaf)
}
