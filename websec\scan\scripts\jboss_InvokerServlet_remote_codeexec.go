package scripts

import (
	"regexp"
	"time"
)

var jbossPattern1 = regexp.MustCompile(`org.jboss.invocation.InvocationException`)
var jbossPattern2 = regexp.MustCompile(`\$org.jboss.invocation.MarshalledValue`)

func JBossInvokerServletRemoteCodeExec(args *ScriptScanArgs) (*ScriptScanResult, error) {
	for _, uri := range []string{
		"/invoker/EJBInvokerServlet/",
		"/invoker/JMXInvokerServlet/",
	} {
		targetURL := constructURL(args, uri)
		status, body, err := httpGetTimeout(targetURL, time.Second*5)
		if err != nil {
			return nil, err
		}
		if status == 200 {
			if jbossPattern1.Match(body) && jbossPattern2.Match(body) {
				return &ScriptScanResult{Vulnerable: true, Output: targetURL, Body: body}, nil
			}
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("jboss_InvokerServlet_remote_codeexec.xml", JBossInvokerServletRemoteCodeExec)
}
