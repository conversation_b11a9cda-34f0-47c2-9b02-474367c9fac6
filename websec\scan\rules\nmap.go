package rules

import (
	"encoding/xml"
	"io/ioutil"
	"os"
	"websec/utils/log"
)

type NmapResult struct {
	Scanner          string       `xml:"scanner,attr"`
	Args             string       `xml:"args,attr"`
	Start            int          `xml:"start,attr"`
	StartStr         string       `xml:"startstr,attr"`
	Version          string       `xml:"version,attr"`
	XMLOutputVersion string       `xml:"xmloutputversion,attr"`
	ScanInfo         nmapScanInfo `xml:"scaninfo"`
	VerboseLevel     int          `xml:"verbose>level"`
	DebuggingLevel   int          `xml:"debugging>level"`
	Host             nmapHost     `xml:"host"`
	RunStats         nmapRunStats `xml:"runstats"`
	Content          []byte
}

type nmapScanInfo struct {
	Type        string `xml:"type,attr"`
	Protocol    string `xml:"protocol,attr"`
	NumServices int    `xml:"numservices,attr"`
	Services    string `xml:"services,attr"`
}

type nmapHost struct {
	StartTime     int               `xml:"starttime,attr"`
	EndTime       int               `xml:"endtime,attr"`
	Status        nmapStatus        `xml:"status"`
	Address       nmapAddress       `xml:"address"`
	HostNames     []nmapHostname    `xml:"hostnames>hostname"`
	Ports         []nmapPort        `xml:"ports>port"`
	ExtraPorts    []nmapExtraPorts  `xml:"ports>extraports"`
	OS            nmapHostOS        `xml:"os"`
	UpTime        nmapUpTime        `xml:"uptime"`
	Distance      int               `xml:"distance>value"`
	TCPSequence   nmapTCPSequence   `xml:"tcpsequence"`
	IPIDSequence  nmapIPIDSequence  `xml:"ipidsequence"`
	TCPTSSequence nmapTCPTSSequence `xml:"tcptssequence"`
	Trace         nmapTrace         `xml:"trace"`
	Times         nmapTimes         `xml:"times"`
}

type nmapStatus struct {
	State  string `xml:"state"`
	Reason string `xml:"reason"`
}

type nmapAddress struct {
	Addr     string `xml:"addr,attr"`
	AddrType string `xml:"addrtype,attr"`
}

type nmapHostname struct {
	Name string `xml:"name,attr"`
	Type string `xml:"type,attr"`
}

type nmapPort struct {
	Protocol string           `xml:"protocol,attr"`
	PortID   int              `xml:"portid,attr"`
	State    nmapPortState    `xml:"state"`
	Service  nmapPortService  `xml:"service"`
	Scripts  []nmapPortScript `xml:"script"`
}

type nmapExtraPorts struct {
	State   string            `xml:"state,attr"`
	Count   int               `xml:"count,attr"`
	Reasons []nmapExtraReason `xml:"extrareasons"`
}

type nmapExtraReason struct {
	Reason string `xml:"reason,attr"`
	Count  int    `xml:"count,attr"`
}

type nmapPortState struct {
	State     string `xml:"state,attr"`
	Reason    string `xml:"reason,attr"`
	ReasonTTL int    `xml:"reason_ttl,attr"`
}

type nmapPortService struct {
	Name      string   `xml:"name,attr"`
	Product   string   `xml:"product,attr"`
	Version   string   `xml:"version,attr"`
	ExtraInfo string   `xml:"extrainfo,attr"`
	OSType    string   `xml:"ostype,attr"`
	Method    string   `xml:"method,attr"`
	Conf      string   `xml:"conf,attr"`
	CPEs      []string `xml:"cpe"`
}

type nmapPortScript struct {
	ID     string            `xml:"id,attr"`
	Output string            `xml:"output,attr"`
	Tables []nmapScriptTable `xml:"table"`
}

func (script *nmapPortScript) State() string {
	var state string
	for i := range script.Tables {
		state = script.Tables[i].State()
		if state != "" {
			return state
		}
	}
	return ""
}

type nmapScriptTable struct {
	Key      string            `xml:"key,attr"`
	Elements []nmapScriptElem  `xml:"elem"`
	Tables   []nmapScriptTable `xml:"table"`
}

func (table *nmapScriptTable) State() string {
	for i := range table.Elements {
		if table.Elements[i].Key == "state" {
			return table.Elements[i].Value
		}
	}
	var state string
	for i := range table.Tables {
		state = table.Tables[i].State()
		if state != "" {
			return state
		}
	}
	return ""
}

type nmapScriptElem struct {
	Key   string `xml:"key,attr"`
	Value string `xml:",chardata"`
}

type nmapHostOS struct {
	UsedPorts nmapUsedPort `xml:"portused"`
	OSClass   nmapOSClass  `xml:"osclass"`
	OSMatch   nmapOSMatch  `xml:"osmatch"`
}

type nmapUsedPort struct {
	State  string `xml:"state,attr"`
	Proto  string `xml:"proto,attr"`
	PortID int    `xml:"portid,attr"`
}

type nmapOSClass struct {
	Type       string   `xml:"type,attr"`
	Vendor     string   `xml:"vendor,attr"`
	Family     string   `xml:"osfamily,attr"`
	Generation string   `xml:"osgen,attr"`
	Accuracy   int      `xml:"accuracy,attr"`
	CPEs       []string `xml:"cpe"`
}

type nmapOSMatch struct {
	Name     string `xml:"name,attr"`
	Accuracy int    `xml:"accuracy,attr"`
	Line     int    `xml:"line,attr"`
}

type nmapUpTime struct {
	Seconds  int    `xml:"seconds,attr"`
	LastBoot string `xml:"lastboot,attr"`
}

type nmapTCPSequence struct {
	Index      int    `xml:"index,attr"`
	Difficulty string `xml:"difficulty,attr"`
	Values     string `xml:"values,attr"`
}

type nmapIPIDSequence struct {
	Class  string `xml:"class,attr"`
	Values string `xml:"values,attr"`
}

type nmapTCPTSSequence struct {
	Class  string `xml:"class,attr"`
	Values string `xml:"values,attr"`
}

type nmapTrace struct {
	Port  int       `xml:"port,attr"`
	Proto string    `xml:"proto,attr"`
	Hops  []nmapHop `xml:"hop"`
}

type nmapHop struct {
	TTL    int    `xml:"ttl,attr"`
	IPAddr string `xml:"ipaddr,attr"`
	RTT    string `xml:"rtt,attr"`
	Host   string `xml:"host,attr"`
}

type nmapTimes struct {
	Srtt   int `xml:"srtt,attr"`
	RttVar int `xml:"rttvar,attr"`
	To     int `xml:"to,attr"`
}

type nmapRunStats struct {
	Finished nmapRunStatsFinished `xml:"finished"`
	Hosts    nmapRunStatsHosts    `xml:"hosts"`
}

type nmapRunStatsFinished struct {
	Time    int     `xml:"time,attr"`
	TimeStr string  `xml:"timestr,attr"`
	Elapsed float64 `xml:"elapsed,attr"`
	Summary string  `xml:"summary"`
	Exit    string  `xml:"exit"`
}

type nmapRunStatsHosts struct {
	Up    int `xml:"up,attr"`
	Down  int `xml:"down,attr"`
	Total int `xml:"total,attr"`
}

func ParseNmapResult(filename string) (*NmapResult, error) {
	f, err := os.Open(filename)
	if err != nil {
		log.Fatalln("failed to open file:", filename, err)
		return nil, err
	}
	defer f.Close()
	buf, err := ioutil.ReadAll(f)
	if err != nil {
		log.Fatalln("failed to read file:", filename, err)
		return nil, err
	}
	result := NmapResult{}
	err = xml.Unmarshal(buf, &result)
	if err != nil {
		return nil, err
	}
	result.Content = buf
	return &result, nil
}
