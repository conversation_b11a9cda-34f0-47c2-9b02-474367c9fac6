package server

import (
	"bufio"
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"io"
	"net"
	"os"
	"os/signal"
	"reflect"
	"regexp"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"
	"websec/rpc/log"
	"websec/rpc/protocol"
	"websec/rpc/serverplugin"
	"websec/rpc/share"
)

var (
	ErrServerClosed     = errors.New("Server closed")
	ErrUnsupportedCodec = errors.New("unsupported codec")
	ErrClientNotFind    = errors.New("Client not find")
)

const (
	ReaderBuffsize = 1024

	WriterBuffsize = 1024
)

type ClientError string

func (e ClientError) Error() string {
	return string(e)
}

type contextKey struct {
	name string
}

func (k *contextKey) String() string { return "context value " + k.name }

var (
	RemoteConnContextKey = &contextKey{"remote-conn"}

	StartRequestContextKey = &contextKey{"start-parse-request"}

	StartSendRequestContextKey = &contextKey{"start-send-request"}
)

type Server struct {
	ln           net.Listener
	readTimeout  time.Duration
	writeTimeout time.Duration

	serviceMapMu sync.RWMutex
	serviceMap   map[string]*service

	mu         sync.RWMutex
	activeConn map[net.Conn]struct{} //to do,可以是名字或者
	doneChan   chan struct{}
	seq        uint64

	inShutdown int32
	onShutdown []func(s *Server)

	options map[string]interface{}

	tlsConfig *tls.Config

	SerializeType protocol.SerializeType
	CompressType  protocol.CompressType

	Plugins PluginContainer

	handlerMsgNum int32

	pending map[uint64]*share.Call
	pmutex  sync.Mutex
}

func NewServer(options ...OptionFn) *Server {
	s := &Server{
		Plugins: &pluginContainer{},
		options: make(map[string]interface{}),
	}

	for _, op := range options {
		op(s)
	}

	s.SerializeType = protocol.MsgPack
	s.CompressType = protocol.None
	s.Plugins.Add(serverplugin.GetClientManagerPlugin())

	return s
}

func (s *Server) Address() net.Addr {
	s.mu.RLock()
	defer s.mu.RUnlock()
	if s.ln == nil {
		return nil
	}
	return s.ln.Addr()
}

func (s *Server) ActiveClientConn() []net.Conn {
	var result []net.Conn

	s.mu.RLock()
	for clientConn, _ := range s.activeConn {
		result = append(result, clientConn)
	}
	s.mu.RUnlock()
	return result
}

func (s *Server) RandomActiveConn() net.Conn {
	s.mu.RLock()
	defer s.mu.RUnlock()
	for k, _ := range s.activeConn {
		return k
	}
	return nil
}

func (s *Server) SendMessage(conn net.Conn, servicePath, serviceMethod string, args interface{}, metadata map[string]string) error {
	codec := share.Codecs[s.SerializeType]
	if codec == nil {
		return fmt.Errorf("can not find codec for %d", s.SerializeType)
	}
	data, err := codec.Encode(args)
	if err != nil {
		return ErrUnsupportedCodec
	}

	ctx := context.WithValue(context.Background(), StartSendRequestContextKey, time.Now().UnixNano())
	s.Plugins.DoPreWriteRequest(ctx)

	req := protocol.GetPooledMsg()
	req.SetMessageType(protocol.Request)

	seq := atomic.AddUint64(&s.seq, 1)
	req.SetSeq(seq)
	req.SetOneway(true)
	req.SetSerializeType(s.SerializeType)
	req.ServicePath = servicePath
	req.ServiceMethod = serviceMethod
	req.Metadata = metadata
	req.Payload = data

	if len(data) > 1024 && s.CompressType != protocol.None {
		req.SetCompressType(s.CompressType)
	}

	reqData := req.Encode()
	_, err = conn.Write(reqData)
	if err != nil {
		log.Error("write error", err)
	}
	s.Plugins.DoPostWriteRequest(ctx, req, err)
	protocol.FreeMsg(req)
	return err
}

func (s *Server) CallByHost(ctx context.Context, host string, servicePath, serviceMethod string, args interface{}, reply interface{}) error {
	conn := serverplugin.GetClientManagerPlugin().GetClientConn(host)
	if conn == nil {
		log.Error("client not find by host ", host, servicePath, serviceMethod)
		return ErrClientNotFind
	}

	return s.Call(ctx, conn, servicePath, serviceMethod, args, reply)
}

func (s *Server) Call(ctx context.Context, conn net.Conn, servicePath, serviceMethod string, args interface{}, reply interface{}) error {
	seq := new(uint64)
	ctx = context.WithValue(ctx, share.SeqKey{}, seq)
	Done := s.Go(ctx, conn, servicePath, serviceMethod, args, reply, make(chan *share.Call, 1)).Done

	var err error
	select {
	case <-ctx.Done(): //cancel by context
		s.pmutex.Lock()
		call := s.pending[*seq]
		delete(s.pending, *seq)
		s.pmutex.Unlock()
		if call != nil {
			call.Error = ctx.Err()
			call.CallDone()
		}

		return ctx.Err()
	case call := <-Done:
		err = call.Error
		meta := ctx.Value(share.ResMetaDataKey)
		if meta != nil && len(call.ResMetadata) > 0 {
			resMeta := meta.(map[string]string)
			for k, v := range call.ResMetadata {
				resMeta[k] = v
			}
		}
	}

	return err
}

func (s *Server) Go(ctx context.Context, conn net.Conn, servicePath, serviceMethod string, args interface{}, reply interface{}, done chan *share.Call) *share.Call {
	call := new(share.Call)
	call.ServicePath = servicePath
	call.ServiceMethod = serviceMethod
	meta := ctx.Value(share.ReqMetaDataKey)
	if meta != nil { //copy meta in context to meta in requests
		call.Metadata = meta.(map[string]string)
	}
	call.Args = args
	call.Reply = reply
	if done == nil {
		done = make(chan *share.Call, 10) // buffered.
	} else {

		if cap(done) == 0 {
			log.Panic("rpc: done channel is unbuffered")
		}
	}
	call.Done = done
	s.send(ctx, conn, call)
	return call
}

func (s *Server) send(ctx context.Context, conn net.Conn, call *share.Call) {

	s.pmutex.Lock()
	if isShutdown(s) {
		call.Error = ErrServerClosed
		s.pmutex.Unlock()
		call.CallDone()
		return
	}

	codec := share.Codecs[s.SerializeType]
	if codec == nil {
		call.Error = ErrUnsupportedCodec
		s.pmutex.Unlock()
		call.CallDone()
		return
	}

	if s.pending == nil {
		s.pending = make(map[uint64]*share.Call)
	}

	ctx = context.WithValue(ctx, StartSendRequestContextKey, time.Now().UnixNano())
	s.Plugins.DoPreWriteRequest(ctx)

	seq := atomic.AddUint64(&s.seq, 1)
	s.pending[seq] = call
	s.pmutex.Unlock()

	if cseq, ok := ctx.Value(share.SeqKey{}).(*uint64); ok {
		*cseq = seq
	}

	req := protocol.GetPooledMsg()
	defer protocol.FreeMsg(req)
	req.SetMessageType(protocol.Request)
	req.SetSeq(seq)
	if call.Reply == nil {
		req.SetOneway(true)
	}

	if call.ServicePath == "" && call.ServiceMethod == "" {
		req.SetHeartbeat(true)
	} else {
		req.SetSerializeType(s.SerializeType)
		if call.Metadata != nil {
			req.Metadata = call.Metadata
		}

		req.ServicePath = call.ServicePath
		req.ServiceMethod = call.ServiceMethod

		data, err := codec.Encode(call.Args)
		if err != nil {
			call.Error = err
			call.CallDone()
			return
		}
		if len(data) > 1024 && s.CompressType != protocol.None {
			req.SetCompressType(s.CompressType)
		}

		req.Payload = data
	}

	data := req.Encode()

	_, err := conn.Write(data)
	s.Plugins.DoPostWriteRequest(ctx, req, err)
	if err != nil {
		s.pmutex.Lock()
		call = s.pending[seq]
		delete(s.pending, seq)
		s.pmutex.Unlock()
		if call != nil {
			call.Error = err
			call.CallDone()
		}
		return
	}

	isOneway := req.IsOneway()

	if isOneway {
		s.pmutex.Lock()
		call = s.pending[seq]
		delete(s.pending, seq)
		s.pmutex.Unlock()
		if call != nil {
			call.CallDone()
		}
	}

	if s.writeTimeout != 0 {
		conn.SetWriteDeadline(time.Now().Add(s.writeTimeout))
	}
}

func (s *Server) getDoneChan() <-chan struct{} {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.doneChan == nil {
		s.doneChan = make(chan struct{})
	}
	return s.doneChan
}

func (s *Server) startShutdownListener() {
	go func(s *Server) {
		log.Info("server pid:", os.Getpid())
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGHUP)
		si := <-c
		log.Info("receive sig", si)
		switch si {
		case syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT:
			if nil != s.onShutdown && len(s.onShutdown) > 0 {
				for _, sd := range s.onShutdown {
					sd(s)
				}
			}
			ctx, _ := context.WithTimeout(context.Background(), 155*time.Second)
			s.Shutdown(ctx)
		default:
			log.Warn("invalid sig", si)
		}
	}(s)
}

func (s *Server) Serve(network, address string) (err error) {
	s.startShutdownListener()
	var ln net.Listener
	ln, err = s.makeListener(network, address)
	if err != nil {
		log.Error("serve error", err)
		return
	}
	return s.serveListener(ln)
}

func (s *Server) serveListener(ln net.Listener) error {
	if s.Plugins == nil {
		s.Plugins = &pluginContainer{}
	}

	var tempDelay time.Duration

	s.mu.Lock()
	s.ln = ln
	if s.activeConn == nil {
		s.activeConn = make(map[net.Conn]struct{})
	}
	s.mu.Unlock()

	for {
		conn, e := ln.Accept()
		if e != nil {
			select {
			case <-s.getDoneChan():
				return ErrServerClosed
			default:
			}

			if ne, ok := e.(net.Error); ok && ne.Temporary() {
				if tempDelay == 0 {
					tempDelay = 5 * time.Millisecond
				} else {
					tempDelay *= 2
				}

				if max := 1 * time.Second; tempDelay > max {
					tempDelay = max
				}

				log.Errorf("Accept error: %v; retrying in %v", e, tempDelay)
				time.Sleep(tempDelay)
				continue
			}
			return e
		}
		tempDelay = 0

		if tc, ok := conn.(*net.TCPConn); ok {
			tc.SetKeepAlive(true)
			tc.SetKeepAlivePeriod(3 * time.Minute)
			tc.SetLinger(10)
		}

		s.mu.Lock()
		s.activeConn[conn] = struct{}{}
		s.mu.Unlock()

		conn, ok := s.Plugins.DoPostConnAccept(conn)
		if !ok {
			continue
		}

		go s.serveConn(conn)
	}
}

func (s *Server) serveConn(conn net.Conn) {
	defer func() {
		if err := recover(); err != nil {
			const size = 64 << 10
			buf := make([]byte, size)
			ss := runtime.Stack(buf, false)
			if ss > size {
				ss = size
			}
			buf = buf[:ss]
			log.Errorf("serving %s panic error: %s, stack:\n %s", conn.RemoteAddr(), err, buf)
		}
		s.mu.Lock()
		delete(s.activeConn, conn)
		s.mu.Unlock()
		conn.Close()

		if s.Plugins == nil {
			s.Plugins = &pluginContainer{}
		}

		s.Plugins.DoPostConnClose(conn)
	}()

	if isShutdown(s) {
		closeChannel(s, conn)
		return
	}

	if tlsConn, ok := conn.(*tls.Conn); ok {
		if d := s.readTimeout; d != 0 {
			conn.SetReadDeadline(time.Now().Add(d))
		}
		if d := s.writeTimeout; d != 0 {
			conn.SetWriteDeadline(time.Now().Add(d))
		}
		if err := tlsConn.Handshake(); err != nil {
			log.Errorf("rpc: TLS handshake error from %s: %v", conn.RemoteAddr(), err)
			return
		}
	}

	log.Info("receive client connect, ip ", conn.RemoteAddr())
	r := bufio.NewReaderSize(conn, ReaderBuffsize)

	for {
		if isShutdown(s) {
			closeChannel(s, conn)
			return
		}

		t0 := time.Now()
		if s.readTimeout != 0 {
			conn.SetReadDeadline(t0.Add(s.readTimeout))
		}

		ctx := context.WithValue(context.Background(), RemoteConnContextKey, conn)
		req, err := s.readRequest(ctx, r)
		if err != nil {
			if err == io.EOF {
				log.Infof("client has closed this connection: %s", conn.RemoteAddr().String())
			} else if strings.Contains(err.Error(), "use of closed network connection") {
				log.Infof("connection %s is closed", conn.RemoteAddr().String())
			} else {
				log.Warnf("failed to read request: %v", err)
			}
			return
		}

		if s.writeTimeout != 0 {
			conn.SetWriteDeadline(t0.Add(s.writeTimeout))
		}

		ctx = context.WithValue(ctx, StartRequestContextKey, time.Now().UnixNano())

		if req.MessageType() == protocol.Response {
			s.handleClientResponse(req)
		} else {
			go s.handleClientRequest(ctx, conn, req)
		}

	}
}

func (s *Server) handleClientResponse(res *protocol.Message) {
	seq := res.Seq()
	var call *share.Call
	s.pmutex.Lock()
	call = s.pending[seq]
	delete(s.pending, seq)
	s.pmutex.Unlock()

	switch {
	case res.MessageStatusType() == protocol.Error:

		if len(res.Metadata) > 0 {
			meta := make(map[string]string, len(res.Metadata))
			for k, v := range res.Metadata {
				meta[k] = v
			}
			call.ResMetadata = meta
			call.Error = ClientError(meta[protocol.ServiceError])
		}

		if call.Raw {
			call.Metadata, call.Reply, _ = protocol.ConvertRes2Raw(res)
			call.Metadata[protocol.XErrorMessage] = call.Error.Error()
		}
		call.CallDone()
	default:
		if call.Raw {
			call.Metadata, call.Reply, _ = protocol.ConvertRes2Raw(res)
		} else {
			data := res.Payload
			if len(data) > 0 {
				codec := share.Codecs[res.SerializeType()]
				if codec == nil {
					call.Error = ClientError(ErrUnsupportedCodec.Error())
				} else {
					err := codec.Decode(data, call.Reply)
					if err != nil {
						call.Error = ClientError(err.Error())
					}
				}
			}
			if len(res.Metadata) > 0 {
				meta := make(map[string]string, len(res.Metadata))
				for k, v := range res.Metadata {
					meta[k] = v
				}
				call.ResMetadata = res.Metadata
			}
		}

		call.CallDone()
	}
	protocol.FreeMsg(res)
}

func (s *Server) handleClientRequest(ctx context.Context, conn net.Conn, req *protocol.Message) {
	atomic.AddInt32(&s.handlerMsgNum, 1)
	defer func() {
		atomic.AddInt32(&s.handlerMsgNum, -1)
	}()
	if req.IsHeartbeat() {
		req.SetMessageType(protocol.Response)
		data := req.Encode()
		_, err := conn.Write(data)
		if err != nil {
			log.Error("write error", err)
		}
		return
	}

	log.Debugf("req %s.%s, msgnum %d", req.ServicePath, req.ServiceMethod, atomic.LoadInt32(&s.handlerMsgNum))
	resMetadata := make(map[string]string)
	newCtx := context.WithValue(context.WithValue(ctx, share.ReqMetaDataKey, req.Metadata),
		share.ResMetaDataKey, resMetadata)

	res, err := s.handleRequest(newCtx, req)

	if err != nil {
		log.Warnf("rpc: failed to handle request: %v", err)
	}

	s.Plugins.DoPreWriteResponse(newCtx, req, res)
	if !req.IsOneway() {
		if len(resMetadata) > 0 { //copy meta in context to request
			meta := res.Metadata
			if meta == nil {
				res.Metadata = resMetadata
			} else {
				for k, v := range resMetadata {
					meta[k] = v
				}
			}
		}

		if len(res.Payload) > 1024 && req.CompressType() != protocol.None {
			res.SetCompressType(req.CompressType())
		}
		data := res.Encode()
		_, err = conn.Write(data)
		if err != nil {
			log.Error("write error", err)
		}

	}
	s.Plugins.DoPostWriteResponse(newCtx, req, res, err)

	protocol.FreeMsg(req)
	protocol.FreeMsg(res)
}

func isShutdown(s *Server) bool {
	return atomic.LoadInt32(&s.inShutdown) == 1
}

func closeChannel(s *Server, conn net.Conn) {
	s.mu.Lock()
	delete(s.activeConn, conn)
	s.mu.Unlock()
	conn.Close()
}

func (s *Server) readRequest(ctx context.Context, r io.Reader) (req *protocol.Message, err error) {
	err = s.Plugins.DoPreReadRequest(ctx)
	if err != nil {
		return nil, err
	}

	req = protocol.GetPooledMsg()
	err = req.Decode(r)
	perr := s.Plugins.DoPostReadRequest(ctx, req, err)
	if err == nil {
		err = perr
	}
	return req, err
}

func (s *Server) handleRequest(ctx context.Context, req *protocol.Message) (res *protocol.Message, err error) {
	serviceName := req.ServicePath
	methodName := req.ServiceMethod

	res = req.Clone()

	res.SetMessageType(protocol.Response)
	s.serviceMapMu.RLock()
	service := s.serviceMap[serviceName]
	s.serviceMapMu.RUnlock()
	if service == nil {
		err = errors.New("can't find service " + serviceName)
		return handleError(res, err)
	}
	mtype := service.method[methodName]
	if mtype == nil {
		if service.function[methodName] != nil { //check raw functions
			return s.handleRequestForFunction(ctx, req)
		}
		err = errors.New("rpc: can't find method " + methodName)
		return handleError(res, err)
	}

	var argv = argsReplyPools.Get(mtype.ArgType)

	codec := share.Codecs[req.SerializeType()]
	if codec == nil {
		err = fmt.Errorf("can not find codec for %d", req.SerializeType())
		return handleError(res, err)
	}

	err = codec.Decode(req.Payload, argv)
	if err != nil {
		return handleError(res, err)
	}

	replyv := argsReplyPools.Get(mtype.ReplyType)

	if mtype.ArgType.Kind() != reflect.Ptr {
		err = service.call(ctx, mtype, reflect.ValueOf(argv).Elem(), reflect.ValueOf(replyv))
	} else {
		err = service.call(ctx, mtype, reflect.ValueOf(argv), reflect.ValueOf(replyv))
	}

	argsReplyPools.Put(mtype.ArgType, argv)
	if err != nil {
		argsReplyPools.Put(mtype.ReplyType, replyv)
		return handleError(res, err)
	}

	if !req.IsOneway() {
		data, err := codec.Encode(replyv)
		argsReplyPools.Put(mtype.ReplyType, replyv)
		if err != nil {
			return handleError(res, err)

		}
		res.Payload = data
	}

	return res, nil
}

func (s *Server) handleRequestForFunction(ctx context.Context, req *protocol.Message) (res *protocol.Message, err error) {
	res = req.Clone()

	res.SetMessageType(protocol.Response)

	serviceName := req.ServicePath
	methodName := req.ServiceMethod
	s.serviceMapMu.RLock()
	service := s.serviceMap[serviceName]
	s.serviceMapMu.RUnlock()
	if service == nil {
		err = errors.New("rpc: can't find service  for func raw function")
		return handleError(res, err)
	}
	mtype := service.function[methodName]
	if mtype == nil {
		err = errors.New("rpc: can't find method " + methodName)
		return handleError(res, err)
	}

	var argv = argsReplyPools.Get(mtype.ArgType)

	codec := share.Codecs[req.SerializeType()]
	if codec == nil {
		err = fmt.Errorf("can not find codec for %d", req.SerializeType())
		return handleError(res, err)
	}

	err = codec.Decode(req.Payload, argv)
	if err != nil {
		return handleError(res, err)
	}

	replyv := argsReplyPools.Get(mtype.ReplyType)

	err = service.callForFunction(ctx, mtype, reflect.ValueOf(argv), reflect.ValueOf(replyv))

	argsReplyPools.Put(mtype.ArgType, argv)

	if err != nil {
		argsReplyPools.Put(mtype.ReplyType, replyv)
		return handleError(res, err)
	}

	if !req.IsOneway() {
		data, err := codec.Encode(replyv)
		argsReplyPools.Put(mtype.ReplyType, replyv)
		if err != nil {
			return handleError(res, err)

		}
		res.Payload = data
	}

	return res, nil
}

func handleError(res *protocol.Message, err error) (*protocol.Message, error) {
	res.SetMessageStatusType(protocol.Error)
	if res.Metadata == nil {
		res.Metadata = make(map[string]string)
	}
	res.Metadata[protocol.ServiceError] = err.Error()
	return res, err
}

func (s *Server) Close() error {
	log.Info("server close")
	s.mu.Lock()
	defer s.mu.Unlock()
	s.closeDoneChanLocked()
	var err error
	if s.ln != nil {
		err = s.ln.Close()
	}

	for c := range s.activeConn {
		c.Close()
		delete(s.activeConn, c)
		s.Plugins.DoPostConnClose(c)
	}
	return err
}

func (s *Server) RegisterOnShutdown(f func(s *Server)) {
	s.mu.Lock()
	s.onShutdown = append(s.onShutdown, f)
	s.mu.Unlock()
}

var shutdownPollInterval = 1000 * time.Millisecond

func (s *Server) Shutdown(ctx context.Context) error {
	if atomic.CompareAndSwapInt32(&s.inShutdown, 0, 1) {
		log.Info("shutdown begin")
		ticker := time.NewTicker(shutdownPollInterval)
		defer ticker.Stop()
		for {
			if s.checkProcessMsg() {
				break
			}
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-ticker.C:
			}
		}
		s.Close()
		log.Info("shutdown end")
	}
	return nil
}

func (s *Server) checkProcessMsg() bool {
	size := s.handlerMsgNum
	log.Info("need handle msg size:", size)
	if size == 0 {
		return true
	}
	return false
}

func (s *Server) closeDoneChanLocked() {
	ch := s.getDoneChanLocked()
	select {
	case <-ch:

	default:

		close(ch)
	}
}
func (s *Server) getDoneChanLocked() chan struct{} {
	if s.doneChan == nil {
		s.doneChan = make(chan struct{})
	}
	return s.doneChan
}

var ip4Reg = regexp.MustCompile(`^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$`)

func validIP4(ipAddress string) bool {
	ipAddress = strings.Trim(ipAddress, " ")
	i := strings.LastIndex(ipAddress, ":")
	ipAddress = ipAddress[:i] //remove port

	return ip4Reg.MatchString(ipAddress)
}
