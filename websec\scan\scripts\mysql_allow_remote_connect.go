package scripts

import (
	"database/sql"
	"fmt"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func MySQLAllowRemoteConnect(args *ScriptScanArgs) (*ScriptScanResult, error) {
	passwd := "qianwanbiezhong!!!ohehe"
	db, err := sql.Open("mysql", fmt.Sprintf("root:%v@tcp(%v:3306)", passwd, args.Host))
	if err != nil {
		return nil, err
	}
	defer db.Close()
	err = db.<PERSON>()
	if err != nil {
		if strings.Index(err.Error(), "denied for") > 0 {
			return &ScriptScanResult{Vulnerable: true, Output: "Mysql Allow Remote Connect"}, nil
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("mysql_allow_remote_connect_vul.xml", MySQLAllowRemoteConnect)
}
