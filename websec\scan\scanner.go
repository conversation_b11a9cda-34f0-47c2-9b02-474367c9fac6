package scan

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/textproto"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"websec/common/consts"
	"websec/common/logger"
	"websec/scan/rules"
	"websec/scan/xsscheck"
	"websec/utils"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/tuchuang"

	"github.com/sirupsen/logrus"

	"github.com/valyala/fasthttp"
)

const webscanVerifyURL = "http://scan.websec.cn/"

var (
	paramForXSSCheck      = &rules.Param{Value: "f7f7de57eba21d0ef8f0686fc8ab0023"}
	paramForFileXSSCheck1 = &rules.Param{Value: "?f7f7de57eba21d0ef8f0686fc8ab0023"}
	paramForFileXSSCheck2 = &rules.Param{Value: "/f7f7de57eba21d0ef8f0686fc8ab0023"}
	ruleTestForXSSCheck   = &rules.RuleTest{}
)

type WSScanner struct {
	Options            *Options
	affectModules      map[string][]*rules.Module
	scanLinksChannel   chan *ScanLink
	affectLinksChannel chan *AffectLink
	scannedLinksFilter map[string]struct{}
	goroutines         sync.WaitGroup
	expiredAt          time.Time
	reason             string
	sema
	stats
	processorFlags
	httpClient        *fasthttp.Client
	cookiejar         *myCookieJar
	scanResultChannel chan *ScanResult
	textVulsFilter    *vulsFilter
	connectionClose   bool
	affectOptions     int
	specificVulXMLs   map[string]bool

	bucketSelect  *tuchuang.BucketSelect
	currentLink   *ScanLink
	processStatus int64
}

type sema struct {
	linkSema      *semaphore.Weighted // 链接处理并发控制
	linkCtx       context.Context
	linkCtxCancel context.CancelFunc
	requestSema   *semaphore.Weighted // 请求并发控制
	requestCtx    context.Context
}

type stats struct {
	errorCount        int64
	requestCount      int64
	recentFailedCount int64
	processingCount   int64
	foundVulsCount    int64
}

type processorFlags struct {
	isCanceled      bool
	isCanceldActive bool
	isAborting      bool
	isPaused        bool
}

func (scanner *WSScanner) Go() (chan *ScanResult, error) {
	go scanner.run()

	return scanner.scanResultChannel, nil
}

func (scanner *WSScanner) Result() chan *ScanResult {
	return scanner.scanResultChannel
}

var affectToOptionMapping = map[string]int{
	AffectContent:   AffectOptionContent,
	AffectDirectory: AffectOptionDirectory,
	AffectFile:      AffectOptionFile,
	AffectParameter: AffectOptionParameter,
	AffectSQLInject: AffectOptionSQLInject,
	AffectServer:    AffectOptionServer,
	AffectScript:    AffectOptionScript,
	AffectNmap:      AffectOptionNmap,
	AffectFuzzy:     AffectOptionFuzzy,
	AffectXray:      AffectOptionXray,
}

func (scanner *WSScanner) initAffectOptionsAndXMLs() {

	var option int
	if len(scanner.Options.SpecificAffects) > 0 {
		for _, affect := range scanner.Options.SpecificAffects {
			if opt, ok := affectToOptionMapping[affect]; ok {
				option |= opt
			}
		}
	} else {
		option = AffectOptionAll
	}
	scanner.affectOptions = option

	if len(scanner.Options.SpecificXMLs) > 0 {
		xmls := map[string]bool{}
		for _, xml := range scanner.Options.SpecificXMLs {
			xmls[xml] = true
		}
		scanner.specificVulXMLs = xmls
	}
}

var ingoredExts = map[string]bool{
	".rar": true, ".zip": true, ".gz": true, ".tar": true, ".tgz": true, ".cab": true, "7z": true, ".bz": true, ".bz2": true,
	".pdf": true, ".ps": true, ".doc": true, ".docx": true, ".ppt": true, ".pptx": true, ".xls": true, ".xlsx": true, ".rtf": true,
	".dot": true, ".mpp": true, ".mpt": true, ".mpd": true, ".mdb": true, ".csv": true, ".pps": true, ".ppa": true, ".dif": true,
	".rmvb": true, ".avi": true, ".mpg": true, ".mpeg": true, ".mov": true, ".movie": true, ".rm": true, ".asf": true, ".mpe": true,
	".asx": true, ".m1v": true, ".mpa": true, ".wmv": true, ".wav": true, ".mp3": true, ".ra": true, ".au": true, ".aiff": true,
	".mpga": true, ".wma": true, ".mid": true, ".midi": true, ".rmi": true, ".m3u": true, ".gif": true, ".bmp": true, ".jpg": true,
	".jpeg": true, ".png": true, ".tif": true, ".wmf": true, ".tiff": true, ".ico": true, ".icon": true, ".pcx": true, ".exe": true,
	".dll": true, ".apk": true, ".jar": true, ".sisx": true, ".js": true, ".css": true, ".swf": true,
	"mp4": true,
}

func shouldIgnore(path string) bool {
	parts := strings.Split(path, ".")
	if len(parts) > 1 {
		ext := parts[len(parts)-1]
		if _, ok := ingoredExts[ext]; ok {
			return true
		}
	}
	return false
}

func addBracketToQuery(u *url.URL) *url.URL {
	result := new(url.URL)
	qs := u.Query()
	pairs := make([]string, 0, len(qs))
	for key := range qs {
		for _, value := range qs[key] {
			pairs = append(pairs, fmt.Sprintf("%v[]=%v", key, value))
		}
	}
	*result = *u
	result.RawQuery = strings.Join(pairs, "&")
	return result
}

func (scanner *WSScanner) genAffectLinks(link *ScanLink) []*AffectLink {
	results := []*AffectLink{}
	u, err := url.Parse(link.URL)
	if err != nil {
		log.Errorln("failed to parse URL:", link.URL)
		return results
	}

	var method string
	option := scanner.affectOptions
	if link.Method == http.MethodPost {
		method = http.MethodPost
		option &^= AffectOptionContent | AffectOptionDirectory | AffectOptionFile
	} else {
		method = http.MethodGet
		if link.IsStatic {
			option &^= AffectOptionContent
		}
	}

	ignored := shouldIgnore(u.Path)
	if option&AffectOptionContent != 0 && !ignored {
		results = append(results, &AffectLink{UrlID: link.UrlID, Affect: AffectContent, URL: link.URL, Method: method})
		if u.RawQuery != "" {
			results = append(results, &AffectLink{UrlID: link.UrlID, Affect: AffectContent, URL: addBracketToQuery(u).String(), Method: method})
		}
	}

	if option&AffectOptionDirectory != 0 {
		subPaths := strings.Split(u.Path, "/")
		suffix := subPaths[len(subPaths)-1]

		if suffix != "" && !strings.Contains(suffix, ".") {
			subPaths = append(subPaths, "")
		} else {
			subPaths[len(subPaths)-1] = ""
		}
		for i := 1; i < len(subPaths); i++ {
			path := strings.Join(subPaths[:i], "/") + "/"
			URL := url.URL{Scheme: u.Scheme, Host: u.Host, Path: path}
			results = append(results, &AffectLink{UrlID: link.UrlID, Affect: AffectDirectory, URL: (&URL).String(), Method: method})
		}
	}

	if option&AffectOptionFile != 0 {
		if u.Path != "" && !strings.HasSuffix(u.Path, "/") && !ignored {
			URL := url.URL{Scheme: u.Scheme, Host: u.Host, Path: u.Path}
			results = append(results, &AffectLink{UrlID: link.UrlID, Affect: AffectFile, URL: (&URL).String(), Method: method})
		}
	}

	if ((u.RawQuery != "" && strings.Contains(u.RawQuery, "=")) ||
		(link.Data != "" && strings.Contains(link.Data, "="))) &&
		!ignored {
		if option&AffectOptionParameter != 0 {
			results = append(results, &AffectLink{UrlID: link.UrlID, Affect: AffectParameter, URL: link.URL, Method: method, Data: link.Data})
		}
		if option&AffectOptionSQLInject != 0 && len(link.URL) <= 512 && len(u.Query()) <= 6 {
			results = append(results, &AffectLink{UrlID: link.UrlID, Affect: AffectSQLInject, URL: link.URL, Method: method, Data: link.Data})
		}

	}

	if option&AffectOptionWeakPass != 0 && u.Path != "" && scanner.Options.WeakPassCheckURL != "" {
		for _, keyword := range []string{
			"signin", "login", "admin",
		} {
			if strings.Contains(u.Path, keyword) {

				results = append(results, &AffectLink{UrlID: link.UrlID, Affect: AffectWeakPass, URL: link.URL, Method: method, Data: link.Data})
			}
		}
	}

	return results
}

func (scanner *WSScanner) addLink(linkOrg *AffectLink) {

	link := linkOrg.DeepCopy()
	for k, vs := range scanner.Options.Headers {
		for i := range vs {
			link.Headers.Add(k, vs[i])
		}
	}

	fp := link.Fingerprint()
	if _, ok := scanner.scannedLinksFilter[fp]; !ok {
		scanner.scannedLinksFilter[fp] = struct{}{}
		scanner.affectLinksChannel <- link
	}
}

func (scanner *WSScanner) loadLinks() {
	defer scanner.goroutines.Done()

	entry := scanner.Options.Entry
	parts, err := url.Parse(entry)
	if err != nil {
		log.Errorln("failed to parse task entry: ", entry)
	} else {
		homepage := (&url.URL{Scheme: parts.Scheme, Host: parts.Host, Path: "/"}).String()

		if scanner.affectOptions&AffectOptionServer != 0 {
			serverLink := AffectLink{Affect: AffectServer, URL: homepage}
			scanner.addLink(&serverLink)
		}
		if scanner.affectOptions&AffectOptionScript != 0 {
			scriptLink := AffectLink{Affect: AffectScript, URL: homepage}
			scanner.addLink(&scriptLink)
		}
		if scanner.affectOptions&AffectOptionNmap != 0 {
			nmapLink := AffectLink{Affect: AffectNmap, URL: homepage}
			scanner.addLink(&nmapLink)
		}
		if scanner.affectOptions&AffectOptionFuzzy != 0 {
			fuzzyLink := AffectLink{Affect: AffectFuzzy, URL: homepage}
			scanner.addLink(&fuzzyLink)
		}
	}

Loading:
	for link := range scanner.scanLinksChannel {
		scanner.currentLink = link

		affectLinks := scanner.genAffectLinks(link)
		for i := range affectLinks {
			scanner.waitIfPaused()
			if scanner.shouldStop() {
				break Loading
			}
			fmt.Println("gen affect url:", link.URL, affectLinks[i].URL)
			scanner.addLink(affectLinks[i])
		}
	}
	close(scanner.affectLinksChannel)
}

func (scanner *WSScanner) processLink(linkOrg *AffectLink) {
	defer scanner.afterProcessing()

	link := linkOrg.DeepCopy()
	log.Infoln("processing link:", link)
	switch link.Affect {
	case AffectContent, AffectDirectory, AffectFile, AffectParameter, AffectServer:
		scanner.scanLink(link)
	case AffectWeakPass:
		scanner.scanNewWeakPass(link)
	case AffectNmap:
		scanner.scanNmapWrap(link)
	case AffectScript:
		scanner.scanScript(link)
	case AffectSQLInject:
		scanner.scanSQLInjectWrap(link)

	case AffectFuzzy:
		scanner.scanBy3rdPartyService(link)
	case AffectXray:
		scanner.ScanXray(link)
	}
	log.Infoln("scan process finished:", link)
}

func (scanner *WSScanner) shouldSaveVul(link *AffectLink, vul *rules.Vulnerability) bool {
	if vul.Affects == "text" {
		if scanner.textVulsFilter.ContainsOrFull(vul.VulXML, link.URL) {
			return false
		}
	}
	return true
}

func (scanner *WSScanner) shouldScanLink(link *AffectLink, vul *rules.Vulnerability) bool {

	if vul.Affects == "text" {
		if scanner.textVulsFilter.ContainsOrFull(vul.VulXML, link.URL) {
			return false
		}
	}
	if vul.VulXML == XMLStruct2_057 {
		urlWithoutQuery := strings.SplitN(link.URL, "?", 2)[0]
		if !strings.HasSuffix(urlWithoutQuery, ".action") {
			return false
		}
	}
	return true
}

func (scanner *WSScanner) beforeProcessing() bool {
	err := scanner.linkSema.Acquire(scanner.linkCtx, 1)
	if err != nil {
		return false
	}
	atomic.AddInt64(&scanner.processingCount, 1)
	return true
}

func (scanner *WSScanner) afterProcessing() {
	scanner.goroutines.Done()
	scanner.linkSema.Release(1)
	atomic.AddInt64(&scanner.processingCount, -1)
}

func (scanner *WSScanner) run() {
	scanner.goroutines.Add(1)
	go scanner.loadLinks()

	var r *regexp.Regexp
	var err error
	if scanner.Options.FilterReg != "" {
		r, err = regexp.Compile(scanner.Options.FilterReg)
		if err != nil {
			log.Errorln("failed to load urls:", err)
		}
	}
Loading:
	for {
		scanner.waitIfPaused()

		if scanner.shouldStop() {

			t := time.NewTimer(5 * time.Second)
			select {
			case _, ok := <-scanner.affectLinksChannel:
				if !ok {
					break Loading
				}
			case <-t.C:
				break Loading
			}
		} else {
			t := time.NewTimer(5 * time.Second)
			select {
			case link := <-scanner.affectLinksChannel:
				t.Stop()
				if link == nil {
					log.Info("link nil")
					break Loading
				}

				if r != nil {

					uri, err := url.Parse(link.URL)
					if err != nil {
						log.Errorln("failed to Parse uri", err)
						break
					}

					if r.MatchString(uri.Path) {
						log.Info("not scan url", uri.Path)
						break
					}
				}

				if scanner.beforeProcessing() {
					scanner.goroutines.Add(1)
					go scanner.processLink(link)
				}
			case <-t.C:

			}
		}
	}
	scanner.finish()
}

func (scanner *WSScanner) shouldPause() bool {
	return scanner.isPaused
}

func (scanner *WSScanner) waitIfPaused() {
	for {
		if scanner.shouldStop() {
			return
		}

		if scanner.shouldPause() {
			time.Sleep(time.Second * 2)
			continue
		} else {
			return
		}
	}
}

func (scanner *WSScanner) finish() {

	log.Infoln("scanner finish start", scanner.Options.Entry)
	scanner.goroutines.Wait()
	close(scanner.scanResultChannel)

	if scanner.cookiejar != nil {
		scanner.cookiejar.Release()
	}
	log.Infoln("scanner finish end", scanner.Options.Entry)
}

func (scanner *WSScanner) Cancel() {
	scanner.processStatus = consts.TaskResultCanceled
	scanner.isCanceled = true
	scanner.linkCtxCancel()
}

func (scanner *WSScanner) shouldStop() bool {

	if scanner.isCanceled || scanner.isAborting {
		return true
	}
	if time.Now().After(scanner.expiredAt) {
		scanner.processStatus = consts.TaskResultPaused
		return true
	}
	return false
}

func (scanner *WSScanner) doRequest(request *ScanRequest) (*fasthttp.Response, string, error) {

	httpRequest := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(httpRequest)

	if scanner.cookiejar != nil {
		scanner.cookiejar.CopyToRequest(httpRequest)
	}

	httpRequest.SetRequestURI(request.URL)

	header := &httpRequest.Header
	header.SetMethod(request.Method)
	for key, values := range request.Headers {
		for i := range values {
			if i == 0 {

				header.Set(key, values[i])
			} else {
				header.Add(key, values[i])
			}
		}
	}
	if scanner.connectionClose {
		httpRequest.SetConnectionClose()
	}

	if request.Data != "" {
		httpRequest.SetBodyString(request.Data)
	}
	httpResponse := fasthttp.AcquireResponse()

	requestString := httpRequest.String()
	var err error
	for {
		if httpRequest.ConnectionClose() {
			httpClient := newHTTPClient(scanner.Options.UserAgent)
			err = httpClient.DoTimeout(httpRequest, httpResponse, 20*time.Second)
		} else {
			err = scanner.httpClient.DoTimeout(httpRequest, httpResponse, 20*time.Second)
		}
		if err == fasthttp.ErrConnectionClosed && !httpRequest.ConnectionClose() {

			scanner.connectionClose = true
			httpRequest.SetConnectionClose()
		} else {
			break
		}
	}

	if err != nil {
		log.Errorln("Request:", request.Method, request.URL, "***", request.Headers, "***", request.Data, "err:", err)
		fasthttp.ReleaseResponse(httpResponse)
		return nil, requestString, err
	} else {
		log.Infoln("Request:", request.Method, request.URL, "***", request.Headers, "***", request.Data, "ok.")
	}

	if scanner.cookiejar != nil {
		scanner.cookiejar.CopyFromResponse(httpResponse)
	}
	return httpResponse, requestString, nil
}

func (scanner *WSScanner) scanLink(link *AffectLink) {
	modules := scanner.affectModules[link.Affect]
	log.Info("scan link modules", modules, link.Affect)
Scanning:
	for i := range modules {
		for j := range modules[i].Vulnerabilities {
			vul := &(modules[i].Vulnerabilities[j])
			scanner.waitIfPaused()
			if scanner.shouldStop() {
				log.Infoln("should stop now:", scanner.Options.Tag)
				break Scanning
			}

			if scanner.specificVulXMLs != nil {
				if _, ok := scanner.specificVulXMLs[vul.VulXML]; !ok {
					continue
				}
			}

			linkCopy := link.DeepCopy()
			if !scanner.shouldScanLink(linkCopy, vul) {
				log.Errorln("should not scan link:", linkCopy, vul)
				continue
			}

			err := scanner.requestSema.Acquire(scanner.requestCtx, 1)
			if err != nil {
				log.Errorln("failed to acquire requestSema", err)
				break
			}

			scanner.goroutines.Add(1)
			go scanner.scanVul(linkCopy, vul)
		}
	}
}

func (scanner *WSScanner) scanVul(link *AffectLink, vul *rules.Vulnerability) {
	defer scanner.goroutines.Done()
	defer scanner.requestSema.Release(1)

	logger.Info("vul_name %v %v", vul.VulXML, link.Affect)
	result := &ScanResult{}
	params := vul.Params
	if len(params) == 0 {
		params = make([]rules.Param, 0, 1)
	}
	switch link.Affect {
	case AffectParameter:

		var qs url.Values

		if link.Method == http.MethodPost {
			if postQS, err := url.ParseQuery(link.Data); err == nil {
				qs = postQS
			} else {
				log.Errorln("failed to parse link.URL:", link.URL, err)
			}
		} else {
			if parts, err := url.Parse(link.URL); err == nil {
				qs = parts.Query()
			} else {
				log.Errorln("failed to parse link.URL:", link.URL, err)
			}

		}
		logger.Info("qs %v", qs)

		for field := range qs {
			if (vul.VulXML == XMLXSS ||
				vul.VulXML == XMLUTF7BOMXSS) &&
				!scanner.pretestForXSS(link, vul, paramForXSSCheck, field) {

				continue
			}

			if vul.VulXML == XMLXssStorage {

				logger.Info("check XMLXssStorage %v", link)
				scanner.DoXssVulnerableTest(link, field)
				continue
			}

			if len(params) == 0 {
				vulnerable, testResult := scanner.doVulnerableTest(link, vul, nil, field)

				result.Merge(testResult)
				if vulnerable {

					break
				}
			}

			for i := range params {

				vulnerable, testResult := scanner.doVulnerableTest(link, vul, &params[i], field)

				result.Merge(testResult)
				if vulnerable {

					break
				}

			}
		}

	case AffectDirectory:
		if len(params) == 0 {
			vulnerable, testResult := scanner.doVulnerableTestDirectory(link, vul, nil)
			result.Merge(testResult)
			if vulnerable && !(vul.VulXML == XMLPossibleSensitiveDirs ||
				vul.VulXML == XMLPossibleSensitiveFiles ||
				vul.VulXML == XMLPossibleSensitiveFiles2) {
				break
			}
		}

		for i := range params {
			vulnerable, testResult := scanner.doVulnerableTestDirectory(link, vul, &params[i])
			result.Merge(testResult)
			if vulnerable && !(vul.VulXML == XMLPossibleSensitiveDirs ||
				vul.VulXML == XMLPossibleSensitiveFiles ||
				vul.VulXML == XMLPossibleSensitiveFiles2) {
				break
			}
		}
	case AffectFile:
		if vul.VulXML == XMLFileXSS {
			if !scanner.pretestForXSS(link, vul, paramForFileXSSCheck1) &&
				!scanner.pretestForXSS(link, vul, paramForFileXSSCheck2) {
				break
			}
		}
		if len(params) == 0 {
			vulnerable, testResult := scanner.doVulnerableTestFile(link, vul, nil)
			result.Merge(testResult)
			if vulnerable {
				break
			}
		}

		for i := range params {
			vulnerable, testResult := scanner.doVulnerableTestFile(link, vul, &params[i])
			result.Merge(testResult)
			if vulnerable {
				break
			}
		}
	default:
		for i := range params {
			vulnerable, testResult := scanner.doVulnerableTest(link, vul, &params[i])
			result.Merge(testResult)
			if vulnerable {
				break
			}
		}

		if len(params) == 0 {
			vulnerable, testResult := scanner.doVulnerableTest(link, vul, nil)
			result.Merge(testResult)
			if vulnerable {
				break
			}
		}
	}
	scanner.outputResult(result)
}

var partRegex = regexp.MustCompile(`\$\(([a-z]+)\)`)
var portRegex = regexp.MustCompile(`(https?://[^:/]+):80(/|$)`)

func formatURL(baseURL string, pattern string) string {
	result := ""
	if pattern == "$(scheme)://$(host):$(port)$(path)" || pattern == "" {
		result = baseURL
	} else {
		parts, err := url.Parse(baseURL)
		if err != nil {
			log.Errorln("failed to parse base url:", baseURL)
			return ""
		}
		result = partRegex.ReplaceAllStringFunc(pattern, func(match string) string {
			switch match {
			case "$(scheme)":
				return parts.Scheme
			case "$(port)":
				port := parts.Port()
				if port == "" {
					port = "80"
				}
				return port
			case "$(host)":
				return parts.Hostname()
			case "$(path)":
				if parts.RawQuery == "" {
					return parts.Path
				}
				return fmt.Sprintf("%v?%v", parts.Path, parts.RawQuery)
			default:
				log.Warnln("unknown url element:", match)
				return ""
			}
		})
	}

	return portRegex.ReplaceAllString(result, "$1$2")
}

func getS2057PayloadURL(rawurl string) string {
	parts, err := url.Parse(rawurl)
	if err != nil {
		return ""
	}
	path := parts.Path
	dirs := strings.Split(path, "/")
	dirsLength := len(dirs)
	filename := dirs[dirsLength-1]
	dirs = append(dirs, filename)
	dirs[dirsLength-1] = "${(9000+527)}"
	parts.RawPath = strings.Join(dirs, "/")
	return utils.ToString(parts)
}

func genTestURL(test *rules.RuleTest, link *AffectLink, vul *rules.Vulnerability, param *rules.Param, field ...string) string {
	rawurl := formatURL(link.URL, test.Request.URL)
	if rawurl == "" {
		return ""
	}
	if param == nil || param.Value == "" {
		return rawurl
	}
	if vul.VulXML == XMLStruct2_057 {
		return getS2057PayloadURL(rawurl)
	}
	switch link.Affect {
	case AffectContent:
		return rawurl
	case AffectFile:
		return genFileTestURL(rawurl, param)
	case AffectDirectory:
		return genDirectoryTestURL(rawurl, param)
	case AffectParameter:
		if link.Method == http.MethodPost {

			return rawurl
		}
		return genParameterTestURL(rawurl, param, vul, field...)
	case AffectServer:
		return genServerTestURL(rawurl, param)
	default:
		return ""
	}
}

func parseHeader(headerStr string) http.Header {
	reader := bufio.NewReader(strings.NewReader(headerStr + "\r\n\r\n"))
	tp := textproto.NewReader(reader)

	mimeHeader, err := tp.ReadMIMEHeader()
	if err != nil {
		log.Errorln("failed to parse header:", headerStr, err)
		return http.Header{}
	}
	return http.Header(mimeHeader)
}

func genTestRequest(test *rules.RuleTest, linkOrg *AffectLink, vul *rules.Vulnerability, param *rules.Param, field ...string) *ScanRequest {
	var url, method, data string
	link := linkOrg.DeepCopy()

	if strings.HasPrefix(test.Request.URL, webscanVerifyURL) {
		url = test.Request.URL
		method = test.Request.Method
		data = test.Request.PostText
	} else {
		url = genTestURL(test, link, vul, param, field...)
		if url == "" {
			return nil
		}

		if link.Method == http.MethodPost || test.Request.Method == "" {
			method = link.Method
		} else {
			method = test.Request.Method
		}

		data = test.Request.PostText
		if link.Affect == AffectParameter && link.Method == http.MethodPost {
			data = genParameterPostData(link, vul, param, field...)
		}
		if vul.VulXML == XMLStruts2_046 || vul.VulXML == XMLStruts2_046Header {
			data = strings.Replace(data, "[null byte]", "\x00", -1)
		}
	}

	headers := link.Headers

	th := parseHeader(test.Request.CustomHeaders)
	for k := range th {
		v := th.Get(k)
		headers.Set(k, v)
	}
	headers.Add("Cookie", test.Request.Cookies)
	if headers.Get("Referer") == "" {

		headers.Set("Referer", link.URL)
	}

	request := ScanRequest{
		Method:  method,
		URL:     url,
		Headers: headers,
		Data:    data,
	}

	return &request
}

func genWsdlTestRequest(test *rules.RuleTest, linkOrg *AffectLink, vul *rules.Vulnerability,
	param *rules.Param, interfaceName string, field ...string) *WsdlRequest {
	var url string
	link := linkOrg.DeepCopy()

	url = genTestURL(test, link, vul, param, field...)
	if url == "" {
		return nil
	}

	request := WsdlRequest{
		URL:           url,
		InterfaceName: interfaceName,
		Args:          field,
	}

	return &request
}

func isTextContentType(contentType []byte) bool {
	return bytes.HasPrefix(contentType, []byte("text/"))
}

func replaceRandStrInRequest(request *ScanRequest, rmd string) {
	tag := "[RANDSTR]"
	request.URL = strings.Replace(request.URL, tag, rmd, -1)
	request.Data = strings.Replace(request.Data, tag, rmd, -1)
	newHeader := http.Header{}
	for field, values := range request.Headers {
		for i := range values {
			newHeader.Add(strings.Replace(field, tag, rmd, -1), strings.Replace(values[i], tag, rmd, -1))
		}
	}
	request.Data = strings.Replace(request.Data, tag, rmd, -1)
}

func replaceRandStrInWsdlRequest(request *WsdlRequest, rmd string) {
	tag := "[RANDSTR]"

	for index, values := range request.Args {

		newValue := strings.Replace(values, tag, rmd, -1)
		request.Args[index] = newValue

	}

}

func (scanner *WSScanner) pretestForXSS(link *AffectLink, vul *rules.Vulnerability, param *rules.Param, field ...string) bool {

	scanner.waitIfPaused()
	if scanner.shouldStop() {
		return false
	}
	request := genTestRequest(ruleTestForXSSCheck, link, vul, param, field...)
	if request == nil {
		log.Errorln("failed to gen pre-test xss request:", link, vul)
		return true
	}
	if request.Method == http.MethodPost {
		if request.Data != "" {
			if request.Headers.Get("Content-Type") == "" {
				request.Headers.Set("Content-Type", "application/x-www-form-urlencoded")
			}
		}
	}
	response, _, err := scanner.doRequest(request)
	if err != nil {
		log.Errorln("failed to fetch", request.Method, request.URL, err)
		return true
	}
	defer fasthttp.ReleaseResponse(response)

	body, err := utils.GetOriginalBody(response)
	if err != nil {
		log.Errorln("failed to get pre-test response body", request.URL, err)
		return true
	}
	return bytes.Contains(body, []byte(param.Value))
}

func (scanner *WSScanner) doVulnerableTest(link *AffectLink, vul *rules.Vulnerability,
	param *rules.Param, field ...string) (bool, *ScanResult) {
	scanner.waitIfPaused()
	if scanner.shouldStop() {
		logger.Info("stop %v", scanner.shouldStop())
		return false, nil
	}

	logger.Info("%v, %v, %v, %v", vul.VulXML, param, field, vul.Rule.Tests)
	foundVuls := []*FoundVul{}

	var requestCount, errCount int64
	var firstContext *lazyContext
	var firstRequest *ScanRequest
	var firstRequestString string
	var contextValue VulContext
	var vulnerable bool

	var rmd = utils.RandLetterNumbers(32) + link.Fingerprint()

	var vulURL string
	start := time.Now()

	for i, test := range vul.Rule.Tests {
		request := genTestRequest(&test, link, vul, param, field...)
		if request == nil {
			log.Errorln("failed to gen test request:", test, link, vul, param)
			return false, nil
		}

		replaceRandStrInRequest(request, rmd)
		if vul.VulXML == "XPath_injection.xml" {
			log.Info("rand request", request.URL, request.Data, vul.VulXML)
		}

		verifying := strings.HasPrefix(request.URL, webscanVerifyURL)
		if verifying {

			time.Sleep(time.Second * 3)
		} else {

			vulURL = request.URL
		}
		if request.Method == http.MethodPost {
			vulURL = fmt.Sprintf("%v?%v", request.URL, request.Data)
			if request.Data != "" {
				if request.Headers.Get("Content-Type") == "" {
					request.Headers.Set("Content-Type", "application/x-www-form-urlencoded")
				}
			}
		}
		requestCount++
		response, requestString, err := scanner.doRequest(request)
		if err != nil {
			log.Errorln("failed to fetch", request.URL, err)
			atomic.AddInt64(&scanner.recentFailedCount, 1)
			errCount++
			if atomic.LoadInt64(&scanner.recentFailedCount) >= 500 {
				scanner.abort("too many errors")
			}
			break
		} else {
			atomic.StoreInt64(&scanner.recentFailedCount, 0)

			defer fasthttp.ReleaseResponse(response)
		}
		context := lazyContext{response: response, values: map[string]interface{}{}}

		testVulnerable := test.Match(context)

		contentType := response.Header.ContentType()
		if link.Affect != AffectContent && isTextContentType(contentType) {
			foundVuls = append(foundVuls, scanner.checkResponseForTextVuls(link, requestString, request, context)...)
		}

		if !testVulnerable {

			break
		}

		if link.Affect == AffectFile || link.Affect == AffectDirectory || link.Affect == AffectParameter {
			var needVerify bool
			for _, xmlName := range xsscheck.XSSXMLList {
				if vul.VulXML == xmlName {
					needVerify = true
					break
				}
			}

			if needVerify {
				checkResult, curReq, err := xsscheck.RunXssCheck(
					link.URL,
					link.Method,
					scanner.cookiejar.CopyToString(),
					scanner.Options.UserAgent,
					link.Data,
					link.Affect,
					scanner.Options.XssPayLoadPath,
				)
				if err != nil {
					log.Error(vul, "verify xss err", err)
					return vulnerable, &ScanResult{FoundVuls: foundVuls, RequestCount: requestCount, ErrorCount: errCount}
				}

				if len(checkResult) == 0 {
					log.Error(vul, "verify xss failed")

				} else {
					requestString = curReq
				}
			}
		}

		if i == 0 {
			firstRequest = request
			firstContext = &context
			firstRequestString = requestString
		}

		if i == len(vul.Rule.Tests)-1 {

			vulnerable = true

			if verifying || vul.VulXML == XMLWebDavDirWithWritePerm {
				contextValue = firstContext.Values()
				contextValue["method"] = firstRequest.Method
			} else {
				contextValue = context.Values()
				contextValue["method"] = request.Method
			}

			if len(field) > 0 {
				contextValue["field"] = field[0]
			}
			contextValue["request_data"] = firstRequestString

			foundVul := &FoundVul{Link: link, Vul: vul, VulURL: vulURL, Severity: vul.Severity, Context: contextValue}

			if scanner.Options.ValidateXSS &&
				(vul.VulXML == XMLXSSInPath ||
					vul.VulXML == XMLFileXSS ||
					vul.VulXML == XMLXSS ||
					vul.VulXML == XMLCrossFrameScripting) {
				scanner.validateXSSVulWithScreenshot(link, vul, foundVul)
			} else if scanner.Options.ValidateSQLInjection &&
				(vul.VulXML == XMLSQLInjection ||
					vul.VulXML == XMLSQLInjectionInPath) {
				cookie := scanner.Options.Headers.Clone().Get("cookie")
				scanner.validateSQLInjectVulWithScreenshot(link, vul, foundVul, []byte(cookie))
			}

			foundVuls = append(foundVuls, foundVul)
		}
	}

	timeCost := time.Now().Sub(start)
	if timeCost < time.Second*1 {
		time.Sleep(time.Second*1 - timeCost)
	}
	return vulnerable, &ScanResult{FoundVuls: foundVuls, RequestCount: requestCount, ErrorCount: errCount}
}

func (scanner *WSScanner) abort(reason string) {
	log.Infoln("aborting:", scanner.Options.Tag, reason)
	scanner.reason = reason
	scanner.isAborting = true
}

func (scanner *WSScanner) checkResponseForTextVuls(linkOrg *AffectLink, requestString string, request *ScanRequest, context lazyContext) []*FoundVul {
	link := linkOrg.DeepCopy()
	link.Affect = AffectContent
	foundVuls := []*FoundVul{}
	modules := scanner.affectModules[AffectContent]
	for i := range modules {
	ModuleTest:
		for j := range modules[i].Vulnerabilities {
			vul := &(modules[i].Vulnerabilities[j])
			if !scanner.shouldScanLink(link, vul) {
				continue
			}
			if vul.VulXML == XMLSourceCodeDisclosure && (strings.Contains(request.URL, "print") || strings.Contains(request.Data, "print")) {
				continue
			}

			if scanner.specificVulXMLs != nil {
				if _, ok := scanner.specificVulXMLs[vul.VulXML]; !ok {
					continue
				}
			}

			tests := vul.Rule.Tests

			for k := range tests {
				if !tests[k].Match(context) {
					continue ModuleTest
				}
			}
			copiedContext := map[string]interface{}{}
			for k, v := range context.Values() {
				copiedContext[k] = v
			}
			copiedContext["request_data"] = requestString
			foundVuls = append(foundVuls, &FoundVul{Link: link, Vul: vul, VulURL: request.URL,
				Severity: vul.Severity, Context: copiedContext})
		}
	}
	return foundVuls
}

func (scanner *WSScanner) outputResult(result *ScanResult) {
	if result == nil {
		return
	}

	atomic.AddInt64(&scanner.errorCount, result.ErrorCount)
	atomic.AddInt64(&scanner.requestCount, result.RequestCount)

	vuls := result.FoundVuls
	for i := range vuls {
		vul := vuls[i]
		log.Info(vul.Link, vul.Vul)
		if !scanner.shouldSaveVul(vul.Link, vul.Vul) {
			vuls[i] = nil
		}
		atomic.AddInt64(&scanner.foundVulsCount, 1)
		xmlName := vul.Vul.VulXML
		if vul.Vul.Affects == "text" {
			scanner.textVulsFilter.Add(xmlName, vul.Link.URL)
		}
		scanner.validateFoundVul(vul)
	}
	scanner.scanResultChannel <- result
}

func (scanner *WSScanner) IsRunning() bool {
	return true
}

func (scanner *WSScanner) IsCanceldActive() bool {
	return scanner.isCanceldActive
}

func (scanner *WSScanner) Add(link *ScanLink) {

Adding:
	for {
		scanner.waitIfPaused()
		if scanner.shouldStop() {
			return
		}

		t := time.NewTimer(5 * time.Second)
		select {
		case scanner.scanLinksChannel <- link:
			break Adding
		case <-t.C:
		}
	}
}

func (scanner *WSScanner) Stop() {
	scanner.isCanceled = true
	scanner.linkCtxCancel()
}

func (scanner *WSScanner) AddDone() {
	close(scanner.scanLinksChannel)
}

func (scanner *WSScanner) GetStats() *Stats {
	stats := &Stats{
		FinishStatus:   scanner.processStatus,
		RequestCount:   scanner.stats.requestCount,
		FoundVulsCount: scanner.stats.foundVulsCount,
		ErrorReason:    scanner.reason,
		ErrorCount:     scanner.stats.errorCount,
	}

	return stats
}

func (scanner *WSScanner) GetOffset() string {

	currentLink := scanner.currentLink
	if currentLink != nil {
		return currentLink.URL
	}
	return ""
}

func (scanner *WSScanner) doWsdlVulnerableTest(link *AffectLink, vul *rules.Vulnerability, param *rules.Param,
	interfaceName string, field ...string) (bool, *ScanResult) {
	scanner.waitIfPaused()
	if scanner.shouldStop() {
		return false, nil
	}

	foundVuls := []*FoundVul{}

	var requestCount, errCount int64
	var firstContext *wsdlContext
	var firstRequest *WsdlRequest
	var contextValue VulContext
	var vulnerable bool

	var rmd = utils.RandLetterNumbers(32) + link.Fingerprint()

	var vulURL string
	start := time.Now()
	for i, test := range vul.Rule.Tests {
		request := genWsdlTestRequest(&test, link, vul, param, interfaceName, field...)
		if request == nil {
			log.Errorln("failed to gen test request:", test, link, vul, param)
			return false, nil
		}

		replaceRandStrInWsdlRequest(request, rmd)

		verifying := strings.HasPrefix(request.URL, webscanVerifyURL)
		if verifying {

			time.Sleep(time.Second * 3)
		} else {

			vulURL = request.URL
		}

		requestCount++
		response, err := scanner.doWsdlRequest(request)
		if err != nil {
			log.Errorln("failed to fetch", request.URL, err)
			atomic.AddInt64(&scanner.recentFailedCount, 1)
			errCount++
			if atomic.LoadInt64(&scanner.recentFailedCount) >= 500 {
				scanner.abort("too many errors")
			}
			break
		} else {
			atomic.StoreInt64(&scanner.recentFailedCount, 0)

			defer response.Response.Body.Close()
		}
		context := wsdlContext{response: response, values: map[string]interface{}{}}
		testVulnerable := test.Match(context)

		if !testVulnerable {

			break
		}

		if i == 0 {
			firstRequest = request
			firstContext = &context
		}

		vulnerable = true
		if verifying || vul.VulXML == XMLWebDavDirWithWritePerm {
			contextValue = firstContext.Values()
			contextValue["method"] = firstRequest.InterfaceName
		} else {
			contextValue = context.Values()
			contextValue["method"] = request.InterfaceName
		}

		if len(field) > 0 {
			contextValue["field"] = field[0]
		}
		log.Info("context:", context)
		foundVul := &FoundVul{Link: link, Vul: vul, VulURL: vulURL, Severity: vul.Severity, Context: contextValue}

		foundVuls = append(foundVuls, foundVul)

	}

	timeCost := time.Now().Sub(start)
	if timeCost < time.Second*1 {
		time.Sleep(time.Second*1 - timeCost)
	}
	return vulnerable, &ScanResult{FoundVuls: foundVuls, RequestCount: requestCount, ErrorCount: errCount}
}

func (scanner *WSScanner) doWsdlRequest(request *WsdlRequest) (res *WsdlResponse,
	err error) {

	url := "http://127.0.0.1:7007/ws/services"
	curB, err := json.Marshal(request)
	if err != nil {
		log.Error("marshal json err", err)
		return
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(curB))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {

		log.Error("resp err", err)
		return
	}

	res = &WsdlResponse{
		URL:           request.URL,
		InterfaceName: request.InterfaceName,
		Response:      resp,
	}

	return
}

func (scanner *WSScanner) getWsdlInterface(linkUrl string, target *WsdlResponse) error {
	myclient := http.Client{}

	req, err := http.NewRequest("GET", "http://127.0.0.1:7007/ws/services", nil)
	if err != nil {
		return err
	}
	q := req.URL.Query()
	q.Add("wsdl", linkUrl)
	req.URL.RawQuery = q.Encode()
	resp, err := myclient.Do(req)
	if err != nil {
		log.Info("request err", err)
		return err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	return json.Unmarshal(body, target)
}

func (scanner *WSScanner) GetWsdlVul(linkUrl []string) {
	for _, curUrl := range linkUrl {
		link := &AffectLink{
			Method: "GET",
			URL:    curUrl,
			Affect: AffectParameter,
		}
		scanner.scanLink(link)
	}
	scanner.finish()
	return
}

func (scanner *WSScanner) GetResultChan() (chan *ScanResult, error) {
	return scanner.scanResultChannel, nil
}

func (scanner *WSScanner) ScanSingleLink(link *AffectLink) (chan *ScanResult, error) {

	if scanner.beforeProcessing() {
		scanner.goroutines.Add(1)
		scanner.processLink(link)
	}

	defer scanner.finish()
	fmt.Println("done.")
	return scanner.scanResultChannel, nil
}

func (scanner *WSScanner) scanBy3rdPartyService(link *AffectLink) error {
	assetID := scanner.Options.Task.AssetID.Hex()
	host := scanner.Options.Task.Host
	return scanner.triggerNmapHydraScan(assetID, host)
}

func (scanner *WSScanner) ScanPythonScripts(entriesURL string) error {

	u, err := url.Parse(entriesURL)
	if err != nil {
		return err
	}

	host := u.Hostname()
	port := u.Port()
	schema := u.Scheme
	isHttps := "False"

	if schema == "https" {
		isHttps = "True"
	}

	params := url.Values{"host": []string{host}, "port": []string{port},
		"asset_id": []string{scanner.Options.AssetID}, "is_https": []string{isHttps}}

	hc := http.Client{}
	req, err := http.NewRequest("POST", scanner.Options.PythonScriptsCheckURL, strings.NewReader(params.Encode()))
	if err != nil {
		return err
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	resp, err := hc.Do(req)
	if err != nil {
		return err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {

		return err
	}
	logrus.Info("python scripts", string(body))

	return nil
}

func (scanner *WSScanner) scanNewWeakPass(linkOrg *AffectLink) error {

	form := url.Values{}
	form.Add("login_url", linkOrg.URL)
	form.Add("asset_id", scanner.Options.AssetID)

	log.Info("TestScanNewWeakPass", scanner.Options.WeakPassCheckURL)
	hc := http.Client{}
	req, err := http.NewRequest("POST", scanner.Options.WeakPassCheckURL, strings.NewReader(form.Encode()))
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	resp, err := hc.Do(req)
	if err != nil {
		return err
	}

	defer resp.Body.Close()

	newBody, err := io.ReadAll(resp.Body)
	if err != nil {

		return err
	}

	logrus.Info("weakpass check", string(newBody))
	return nil
}

func (scanner *WSScanner) Die() {
	return
	/*
		scanner.Options = nil
		scanner.affectModules = map[string][]*rules.Module{}
		scanner.scannedLinksFilter = map[string]struct{}{}
		scanner.httpClient = nil
		scanner.cookiejar = nil
		scanner.textVulsFilter = nil
		scanner.specificVulXMLs = map[string]bool{}
	*/
}
