package scripts

import (
	"fmt"
	"os/exec"
)

func FrontEndBTC(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var rawurl = constructURL(args, "/")
	cmd := exec.Command("/usr/local/bin/phantomjs", "--ignore-ssl-errors=true",
		"/data/webscan/release/bin/script_file/spider.js", rawurl)
	var output []byte
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("frontendbtc %v", err)
		return nil, err
	}

	result_str := string(output)
	if len(result_str) > 0 {
		return &ScriptScanResult{
			Vulnerable: true,
			Output:     fmt.Sprintf("%v|url:%v", rawurl, result_str),
		}, nil
	}
	return &invulnerableResult, err

}
