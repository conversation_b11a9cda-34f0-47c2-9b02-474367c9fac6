package scripts

import (
	"bytes"
	"fmt"
	"net"
	"time"
)

func ZookeeperUnauthority(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addr := args.Host + ":2181"
	conn, err := net.DialTimeout("tcp", addr, time.Second*5)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	payload := []byte("envidddfdsfsafafaerwrwerqwe")
	conn.SetWriteDeadline(time.Now().Add(time.Second * 15))
	conn.Write(payload)

	response := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(time.Second * 15))

	n, err := conn.Read(response)
	if err != nil {
		fmt.Println(n)
		return nil, err
	}

	if bytes.Contains(response, []byte("Environment")) {
		return &ScriptScanResult{Vulnerable: true, Output: addr, Body: response}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("zookeeper_unauth_access.xml", ZookeeperUnauthority)
}
