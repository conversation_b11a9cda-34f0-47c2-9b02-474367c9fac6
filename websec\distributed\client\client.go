package client

import (
	"net"
	"time"
	"websec/distributed/endpoint"
	"websec/utils/log"
)

type Client struct {
	*endpoint.Endpoint

	serverAddr string
	retries    int
}

func (client *Client) SetRetry(retryCount int) error {
	client.retries = retryCount
	return nil
}

func (client *Client) processConnectionLost() endpoint.OnLostFunc {
	return func(name string) error {
		log.Infoln("connection lost:", name)
		for {
			conn, err := net.Dial("tcp", client.serverAddr)
			if err != nil {
				log.Errorln("failed to connect server:", client.serverAddr, err)
				time.Sleep(time.Second * 1)
				continue
			}
			client.SetConnection(conn)
			break
		}
		return nil
	}
}

func (client *Client) Reconnect() {
	log.Infof("connection begin reconnect tag(%s) serverAddr(%s)", client.Tag, client.serverAddr)
	retry := 1
	maxRetryTime := 10
	for {
		conn, err := net.Dial("tcp", client.serverAddr)
		if err != nil {
			log.Errorf("failed to connect server: %s %s %v", client.Tag, client.serverAddr, err)
			if retry*2 > maxRetryTime {
				time.Sleep(time.Second * time.Duration(maxRetryTime))
			} else {
				time.Sleep(time.Second * time.Duration(retry*2))
			}
			retry++
			continue
		}
		client.SetConnection(conn)
		client.Run()
		log.Infof("connection reconnect tag(%s) serverAddr(%s) success", client.Tag, client.serverAddr)
		break
	}
}

func (client *Client) Run() {
	go client.Consume()
}

func NewClient(serverAddr string, options ...optionFunc) (*Client, error) {
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return nil, err
	}

	ep, err := endpoint.NewEndpoint(
		endpoint.WithConnection(conn),
		endpoint.WithTimeout(time.Second*10),
	)
	if err != nil {
		return nil, err
	}

	client := &Client{
		Endpoint:   ep,
		serverAddr: serverAddr,
		retries:    3,
	}

	for _, opt := range options {
		err := opt(client)
		if err != nil {
			return nil, err
		}
	}
	return client, nil
}
