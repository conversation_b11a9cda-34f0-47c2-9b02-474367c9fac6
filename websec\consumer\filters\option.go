package filters

import (
	"websec/common/db"
	"websec/utils/semaphore"
)

type OptionFn func(*Filter) error

func WithConsumeConcurrency(concurrency int64) OptionFn {
	return func(filter *Filter) error {
		filter.consumeSema = semaphore.NewWeighted(concurrency)
		return nil
	}
}

func WithDBConnection(dbConnection *db.DBConnection) OptionFn {
	return func(filter *Filter) error {
		filter.dbConnection = dbConnection
		return nil
	}
}
