package scripts

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"net"
	"regexp"
	"strconv"
	"strings"
	"time"

	"golang.org/x/net/html/atom"
)

func versionCompare(dv string, cv string) (int, error) {
	dvl := strings.Split(dv, ".")
	cvl := strings.Split(cv, ".")
	dlen := len(dvl)
	clen := len(cvl)

	var maxlen int
	if dlen > clen {
		maxlen = clen
	} else {
		maxlen = dlen
	}

	for i := 0; i < maxlen; i++ {
		dvnum, err := strconv.Atoi(dvl[i])
		if err != nil {
			return 0, err
		}

		cvnum, err := strconv.Atoi(cvl[i])
		if err != nil {
			return 0, err
		}

		if dvnum != cvnum {
			return dvnum - cvnum, nil
		}
	}

	return 0, nil
}

var mysqlVersionPattern = regexp.MustCompile(`\d*\.\d*\.\d*`)

func MySQLVersion(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var port = 3306
	rawurl := args.Host + ":" + strconv.Itoa(port)
	conn, err := net.DialTimeout("tcp", rawurl, 10*time.Second)

	if err != nil {

		return nil, err
	}

	defer conn.Close()
	conn.SetDeadline(time.Now().Add(10 * time.Second))

	result, err := ioutil.ReadAll(conn)
	if err != nil {

		return nil, err
	}

	if bytes.Contains(result, []byte("not allowed to")) {
		return nil, err
	}

	var version []byte

	groups := mysqlVersionPattern.FindSubmatch(result)
	flag := false
	if groups != nil {
		version = groups[0]
		vstring := atom.String(version)
		result, _ := versionCompare(vstring, "4.0.0")

		if result < 0 {
			result, _ := versionCompare(vstring, "3.23.36")
			if result < 0 {
				flag = true
			}
		} else {
			result, _ := versionCompare(vstring, "5.0.0")
			if result < 0 {
				flag = true
			} else {
				result, _ := versionCompare(vstring, "6.0.0")
				if result < 0 {
					result, _ := versionCompare(vstring, "5.0.45")
					if result < 0 {
						flag = true
					}

					sresult, _ := versionCompare(vstring, "5.1.0")
					eresult, _ := versionCompare(vstring, "5.1.23")
					if sresult >= 0 && eresult < 0 {
						flag = true
					}
				} else {
					result, _ := versionCompare(vstring, "6.0.4")
					if result < 0 {
						flag = true
					}
				}
			}
		}
	} else {
		return nil, err
	}

	if flag {
		return &ScriptScanResult{
			Vulnerable: true,
			Output: fmt.Sprintf(
				"http://%v:3306|MySQL Version:%v", args.Host, string(version)),
		}, nil
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("mysqlversion.xml", MySQLVersion)
}
