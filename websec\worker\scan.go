package main

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/distributed/protocol"
	"websec/scan"
	"websec/scan/rules"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (processor *Processor) createScanner(task *schema.Task) (scan.Scanner, error) {
	now := time.Now()
	expiredAt := now.Add(time.Second * time.Duration(task.Scan.Timeout))

	maxExpireAt := now.Add(24 * time.Hour)
	if expiredAt.After(maxExpireAt) {
		expiredAt = maxExpireAt
	}

	log.Debug("scan expireAt begin", task.ID, task.Host, expiredAt)

	blueprint, err := rules.LoadBlueprint(settings.Scanner.BlueprintPath, settings.Scanner.VulsPath)
	if err != nil {
		log.Errorln("error while createScanner when load blueprint", err)
		return nil, err
	}

	scanOption := &scan.Options{
		Tag:                  task.ID.String(),
		Entry:                fmt.Sprintf("http://%v/", task.Host),
		UserAgent:            settings.UserAgent,
		Sema:                 processor.sema,
		ExpiredAt:            expiredAt,
		Ctx:                  processor.ctx,
		Blueprint:            &blueprint,
		Headers:              task.CustomHeaders(),
		SpecificAffects:      task.Scan.SpecificAffects,
		SpecificXMLs:         task.Scan.SpecificXMLs,
		FilterReg:            task.Scan.FilterReg,
		ValidateSQLInjection: true,
		ValidateXSS:          true,
		SqlmapPath:           settings.Scanner.SqlmapPath,
		SqlmapScreenshotPath: settings.Scanner.SqlmapScreenshotPath,
		XSSJSPath:            settings.Scanner.XSSJSPath,
		XSSJSScreenshotPath:  settings.Scanner.XSSJSScreenshotPath,
		XssPayLoadPath:       settings.Scanner.XssPayloadPath,
		PangolinAddress:      settings.Webscan.Host + ":" + strconv.Itoa(int(settings.Webscan.SQLInjectPort)),
		BucketSelect:         processor.manager.bucketSelect,
		NmapHydraService:     settings.NmapHydraService,
		Timeout:              time.Duration(task.Scan.Timeout) * time.Second,

		WeakPassCheckURL:      settings.ThirdPartyScan.WeakCheckURL,
		PythonScriptsCheckURL: settings.ThirdPartyScan.PythonScripts,
		AssetID:               task.AssetID.Hex(),
		JobID:                 task.JobID,
		Task:                  task,
	}
	return scan.NewScanner(scanOption)
}

func (processor *Processor) feedScanner(task *schema.Task, scanner scan.Scanner) {
	defer processor.wg.Done()
	retry := 0
	maxRetry := 3
	var err error

	for retry < maxRetry {
		if task.Schedule.Tag == consts.TaskTagScanSmart {
			err = processor.loadScannerLinks(task, scanner, true)
		} else {
			err = processor.loadScannerLinks(task, scanner, false)
		}

		if err != nil {
			retry++
		} else {
			break
		}
	}

	scanner.AddDone()
}

func (processor *Processor) loadScannerLinks(task *schema.Task, scanner scan.Scanner, once bool) error {
	const batchSize int64 = 10000

	var urlCount int64 = 0

	var offset string
	var offsetFound bool

	if !task.Track.LastTaskID.IsZero() {
		offset = processor.getProgress(task.Track.LastTaskID.Hex())

		if offset != "" {
			processor.delProgress(task.Track.LastTaskID.Hex())
			processor.setProgress(task.ID.Hex(), offset)
		}
	}

	if offset == "" {
		offset = processor.getProgress(task.ID.Hex())
	}

	if offset == "" {
		offsetFound = true
	}

	findOption := options.Find().SetBatchSize(10000)
	if once {
		findOption.SetLimit(batchSize)
	}

	filterSearch := bson.M{
		"job_id": task.JobID,
		"host":   task.Host,
		"depth":  bson.M{"$lte": task.Crawl.MaxDepth},
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	cursor, err := processor.GetDBConnection().GetMongoDatabase().Collection(consts.CollectionURLs).
		Find(ctx, filterSearch, findOption)
	if err != nil {
		log.Errorln("failed to load urls:", err)
		cancel()
		return err
	}

	for cursor.Next(ctx) {
		var u schema.SiteURL
		err := cursor.Decode(&u)
		if err != nil {
			log.Errorln("failed to decode url:", err)
			continue
		}
		urlCount++
		{
			v := &u
			offsetFound = offsetFound || offset == v.String()
			if !offsetFound {

				continue
			}
			for _, link := range processor.generateScannerLinks(task, v) {
				scanner.Add(link)
			}

		}
	}

	if err = cursor.Err(); err != nil {
		cursor.Close(ctx)
		cancel()
		return err
	}

	cursor.Close(ctx)
	cancel()

	log.Debugln("load scan urls finished, total found: ", urlCount, " ", task.Host)
	return nil
}

func (processor *Processor) generateScannerLinks(task *schema.Task, u *schema.SiteURL) []*scan.ScanLink {
	result := make([]*scan.ScanLink, 0, 2)
	url := u.String()
	isStatic := u.IsStatic()
	if u.Methods&consts.MethodBitGet != 0 {
		result = append(result, &scan.ScanLink{
			UrlID:    u.ID.Hex(),
			Method:   http.MethodGet,
			URL:      url,
			IsStatic: isStatic,
			Headers:  task.CustomHeaders(),
		})
	}
	if u.Methods&consts.MethodBitPost != 0 {
		result = append(result, &scan.ScanLink{
			UrlID:    u.ID.Hex(),
			Method:   http.MethodPost,
			URL:      url,
			IsStatic: isStatic,
			Data:     u.PostData,
			Headers:  task.CustomHeaders(),
		})
	}
	return result
}

func (processor *Processor) watchScanner(task *schema.Task, scanner scan.Scanner, future chan<- *protocol.TaskFinishBody) {
	runAt := time.Now()
	result := &protocol.TaskFinishBody{
		TaskID:  task.ID,
		AssetID: task.AssetID,
	}

	defer func() {
		log.Infof("watchScanner finish taskID:%s result:%v", task.ID.Hex(), result)
		future <- result
		processor.scannerMap.Delete(task.ID.Hex())
		processor.wg.Done()
	}()

	if len(processor.asset.Entries) > 0 && settings.ThirdPartyScan.PythonScripts != "" {
		scanner.ScanPythonScripts(processor.asset.Entries[0])
	}

	scanChan, err := scanner.Go()
	if err != nil {
		result.Status = consts.TaskResultError
		result.Reason = err.Error()
		return
	}

Watching:
	for {
		timer := time.NewTimer(time.Second * 5)
		select {

		case scanResult, ok := <-scanChan:
			timer.Stop()
			if ok {
				processor.saveScanResult(task, scanResult)
			} else {
				break Watching
			}
		case <-timer.C:
			processor.setProgress(task.ID.Hex(), scanner.GetOffset())
		}
	}

	stats := scanner.GetStats()
	if stats.FinishStatus != consts.TaskResultPaused {
		processor.delProgress(task.ID.Hex())
	}
	result.Status = stats.FinishStatus

	taskStats := new(schema.TaskStats)
	taskStats.TaskID = task.ID.Hex()
	taskStats.Host = task.Host
	taskStats.AssetID = task.AssetID.Hex()
	taskStats.RunAt = runAt
	taskStats.Stats.RequestCount = stats.RequestCount
	taskStats.Stats.FoundVulsCount = stats.FoundVulsCount
	taskStats.Stats.ErrorCount = stats.ErrorCount
	taskStats.Stats.ErrorReason = stats.ErrorReason
	processor.AddTaskStats(taskStats)
	scanner.(*scan.WSScanner).Die()
}

func (processor *Processor) saveScanResult(task *schema.Task, result *scan.ScanResult) {
	if result == nil {
		return
	}
}
