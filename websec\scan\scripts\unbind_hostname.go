package scripts

import (
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
)

func UnbindHostname(args *ScriptScanArgs) (*ScriptScanResult, error) {
	if ipRegex.MatchString(args.Host) {
		return &invulnerableResult, nil
	}

	rawurl := constructURL(args, "/")
	req, err := http.NewRequest(http.MethodGet, rawurl, nil)
	if err != nil {
		return nil, err
	}
	respA, err := goHTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer respA.Body.Close()

	bodyA, err := ioutil.ReadAll(respA.Body)
	if err != nil {
		return nil, err
	}
	fmt.Println(respA.StatusCode, rawurl)
	if respA.StatusCode == 200 {
		ips, err := net.LookupIP(args.Host)
		if err != nil {
			return nil, err
		}
		fmt.Println(ips)
		for i := range ips {
			var newArgs = &ScriptScanArgs{}
			fmt.Println(ips[i].String(), i)
			newArgs.Host = ips[i].String()
			newArgs.Port = args.Port
			newArgs.IsHTTPS = false
			newrawurl := constructURL(newArgs, "/")
			fmt.Println(newrawurl)
			newreq, err := http.NewRequest(http.MethodGet, newrawurl, nil)
			if err != nil {
				return nil, err
			}
			respCur, err := goHTTPClient.Do(newreq)
			if err != nil {
				return nil, err
			}
			defer respCur.Body.Close()
			bodyCur, err := ioutil.ReadAll(respCur.Body)
			if err != nil {
				return nil, err
			}
			if respCur.StatusCode == 200 {
				if textSimilarity(string(bodyA), string(bodyCur)) >= 0.9 {
					return &ScriptScanResult{Vulnerable: true, Output: rawurl + "|" + newrawurl}, nil
				}
			}
		}
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("unbind_hostname.xml", UnbindHostname)
}
