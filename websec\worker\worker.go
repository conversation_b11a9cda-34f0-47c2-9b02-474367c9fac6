package main

import (
	"fmt"
	_ "net/http/pprof"
	"os"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
	"websec/common"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/distributed/client"
	"websec/distributed/endpoint"
	"websec/distributed/protocol"
	"websec/utils/log"
	"websec/utils/stream"
	"websec/utils/tuchuang"

	"github.com/shirou/gopsutil/cpu"
	"github.com/shirou/gopsutil/mem"
	"github.com/shirou/gopsutil/process"
)

var (
	finishedTaskBodyChan = make(chan *protocol.TaskFinishBody, 100)
)

type Worker struct {
	client *client.Client

	closeCh chan struct{}

	tasks    sync.Map
	messages chan *protocol.Message
	manager  *processorManager

	count int32
}

func (worker *Worker) processTask(msg *protocol.Message) (uint8, interface{}) {
	defer log.Debugln("process task msg finished:", msg)

	task := &schema.Task{}
	err := msg.DecodeBody(task)
	if err != nil {
		log.Errorln("failed to decode message body:", err)
		return protocol.StatusFailed, err.Error()
	}

	if _, ok := worker.tasks.Load(task.ID); ok {
		log.Infoln("task existed:", task.ID)
		return protocol.StatusTaskExisted, nil
	}

	processor, err := worker.manager.Get(task)
	if err != nil {
		log.Errorln("failed to create processor:", err)
		return protocol.StatusFailed, err.Error()
	}
	future, err := processor.Process(task)
	if err != nil {
		log.Errorln("failed to process task:", task.ID, err)
		return protocol.StatusFailed, err.Error()
	}
	atomic.AddInt32(&worker.count, 1)
	go worker.waitProcessResult(task, future)
	return protocol.StatusOK, nil
}

func (worker *Worker) waitProcessResult(task *schema.Task, future <-chan *protocol.TaskFinishBody) {
	worker.tasks.Store(task.ID, true)
	defer worker.tasks.Delete(task.ID)

	result := <-future
	finishedTaskBodyChan <- result

	atomic.AddInt32(&worker.count, -1)
}

func (worker *Worker) processStopTask(msg *protocol.Message) (uint8, interface{}) {
	task := new(schema.Task)
	err := msg.DecodeBody(task)
	if err != nil {
		log.Errorln("failed to decode message body:", err)
		return protocol.StatusFailed, err.Error()
	}

	if _, ok := worker.tasks.Load(task.ID); !ok {
		return protocol.StatusFailed, "task not exist"
	}

	processor := worker.manager.get(task.Host)
	if processor == nil {
		return protocol.StatusFailed, "processor not found"
	}

	if processor.CancelTask(task.ID.Hex()) {
		return protocol.StatusOK, nil
	} else {
		return protocol.StatusFailed, "stop failed"
	}
}

func (worker *Worker) watchFinishedTasks() {
	for body := range finishedTaskBodyChan {
		log.Infoln("process result:", body.TaskID, body.Status, body.Reason)

		msg := &protocol.Message{
			Type:   protocol.TypeRequest,
			Action: protocol.ActionFinish,
			Body:   body,
		}

		task := protocol.TaskFinishBody{
			TaskID:  body.TaskID,
			AssetID: body.AssetID,
			Status:  body.Status,
			Reason:  body.Reason,
		}

		worker.manager.producer.ProduceBytes(consts.TopicTaskFinished, &task)

		for {
			if worker.client.Closed() {
				log.Infoln("client is close")
				time.Sleep(time.Second * 1)
				continue
			}
			_, err := worker.client.Request(msg)
			if err != nil {
				log.Errorln("failed to request", err)
				time.Sleep(time.Second * 1)
				continue
			}
			break
		}
	}
}

func (worker *Worker) Register() bool {
	request := &protocol.Message{
		Action: protocol.ActionRegister,
	}
	resp, err := worker.client.Request(request)
	if err != nil {
		log.Errorln("failed to register:", err)
		return false
	}
	if resp.Status != protocol.StatusOK {
		log.Errorln("failed to register, status:", resp.Status)
		return false
	}
	return true
}

func (worker *Worker) computeCapacity() int32 {
	pid := int32(os.Getpid())
	p, err := process.NewProcess(pid)
	if err != nil {
		log.Errorln("init process error")
		return 0
	}

	totalCPUPercents, err := cpu.Percent(0, false)
	if err != nil || len(totalCPUPercents) == 0 {
		log.Errorln("failed to get total cpu usage:", err)
		return 0
	}
	totalCPUPercent := totalCPUPercents[0]

	cpuPercent, err := p.CPUPercent()
	if err != nil {
		log.Errorln("failed to get cpu usage percent:", err)
		return 0
	}

	v, err := mem.VirtualMemory()
	if err != nil {
		log.Errorln("failed to get system memory usage:", err)
		return 0
	}
	info, err := p.MemoryInfo()
	if err != nil {
		log.Errorln("failed to get memory info:", err)
		return 0
	}

	log.Infoln("cpu: ", runtime.NumCPU(), totalCPUPercent, cpuPercent)
	log.Infoln("mem: ", v.Total, v.Used, info.RSS+info.Swap)
	capacityByCPU := int32(float64(worker.count+1) * (float64(runtime.NumCPU())*0.8*100 - totalCPUPercent) / (cpuPercent + 1))

	var total uint64
	total = v.Total
	if v.SwapTotal > 0 {
		total += v.SwapTotal
	}

	var used uint64
	unused := v.Available + v.SwapFree
	used = total - unused

	capacityByMem := int32(float64(worker.count+1) * (float64(total)*0.8 - float64(used)) / float64(info.RSS+info.Swap))

	log.Infoln("capacity: ", capacityByCPU, capacityByMem, " count:", worker.count)
	if capacityByCPU < capacityByMem {
		return capacityByCPU
	}
	return capacityByMem
}

func (worker *Worker) Heartbeat() {
	ticker := time.NewTicker(time.Second * 5)
	defer ticker.Stop()

	for range ticker.C {
		beat := &protocol.HeartbeatBody{
			Capacity: worker.computeCapacity(),
			Count:    worker.count,
		}
		msg := &protocol.Message{
			Action: protocol.ActionHeartbeat,
			Body:   beat,
		}
		_, err := worker.client.Request(msg)
		if err != nil {
			log.Errorln("failed to send heartbeat:", err, worker.client.Tag)
		}
		common.PrintLinkPoolCount()
	}
}

func (worker *Worker) processRequest(msg *protocol.Message) {
	var (
		err    error
		status uint8
		body   interface{}
	)

	switch msg.Action {
	case protocol.ActionTask:
		status, body = worker.processTask(msg)
	case protocol.ActionStopTask:
		status, body = worker.processStopTask(msg)
	default:
		status = protocol.StatusInvalidAction
		body = fmt.Sprintf("unknown action:%v", msg.Action)
	}
	log.Infoln("process request:", msg.ClientID, msg.ID, status, body)

	err = worker.reply(status, msg, body)
	if err != nil {
		log.Errorln("failed to reply:", err)
		worker.client.AddPendingResponse(status, msg, body)
	}
}

func (worker *Worker) processConnectionLost() endpoint.OnLostFunc {
	return func(name string) error {
		log.Infoln("connection lost")

		for {
			worker.client.Close()

			log.Infoln("reconnecting...")
			cli, err := newClient()
			if err != nil {
				time.Sleep(time.Second * 5)
				continue
			}
			cli.AddLostHandle(worker.processConnectionLost())
			cli.Run()

			worker.client.MoveTo(cli.Endpoint)
			worker.client = cli

			if worker.Register() {
				log.Infoln("send pending responses")
				cli.SendPendingResponses()
				break
			}
		}
		return nil
	}
}

func (worker *Worker) reply(status uint8, request *protocol.Message, body interface{}) error {
	return worker.client.Reply(status, request, body)
}

func (worker *Worker) Run() {
	log.Infoln("worker run")

	go worker.Heartbeat()
	go worker.watchFinishedTasks()

Exit:
	for {
		select {
		case msg, ok := <-worker.client.Requests():
			if ok {
				worker.processRequest(msg)
			} else {
				time.Sleep(time.Second * 1)
			}
		case <-worker.closeCh:
			break Exit
		}
	}
	worker.waitTaskFinish()
	worker.manager.producer.Close()
	log.Infoln("Worker Grace Exit")
}

func (worker *Worker) Stop() {
	worker.closeCh <- struct{}{}
}

func (worker *Worker) waitTaskFinish() {
	worker.manager.StopAllProcessor()
	for {
		count := worker.manager.Count()
		log.Infoln("processor left num:", count)
		if count == 0 {
			break
		}
		worker.manager.Dump()
		time.Sleep(1 * time.Second)
	}
}

func newClient() (*client.Client, error) {
	schedulerAddr := fmt.Sprintf("%v:%v", settings.Scheduler.Host, settings.Scheduler.Port)
	return client.NewClient(schedulerAddr)
}

func NewWorker(producer *stream.Producer, dbConnection *db.DBConnection,
	bucketSelect *tuchuang.BucketSelect) (*Worker, error) {

	cli, err := newClient()
	if err != nil {
		return nil, err
	}
	manager, err := newProcessorManager(producer, dbConnection, bucketSelect)
	if err != nil {
		return nil, err
	}

	worker := &Worker{
		client:   cli,
		tasks:    sync.Map{},
		messages: make(chan *protocol.Message, 100),
		manager:  manager,
		closeCh:  make(chan struct{}),
	}
	cli.AddLostHandle(worker.processConnectionLost())
	cli.Run()

	return worker, nil
}
