package api

import (
	"compress/gzip"
	"io"
	"io/ioutil"
	"net/http"
)

const MaxMemory int64 = 1 << 26 // 64MB

func getHttpBody(req *http.Request) []byte {
	if req.Body == nil {
		return []byte{}
	}

	var requestBody []byte
	safe := &io.LimitedReader{R: req.Body, N: MaxMemory}
	if req.Header.Get("Content-Encoding") == "gzip" {
		reader, err := gzip.NewReader(safe)
		if err != nil {
			return nil
		}
		requestBody, _ = ioutil.ReadAll(reader)
	} else {
		requestBody, _ = ioutil.ReadAll(safe)
	}

	req.Body.Close()
	return requestBody
}
