package filters

import (
	"encoding/json"
	"websec/common/schema"
	"websec/utils/stream"

	"github.com/sirupsen/logrus"
)

func (filter *Filter) processContentChange(msg *stream.Message) error {
	var doc = new(schema.RawContentChangeResult)
	err := json.Unmarshal(msg.Value, doc)
	if err != nil {
		logrus.Errorln("filter.processContentChange Error:", err)
		return err
	}

	logrus.Debugln("filter.processContentChange AddFinalContentChange:", doc.URL, doc.DiffPercent)
	filter.AddFinalContentChange(doc)
	return nil
}
