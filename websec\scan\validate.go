package scan

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"time"
	"websec/scan/rules"
	"websec/scan/validators"
	"websec/utils/log"

	"github.com/ugorji/go/codec"
)

type screenshotResult struct {
	Screenshot  string `codec:"screenshot"`
	Information string `codec:"information"`
}

var validatePayloadCommentPattern = regexp.MustCompile("(/[*].*?[*]/)")
var validateXSSPattern = regexp.MustCompile("(alert|prompt|confirm|found):")

func (scanner *WSScanner) validateSQLInjectVul(link *AffectLink, vul *rules.Vulnerability, foundVul *FoundVul) {
	var sqlmapFile = filepath.Join(scanner.Options.SqlmapPath, "sqlmap.py")
	params := []string{
		sqlmapFile, "--batch", "--flush-session", "--disable-coloring", "--random-agent", "--risk", "3", "--output-dir=/tmp/",
	}

	targetURL := link.URL
	if validatePayloadCommentPattern.MatchString(link.URL) {
		params = append(params, "--tamper", "space2comment")
	}
	if vul.VulXML == XMLSQLInjectionInPath {
		targetURL += "*"
	} else if link.Method == http.MethodPost {
		params = append(params, "--data", link.Data)
	}

	params = append(params, "-u", targetURL)

	if vul.VulXML == XMLSQLInjectionInsideCheck || vul.VulXML == XMLSQLInjection {
		field, ok := foundVul.Context["field"]
		if !ok {
			log.Errorln("!!! field not found.", vul.VulXML, foundVul.Context)
			return
		}
		params = append(params, "-p", field.(string))
	}

	log.Infoln("validating SQLInject:", strings.Join(params, " "))

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	var validationInfo string
	var isValid = 0

	cmd := exec.CommandContext(ctx, scanner.Options.PythonPath, params...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		validationInfo = "failed to validate:" + err.Error()
	} else {
		outputString := string(output)
		if strings.Contains(outputString, "sqlmap identified the following injection point(s)") {
			start := strings.Index(outputString, "---")
			end := strings.LastIndex(outputString, "---")
			if start < 0 || end < 0 || start == end {
				validationInfo = "failed to find two ---:" + outputString
			} else {
				validationInfo = outputString[start:end]
				isValid = 1
			}
		} else if strings.Contains(outputString, "all tested parameters appear to be not injectable") {
			validationInfo = "all tested parameters appear to be not injectable"
			isValid = 2
		} else {
			validationInfo = "\n" + outputString
		}
	}

	foundVul.Context["validation_info"] = validationInfo
	if isValid != 0 {
		foundVul.Context["is_valid"] = isValid == 1
	}
}

func (scanner *WSScanner) validateSQLInjectVulWithScreenshot(link *AffectLink, vul *rules.Vulnerability, foundVul *FoundVul, cookie []byte) {
	var sqlmapFile = filepath.Join(scanner.Options.SqlmapPath, "sqlmap.py")
	params := []string{
		sqlmapFile, "--batch", "--flush-session", "--disable-coloring", "--random-agent", "--risk", "3", "--output-dir=/tmp/",
	}

	targetURL := foundVul.VulURL
	if validatePayloadCommentPattern.MatchString(targetURL) {
		params = append(params, "--tamper", "space2comment")
	}
	if vul.VulXML == XMLSQLInjectionInPath {
		targetURL += "*"
	} else if link.Method == http.MethodPost {
		if len(link.Data) > 0 {
			params = append(params, "--data", link.Data)
		}
	}

	if len(cookie) > 0 {
		params = append(params, "--cookie", string(cookie))
	}

	params = append(params, "-u", targetURL)

	if vul.VulXML == XMLSQLInjectionInsideCheck || vul.VulXML == XMLSQLInjection {
		field, ok := foundVul.Context["field"]
		if !ok {
			log.Errorln("!!! field not found.", vul.VulXML, foundVul.Context)
			return
		}
		params = append(params, "-p", field.(string))
	}

	log.Infoln("validating SQLInject:", strings.Join(params, " "))

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	var validationInfo string
	var isValid = 0

	cmd := exec.CommandContext(ctx, scanner.Options.PythonPath, params...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		validationInfo = "failed to validate:" + err.Error()
	} else {
		outputString := string(output)
		if strings.Contains(outputString, "sqlmap identified the following injection point(s)") {
			start := strings.Index(outputString, "---")
			end := strings.LastIndex(outputString, "---")
			if start < 0 || end < 0 || start == end {
				validationInfo = "failed to find two ---:" + outputString
			} else {
				validationInfo = outputString[start:end]
				isValid = 1
				screenshotURL, err := scanner.takeShellScreenshot(strings.Join(cmd.Args, " "), outputString)
				if err != nil {
					foundVul.Context["screenshot_error"] = err.Error()
				} else if screenshotURL != "" {
					foundVul.Context["screenshot"] = screenshotURL
				}

				col := "back-end DBMS:"
				if strings.Contains(outputString[end:], col) {
					dbmsIndex := strings.Index(outputString, col)
					r := strings.NewReader(outputString[dbmsIndex:])
					b := bufio.NewReader(r)
					line, _, _ := b.ReadLine()
					if len(line) > 0 {
						dbName := strings.TrimPrefix(string(line), col)
						dbName = strings.TrimSpace(dbName)
						if len(dbName) > 0 {
							foundVul.Context["db_type"] = dbName
						}
					}
				}
			}
		} else if strings.Contains(outputString, "all tested parameters appear to be not injectable") {
			validationInfo = "all tested parameters appear to be not injectable"
			isValid = 2
		} else {
			validationInfo = "\n" + outputString
		}
	}

	foundVul.Context["validation_info"] = validationInfo
	if isValid != 0 {
		foundVul.Context["is_valid"] = isValid == 1
	}
}

func (scanner *WSScanner) takeShellScreenshot(cmdString, cmdOutput string, highlights ...string) (string, error) {
	var jh codec.JsonHandle
	var shellJSFile = scanner.Options.SqlmapScreenshotPath
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()

	var highlight string
	if len(highlights) > 0 {
		highlight = highlights[0]
	}
	cmd := exec.CommandContext(ctx, scanner.Options.PhantomJSPath, shellJSFile, cmdString, cmdOutput, highlight)
	output, err := cmd.CombinedOutput()

	dec := codec.NewDecoderBytes(output, &jh)
	result := screenshotResult{}
	err = dec.Decode(&result)
	if err != nil {
		return "", err
	}
	if result.Screenshot != "" {
		return scanner.bucketSelect.UploadImgFile(result.Screenshot, "")
	}
	return "", nil
}

func (scanner *WSScanner) validateXSSVul(link *AffectLink, vul *rules.Vulnerability, foundVul *FoundVul) {
	var xssJSFile = scanner.Options.XSSJSPath

	method, ok := foundVul.Context["method"]
	if !ok {
		log.Errorln("failed to get `method` from vul context.")
		return
	}
	params := []string{
		"--ssl-protocol=any", xssJSFile, method.(string), foundVul.VulURL,
	}

	log.Infoln("validating XSS:", strings.Join(params, " "))

	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()

	var validationInfo string
	var isValid = 0

	cmd := exec.CommandContext(ctx, scanner.Options.PhantomJSPath, params...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		validationInfo = "failed to validate:" + err.Error()
	} else {
		outputString := string(output)
		if validateXSSPattern.MatchString(outputString) {
			validationInfo = outputString
			isValid = 1
		} else {
			validationInfo = "no alert/prompt/confirm"
			isValid = 2
		}
	}

	foundVul.Context["validation_info"] = validationInfo
	if isValid != 0 {
		foundVul.Context["is_valid"] = isValid == 1
	}
}

func (scanner *WSScanner) validateXSSVulWithScreenshot(link *AffectLink, vul *rules.Vulnerability, foundVul *FoundVul) {
	var jh codec.JsonHandle
	var xssJSFile = scanner.Options.XSSJSScreenshotPath

	method, ok := foundVul.Context["method"]
	if !ok {
		log.Errorln("failed to get `method` from vul context.")
		return
	}
	params := []string{
		"--ssl-protocol=any", xssJSFile, method.(string), foundVul.VulURL,
	}

	log.Infoln("validating XSS:", strings.Join(params, " "))

	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()

	var validationInfo string
	var isValid = 0

	cmd := exec.CommandContext(ctx, scanner.Options.PhantomJSPath, params...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		validationInfo = "failed to validate:" + err.Error()
	} else if len(output) > 0 {
		dec := codec.NewDecoderBytes(output, &jh)
		result := screenshotResult{}
		err = dec.Decode(&result)
		if err != nil {
			validationInfo = "failed to parse output: " + string(output)
		} else {
			isValid = 1
			validationInfo = result.Information
			if result.Screenshot != "" {
				screenShotURL, err := scanner.bucketSelect.UploadImgFile(result.Screenshot, "")
				if err != nil {
					foundVul.Context["screenshot_error"] = err.Error()
				} else if screenShotURL != "" {
					foundVul.Context["screenshot"] = screenShotURL
				}
			}
		}
	} else {
		validationInfo = "no alert/prompt/confirm"
		isValid = 2
	}

	foundVul.Context["validation_info"] = validationInfo
	if isValid != 0 {
		foundVul.Context["is_valid"] = isValid == 1
	}
}

func (scanner *WSScanner) validateFoundVul(foundVul *FoundVul) {
	var (
		link = foundVul.Link
		vul  = foundVul.Vul
	)
	var body interface{}
	body, ok := foundVul.Context["body"]
	if !ok {
		body = []byte{}
	}
	result, err := validators.Validate(&validators.ValidationArgs{
		VulXML: vul.VulXML,
		Method: link.Method,
		URL:    link.URL,
		VulURL: foundVul.VulURL,
		Body:   body.([]byte),
	})
	if err != nil {
		log.Errorln("failed to validate vul:", vul, err)
		foundVul.Context["validation_error"] = err.Error()
		return
	}
	if result != nil {
		switch result.Status {
		case validators.VulIsValid:
			foundVul.Context["is_valid"] = true
			if result.NeedSnapshot {
				screenshotURL, err := scanner.takeShellScreenshot(result.Command, result.Output, result.Highlight)
				if err != nil {
					foundVul.Context["screenshot_error"] = err.Error()
				} else if screenshotURL != "" {
					foundVul.Context["screenshot"] = screenshotURL
				}
			}
		case validators.VulIsInvalid:
			foundVul.Context["is_valid"] = false
		}
		foundVul.Context["validation_info"] = result.Output
	}
}

func (scanner *WSScanner) scanCookieInjectFoundVul(link *AffectLink) error {
	if scanner.specificVulXMLs != nil {
		if _, ok := scanner.specificVulXMLs["Cookies_Sql_Injection.xml"]; !ok {
			log.Info("no need to scan cookie sqlInject")
			return errors.New("no need to scan cookie sqlInject")
		}
	}

	var sqlmapFile = filepath.Join(scanner.Options.SqlmapPath, "sqlmap.py")
	var failedCount int64
	params := []string{
		sqlmapFile, "--batch", "--flush-session", "--disable-coloring", "--random-agent", "--risk",
		"3", "--output-dir=/tmp/", "--level=3",
	}

	targetURL := link.URL
	if validatePayloadCommentPattern.MatchString(link.URL) {
		params = append(params, "--tamper", "space2comment")
	}

	u, err := url.Parse(link.URL)
	if err != nil {
		log.Errorln("failed to parse URL:", link.URL)
		return errors.New("no args to scan cookie sqlInject")
	}

	targetURL = u.Scheme + "://" + u.Host + u.Path

	var cookieArgs string
	if link.Method == http.MethodPost {
		cookieArgs = link.Data
	} else {
		cookieArgs = u.RawQuery
	}

	params = append(params, "-u", targetURL)

	valueList := strings.Split(cookieArgs, "&")

	for _, value := range valueList {
		kvSlice := strings.Split(value, "=")
		if len(kvSlice) != 2 {
			continue
		}
		keyField := kvSlice[0]
		commandParmas := []string{}
		commandParmas = append(commandParmas, params...)
		commandParmas = append(commandParmas, "--cookie", value)
		commandParmas = append(commandParmas, "-p", keyField)

		log.Infoln("scan CookieInject:", strings.Join(commandParmas, " "))
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
		defer cancel()

		var validationInfo string
		var isValid = 0

		notExistString := fmt.Sprintf("Cookie parameter '%v' does not seem to be injectable", keyField)

		cmd := exec.CommandContext(ctx, scanner.Options.PythonPath, commandParmas...)
		output, err := cmd.CombinedOutput()
		if err != nil {
			validationInfo = "failed to validate:" + err.Error()
			failedCount++
		} else {
			outputString := string(output)
			if strings.Contains(outputString, "sqlmap identified the following injection point(s)") {
				start := strings.Index(outputString, "---")
				end := strings.LastIndex(outputString, "---")
				if start < 0 || end < 0 || start == end {
					validationInfo = "failed to find two ---:" + outputString
				} else {
					validationInfo = outputString[start:end]
					isValid = 1
				}
			} else if strings.Contains(outputString, notExistString) {
				validationInfo = "all tested parameters appear to be not injectable"
				isValid = 2
			} else {
				validationInfo = "\n" + outputString
			}
		}

		log.Info("cookie valid info", validationInfo)

		if isValid != 1 {
			continue
		}

		cookieVul := rules.Vulnerability{
			Name:     "cookie sqlinject",
			VulXML:   "Cookies_Sql_Injection.xml",
			Severity: "high",
		}

		foundVul := FoundVul{
			Link:     link,
			Vul:      &cookieVul,
			VulURL:   link.URL,
			Severity: cookieVul.Severity,
		}

		result := &ScanResult{
			RequestCount: 1,
			ErrorCount:   failedCount,
			FoundVuls:    []*FoundVul{&foundVul},
		}

		scanner.outputResult(result)

		return nil
	}

	return errors.New("not found cookies sqlinject")

}
