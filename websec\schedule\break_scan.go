package schedule

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/semaphore"

	"github.com/go-redis/redis"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type BreakScan struct {
	stopAction map[string]*schema.BreakScanAction
	lock       sync.RWMutex

	scheduler *Scheduler

	wg          sync.WaitGroup
	concurrency *semaphore.Weighted
}

func NewBreakScan(scheduler *Scheduler) *BreakScan {
	b := &BreakScan{
		scheduler:   scheduler,
		concurrency: semaphore.NewWeighted(20),
		stopAction:  make(map[string]*schema.BreakScanAction),
	}
	b.loadBreakScanTask()
	return b
}

func (b *BreakScan) getAction(assetID string) *schema.BreakScanAction {
	b.lock.RLock()
	defer b.lock.RUnlock()

	if v, ok := b.stopAction[assetID]; ok {
		return v
	}
	return nil
}

func (b *BreakScan) IsStop(assetID string) bool {
	action := b.getAction(assetID)
	if action != nil && action.Action == consts.ActionStop {
		return true
	}
	return false
}

func (b *BreakScan) RunStopTodoTask() {
	t := time.NewTicker(5 * time.Second)
	for {
		select {
		case <-t.C:
			b.stopTodoTask()
			b.loadBreakScanTask()
			b.scanAtOnce()
		}
	}
}

func (b *BreakScan) stopTodoTask() {
	tmp := make(map[string]*schema.BreakScanAction)
	b.lock.RLock()
	for k, v := range b.stopAction {
		tmp[k] = v
	}
	b.lock.RUnlock()

	for k, v := range tmp {
		if v.Done == 0 {
			if err := b.concurrency.Acquire(context.Background(), 1); err == nil {
				b.wg.Add(1)
				go b.doTask(k, v)
			}
		}
	}
	b.wg.Wait()
}

func (b *BreakScan) doTask(assetID string, scan *schema.BreakScanAction) {
	defer func() {
		b.concurrency.Release(1)
		b.wg.Done()
	}()

	if scan.Action == consts.ActionStop {
		b.stopTask(assetID, scan)
	}
}

func (b *BreakScan) stopTask(assetID string, scan *schema.BreakScanAction) error {
	log.Infoln("breakscan begin stop task", assetID)

	asset, err := b.scheduler.dbConnection.GetAssetByID(assetID, true)
	if err != nil {
		log.Errorln(err)
		return err
	}
	log.Info("stop task", assetID, asset.Options.ScheduleTag, b.scheduler.tag)

	if asset.Options.ScheduleTag != b.scheduler.tag {
		return nil
	}

	objectID, err := primitive.ObjectIDFromHex(assetID)

	if err != nil {
		return err
	}

	defer func() {
		scan.Done = 1
		b.scheduler.dbConnection.HSetObject(consts.RedisBreakContinueScan, assetID, scan)
	}()

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	taskTypeList := []string{consts.TaskTypeCrawl, consts.TaskTypeScan}
	for _, taskType := range taskTypeList {
		task := new(schema.Task)
		err = b.scheduler.dbConnection.GetMongoDatabase().Collection(consts.CollectionTasks).FindOne(ctx,
			bson.M{
				"asset_id": objectID,
				"type":     taskType,
				"status":   consts.TaskStatusRunning,
			}).Decode(task)

		if err != nil {
			log.Warnln("breakscan get from mongo error:", err, taskType)
			continue
		}

		log.Info("task.track", task.Track, task.Schedule)

		worker := workers.GetWorkerByAddress(task.Track.Address)
		if worker == nil || worker.Closed() || !worker.ConnectionAliveBool() {
			log.Warnln("breakscan worker nil", assetID)
			return nil
		}

		if !worker.ExistTask(task.ID) {
			log.Warnln("breakscan task finished", assetID, task.ID)
			continue
		}

		err = worker.StopTask(task)
		if err != nil {
			log.Warnln("breakscan stop task result failed:", assetID, err)
			continue
		}
	}

	log.Infoln("breakscan stop task result success", assetID)
	return nil
}

func (b *BreakScan) scanAtOnce() {
	res, err := b.scheduler.dbConnection.HGetAll(consts.RedisStartAtOnceScan)
	if err != nil {
		if err != redis.Nil {
			log.Errorln(err)
		}
		return
	}

	for k := range res {
		b.todoTask(k)
	}
}

func (b *BreakScan) todoTask(assetID string) {
	log.Infoln("breakscan begin todo task", assetID)

	asset, err := b.scheduler.dbConnection.GetAssetByID(assetID, true)
	if err != nil {
		log.Errorln(err)
		return
	}

	if asset.Options.ScheduleTag != b.scheduler.tag {
		return
	}

	objectID, err := primitive.ObjectIDFromHex(assetID)

	if err != nil {
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	_, err = b.scheduler.dbConnection.GetMongoDatabase().Collection(consts.CollectionTasks).FindOne(ctx,
		bson.M{
			"asset_id":     objectID,
			"schedule.tag": consts.TaskTagScanAtOnce,
		}).DecodeBytes()
	if err != nil && err != mongo.ErrNoDocuments {
		log.Errorln(err)
		return
	}

	if err == nil {
		log.Warnln("atonce scan task existed:", assetID)
		b.scheduler.dbConnection.HDel(consts.RedisStartAtOnceScan, assetID)
		return
	}

	task := b.scheduler.generateTaskAtOnceScan(asset)
	_, err = b.scheduler.dbConnection.GetMongoDatabase().Collection(consts.CollectionTasks).InsertOne(ctx, task)
	if err != nil {
		log.Errorln(err)
		return
	}

	b.scheduler.dbConnection.HDel(consts.RedisStartAtOnceScan, assetID)
}

func (b *BreakScan) loadBreakScanTask() {
	res, err := b.scheduler.dbConnection.HGetAll(consts.RedisBreakContinueScan)
	if err != nil {
		if err != redis.Nil {
			log.Errorln(err)
		}
		return
	}

	tmp := make(map[string]*schema.BreakScanAction)
	buff := new(bytes.Buffer)
	for k, v := range res {
		doc := new(schema.BreakScanAction)
		err = json.Unmarshal([]byte(v), doc)
		if err != nil {
			log.Errorln(err)
			continue
		}
		fmt.Fprintf(buff, "[host %s Action %d, Done %d] ", k, doc.Action, doc.Done)
		tmp[k] = doc
	}

	b.lock.Lock()
	b.stopAction = tmp
	b.lock.Unlock()

	log.Infoln("load breakscan ", buff.String())
	buff.Reset()
}
