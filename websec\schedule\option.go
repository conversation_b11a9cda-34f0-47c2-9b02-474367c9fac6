package schedule

import (
	"websec/common/db"
	"websec/utils/semaphore"

	"go.mongodb.org/mongo-driver/mongo"
)

type optionFunc func(*Scheduler) error

func WithMongo(db *mongo.Database) optionFunc {
	return func(scheduler *Scheduler) error {
		scheduler.mongodb = db
		return nil
	}
}

func WithDBConnection(connection *db.DBConnection) optionFunc {
	return func(scheduler *Scheduler) error {
		scheduler.dbConnection = connection
		return nil
	}
}

func WithMaxTaskCount(count int) optionFunc {
	return func(scheduler *Scheduler) error {
		scheduler.maxTaskCount = int32(count)
		return nil
	}
}

func WithTag(tag string) optionFunc {
	return func(scheduler *Scheduler) error {
		scheduler.tag = tag
		return nil
	}
}

func WithDispatchConcurrency(concurrency int64) optionFunc {
	return func(scheduler *Scheduler) error {
		scheduler.dispatchSema = semaphore.NewWeighted(concurrency)
		return nil
	}
}
