package matcher

import (
	"fmt"
	"sync"
	"time"
	"websec/common/resource"
	"websec/utils"
	"websec/utils/acsmx"
	"websec/utils/log"
)

type BlackWordMatcherManager struct {
	matchers  sync.Map
	existTime int //matcher exist time
}

type blackWordMatcher struct {
	matcher *acsmx.Matcher
	timeOut time.Time //超时重新生成时间
	md5     string    //唯一hash，根据自定义黑链来生成
}

func NewBlackWordMatcherManager(sec int) *BlackWordMatcherManager {
	s := &BlackWordMatcherManager{
		matchers:  sync.Map{},
		existTime: sec,
	}
	go s.timerDelete()
	return s
}

func (s *BlackWordMatcherManager) timerDelete() {
	t := time.NewTicker(60 * time.Second)
	for {
		select {
		case <-t.C:
			needDel := make([]string, 0, 1)
			s.matchers.Range(func(key, value interface{}) bool {
				host := key.(string)
				matcher := value.(*blackWordMatcher)
				if matcher.timeOut.Before(time.Now()) {
					needDel = append(needDel, host)
				}
				return true
			})

			for _, v := range needDel {
				s.matchers.Delete(v)
				log.Infof("BlackLinkMatcherManager del %s timeEnd", v)
			}
		}
	}
}

func (s *BlackWordMatcherManager) GetCustomizedBlackWordMatcher(host string, customized []string) *acsmx.Matcher {
	v, ok := s.matchers.Load(host)
	newMd5 := utils.Md5(fmt.Sprintf("%v", customized))
	var bmatcher *blackWordMatcher
	if ok {
		bmatcher = v.(*blackWordMatcher)
		if bmatcher.md5 == newMd5 {
			bmatcher.timeOut = time.Now().Add(time.Duration(s.existTime) * time.Second)
			return bmatcher.matcher
		}
	}

	bmatcher = &blackWordMatcher{
		md5:     newMd5,
		timeOut: time.Now().Add(time.Duration(s.existTime) * time.Second),
		matcher: newBlackWordMatcher(customized),
	}

	s.matchers.Store(host, bmatcher)
	return bmatcher.matcher
}

func newBlackWordMatcher(customized []string) *acsmx.Matcher {
	matcher := acsmx.NewMatcher()
	flag := false
	defaultBlackWords := resource.DefaultBlackWords()
	for _, word := range customized {
		if !defaultBlackWords.Contains(word) {
			matcher.AddPatternString(word)
			flag = true
		}
	}
	if flag {
		for word := range defaultBlackWords {
			matcher.AddPatternString(word)
		}
		matcher.Compile()
	} else {

		matcher = resource.DefaultBlackWordsMatcher()
	}

	return matcher
}
