package ocr

import (
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"strings"
	"websec/utils/log"
)

type OcrRequestV1 struct {
	ObjectType        string `url:"object_type"`
	DispLinePoly      bool   `url:"disp_line_poly"`
	Type              string `url:"type"`
	LanguageType      string `url:"languagetype"`
	EngGranularity    string `url:"eng_granularity"`
	LineProbability   bool   `url:"line_probability"`
	ImgDirection      string `url:"imgDirection"`
	DispParagraphPoly bool   `url:"disp_paragraph_poly"`
	Image             string `url:"image"`
}

func (req *OcrRequestV1) SetImageRaw(imageBytes []byte) {
	imageB64 := base64.StdEncoding.EncodeToString(imageBytes)
	req.Image = imageB64
}

func (req OcrRequestV1) Serialize() (string, error) {
	v := reflect.ValueOf(req)
	if v.Kind() != reflect.Struct {
		return "", fmt.Errorf("data is not a struct")
	}

	var s string
	t := v.Type()
	for i := 0; i < v.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i).Interface()
		tag := field.Tag.Get("url")
		if strings.TrimSpace(tag) == "" {
			continue
		}
		s += fmt.Sprintf("%s=%v", tag, value)
		if i != v.NumField()-1 {
			s += "&"
		}
	}

	data := base64.StdEncoding.EncodeToString([]byte(s))
	m := map[string]interface{}{
		"data": data,
	}

	jsonData, err := json.Marshal(m)
	if err != nil {
		log.Errorf("------------ serialize ocr request error: %v", err)
		log.Errorf("------------ endoceToString: %v", data)
		return "", err
	}

	return string(jsonData), nil
}

func NewOcrRequestV1() *OcrRequestV1 {
	return &OcrRequestV1{
		ObjectType:        "general_v5",
		DispLinePoly:      true,
		Type:              "st_ocrapi",
		LanguageType:      "CHN_ENG",
		EngGranularity:    "word",
		LineProbability:   true,
		ImgDirection:      "setImgDirFlag",
		DispParagraphPoly: false,
	}
}

type Rect struct {
	Left   int `json:"left"`
	Top    int `json:"top"`
	Width  int `json:"width"`
	Height int `json:"height"`
}

type WordRect struct {
	Word string `json:"word"`
	Rect Rect   `json:"rect"`
}

type OcrResultV1 struct {
	LogId  string     `json:"log_id"`
	ErrNo  int        `json:"err_no"`  // 0
	ErrMsg string     `json:"err_msg"` // SUCCESS
	Ret    []WordRect `json:"ret"`
}

type OcrHTTPResponseV1 struct {
	UniNetBody OcrHTTPResultBody `json:"UNI_NET_BODY"`
	UniNetHead OcrHTTPResultHead `json:"UNI_NET_HEAD"`
}

type OcrHTTPResultBody struct {
	ErrNo      int    `json:"err_no"`
	ErrMsg     string `json:"err_msg"`
	Result     string `json:"result"`
	CalcTimeMs int    `json:"calc_time_ms"`
	WaitTimeMs int    `json:"wait_time_ms"`
}

func (b *OcrHTTPResultBody) UnmarshalJSON(data []byte) error {
	// Check if the incoming JSON is a string containing the serialized object
	if len(data) > 0 && data[0] == '"' {
		var innerStr string
		if err := json.Unmarshal(data, &innerStr); err != nil {
			return err
		}
		return json.Unmarshal([]byte(innerStr), b)
	}
	// Otherwise, unmarshal directly as a JSON object
	type Alias OcrHTTPResultBody
	var a Alias
	if err := json.Unmarshal(data, &a); err != nil {
		return err
	}
	*b = OcrHTTPResultBody(a)
	return nil
}

type OcrHTTPResultHead struct {
	RespCode  string `json:"RESP_CODE"`
	RespDesc  string `json:"RESP_DESC"`
	Timestamp string `json:"TIMESTAMP"`
}

const (
	ErrNoSuccess  = 0
	ErrMsgSuccess = "SERVER_SUCCESS"
)

func NewOcrResultV1FromResponse(body []byte) (*OcrResultV1, error) {
	var resp OcrHTTPResponseV1
	err := json.Unmarshal(body, &resp)
	if err != nil {
		log.Infoln("------------ v1.go NewOcrResultV1FromResponse json.Unmarshal error ", err)
		return nil, err
	}

	if resp.UniNetBody.ErrNo != ErrNoSuccess {
		log.Infoln("------------ v1.go NewOcrResultV1FromResponse resp.UniNetBody.ErrNo != ErrNoSuccess", resp.UniNetBody.ErrNo, resp.UniNetBody.ErrMsg)
		return nil, fmt.Errorf("ocr error: %s", resp.UniNetBody.ErrMsg)
	}

	var result OcrResultV1

	decoded, err := base64.StdEncoding.DecodeString(resp.UniNetBody.Result)
	if err != nil {
		log.Infoln("------------ v1.go NewOcrResultV1FromResponse base64.StdEncoding.DecodeString error ", err)
		return nil, err
	}

	err = json.Unmarshal(decoded, &result)
	if err != nil {
		log.Infoln("------------ v1.go NewOcrResultV1FromResponse json.Unmarshal error ", err)
		return nil, err
	}

	return &result, nil
}

type OCRClientV1 struct {
	URL string
}

func (c *OCRClientV1) OCRRequest(img []byte) (*OcrResultV1, error) {
	req := NewOcrRequestV1()
	req.SetImageRaw(img)
	data, err := req.Serialize()
	if err != nil {
		return nil, err
	}

	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// golang  ternary operation  data[:len(data) > 100 ? 100 : len(data)]
	strlen := len(data)
	if strlen > 120 {
		strlen = 120
	}

	resp, err := client.Post(c.URL, "application/json", strings.NewReader(data))
	log.Infoln("------------ v1.go OCRRequest client.Post to URL", c.URL, " JSON payload truncated", data[:strlen], "...")

	if err != nil {
		log.Errorln("------------ v1.go OCRRequest client.Post error ", err)
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorln("------------ v1.go OCRRequest io.ReadAll error ", err)
		return nil, err
	}

	return NewOcrResultV1FromResponse(body)
}

var defaultOCRClient *OCRClientV1

func SetDefaultOCRClient(client *OCRClientV1) {
	defaultOCRClient = client
}

func GetDefaultOCRClient() *OCRClientV1 {
	return defaultOCRClient
}

func DoOCRV1(img []byte) (*OcrResultV1, error) {
	if defaultOCRClient == nil {
		return nil, fmt.Errorf("default ocr client is not set")
	}

	return defaultOCRClient.OCRRequest(img)
}
