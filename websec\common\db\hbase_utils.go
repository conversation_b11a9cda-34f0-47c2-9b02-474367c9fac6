package db

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils"
	"websec/utils/hbase"
	"websec/utils/log"

	"github.com/tsuna/gohbase/hrpc"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	headers = map[string][]string{
		"data": []string{"content"},
	}
	ErrNotUseHBase = errors.New("not use hbase")
	ErrNotFound    = errors.New("not found sourcecode")
)

func GenerateHBaseContentKey(rawURL, urlHash string, timestamp int64) string {
	return fmt.Sprintf("%s_%s_%d", utils.GetReverseDomain(rawURL), urlHash, timestamp)
}

func (d *DBConnection) GetHBaseContent(jobID, rawURL, urlHash string, timestamp int64) ([]byte, error) {

	if timestamp != 0 {
		return d.getHBaseContent(jobID, rawURL, timestamp)
	}
	return d.getLastHBaseContentFromMongo(jobID, rawURL)
}

func (d *DBConnection) GetOldReferPageContent(jobID, assetID, host, rawURL, urlHash string, timestamp int64) ([]byte, error) {
	snapshotType, err := d.getContentChangeSnapshotType(jobID, assetID, host)

	if err != nil {
		return nil, err
	}

	if isStaticSnapshot(snapshotType) {
		return d.getLastWebpageSnapshotFromMongo(jobID, rawURL)
	} else {
		if timestamp != 0 {
			return d.getHBaseContent(jobID, rawURL, timestamp)
		}
	}

	return nil, errors.New("cannot find the page content by " + jobID + " " + assetID + " " + rawURL)
}

func (d *DBConnection) GetWebpageSnapshotContent(jobID, rawURL string) ([]byte, error) {
	return d.getLastWebpageSnapshotFromMongo(jobID, rawURL)
}

func (d *DBConnection) GetWebpageContentHash(jobID, rawURL string) (string, error) {
	return d.getLastWebpageContentHashFromMongo(jobID, rawURL)
}

func (d *DBConnection) GetHBaseContentHash(jobID, rawURL string) (string, error) {
	return d.getLastHBaseContentHashFromMongo(jobID, rawURL)
}

func (d *DBConnection) GetLastHBaseContent(jobID string, rawURL string) ([]byte, error) {
	return d.getLastHBaseContentFromMongo(jobID, rawURL)
}

func (d *DBConnection) GetLastWebpageSnapshotContent(jobID string, rawURL string) ([]byte, error) {
	return d.getLastWebpageSnapshotFromMongo(jobID, rawURL)
}

func (d *DBConnection) SetHBaseContent(docs []*schema.PageArchiveHBase) error {
	if d.hBaseConfig.ClientType == HBaseOriginal {
		return d.setHBaseContentWithOriginal(docs)
	} else if d.hBaseConfig.ClientType == HBaseThrift {
		return d.setHBaseContentWithThrift(docs)
	} else {
		return ErrNotUseHBase
	}
}

func (d *DBConnection) getHBaseContentWithOriginal(rawURL, urlHash string, timestamp int64) ([]byte, error) {
	key := GenerateHBaseContentKey(rawURL, urlHash, timestamp)
	content, err := d.GetSourceCode(key)
	if err == nil {
		return content, nil
	} else {
		log.Warnln("get redis failed:", err)
	}

	content, err = d.getHBaseContentFromMongo(key)
	if err == nil {
		return content, nil
	} else {
		log.Warnln("get sourcecode from mongo failed:", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	get, err := hrpc.NewGetStr(ctx, consts.CollectionHBaseContent, key, hrpc.Families(headers))
	if err != nil {
		log.Errorln(err)
		return nil, err
	}

	rsp, err := d.hBaseClient.Get(get)
	if err != nil {
		log.Errorln("hbase go client error:", err)
		return nil, err
	}

	if len(rsp.Cells) == 0 {
		log.Warnln("rsp Cells len == 0")
		return nil, errors.New("rsp Cells len == 0")
	}

	return rsp.Cells[0].Value, nil
}

func (d *DBConnection) getHBaseContentWithThrift(rawURL, urlHash string, timestamp int64) ([]byte, error) {
	key := GenerateHBaseContentKey(rawURL, urlHash, timestamp)
	content, err := d.GetSourceCode(key)
	if err == nil {
		return content, nil
	} else {
		log.Warnln("get sourcecode from redis failed:", err)
	}

	content, err = d.getHBaseContentFromMongo(key)
	if err == nil {
		return content, nil
	} else {
		log.Warnln("get sourcecode from mongo failed:", err)
	}

	result, err := d.hBaseThriftClient.Get([]byte(consts.HBaseContentTableProduct), []byte(key), []byte("data:content"))
	if err != nil {
		log.Errorln("hbase thrift client error:", err)
		return nil, err
	}

	if len(result) == 0 {
		log.Warnln("thrift hbase get result len == 0")
		return nil, errors.New("thrift hbase get result len == 0")
	}

	return result[0].Value, nil
}

func (d *DBConnection) getHBaseContent(jobID, rawURL string, timestamp int64) ([]byte, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var doc schema.PageArchiveHBase

	filter := bson.M{
		"job_id":       jobID,
		"url":          rawURL,
		"version_time": timestamp,
	}
	opts := options.FindOne()
	err := d.GetMongoDatabase().Collection(consts.CollectionHBaseContent).FindOne(ctx, filter, opts).Decode(&doc)
	if err != nil {
		log.Errorln(err, jobID, rawURL, timestamp)
		return nil, err
	}

	return doc.Content, nil
}

func (d *DBConnection) setHBaseContentWithOriginal(docs []*schema.PageArchiveHBase) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	for _, doc := range docs {
		key := GenerateHBaseContentKey(doc.URL, doc.URLHash, doc.VersionTime)
		doc.RowKey = key
		putStr, err := hrpc.NewPutStr(ctx, consts.CollectionHBaseContent, key, generateHBaseMessage(doc))
		if err != nil {
			log.Errorf("NewPutStr error key %s", key)
			return err
		}
		_, err = d.hBaseClient.Put(putStr)
		if err == nil {
			log.Infoln("save to hbase success, key ", key)
		} else {
			return err
		}
	}

	return nil
}

func (d *DBConnection) setHBaseContentWithThrift(docs []*schema.PageArchiveHBase) error {
	batch := make([]*hbase.BatchMutation, 0, len(docs))
	for _, doc := range docs {
		key := GenerateHBaseContentKey(doc.URL, doc.URLHash, doc.VersionTime)
		doc.RowKey = key
		mutations := make([]*hbase.Mutation, 0, 10)
		mutations = append(mutations, &hbase.Mutation{
			Column: []byte("info:url"),
			Value:  []byte(doc.URL),
		})

		mutations = append(mutations, &hbase.Mutation{
			Column: []byte("info:content_hash"),
			Value:  []byte(doc.ContentHash),
		})

		mutations = append(mutations, &hbase.Mutation{
			Column: []byte("info:header"),
			Value:  []byte(doc.Header),
		})

		mutations = append(mutations, &hbase.Mutation{
			Column: []byte("info:old_version_time"),
			Value:  []byte(strconv.FormatInt(doc.OldVersionTime, 10)),
		})

		mutations = append(mutations, &hbase.Mutation{
			Column: []byte("info:host"),
			Value:  []byte(doc.Host),
		})

		mutations = append(mutations, &hbase.Mutation{
			Column: []byte("info:main_frame_url"),
			Value:  []byte(doc.MainFrameURL),
		})

		mutations = append(mutations, &hbase.Mutation{
			Column: []byte("info:status_code"),
			Value:  []byte(strconv.FormatInt(int64(doc.StatusCode), 10)),
		})

		mutations = append(mutations, &hbase.Mutation{
			Column: []byte("data:content"),
			Value:  doc.Content,
		})

		mutation := &hbase.BatchMutation{
			Row:       []byte(key),
			Mutations: mutations,
		}
		batch = append(batch, mutation)
	}

	err := d.hBaseThriftClient.MutateRows([]byte(consts.HBaseContentTableProduct), batch)
	if err != nil {
		log.Errorf("save to hbase failed:%v", err)
	} else {
		log.Infoln("save to hbase success ")
	}
	return err
}

func generateHBaseMessage(val *schema.PageArchiveHBase) map[string]map[string][]byte {
	values := make(map[string]map[string][]byte)
	values["info"] = make(map[string][]byte)
	values["info"]["content_hash"] = []byte(val.ContentHash)
	values["info"]["url"] = []byte(val.URL)
	values["info"]["header"] = []byte(val.Header)
	values["info"]["old_version_time"] = []byte(strconv.FormatInt(val.OldVersionTime, 10))
	values["info"]["host"] = []byte(val.Host)
	values["info"]["job_id"] = []byte(val.JobID)
	values["info"]["status_code"] = []byte(strconv.FormatInt(int64(val.StatusCode), 10))
	values["info"]["main_frame_url"] = []byte(val.MainFrameURL)
	values["data"] = make(map[string][]byte)
	values["data"]["content"] = val.Content
	return values
}

func (d *DBConnection) getHBaseContentFromMongo(key string) ([]byte, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var doc schema.PageArchiveHBase
	err := d.GetMongoDatabase().Collection(consts.CollectionHBaseContent).FindOne(ctx, bson.M{
		"row_key": key,
	}).Decode(&doc)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}
	return doc.Content, nil
}

func (d *DBConnection) getWebpageSnapshotContentFromMongo(jobID string, rawURL string, urlHash string, timestamp int64) ([]byte, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var doc schema.WebpageSnapshot
	err := d.GetMongoDatabase().Collection(consts.CollectionWebpageSnapshot).FindOne(ctx, bson.M{
		"job_id":   jobID,
		"url":      rawURL,
		"url_hash": urlHash,
	}).Decode(&doc)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}
	return doc.Content, nil
}

func (d *DBConnection) getLastHBaseContentFromMongo(jobID, rawURL string) ([]byte, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var doc schema.PageArchiveHBase
	filter := bson.M{
		"job_id": jobID,
		"url":    rawURL,
	}
	opts := options.FindOne().SetSort(bson.D{{"version_time", -1}})
	err := d.GetMongoDatabase().Collection(consts.CollectionHBaseContent).FindOne(ctx, filter, opts).Decode(&doc)
	if err != nil {
		log.Errorln(err, jobID, rawURL)
		return nil, err
	}
	return doc.Content, nil
}

func (d *DBConnection) getLastWebpageSnapshotFromMongo(jobID, rawURL string) ([]byte, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var doc schema.WebpageSnapshot

	filter := bson.M{
		"job_id": jobID,
		"url":    rawURL,
	}
	opts := options.FindOne().SetSort(bson.D{{"version_time", -1}})
	err := d.GetMongoDatabase().Collection(consts.CollectionWebpageSnapshot).FindOne(ctx, filter, opts).Decode(&doc)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}

	return doc.Content, nil
}

func (d *DBConnection) getLastWebpageContentHashFromMongo(jobID, rawURL string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var doc schema.WebpageSnapshot
	filter := bson.M{
		"job_id": jobID,
		"url":    rawURL,
	}
	opts := options.FindOne().SetSort(bson.D{{"version_time", -1}})
	err := d.GetMongoDatabase().Collection(consts.CollectionWebpageSnapshot).FindOne(ctx, filter, opts).Decode(&doc)
	if err != nil {
		log.Errorln(err)
		return "", err
	}
	return doc.ContentHash, nil
}

func (d *DBConnection) getLastHBaseContentHashFromMongo(jobID, rawURL string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var doc schema.WebpageSnapshot
	filter := bson.M{
		"job_id": jobID,
		"url":    rawURL,
	}
	opts := options.FindOne().SetSort(bson.D{{"version_time", -1}})
	err := d.GetMongoDatabase().Collection(consts.CollectionHBaseContent).FindOne(ctx, filter, opts).Decode(&doc)
	if err != nil {
		log.Errorln(err)
		return "", err
	}
	return doc.ContentHash, nil
}

func (d *DBConnection) getContentChangeSnapshotType(jobID, assetID, host string) (int, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	objID, err := primitive.ObjectIDFromHex(assetID)

	if err != nil {
		return -999, err
	}

	var doc schema.Asset
	filter := bson.M{
		"_id":    objID,
		"job_id": jobID,
		"host":   host,
	}
	opts := options.FindOne()
	err = d.GetMongoDatabase().Collection(consts.CollectionAssets).FindOne(ctx, filter, opts).Decode(&doc)
	if err != nil {
		log.Errorln(err)
		return -999, err
	}
	return doc.Options.ContentChange.SnapshotType, nil
}

func (d *DBConnection) SaveHBaseImageContent(doc *schema.PageArchiveHBase) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	key := fmt.Sprintf("%s_%s_%d", doc.JobID, utils.GetReverseDomain(doc.URL), doc.VersionTime)
	doc.RowKey = key
	filter := bson.M{"url": doc.URL, "job_id": doc.JobID, "version_time": doc.VersionTime}
	update := bson.M{"$set": doc}
	opts := options.Update().SetUpsert(true)
	result, err := d.GetMongoDatabase().Collection(consts.CollectionHBaseContent).UpdateOne(ctx, filter, update, opts)

	if err != nil {
		log.Fatalln(err)
		return err
	}

	if result.UpsertedCount > 0 {
		log.Infoln("SaveToMongo success - insert: ", consts.CollectionHBaseContent, result.UpsertedCount)
	} else if result.ModifiedCount > 0 {
		log.Infoln("SaveToMongo success - update: ", consts.CollectionHBaseContent, result.ModifiedCount)
	} else {
		log.Infoln("SaveToMongo fail: ", consts.CollectionHBaseContent)
	}

	return nil
}

func isStaticSnapshot(snapshotType int) bool {
	return snapshotType == 0 || snapshotType == -1
}
