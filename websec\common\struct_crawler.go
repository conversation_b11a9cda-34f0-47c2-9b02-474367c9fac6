package common

import (
	"sync"
)

var defaultPool Pool

type Pool struct {
	usingSize int64
	pool      sync.Pool
}

func Get() *Webpage  { return defaultPool.Get() }
func Put(b *Webpage) { defaultPool.Put(b) }

func (p *Pool) Get() *Webpage {
	return &Webpage{}
	v := p.pool.Get()
	if v != nil {
		return v.(*Webpage)
	}
	return &Webpage{}
}

func (p *Pool) Put(b *Webpage) {
	b.Reset()
	return
	p.pool.Put(b)
}
