package sensitiveimage

import (
	"bufio"
	"bytes"
	"context"
	"crypto/sha1"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"image"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/stream"

	"gopkg.in/mgo.v2/bson"
)

var imageFormat = map[string]struct{}{
	"png":  struct{}{},
	"jpeg": struct{}{},
	"gif":  struct{}{},
}

const (
	imageWidthLimit  int = 100
	imageHeightLimit     = 100
)

type ImageMeta struct {
	ContentChecksum string
	Format          string
	Height          int
	Width           int
	Content         []byte
}

func checksum(u string) string {
	hash := sha1.New()
	hash.Write([]byte(u))
	return hex.EncodeToString(hash.Sum(nil))
}

func getFilename(u string) (string, error) {
	parts, err := url.Parse(u)
	if err != nil {
		return "", err
	}
	pathParts := strings.Split(parts.Path, "/")
	return pathParts[len(pathParts)-1], err
}

func (processor *Processor) processImages(msg *stream.Message) error {

	var crawledImageURLS = new(schema.CrawledImageURLS)
	err := json.Unmarshal(msg.Value, crawledImageURLS)
	if err != nil {
		log.Errorln("Unmarshal failed:", err)
		return err
	}

	assetID := crawledImageURLS.AssetID
	jobID := crawledImageURLS.JobID

	log.Infoln("---------------- processImages from sensitiveimage", jobID, crawledImageURLS.URL)

	for _, imageURL := range crawledImageURLS.Images {
		ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
		defer cancel()

		semiImage := &schema.ImageDoc{
			Host:             crawledImageURLS.Host,
			AssetID:          assetID,
			JobID:            jobID,
			ImageURL:         imageURL,
			ImageURLChecksum: checksum(imageURL),
			Referer:          crawledImageURLS.URL,
		}
		processor.processImage(ctx, semiImage)
	}
	return nil
}

func (processor *Processor) processImage(ctx context.Context, semiImage *schema.ImageDoc) error {
	meta, err := processor.getImageMeta(ctx, semiImage.ImageURL)
	if err != nil {
		log.Errorf("download image error, %v %v", semiImage.ImageURL, err)
		return err
	}
	semiImage.ContentChecksum = meta.ContentChecksum

	effective, err := processor.validImage(*meta)
	if err != nil || !effective {
		log.Errorf("image is not effective, %v %v", semiImage.ImageURL, err)
		return err
	}

	needCheck := false
	imageDoc := processor.getImageFromMongo(semiImage.Host, semiImage.ImageURLChecksum)
	log.Infof("get img from mongo imageDoc %+v", imageDoc)
	log.Infof("semiImage  ContentChecksum %v", semiImage.ContentChecksum)
	if imageDoc != nil {
		if imageDoc.ContentChecksum != semiImage.ContentChecksum {
			needCheck = true
		}

		imageDoc.ContentChecksum = semiImage.ContentChecksum
		imageDoc.LastCheckedAt = time.Now()
		processor.updateImage(imageDoc)
	} else {
		needCheck = true
		imageDoc := &schema.ImageDoc{
			Host:             semiImage.Host,
			AssetID:          semiImage.AssetID,
			ImageURL:         semiImage.ImageURL,
			ImageURLChecksum: semiImage.ImageURLChecksum,
			Referer:          semiImage.Referer,
			ContentChecksum:  semiImage.ContentChecksum,
			FoundAt:          time.Now(),
			LastCheckedAt:    time.Now(),
		}
		processor.insertImage(imageDoc)
	}

	if needCheck {
		log.Infoln("start check image, ", semiImage.ImageURL)
		resp, err := CheckImage(ctx, meta.ContentChecksum, meta.Content)
		if err != nil {
			log.Errorf("image check failed err %v", err)
			return err
		}
		log.Infof("img check res %+v", resp)
		if resp.IsSuccessful() {
			desc := resp.IllegalDesc()
			log.Infoln("image check res url: %s desc: %s", semiImage.ImageURL, desc)
			if len(desc) == 0 {
				return nil
			} else {

				doc := schema.FoundSensitiveImgDoc{
					ID:          bson.NewObjectId().Hex(),
					Host:        semiImage.Host,
					ImageClass:  desc,
					ImageURL:    semiImage.ImageURL,
					AffectedURL: semiImage.Referer,
					FoundAt:     time.Now().UTC(),
					AssetID:     semiImage.AssetID,
					JobID:       semiImage.JobID,
				}
				processor.producer.Produce(consts.TopicShouWeiSensitiveImgResults, doc)
			}
		}
	}
	log.Infoln("end check image, ", semiImage.ImageURL)
	return nil
}

func (processor *Processor) getImageMeta(ctx context.Context, imageURL string) (*ImageMeta, error) {
	meta := new(ImageMeta)
	log.Infoln("start download image", imageURL)
	request, err := http.NewRequestWithContext(ctx, http.MethodGet, imageURL, nil)
	if err != nil {
		log.Errorln("downloadImage new request error.", err)
		return nil, err
	}
	request.Header.Add("Referer", "site")

	httpClient := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	resp, err := httpClient.Do(request)
	if err != nil {
		log.Errorln("downloadImage error.", err)
		return nil, err
	}
	defer resp.Body.Close()

	bufReader := bufio.NewReader(resp.Body)
	magic, err := bufReader.Peek(512) // 512 is enough ?
	if err != nil {
		return nil, err
	}

	conf, format, err := image.DecodeConfig(bytes.NewReader(magic))
	if err != nil {
		return nil, err
	}
	meta.Format = format
	meta.Height = conf.Height
	meta.Width = conf.Width

	content, err := ioutil.ReadAll(bufReader)
	if err != nil {
		return nil, err
	}
	meta.Content = content

	h := sha1.New()
	_, err = h.Write(meta.Content)
	if err != nil {
		return nil, err
	}
	meta.ContentChecksum = hex.EncodeToString(h.Sum(nil))

	return meta, nil
}

func (processor *Processor) validImage(meta ImageMeta) (bool, error) {
	if meta.Height <= imageHeightLimit && meta.Width <= imageWidthLimit {
		return false, nil
	}

	if _, ok := imageFormat[meta.Format]; !ok {
		return false, nil
	}

	return true, nil
}

func (processor *Processor) getImageFromMongo(host string, imageURLChecksum string) *schema.ImageDoc {
	mdb := processor.dbConnection.GetMongoDatabase()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var doc schema.ImageDoc
	filter := bson.M{
		"host":               host,
		"image_url_checksum": imageURLChecksum,
	}
	err := mdb.Collection(consts.CollectionImages).FindOne(ctx, filter).Decode(&doc)
	if err != nil {
		log.Errorf("get image from mongo error, %v %v", host, imageURLChecksum)
		return nil
	}

	return &doc
}

func (processor *Processor) updateImage(imageDoc *schema.ImageDoc) {
	filter := bson.M{
		"host":               imageDoc.Host,
		"image_url_checksum": imageDoc.ImageURLChecksum,
		"_id":                imageDoc.ID,
	}

	data := bson.M{
		"$set": bson.M{
			"content_checksum": imageDoc.ContentChecksum,
			"last_checked_at":  imageDoc.LastCheckedAt,
		},
	}

	mdb := processor.dbConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	for retry := 0; retry < 3; retry++ {
		_, err := mdb.Collection(consts.CollectionImages).UpdateOne(ctx, filter, data)
		if err != nil {
			log.Errorln("update images error: %v %v", err, data)
		} else {
			break
		}
	}
}

func (processor *Processor) insertImage(imageDoc *schema.ImageDoc) {
	mdb := processor.dbConnection.GetMongoDatabase()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	for retry := 0; retry < 3; retry++ {
		_, err := mdb.Collection(consts.CollectionImages).InsertOne(
			ctx, imageDoc)
		if err != nil {
			log.Errorln("insert images error: %v %v", err, imageDoc)
		} else {
			break
		}
	}
}
