package chrome

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"sync/atomic"
	"time"
	"websec/common"
	"websec/crawl"
	"websec/utils/log"
)

type ChromeServer struct {
	Clients          []*crawl.ChromeClient
	ClientPorts      []uint16
	Concurrency      int32
	Server           *http.Server
	ServerPort       uint16
	InjectJavaScript *common.InjectJavaScript
}

type ChromeServerOptions struct {
	ClientPorts      string
	Concurrency      int32
	ServerPort       uint16
	InjectJavaScript *common.InjectJavaScript
}

func NewChromeServer(options *ChromeServerOptions) (*ChromeServer, error) {

	ports := strings.Split(options.ClientPorts, ",")
	clientPorts := []uint16{}
	for _, port := range ports {
		clientPort, err := strconv.Atoi(port)
		if err != nil {
			log.Errorln(err)
			continue
		}
		clientPorts = append(clientPorts, uint16(clientPort))
	}

	if len(clientPorts) == 0 {
		return nil, errors.New("chrome client ports config error")
	}

	chromeServer := &ChromeServer{
		ClientPorts: clientPorts,
		Concurrency: options.Concurrency,
		Server: &http.Server{
			Addr:         fmt.Sprintf(":%v", options.ServerPort),
			ReadTimeout:  60 * time.Second,
			WriteTimeout: 60 * time.Second,
		},
		InjectJavaScript: options.InjectJavaScript,
	}

	http.HandleFunc("/", chromeServer.handler)

	for _, port := range chromeServer.ClientPorts {
		chromeClient := &crawl.ChromeClient{
			Host: "127.0.0.1",
			Port: port,
			HTTPClient: &http.Client{
				Timeout: 20 * time.Second,
			},
			CurTabCount:      0,
			InjectJavaScript: chromeServer.InjectJavaScript,
		}
		chromeClient.SyncTabCount()
		chromeServer.Clients = append(chromeServer.Clients, chromeClient)

	}

	return chromeServer, nil
}

func (cserver *ChromeServer) handler(w http.ResponseWriter, r *http.Request) {
	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		log.Infoln("Failed to read body.", err)
		cserver.writeResponse(w, common.CrawlReadRequestBodyError, nil)
		return
	}

	var crawlRequest common.CrawlRequest
	if err := json.Unmarshal(body, &crawlRequest); err != nil {
		log.Infoln("Failed to unmarshal body.", err, string(body))
		cserver.writeResponse(w, common.CrawlUnmarshalRequestBodyError, nil)
		return
	}

	client := cserver.chooseClient()
	if client == nil {
		log.Infoln("No available tab.", crawlRequest.URL)
		cserver.writeResponse(w, common.CrawlNoAvailableTab, nil)
		return
	}

	link := &common.Link{
		AssetID:   crawlRequest.AssetID,
		JobID:     crawlRequest.JobID,
		URL:       crawlRequest.URL,
		Method:    crawlRequest.Method,
		Depth:     crawlRequest.Depth,
		Referer:   crawlRequest.Referer,
		Static:    crawlRequest.Static,
		Outer:     crawlRequest.Outer,
		Headers:   crawlRequest.Headers,
		UserAgent: crawlRequest.UserAgent,
	}

	webpages, err := client.Navigate(link)
	if err != nil {
		log.Infoln("Navigate error.", link.URL, err)
		cserver.writeResponse(w, common.CrawlNavigateError, nil)
		return
	}

	cserver.writeResponse(w, common.CrawlSuccess, webpages)
}

func (cserver *ChromeServer) writeResponse(w http.ResponseWriter, status common.CrawlResponseStatus, webpages []*common.Webpage) {
	resp := common.CrawlResponse{
		Status:   status,
		Webpages: webpages,
	}
	respData, _ := json.Marshal(resp)
	_, err := w.Write(respData)
	if err != nil {
		log.Infoln(err, resp)
	}
}

func (cserver *ChromeServer) Go() {
	log.Infoln(cserver.Server.ListenAndServe())
}

func (cserver *ChromeServer) chooseClient() *crawl.ChromeClient {

	clientIndex := 0
	for i := range cserver.Clients {
		if atomic.LoadInt32(&cserver.Clients[clientIndex].CurTabCount) > atomic.LoadInt32(&cserver.Clients[i].CurTabCount) {
			clientIndex = i
		}
	}
	if atomic.LoadInt32(&cserver.Clients[clientIndex].CurTabCount) >= cserver.Concurrency {
		return nil
	}
	return cserver.Clients[clientIndex]
}
