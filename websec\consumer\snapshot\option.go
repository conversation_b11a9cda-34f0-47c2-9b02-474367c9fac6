package snapshot

import (
	"strings"
	"websec/common/db"
	"websec/config"
	"websec/utils/semaphore"
	"websec/utils/tuchuang"
)

type OptionFn func(*Processor) error

func WithDBConnection(dbConnection *db.DBConnection) OptionFn {
	return func(processor *Processor) error {
		processor.dbConnection = dbConnection
		return nil
	}
}

func WithConsumeConcurrency(concurrency int64) OptionFn {
	return func(processor *Processor) error {
		processor.consumeSema = semaphore.NewWeighted(concurrency)
		return nil
	}
}

func WithSnapshotConfig(snapshotConfig *config.SnapshotConfig) OptionFn {
	return func(processor *Processor) error {
		processor.snapshotConfig = snapshotConfig
		return nil
	}
}

func WithImgUploader(cfg *config.S3BucketConfig, addr string) OptionFn {
	return func(processor *Processor) error {
		processor.imgUploader = tuchuang.NewBucketSelect(cfg, addr, cfg.BucketType)
		return nil
	}
}

func WithIgnoredHosts(cfg *config.SnapshotConfig) OptionFn {
	hosts := strings.Split(cfg.IgnoredHosts, ",")
	ignoredHostsMap := make(map[string]struct{})
	for _, host := range hosts {
		ignoredHostsMap[host] = struct{}{}
	}
	return func(processor *Processor) error {
		processor.ignoredHostsMap = ignoredHostsMap
		return nil
	}
}
