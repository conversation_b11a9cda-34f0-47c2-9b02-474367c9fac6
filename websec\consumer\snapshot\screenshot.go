package snapshot

import (
	"context"
	"os"
	"path/filepath"
	"sync"
	"time"
	"websec/common"
	"websec/utils/chrome"
	"websec/utils/log"
	"websec/utils/semaphore"
)

type ChromeScreenShot struct {
	client            *chrome.Client
	sensitiveWordChan chan *SensitiveWordChrome
	contentChangeChan chan *ContentChangeChrome
	wg                sync.WaitGroup
	processor         *Processor
}

func NewChromeScreenShot(processor *Processor, address string) *ChromeScreenShot {
	injectJS := &common.InjectJavaScript{
		InjectAfterLoad:  `console.log("inject after load.");`,
		InjectBeforeLoad: `console.log("inject after load.");`,
	}

	client := chrome.NewClient(&chrome.Options{
		Addr:   address,
		Script: injectJS,
	})
	return &ChromeScreenShot{
		client:            client,
		sensitiveWordChan: make(chan *SensitiveWordChrome, 100),
		contentChangeChan: make(chan *ContentChangeChrome, 100),
		processor:         processor,
	}
}

func (c *ChromeScreenShot) Start() {
	c.wg.Add(2)
	go c.sensitiveWordRun()
	go c.contentChangeRun()
}

func (c *ChromeScreenShot) Stop() {
	close(c.sensitiveWordChan)
	close(c.contentChangeChan)
	c.wg.Wait()
}

func (c *ChromeScreenShot) AddSensitiveWordChrome(msg *SensitiveWordChrome) {
	c.sensitiveWordChan <- msg
}

func (c *ChromeScreenShot) AddContentChangeChrome(msg *ContentChangeChrome) {
	c.contentChangeChan <- msg
}

func (c *ChromeScreenShot) sensitiveWordRun() {
	defer c.wg.Done()

	sema := semaphore.NewWeighted(c.processor.snapshotConfig.ChromeSema)
	var err error
	for msg := range c.sensitiveWordChan {
		if err = sema.Acquire(context.Background(), 1); err == nil {
			go c.doSensitiveWord(msg, sema, true)
		} else {
			log.Errorln(err)
			go c.doSensitiveWord(msg, sema, false)
		}
	}
}

func (c *ChromeScreenShot) doScreenShot(filePath string) ([]byte, string, bool, error) {
	wDir := filepath.Dir(filePath)
	chromeURL := "file://" + filePath

	tool := "chrome"
	js := true
	log.Infof("failed to screenshot with chrome js with %s", filePath)
	data, err := c.client.ScreenShot(chromeURL)
	if err != nil {
		return data, tool, js, err
	} else {
		if len(data) > 20*1024 {
			return data, tool, js, nil
		}
	}

	withOutJSPagePath, err := RemoveScriptTagFromFile(filePath)
	if err != nil {
		log.Infof("failed to remove js tag from page %s", filePath)

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		js = true
		tool = "phantomjs"
		data, err = captureByPhantomjs(ctx, chromeURL, wDir) //since remove js failed, try phantomjs with that page
		return data, tool, js, err
	}

	js = false
	tool = "chrome"
	data, err = c.client.ScreenShot("file://" + withOutJSPagePath)
	if err == nil {
		if len(data) > 10*1024 {
			return data, tool, js, nil
		}
	}
	log.Infof("failed to screenshot with chrome withoutjs %s", withOutJSPagePath)

	js = false
	tool = "phantomjs"
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	data, err = captureByPhantomjs(ctx, withOutJSPagePath, wDir) //since remove js failed, try phantomjs with that page
	return data, tool, js, err
}

func (c *ChromeScreenShot) doSensitiveWord(msg *SensitiveWordChrome, sema *semaphore.Weighted, acquire bool) {
	if acquire {
		defer func() {
			sema.Release(1)
		}()
	}

	defer func() {
		err := os.Remove(msg.FilePath)
		if err != nil {
			log.Errorln("remove file faild:", msg.FilePath)
		}
	}()

	log.Debugln("filename:", msg.FilePath)

	start := time.Now()
	data, tool, js, err := c.doScreenShot(msg.FilePath)
	log.Infof("sensitive %s tool: %s, js: %v, err: %v", msg.SnapshotID, tool, js, err)

	res := &SensitiveWordUpload{
		ID:         msg.ID,
		Host:       msg.Host,
		SnapshotID: msg.SnapshotID,
		ImageData:  data,
		Tool:       tool,
		JS:         js,
		Err:        err,
	}

	log.Infof("doSensitiveWord %s %f", msg.SnapshotID, time.Now().Sub(start).Seconds())
	c.processor.uploadService.AddSensitiveWordUpload(res)
}

func (c *ChromeScreenShot) contentChangeRun() {
	defer c.wg.Done()

	sema := semaphore.NewWeighted(c.processor.snapshotConfig.ChromeSema)
	var err error
	for msg := range c.contentChangeChan {
		if err = sema.Acquire(context.Background(), 1); err == nil {
			go c.doContentChange(msg, sema, true)
		} else {
			log.Errorln(err)
			go c.doContentChange(msg, sema, false)
		}
	}
}

func (c *ChromeScreenShot) doContentChange(msg *ContentChangeChrome, sema *semaphore.Weighted, acquire bool) {
	if acquire {
		defer func() {
			sema.Release(1)
		}()
	}

	defer func() {
		os.Remove(msg.OldFilePath)
		os.Remove(msg.NewFilePath)
	}()

	log.Debugf("filename: old %s new %s", msg.OldFilePath, msg.NewFilePath)

	start := time.Now()

	var res = &ContentChangeUpload{
		ID:   msg.ID,
		Host: msg.Host,
	}

	oldData, oldTool, oldJS, oldErr := c.doScreenShot(msg.OldFilePath)
	log.Infof("ContentChange old %s tool: %s, js: %v, err: %v", msg.ID, oldTool, oldJS, oldErr)
	res.OldImageData = oldData
	res.OldJS = oldJS
	res.OldTool = oldTool
	res.Err = oldErr

	if oldErr != nil {
		log.Infof("doContentChange %s %f", msg.Host, time.Since(start).Seconds())
		c.processor.uploadService.AddContentChangeUpload(res)
		return
	}

	newData, newTool, newJS, newErr := c.doScreenShot(msg.NewFilePath)
	log.Infof("ContentChange new %s tool: %s, js: %v, err: %v", msg.ID, newTool, newJS, newErr)
	res.NewImageData = newData
	res.NewJS = newJS
	res.NewTool = newTool
	res.Err = newErr

	log.Infof("doContentChange %s %f", msg.Host, time.Since(start).Seconds())
	c.processor.uploadService.AddContentChangeUpload(res)
}
