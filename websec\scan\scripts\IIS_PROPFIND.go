package scripts

import (
	"bytes"
	"net/http"
	"time"

	"github.com/valyala/fasthttp"
)

func IISPropFind(args *ScriptScanArgs) (*ScriptScanResult, error) {
	rawurl := constructURL(args, "/")

	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.SetRequestURI(rawurl)
	request.Header.SetMethod(http.MethodOptions)

	err := headerNormalizingHTTPClient.DoTimeout(request, response, time.Second*5)
	if err != nil {
		return nil, err
	}

	server := response.Header.Peek("server")
	publicMethod := response.Header.Peek("public")
	if bytes.Contains(server, []byte("IIS")) && bytes.Contains(server, []byte("6.0")) && bytes.Contains(publicMethod, []byte("PROPFIND")) {
		return &ScriptScanResult{Vulnerable: true, Output: rawurl, Body: server}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("IIS_PROPFIND_vul.xml", IISPropFind)
}
