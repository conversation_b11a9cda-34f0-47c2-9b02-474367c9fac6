package detect

import (
	"net/url"
	"strings"
	"websec/utils/log"
)

func isOuterURL(rootDomain, testurl string) bool {
	testurl = strings.TrimSpace(testurl)
	if strings.HasPrefix(testurl, "//") || strings.HasPrefix(testurl, "http://") || strings.HasPrefix(testurl, "https://") {
		parts, err := url.Parse(testurl)
		if err != nil {
			log.Errorln("failed to parse url:", err, testurl)
			return false
		}
		return !strings.HasSuffix(parts.Hostname(), rootDomain)
	}
	return false
}
