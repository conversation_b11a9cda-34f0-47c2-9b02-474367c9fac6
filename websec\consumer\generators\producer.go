package generators

import (
	"websec/common/consts"
	"websec/common/schema"
)

func (generator *Generator) addRawSensitiveWord(val *schema.RawSensitiveWordResult) {
	generator.producer.Produce(consts.TopicRawSensitiveWordResults, val)
}

func (generator *Generator) addRawBlackLink(val *schema.RawBlackLinkResult) {
	generator.producer.Produce(consts.TopicRawBlackLinkResults, val)
}

func (generator *Generator) addRawContentChange(val *schema.RawContentChangeResult) {
	generator.producer.Produce(consts.TopicRawContentChangeResults, val)
}

func (generator *Generator) addRawTrojan(val *schema.RawTrojanResult) {

	generator.producer.Produce("trojan-results", val)
}

func (generator *Generator) addVul(val *schema.FoundVulDoc) {
	generator.producer.Produce(consts.TopicRawVulResults, val)
}

func (generator *Generator) addFoundNewURL(val *schema.FoundNewURLs) {
	generator.producer.Produce(consts.TopicFinalNewURLResults, val)
}

func (generator *Generator) addFoundPageArchive(val *schema.FoundPageArchiveDoc) {
	if generator.useHBase {
		generator.producer.Produce(consts.TopicFinalPageArchiveResults, val)
	}
}

func (generator *Generator) addHotPageArchive(val *schema.FoundPageArchiveDoc) {
	generator.producer.Produce(consts.TopicFinalHotPageArchiveResults, val)
}
