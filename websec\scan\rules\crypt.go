package rules

// #cgo pkg-config: openssl
//
// #include <stdlib.h>
// #include <string.h>
// #include "openssl/des.h"
// #define JSKY_KEY "nosec.org"
//
// static int crypt_buf(unsigned char *buf, int size,
//         unsigned char *outbuf, int encrypt)
// {
//     DES_cblock key;
//     DES_key_schedule schedule;
//     DES_cblock ivec;
//     DES_string_to_key(JSKY_KEY, &key);
//     if (DES_set_key_checked(&key, &schedule) != 0)
//     {
//         return -1;
//     }
//
//     memset((char*) & ivec, 0, sizeof(ivec));
//
//     DES_ncbc_encrypt(buf, outbuf, size, &schedule, &ivec,
//         encrypt ? DES_ENCRYPT : DES_DECRYPT);
//     return 0;
// }
import "C"
import (
	"errors"
)

func decrypt(input []byte) ([]byte, error) {
	output := make([]byte, (len(input)+7)/8*8)
	decResult := C.crypt_buf((*C.uchar)(&input[0]), C.int(len(input)), (*C.uchar)(&output[0]), C.int(0))
	if decResult != 0 {
		return nil, errors.New("failed to decrypt content")
	}

	return output, nil
}

func encrypt(input []byte) ([]byte, error) {
	output := make([]byte, (len(input)+7)/8*8)
	decResult := C.crypt_buf((*C.uchar)(&input[0]), C.int(len(input)), (*C.uchar)(&output[0]), C.int(1))
	if decResult != 0 {
		return nil, errors.New("failed to encrypt content")
	}
	return output, nil
}

func Decrypt(input []byte) ([]byte, error) {
	return decrypt(input)
}

func Encrypt(input []byte) ([]byte, error) {
	return encrypt(input)
}
