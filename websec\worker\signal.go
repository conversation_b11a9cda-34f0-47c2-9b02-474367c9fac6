package main

import (
	"os"
	"os/signal"
	"syscall"
	"websec/utils/log"
)

func setupSignal(closeCb func()) {
	go func() {
		ch := make(chan os.Signal, 1)
		signal.Notify(ch, syscall.SIGINT, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGHUP)
		for si := range ch {
			log.Infoln("receive signal", si)
			switch si {
			case syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT:
				closeCb()
				break
			default:
				log.Errorln("invalid sig", si)
			}
		}
	}()
}
