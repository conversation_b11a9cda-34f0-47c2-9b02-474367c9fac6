// Autogenerated by Thrift Compiler (0.12.0)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"context"
	"flag"
	"fmt"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"websec/utils/hbase"

	"github.com/apache/thrift/lib/go/thrift"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>r, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  void enableTable(Bytes tableName)")
	fmt.Fprintln(os.Stderr, "  void disableTable(Bytes tableName)")
	fmt.Fprintln(os.Stderr, "  bool isTableEnabled(Bytes tableName)")
	fmt.Fprintln(os.Stderr, "  void compact(Bytes tableNameOrRegionName)")
	fmt.Fprintln(os.Stderr, "  void majorCompact(Bytes tableNameOrRegionName)")
	fmt.Fprintln(os.Stderr, "   getTableNames()")
	fmt.Fprintln(os.Stderr, "   getColumnDescriptors(Text tableName)")
	fmt.Fprintln(os.Stderr, "   getTableRegions(Text tableName)")
	fmt.Fprintln(os.Stderr, "  void createTable(Text tableName,  columnFamilies)")
	fmt.Fprintln(os.Stderr, "  void deleteTable(Text tableName)")
	fmt.Fprintln(os.Stderr, "   get(Text tableName, Text row, Text column,  attributes)")
	fmt.Fprintln(os.Stderr, "   getVer(Text tableName, Text row, Text column, i32 numVersions,  attributes)")
	fmt.Fprintln(os.Stderr, "   getVerTs(Text tableName, Text row, Text column, i64 timestamp, i32 numVersions,  attributes)")
	fmt.Fprintln(os.Stderr, "   getRow(Text tableName, Text row,  attributes)")
	fmt.Fprintln(os.Stderr, "   getRowWithColumns(Text tableName, Text row,  columns,  attributes)")
	fmt.Fprintln(os.Stderr, "   getRowTs(Text tableName, Text row, i64 timestamp,  attributes)")
	fmt.Fprintln(os.Stderr, "   getRowWithColumnsTs(Text tableName, Text row,  columns, i64 timestamp,  attributes)")
	fmt.Fprintln(os.Stderr, "   getRows(Text tableName,  rows,  attributes)")
	fmt.Fprintln(os.Stderr, "   getRowsWithColumns(Text tableName,  rows,  columns,  attributes)")
	fmt.Fprintln(os.Stderr, "   getRowsTs(Text tableName,  rows, i64 timestamp,  attributes)")
	fmt.Fprintln(os.Stderr, "   getRowsWithColumnsTs(Text tableName,  rows,  columns, i64 timestamp,  attributes)")
	fmt.Fprintln(os.Stderr, "  void mutateRow(Text tableName, Text row,  mutations,  attributes)")
	fmt.Fprintln(os.Stderr, "  void mutateRowTs(Text tableName, Text row,  mutations, i64 timestamp,  attributes)")
	fmt.Fprintln(os.Stderr, "  void mutateRows(Text tableName,  rowBatches,  attributes)")
	fmt.Fprintln(os.Stderr, "  void mutateRowsTs(Text tableName,  rowBatches, i64 timestamp,  attributes)")
	fmt.Fprintln(os.Stderr, "  i64 atomicIncrement(Text tableName, Text row, Text column, i64 value)")
	fmt.Fprintln(os.Stderr, "  void deleteAll(Text tableName, Text row, Text column,  attributes)")
	fmt.Fprintln(os.Stderr, "  void deleteAllTs(Text tableName, Text row, Text column, i64 timestamp,  attributes)")
	fmt.Fprintln(os.Stderr, "  void deleteAllRow(Text tableName, Text row,  attributes)")
	fmt.Fprintln(os.Stderr, "  void increment(TIncrement increment)")
	fmt.Fprintln(os.Stderr, "  void incrementRows( increments)")
	fmt.Fprintln(os.Stderr, "  void deleteAllRowTs(Text tableName, Text row, i64 timestamp,  attributes)")
	fmt.Fprintln(os.Stderr, "  ScannerID scannerOpenWithScan(Text tableName, TScan scan,  attributes)")
	fmt.Fprintln(os.Stderr, "  ScannerID scannerOpen(Text tableName, Text startRow,  columns,  attributes)")
	fmt.Fprintln(os.Stderr, "  ScannerID scannerOpenWithStop(Text tableName, Text startRow, Text stopRow,  columns,  attributes)")
	fmt.Fprintln(os.Stderr, "  ScannerID scannerOpenWithPrefix(Text tableName, Text startAndPrefix,  columns,  attributes)")
	fmt.Fprintln(os.Stderr, "  ScannerID scannerOpenTs(Text tableName, Text startRow,  columns, i64 timestamp,  attributes)")
	fmt.Fprintln(os.Stderr, "  ScannerID scannerOpenWithStopTs(Text tableName, Text startRow, Text stopRow,  columns, i64 timestamp,  attributes)")
	fmt.Fprintln(os.Stderr, "   scannerGet(ScannerID id)")
	fmt.Fprintln(os.Stderr, "   scannerGetList(ScannerID id, i32 nbRows)")
	fmt.Fprintln(os.Stderr, "  void scannerClose(ScannerID id)")
	fmt.Fprintln(os.Stderr, "  TRegionInfo getRegionInfo(Text row)")
	fmt.Fprintln(os.Stderr, "   append(TAppend append)")
	fmt.Fprintln(os.Stderr, "  bool checkAndPut(Text tableName, Text row, Text column, Text value, Mutation mput,  attributes)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

type httpHeaders map[string]string

func (h httpHeaders) String() string {
	var m map[string]string = h
	return fmt.Sprintf("%s", m)
}

func (h httpHeaders) Set(value string) error {
	parts := strings.Split(value, ": ")
	if len(parts) != 2 {
		return fmt.Errorf("header should be of format 'Key: Value'")
	}
	h[parts[0]] = parts[1]
	return nil
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	headers := make(httpHeaders)
	var parsedUrl *url.URL
	var trans thrift.TTransport
	_ = strconv.Atoi
	_ = math.Abs
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Var(headers, "H", "Headers to set on the http(s) request (e.g. -H \"Key: Value\")")
	flag.Parse()

	if len(urlString) > 0 {
		var err error
		parsedUrl, err = url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http" || parsedUrl.Scheme == "https"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
		if len(headers) > 0 {
			httptrans := trans.(*thrift.THttpClient)
			for key, value := range headers {
				httptrans.SetHeader(key, value)
			}
		}
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	iprot := protocolFactory.GetProtocol(trans)
	oprot := protocolFactory.GetProtocol(trans)
	client := hbase.NewHbaseClient(thrift.NewTStandardClient(iprot, oprot))
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "enableTable":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "EnableTable requires 1 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Bytes(argvalue0)
		fmt.Print(client.EnableTable(context.Background(), value0))
		fmt.Print("\n")
		break
	case "disableTable":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "DisableTable requires 1 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Bytes(argvalue0)
		fmt.Print(client.DisableTable(context.Background(), value0))
		fmt.Print("\n")
		break
	case "isTableEnabled":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "IsTableEnabled requires 1 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Bytes(argvalue0)
		fmt.Print(client.IsTableEnabled(context.Background(), value0))
		fmt.Print("\n")
		break
	case "compact":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "Compact requires 1 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Bytes(argvalue0)
		fmt.Print(client.Compact(context.Background(), value0))
		fmt.Print("\n")
		break
	case "majorCompact":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "MajorCompact requires 1 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Bytes(argvalue0)
		fmt.Print(client.MajorCompact(context.Background(), value0))
		fmt.Print("\n")
		break
	case "getTableNames":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetTableNames requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetTableNames(context.Background()))
		fmt.Print("\n")
		break
	case "getColumnDescriptors":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetColumnDescriptors requires 1 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		fmt.Print(client.GetColumnDescriptors(context.Background(), value0))
		fmt.Print("\n")
		break
	case "getTableRegions":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetTableRegions requires 1 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		fmt.Print(client.GetTableRegions(context.Background(), value0))
		fmt.Print("\n")
		break
	case "createTable":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "CreateTable requires 2 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		arg194 := flag.Arg(2)
		mbTrans195 := thrift.NewTMemoryBufferLen(len(arg194))
		defer mbTrans195.Close()
		_, err196 := mbTrans195.WriteString(arg194)
		if err196 != nil {
			Usage()
			return
		}
		factory197 := thrift.NewTJSONProtocolFactory()
		jsProt198 := factory197.GetProtocol(mbTrans195)
		containerStruct1 := hbase.NewHbaseCreateTableArgs()
		err199 := containerStruct1.ReadField2(jsProt198)
		if err199 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ColumnFamilies
		value1 := argvalue1
		fmt.Print(client.CreateTable(context.Background(), value0, value1))
		fmt.Print("\n")
		break
	case "deleteTable":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "DeleteTable requires 1 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		fmt.Print(client.DeleteTable(context.Background(), value0))
		fmt.Print("\n")
		break
	case "get":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "Get requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		argvalue2 := []byte(flag.Arg(3))
		value2 := hbase.Text(argvalue2)
		arg204 := flag.Arg(4)
		mbTrans205 := thrift.NewTMemoryBufferLen(len(arg204))
		defer mbTrans205.Close()
		_, err206 := mbTrans205.WriteString(arg204)
		if err206 != nil {
			Usage()
			return
		}
		factory207 := thrift.NewTJSONProtocolFactory()
		jsProt208 := factory207.GetProtocol(mbTrans205)
		containerStruct3 := hbase.NewHbaseGetArgs()
		err209 := containerStruct3.ReadField4(jsProt208)
		if err209 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Attributes
		value3 := argvalue3
		fmt.Print(client.Get(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getVer":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetVer requires 5 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		argvalue2 := []byte(flag.Arg(3))
		value2 := hbase.Text(argvalue2)
		tmp3, err213 := (strconv.Atoi(flag.Arg(4)))
		if err213 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg214 := flag.Arg(5)
		mbTrans215 := thrift.NewTMemoryBufferLen(len(arg214))
		defer mbTrans215.Close()
		_, err216 := mbTrans215.WriteString(arg214)
		if err216 != nil {
			Usage()
			return
		}
		factory217 := thrift.NewTJSONProtocolFactory()
		jsProt218 := factory217.GetProtocol(mbTrans215)
		containerStruct4 := hbase.NewHbaseGetVerArgs()
		err219 := containerStruct4.ReadField5(jsProt218)
		if err219 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Attributes
		value4 := argvalue4
		fmt.Print(client.GetVer(context.Background(), value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getVerTs":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "GetVerTs requires 6 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		argvalue2 := []byte(flag.Arg(3))
		value2 := hbase.Text(argvalue2)
		argvalue3, err223 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err223 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err224 := (strconv.Atoi(flag.Arg(5)))
		if err224 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		arg225 := flag.Arg(6)
		mbTrans226 := thrift.NewTMemoryBufferLen(len(arg225))
		defer mbTrans226.Close()
		_, err227 := mbTrans226.WriteString(arg225)
		if err227 != nil {
			Usage()
			return
		}
		factory228 := thrift.NewTJSONProtocolFactory()
		jsProt229 := factory228.GetProtocol(mbTrans226)
		containerStruct5 := hbase.NewHbaseGetVerTsArgs()
		err230 := containerStruct5.ReadField6(jsProt229)
		if err230 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.Attributes
		value5 := argvalue5
		fmt.Print(client.GetVerTs(context.Background(), value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getRow":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetRow requires 3 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		arg233 := flag.Arg(3)
		mbTrans234 := thrift.NewTMemoryBufferLen(len(arg233))
		defer mbTrans234.Close()
		_, err235 := mbTrans234.WriteString(arg233)
		if err235 != nil {
			Usage()
			return
		}
		factory236 := thrift.NewTJSONProtocolFactory()
		jsProt237 := factory236.GetProtocol(mbTrans234)
		containerStruct2 := hbase.NewHbaseGetRowArgs()
		err238 := containerStruct2.ReadField3(jsProt237)
		if err238 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Attributes
		value2 := argvalue2
		fmt.Print(client.GetRow(context.Background(), value0, value1, value2))
		fmt.Print("\n")
		break
	case "getRowWithColumns":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetRowWithColumns requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		arg241 := flag.Arg(3)
		mbTrans242 := thrift.NewTMemoryBufferLen(len(arg241))
		defer mbTrans242.Close()
		_, err243 := mbTrans242.WriteString(arg241)
		if err243 != nil {
			Usage()
			return
		}
		factory244 := thrift.NewTJSONProtocolFactory()
		jsProt245 := factory244.GetProtocol(mbTrans242)
		containerStruct2 := hbase.NewHbaseGetRowWithColumnsArgs()
		err246 := containerStruct2.ReadField3(jsProt245)
		if err246 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Columns
		value2 := argvalue2
		arg247 := flag.Arg(4)
		mbTrans248 := thrift.NewTMemoryBufferLen(len(arg247))
		defer mbTrans248.Close()
		_, err249 := mbTrans248.WriteString(arg247)
		if err249 != nil {
			Usage()
			return
		}
		factory250 := thrift.NewTJSONProtocolFactory()
		jsProt251 := factory250.GetProtocol(mbTrans248)
		containerStruct3 := hbase.NewHbaseGetRowWithColumnsArgs()
		err252 := containerStruct3.ReadField4(jsProt251)
		if err252 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Attributes
		value3 := argvalue3
		fmt.Print(client.GetRowWithColumns(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getRowTs":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetRowTs requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		argvalue2, err255 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err255 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg256 := flag.Arg(4)
		mbTrans257 := thrift.NewTMemoryBufferLen(len(arg256))
		defer mbTrans257.Close()
		_, err258 := mbTrans257.WriteString(arg256)
		if err258 != nil {
			Usage()
			return
		}
		factory259 := thrift.NewTJSONProtocolFactory()
		jsProt260 := factory259.GetProtocol(mbTrans257)
		containerStruct3 := hbase.NewHbaseGetRowTsArgs()
		err261 := containerStruct3.ReadField4(jsProt260)
		if err261 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Attributes
		value3 := argvalue3
		fmt.Print(client.GetRowTs(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getRowWithColumnsTs":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetRowWithColumnsTs requires 5 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		arg264 := flag.Arg(3)
		mbTrans265 := thrift.NewTMemoryBufferLen(len(arg264))
		defer mbTrans265.Close()
		_, err266 := mbTrans265.WriteString(arg264)
		if err266 != nil {
			Usage()
			return
		}
		factory267 := thrift.NewTJSONProtocolFactory()
		jsProt268 := factory267.GetProtocol(mbTrans265)
		containerStruct2 := hbase.NewHbaseGetRowWithColumnsTsArgs()
		err269 := containerStruct2.ReadField3(jsProt268)
		if err269 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Columns
		value2 := argvalue2
		argvalue3, err270 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err270 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		arg271 := flag.Arg(5)
		mbTrans272 := thrift.NewTMemoryBufferLen(len(arg271))
		defer mbTrans272.Close()
		_, err273 := mbTrans272.WriteString(arg271)
		if err273 != nil {
			Usage()
			return
		}
		factory274 := thrift.NewTJSONProtocolFactory()
		jsProt275 := factory274.GetProtocol(mbTrans272)
		containerStruct4 := hbase.NewHbaseGetRowWithColumnsTsArgs()
		err276 := containerStruct4.ReadField5(jsProt275)
		if err276 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Attributes
		value4 := argvalue4
		fmt.Print(client.GetRowWithColumnsTs(context.Background(), value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getRows":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetRows requires 3 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		arg278 := flag.Arg(2)
		mbTrans279 := thrift.NewTMemoryBufferLen(len(arg278))
		defer mbTrans279.Close()
		_, err280 := mbTrans279.WriteString(arg278)
		if err280 != nil {
			Usage()
			return
		}
		factory281 := thrift.NewTJSONProtocolFactory()
		jsProt282 := factory281.GetProtocol(mbTrans279)
		containerStruct1 := hbase.NewHbaseGetRowsArgs()
		err283 := containerStruct1.ReadField2(jsProt282)
		if err283 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Rows
		value1 := argvalue1
		arg284 := flag.Arg(3)
		mbTrans285 := thrift.NewTMemoryBufferLen(len(arg284))
		defer mbTrans285.Close()
		_, err286 := mbTrans285.WriteString(arg284)
		if err286 != nil {
			Usage()
			return
		}
		factory287 := thrift.NewTJSONProtocolFactory()
		jsProt288 := factory287.GetProtocol(mbTrans285)
		containerStruct2 := hbase.NewHbaseGetRowsArgs()
		err289 := containerStruct2.ReadField3(jsProt288)
		if err289 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Attributes
		value2 := argvalue2
		fmt.Print(client.GetRows(context.Background(), value0, value1, value2))
		fmt.Print("\n")
		break
	case "getRowsWithColumns":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetRowsWithColumns requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		arg291 := flag.Arg(2)
		mbTrans292 := thrift.NewTMemoryBufferLen(len(arg291))
		defer mbTrans292.Close()
		_, err293 := mbTrans292.WriteString(arg291)
		if err293 != nil {
			Usage()
			return
		}
		factory294 := thrift.NewTJSONProtocolFactory()
		jsProt295 := factory294.GetProtocol(mbTrans292)
		containerStruct1 := hbase.NewHbaseGetRowsWithColumnsArgs()
		err296 := containerStruct1.ReadField2(jsProt295)
		if err296 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Rows
		value1 := argvalue1
		arg297 := flag.Arg(3)
		mbTrans298 := thrift.NewTMemoryBufferLen(len(arg297))
		defer mbTrans298.Close()
		_, err299 := mbTrans298.WriteString(arg297)
		if err299 != nil {
			Usage()
			return
		}
		factory300 := thrift.NewTJSONProtocolFactory()
		jsProt301 := factory300.GetProtocol(mbTrans298)
		containerStruct2 := hbase.NewHbaseGetRowsWithColumnsArgs()
		err302 := containerStruct2.ReadField3(jsProt301)
		if err302 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Columns
		value2 := argvalue2
		arg303 := flag.Arg(4)
		mbTrans304 := thrift.NewTMemoryBufferLen(len(arg303))
		defer mbTrans304.Close()
		_, err305 := mbTrans304.WriteString(arg303)
		if err305 != nil {
			Usage()
			return
		}
		factory306 := thrift.NewTJSONProtocolFactory()
		jsProt307 := factory306.GetProtocol(mbTrans304)
		containerStruct3 := hbase.NewHbaseGetRowsWithColumnsArgs()
		err308 := containerStruct3.ReadField4(jsProt307)
		if err308 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Attributes
		value3 := argvalue3
		fmt.Print(client.GetRowsWithColumns(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getRowsTs":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetRowsTs requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		arg310 := flag.Arg(2)
		mbTrans311 := thrift.NewTMemoryBufferLen(len(arg310))
		defer mbTrans311.Close()
		_, err312 := mbTrans311.WriteString(arg310)
		if err312 != nil {
			Usage()
			return
		}
		factory313 := thrift.NewTJSONProtocolFactory()
		jsProt314 := factory313.GetProtocol(mbTrans311)
		containerStruct1 := hbase.NewHbaseGetRowsTsArgs()
		err315 := containerStruct1.ReadField2(jsProt314)
		if err315 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Rows
		value1 := argvalue1
		argvalue2, err316 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err316 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg317 := flag.Arg(4)
		mbTrans318 := thrift.NewTMemoryBufferLen(len(arg317))
		defer mbTrans318.Close()
		_, err319 := mbTrans318.WriteString(arg317)
		if err319 != nil {
			Usage()
			return
		}
		factory320 := thrift.NewTJSONProtocolFactory()
		jsProt321 := factory320.GetProtocol(mbTrans318)
		containerStruct3 := hbase.NewHbaseGetRowsTsArgs()
		err322 := containerStruct3.ReadField4(jsProt321)
		if err322 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Attributes
		value3 := argvalue3
		fmt.Print(client.GetRowsTs(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getRowsWithColumnsTs":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetRowsWithColumnsTs requires 5 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		arg324 := flag.Arg(2)
		mbTrans325 := thrift.NewTMemoryBufferLen(len(arg324))
		defer mbTrans325.Close()
		_, err326 := mbTrans325.WriteString(arg324)
		if err326 != nil {
			Usage()
			return
		}
		factory327 := thrift.NewTJSONProtocolFactory()
		jsProt328 := factory327.GetProtocol(mbTrans325)
		containerStruct1 := hbase.NewHbaseGetRowsWithColumnsTsArgs()
		err329 := containerStruct1.ReadField2(jsProt328)
		if err329 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Rows
		value1 := argvalue1
		arg330 := flag.Arg(3)
		mbTrans331 := thrift.NewTMemoryBufferLen(len(arg330))
		defer mbTrans331.Close()
		_, err332 := mbTrans331.WriteString(arg330)
		if err332 != nil {
			Usage()
			return
		}
		factory333 := thrift.NewTJSONProtocolFactory()
		jsProt334 := factory333.GetProtocol(mbTrans331)
		containerStruct2 := hbase.NewHbaseGetRowsWithColumnsTsArgs()
		err335 := containerStruct2.ReadField3(jsProt334)
		if err335 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Columns
		value2 := argvalue2
		argvalue3, err336 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err336 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		arg337 := flag.Arg(5)
		mbTrans338 := thrift.NewTMemoryBufferLen(len(arg337))
		defer mbTrans338.Close()
		_, err339 := mbTrans338.WriteString(arg337)
		if err339 != nil {
			Usage()
			return
		}
		factory340 := thrift.NewTJSONProtocolFactory()
		jsProt341 := factory340.GetProtocol(mbTrans338)
		containerStruct4 := hbase.NewHbaseGetRowsWithColumnsTsArgs()
		err342 := containerStruct4.ReadField5(jsProt341)
		if err342 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Attributes
		value4 := argvalue4
		fmt.Print(client.GetRowsWithColumnsTs(context.Background(), value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "mutateRow":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "MutateRow requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		arg345 := flag.Arg(3)
		mbTrans346 := thrift.NewTMemoryBufferLen(len(arg345))
		defer mbTrans346.Close()
		_, err347 := mbTrans346.WriteString(arg345)
		if err347 != nil {
			Usage()
			return
		}
		factory348 := thrift.NewTJSONProtocolFactory()
		jsProt349 := factory348.GetProtocol(mbTrans346)
		containerStruct2 := hbase.NewHbaseMutateRowArgs()
		err350 := containerStruct2.ReadField3(jsProt349)
		if err350 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Mutations
		value2 := argvalue2
		arg351 := flag.Arg(4)
		mbTrans352 := thrift.NewTMemoryBufferLen(len(arg351))
		defer mbTrans352.Close()
		_, err353 := mbTrans352.WriteString(arg351)
		if err353 != nil {
			Usage()
			return
		}
		factory354 := thrift.NewTJSONProtocolFactory()
		jsProt355 := factory354.GetProtocol(mbTrans352)
		containerStruct3 := hbase.NewHbaseMutateRowArgs()
		err356 := containerStruct3.ReadField4(jsProt355)
		if err356 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Attributes
		value3 := argvalue3
		fmt.Print(client.MutateRow(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "mutateRowTs":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "MutateRowTs requires 5 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		arg359 := flag.Arg(3)
		mbTrans360 := thrift.NewTMemoryBufferLen(len(arg359))
		defer mbTrans360.Close()
		_, err361 := mbTrans360.WriteString(arg359)
		if err361 != nil {
			Usage()
			return
		}
		factory362 := thrift.NewTJSONProtocolFactory()
		jsProt363 := factory362.GetProtocol(mbTrans360)
		containerStruct2 := hbase.NewHbaseMutateRowTsArgs()
		err364 := containerStruct2.ReadField3(jsProt363)
		if err364 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Mutations
		value2 := argvalue2
		argvalue3, err365 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err365 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		arg366 := flag.Arg(5)
		mbTrans367 := thrift.NewTMemoryBufferLen(len(arg366))
		defer mbTrans367.Close()
		_, err368 := mbTrans367.WriteString(arg366)
		if err368 != nil {
			Usage()
			return
		}
		factory369 := thrift.NewTJSONProtocolFactory()
		jsProt370 := factory369.GetProtocol(mbTrans367)
		containerStruct4 := hbase.NewHbaseMutateRowTsArgs()
		err371 := containerStruct4.ReadField5(jsProt370)
		if err371 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Attributes
		value4 := argvalue4
		fmt.Print(client.MutateRowTs(context.Background(), value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "mutateRows":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "MutateRows requires 3 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		arg373 := flag.Arg(2)
		mbTrans374 := thrift.NewTMemoryBufferLen(len(arg373))
		defer mbTrans374.Close()
		_, err375 := mbTrans374.WriteString(arg373)
		if err375 != nil {
			Usage()
			return
		}
		factory376 := thrift.NewTJSONProtocolFactory()
		jsProt377 := factory376.GetProtocol(mbTrans374)
		containerStruct1 := hbase.NewHbaseMutateRowsArgs()
		err378 := containerStruct1.ReadField2(jsProt377)
		if err378 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.RowBatches
		value1 := argvalue1
		arg379 := flag.Arg(3)
		mbTrans380 := thrift.NewTMemoryBufferLen(len(arg379))
		defer mbTrans380.Close()
		_, err381 := mbTrans380.WriteString(arg379)
		if err381 != nil {
			Usage()
			return
		}
		factory382 := thrift.NewTJSONProtocolFactory()
		jsProt383 := factory382.GetProtocol(mbTrans380)
		containerStruct2 := hbase.NewHbaseMutateRowsArgs()
		err384 := containerStruct2.ReadField3(jsProt383)
		if err384 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Attributes
		value2 := argvalue2
		fmt.Print(client.MutateRows(context.Background(), value0, value1, value2))
		fmt.Print("\n")
		break
	case "mutateRowsTs":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "MutateRowsTs requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		arg386 := flag.Arg(2)
		mbTrans387 := thrift.NewTMemoryBufferLen(len(arg386))
		defer mbTrans387.Close()
		_, err388 := mbTrans387.WriteString(arg386)
		if err388 != nil {
			Usage()
			return
		}
		factory389 := thrift.NewTJSONProtocolFactory()
		jsProt390 := factory389.GetProtocol(mbTrans387)
		containerStruct1 := hbase.NewHbaseMutateRowsTsArgs()
		err391 := containerStruct1.ReadField2(jsProt390)
		if err391 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.RowBatches
		value1 := argvalue1
		argvalue2, err392 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err392 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg393 := flag.Arg(4)
		mbTrans394 := thrift.NewTMemoryBufferLen(len(arg393))
		defer mbTrans394.Close()
		_, err395 := mbTrans394.WriteString(arg393)
		if err395 != nil {
			Usage()
			return
		}
		factory396 := thrift.NewTJSONProtocolFactory()
		jsProt397 := factory396.GetProtocol(mbTrans394)
		containerStruct3 := hbase.NewHbaseMutateRowsTsArgs()
		err398 := containerStruct3.ReadField4(jsProt397)
		if err398 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Attributes
		value3 := argvalue3
		fmt.Print(client.MutateRowsTs(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "atomicIncrement":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "AtomicIncrement requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		argvalue2 := []byte(flag.Arg(3))
		value2 := hbase.Text(argvalue2)
		argvalue3, err402 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err402 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.AtomicIncrement(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteAll":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "DeleteAll requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		argvalue2 := []byte(flag.Arg(3))
		value2 := hbase.Text(argvalue2)
		arg406 := flag.Arg(4)
		mbTrans407 := thrift.NewTMemoryBufferLen(len(arg406))
		defer mbTrans407.Close()
		_, err408 := mbTrans407.WriteString(arg406)
		if err408 != nil {
			Usage()
			return
		}
		factory409 := thrift.NewTJSONProtocolFactory()
		jsProt410 := factory409.GetProtocol(mbTrans407)
		containerStruct3 := hbase.NewHbaseDeleteAllArgs()
		err411 := containerStruct3.ReadField4(jsProt410)
		if err411 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Attributes
		value3 := argvalue3
		fmt.Print(client.DeleteAll(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteAllTs":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "DeleteAllTs requires 5 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		argvalue2 := []byte(flag.Arg(3))
		value2 := hbase.Text(argvalue2)
		argvalue3, err415 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err415 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		arg416 := flag.Arg(5)
		mbTrans417 := thrift.NewTMemoryBufferLen(len(arg416))
		defer mbTrans417.Close()
		_, err418 := mbTrans417.WriteString(arg416)
		if err418 != nil {
			Usage()
			return
		}
		factory419 := thrift.NewTJSONProtocolFactory()
		jsProt420 := factory419.GetProtocol(mbTrans417)
		containerStruct4 := hbase.NewHbaseDeleteAllTsArgs()
		err421 := containerStruct4.ReadField5(jsProt420)
		if err421 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Attributes
		value4 := argvalue4
		fmt.Print(client.DeleteAllTs(context.Background(), value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "deleteAllRow":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAllRow requires 3 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		arg424 := flag.Arg(3)
		mbTrans425 := thrift.NewTMemoryBufferLen(len(arg424))
		defer mbTrans425.Close()
		_, err426 := mbTrans425.WriteString(arg424)
		if err426 != nil {
			Usage()
			return
		}
		factory427 := thrift.NewTJSONProtocolFactory()
		jsProt428 := factory427.GetProtocol(mbTrans425)
		containerStruct2 := hbase.NewHbaseDeleteAllRowArgs()
		err429 := containerStruct2.ReadField3(jsProt428)
		if err429 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Attributes
		value2 := argvalue2
		fmt.Print(client.DeleteAllRow(context.Background(), value0, value1, value2))
		fmt.Print("\n")
		break
	case "increment":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "Increment requires 1 args")
			flag.Usage()
		}
		arg430 := flag.Arg(1)
		mbTrans431 := thrift.NewTMemoryBufferLen(len(arg430))
		defer mbTrans431.Close()
		_, err432 := mbTrans431.WriteString(arg430)
		if err432 != nil {
			Usage()
			return
		}
		factory433 := thrift.NewTJSONProtocolFactory()
		jsProt434 := factory433.GetProtocol(mbTrans431)
		argvalue0 := hbase.NewTIncrement()
		err435 := argvalue0.Read(jsProt434)
		if err435 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.Increment(context.Background(), value0))
		fmt.Print("\n")
		break
	case "incrementRows":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "IncrementRows requires 1 args")
			flag.Usage()
		}
		arg436 := flag.Arg(1)
		mbTrans437 := thrift.NewTMemoryBufferLen(len(arg436))
		defer mbTrans437.Close()
		_, err438 := mbTrans437.WriteString(arg436)
		if err438 != nil {
			Usage()
			return
		}
		factory439 := thrift.NewTJSONProtocolFactory()
		jsProt440 := factory439.GetProtocol(mbTrans437)
		containerStruct0 := hbase.NewHbaseIncrementRowsArgs()
		err441 := containerStruct0.ReadField1(jsProt440)
		if err441 != nil {
			Usage()
			return
		}
		argvalue0 := containerStruct0.Increments
		value0 := argvalue0
		fmt.Print(client.IncrementRows(context.Background(), value0))
		fmt.Print("\n")
		break
	case "deleteAllRowTs":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "DeleteAllRowTs requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		argvalue2, err444 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err444 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg445 := flag.Arg(4)
		mbTrans446 := thrift.NewTMemoryBufferLen(len(arg445))
		defer mbTrans446.Close()
		_, err447 := mbTrans446.WriteString(arg445)
		if err447 != nil {
			Usage()
			return
		}
		factory448 := thrift.NewTJSONProtocolFactory()
		jsProt449 := factory448.GetProtocol(mbTrans446)
		containerStruct3 := hbase.NewHbaseDeleteAllRowTsArgs()
		err450 := containerStruct3.ReadField4(jsProt449)
		if err450 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Attributes
		value3 := argvalue3
		fmt.Print(client.DeleteAllRowTs(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "scannerOpenWithScan":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ScannerOpenWithScan requires 3 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		arg452 := flag.Arg(2)
		mbTrans453 := thrift.NewTMemoryBufferLen(len(arg452))
		defer mbTrans453.Close()
		_, err454 := mbTrans453.WriteString(arg452)
		if err454 != nil {
			Usage()
			return
		}
		factory455 := thrift.NewTJSONProtocolFactory()
		jsProt456 := factory455.GetProtocol(mbTrans453)
		argvalue1 := hbase.NewTScan()
		err457 := argvalue1.Read(jsProt456)
		if err457 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg458 := flag.Arg(3)
		mbTrans459 := thrift.NewTMemoryBufferLen(len(arg458))
		defer mbTrans459.Close()
		_, err460 := mbTrans459.WriteString(arg458)
		if err460 != nil {
			Usage()
			return
		}
		factory461 := thrift.NewTJSONProtocolFactory()
		jsProt462 := factory461.GetProtocol(mbTrans459)
		containerStruct2 := hbase.NewHbaseScannerOpenWithScanArgs()
		err463 := containerStruct2.ReadField3(jsProt462)
		if err463 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Attributes
		value2 := argvalue2
		fmt.Print(client.ScannerOpenWithScan(context.Background(), value0, value1, value2))
		fmt.Print("\n")
		break
	case "scannerOpen":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ScannerOpen requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		arg466 := flag.Arg(3)
		mbTrans467 := thrift.NewTMemoryBufferLen(len(arg466))
		defer mbTrans467.Close()
		_, err468 := mbTrans467.WriteString(arg466)
		if err468 != nil {
			Usage()
			return
		}
		factory469 := thrift.NewTJSONProtocolFactory()
		jsProt470 := factory469.GetProtocol(mbTrans467)
		containerStruct2 := hbase.NewHbaseScannerOpenArgs()
		err471 := containerStruct2.ReadField3(jsProt470)
		if err471 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Columns
		value2 := argvalue2
		arg472 := flag.Arg(4)
		mbTrans473 := thrift.NewTMemoryBufferLen(len(arg472))
		defer mbTrans473.Close()
		_, err474 := mbTrans473.WriteString(arg472)
		if err474 != nil {
			Usage()
			return
		}
		factory475 := thrift.NewTJSONProtocolFactory()
		jsProt476 := factory475.GetProtocol(mbTrans473)
		containerStruct3 := hbase.NewHbaseScannerOpenArgs()
		err477 := containerStruct3.ReadField4(jsProt476)
		if err477 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Attributes
		value3 := argvalue3
		fmt.Print(client.ScannerOpen(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "scannerOpenWithStop":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ScannerOpenWithStop requires 5 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		argvalue2 := []byte(flag.Arg(3))
		value2 := hbase.Text(argvalue2)
		arg481 := flag.Arg(4)
		mbTrans482 := thrift.NewTMemoryBufferLen(len(arg481))
		defer mbTrans482.Close()
		_, err483 := mbTrans482.WriteString(arg481)
		if err483 != nil {
			Usage()
			return
		}
		factory484 := thrift.NewTJSONProtocolFactory()
		jsProt485 := factory484.GetProtocol(mbTrans482)
		containerStruct3 := hbase.NewHbaseScannerOpenWithStopArgs()
		err486 := containerStruct3.ReadField4(jsProt485)
		if err486 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Columns
		value3 := argvalue3
		arg487 := flag.Arg(5)
		mbTrans488 := thrift.NewTMemoryBufferLen(len(arg487))
		defer mbTrans488.Close()
		_, err489 := mbTrans488.WriteString(arg487)
		if err489 != nil {
			Usage()
			return
		}
		factory490 := thrift.NewTJSONProtocolFactory()
		jsProt491 := factory490.GetProtocol(mbTrans488)
		containerStruct4 := hbase.NewHbaseScannerOpenWithStopArgs()
		err492 := containerStruct4.ReadField5(jsProt491)
		if err492 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Attributes
		value4 := argvalue4
		fmt.Print(client.ScannerOpenWithStop(context.Background(), value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "scannerOpenWithPrefix":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ScannerOpenWithPrefix requires 4 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		arg495 := flag.Arg(3)
		mbTrans496 := thrift.NewTMemoryBufferLen(len(arg495))
		defer mbTrans496.Close()
		_, err497 := mbTrans496.WriteString(arg495)
		if err497 != nil {
			Usage()
			return
		}
		factory498 := thrift.NewTJSONProtocolFactory()
		jsProt499 := factory498.GetProtocol(mbTrans496)
		containerStruct2 := hbase.NewHbaseScannerOpenWithPrefixArgs()
		err500 := containerStruct2.ReadField3(jsProt499)
		if err500 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Columns
		value2 := argvalue2
		arg501 := flag.Arg(4)
		mbTrans502 := thrift.NewTMemoryBufferLen(len(arg501))
		defer mbTrans502.Close()
		_, err503 := mbTrans502.WriteString(arg501)
		if err503 != nil {
			Usage()
			return
		}
		factory504 := thrift.NewTJSONProtocolFactory()
		jsProt505 := factory504.GetProtocol(mbTrans502)
		containerStruct3 := hbase.NewHbaseScannerOpenWithPrefixArgs()
		err506 := containerStruct3.ReadField4(jsProt505)
		if err506 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Attributes
		value3 := argvalue3
		fmt.Print(client.ScannerOpenWithPrefix(context.Background(), value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "scannerOpenTs":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ScannerOpenTs requires 5 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		arg509 := flag.Arg(3)
		mbTrans510 := thrift.NewTMemoryBufferLen(len(arg509))
		defer mbTrans510.Close()
		_, err511 := mbTrans510.WriteString(arg509)
		if err511 != nil {
			Usage()
			return
		}
		factory512 := thrift.NewTJSONProtocolFactory()
		jsProt513 := factory512.GetProtocol(mbTrans510)
		containerStruct2 := hbase.NewHbaseScannerOpenTsArgs()
		err514 := containerStruct2.ReadField3(jsProt513)
		if err514 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Columns
		value2 := argvalue2
		argvalue3, err515 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err515 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		arg516 := flag.Arg(5)
		mbTrans517 := thrift.NewTMemoryBufferLen(len(arg516))
		defer mbTrans517.Close()
		_, err518 := mbTrans517.WriteString(arg516)
		if err518 != nil {
			Usage()
			return
		}
		factory519 := thrift.NewTJSONProtocolFactory()
		jsProt520 := factory519.GetProtocol(mbTrans517)
		containerStruct4 := hbase.NewHbaseScannerOpenTsArgs()
		err521 := containerStruct4.ReadField5(jsProt520)
		if err521 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Attributes
		value4 := argvalue4
		fmt.Print(client.ScannerOpenTs(context.Background(), value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "scannerOpenWithStopTs":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ScannerOpenWithStopTs requires 6 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		argvalue2 := []byte(flag.Arg(3))
		value2 := hbase.Text(argvalue2)
		arg525 := flag.Arg(4)
		mbTrans526 := thrift.NewTMemoryBufferLen(len(arg525))
		defer mbTrans526.Close()
		_, err527 := mbTrans526.WriteString(arg525)
		if err527 != nil {
			Usage()
			return
		}
		factory528 := thrift.NewTJSONProtocolFactory()
		jsProt529 := factory528.GetProtocol(mbTrans526)
		containerStruct3 := hbase.NewHbaseScannerOpenWithStopTsArgs()
		err530 := containerStruct3.ReadField4(jsProt529)
		if err530 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Columns
		value3 := argvalue3
		argvalue4, err531 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err531 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		arg532 := flag.Arg(6)
		mbTrans533 := thrift.NewTMemoryBufferLen(len(arg532))
		defer mbTrans533.Close()
		_, err534 := mbTrans533.WriteString(arg532)
		if err534 != nil {
			Usage()
			return
		}
		factory535 := thrift.NewTJSONProtocolFactory()
		jsProt536 := factory535.GetProtocol(mbTrans533)
		containerStruct5 := hbase.NewHbaseScannerOpenWithStopTsArgs()
		err537 := containerStruct5.ReadField6(jsProt536)
		if err537 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.Attributes
		value5 := argvalue5
		fmt.Print(client.ScannerOpenWithStopTs(context.Background(), value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "scannerGet":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "ScannerGet requires 1 args")
			flag.Usage()
		}
		tmp0, err538 := (strconv.Atoi(flag.Arg(1)))
		if err538 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := hbase.ScannerID(argvalue0)
		fmt.Print(client.ScannerGet(context.Background(), value0))
		fmt.Print("\n")
		break
	case "scannerGetList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ScannerGetList requires 2 args")
			flag.Usage()
		}
		tmp0, err539 := (strconv.Atoi(flag.Arg(1)))
		if err539 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := hbase.ScannerID(argvalue0)
		tmp1, err540 := (strconv.Atoi(flag.Arg(2)))
		if err540 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.ScannerGetList(context.Background(), value0, value1))
		fmt.Print("\n")
		break
	case "scannerClose":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "ScannerClose requires 1 args")
			flag.Usage()
		}
		tmp0, err541 := (strconv.Atoi(flag.Arg(1)))
		if err541 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := hbase.ScannerID(argvalue0)
		fmt.Print(client.ScannerClose(context.Background(), value0))
		fmt.Print("\n")
		break
	case "getRegionInfo":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetRegionInfo requires 1 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		fmt.Print(client.GetRegionInfo(context.Background(), value0))
		fmt.Print("\n")
		break
	case "append":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "Append requires 1 args")
			flag.Usage()
		}
		arg543 := flag.Arg(1)
		mbTrans544 := thrift.NewTMemoryBufferLen(len(arg543))
		defer mbTrans544.Close()
		_, err545 := mbTrans544.WriteString(arg543)
		if err545 != nil {
			Usage()
			return
		}
		factory546 := thrift.NewTJSONProtocolFactory()
		jsProt547 := factory546.GetProtocol(mbTrans544)
		argvalue0 := hbase.NewTAppend()
		err548 := argvalue0.Read(jsProt547)
		if err548 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.Append(context.Background(), value0))
		fmt.Print("\n")
		break
	case "checkAndPut":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "CheckAndPut requires 6 args")
			flag.Usage()
		}
		argvalue0 := []byte(flag.Arg(1))
		value0 := hbase.Text(argvalue0)
		argvalue1 := []byte(flag.Arg(2))
		value1 := hbase.Text(argvalue1)
		argvalue2 := []byte(flag.Arg(3))
		value2 := hbase.Text(argvalue2)
		argvalue3 := []byte(flag.Arg(4))
		value3 := hbase.Text(argvalue3)
		arg553 := flag.Arg(5)
		mbTrans554 := thrift.NewTMemoryBufferLen(len(arg553))
		defer mbTrans554.Close()
		_, err555 := mbTrans554.WriteString(arg553)
		if err555 != nil {
			Usage()
			return
		}
		factory556 := thrift.NewTJSONProtocolFactory()
		jsProt557 := factory556.GetProtocol(mbTrans554)
		argvalue4 := hbase.NewMutation()
		err558 := argvalue4.Read(jsProt557)
		if err558 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		arg559 := flag.Arg(6)
		mbTrans560 := thrift.NewTMemoryBufferLen(len(arg559))
		defer mbTrans560.Close()
		_, err561 := mbTrans560.WriteString(arg559)
		if err561 != nil {
			Usage()
			return
		}
		factory562 := thrift.NewTJSONProtocolFactory()
		jsProt563 := factory562.GetProtocol(mbTrans560)
		containerStruct5 := hbase.NewHbaseCheckAndPutArgs()
		err564 := containerStruct5.ReadField6(jsProt563)
		if err564 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.Attributes
		value5 := argvalue5
		fmt.Print(client.CheckAndPut(context.Background(), value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
