package scripts

import (
	"database/sql"
	"fmt"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

var mysqlPasswordList = []string{"123456", "111111", "toor", "password", "root", ""}

func MySQLCrack(args *ScriptScanArgs) (*ScriptScanResult, error) {
	for _, passwd := range mysqlPasswordList {
		db, err := sql.Open("mysql", fmt.Sprintf("root:%v@tcp(%v:3306)", passwd, args.Host))
		if err != nil {
			continue
		}
		defer db.Close()
		err = db.<PERSON>()
		if err != nil {
			if strings.Index(err.Error(), "denied for") < 1 {
				break
			} else {
				continue
			}
		}
		return &ScriptScanResult{Vulnerable: true, Output: fmt.Sprintf("%v/user=root&passwd=%v ", args.Host, passwd)}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("mysql_weak_password_vul.xml", MySQLCrack)
}
