package db

import (
	"strings"
	"sync"
	"time"
	"websec/config"
	"websec/utils/log"

	elastic "gopkg.in/olivere/elastic.v5"
)

func getEsClient(options config.ElasticsearchConfig) (*elastic.Client, error) {
	return elastic.NewClient(
		elastic.SetURL(options.URLs...),
		elastic.SetBasicAuth(options.Username, options.Password),
		elastic.SetHealthcheckInterval(10*time.Second),
		elastic.SetRetrier(elastic.NewBackoffRetrier(elastic.NewConstantBackoff(15*time.Second))),
	)
}

type esManager struct {
	sessions map[string]*elastic.Client
	lock     *sync.RWMutex
}

func newEsManager() *esManager {
	return &esManager{
		sessions: make(map[string]*elastic.Client),
		lock:     new(sync.RWMutex),
	}
}

func getEsKey(info config.ElasticsearchConfig) string {
	return strings.Join(info.URLs, ",")
}

func (m *esManager) Get(info config.ElasticsearchConfig) (*elastic.Client, error) {
	key := getEsKey(info)
	m.lock.Lock()
	if session, ok := m.sessions[key]; ok {
		m.lock.Unlock()
		return session, nil
	}
	m.lock.Unlock()

	c, err := getEsClient(info)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}
	m.lock.Lock()
	m.sessions[key] = c
	m.lock.Unlock()
	return c, nil
}

var esmanager = newEsManager()

func GetES(info config.ElasticsearchConfig) (*elastic.Client, error) {
	return esmanager.Get(info)
}
