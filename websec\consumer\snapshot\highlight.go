package snapshot

import (
	"bufio"
	"bytes"
	"errors"
	"fmt"
	"regexp"
	"sort"
	"unicode/utf8"
	"websec/utils/log"
)

func bytesToStringLines(content []byte) (lines []string, err error) {
	if content == nil {
		return nil, errors.New("content cannot be nil")
	}

	buf := bytes.NewBuffer(content)
	scanner := bufio.NewScanner(buf)
	scanner.Buffer(make([]byte, 64*1024), 1024*1024)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}
	err = scanner.Err()
	return
}

func mergeChange(chunks []string, doc *[]string) {
	*doc = append(*doc, `<span style="border:1px dashed red;padding:5px;background-color:yellow;display:inline-block;">`)
	*doc = append(*doc, chunks...)
	*doc = append(*doc, `</span>`)
}

func visualDiff(a, b []string) ([]string, []string) {
	left := []string{}
	right := []string{}

	s := NewMatcher(a, b)
	for _, op := range s.GetOpCodes() {
		if op.Tag == 'e' {
			left = append(left, a[op.I1:op.I2]...)
			right = append(right, b[op.J1:op.J2]...)
		} else {
			leftchunks := a[op.I1:op.I2]
			rightchunks := b[op.J1:op.J2]
			mergeChange(leftchunks, &left)
			mergeChange(rightchunks, &right)
		}
	}
	return left, right
}

func highStyle(s []byte) []byte {
	out := fmt.Sprintf("<span style='background:yellow'>%s</span>", string(s))
	return []byte(out)
}

func highlightSensitiveWords(words []MatchedSensitiveWord, page []byte) ([]byte, error) {
	for _, word := range words {
		r, err := regexp.Compile(`(?si)` + word.Word)
		if err != nil {
			log.Error(err)
			continue
		}
		page = r.ReplaceAllFunc(page, highStyle)
	}
	return page, nil
}

var chineseNumMap = map[rune][]byte{
	'零': []byte{'0'},
	'一': []byte{'1'},
	'二': []byte{'2'},
	'三': []byte{'3'},
	'四': []byte{'4'},
	'五': []byte{'5'},
	'六': []byte{'6'},
	'七': []byte{'7'},
	'八': []byte{'8'},
	'九': []byte{'9'},
	'十': []byte{'1', '0'},
}

func mappingChineseToNum(r rune, padding bool) []byte {
	if v, ok := chineseNumMap[r]; ok {
		if padding {
			paddingV := []byte{' ', ' ', ' '}
			copy(paddingV[len(paddingV)-len(v):], v)
			return paddingV
		}
		return v
	}
	return []byte(string(r))
}

func replaceNums(s []byte, padding bool) []byte {
	nb := make([]byte, len(s))
	offset := 0
	for i := 0; i < len(s); {
		wid := 1
		r := rune(s[i])
		if r >= utf8.RuneSelf {
			r, wid = utf8.DecodeRune(s[i:])
		}
		w := mappingChineseToNum(r, padding)
		copy(nb[offset:], w)
		i += wid
		offset += len(w)
	}
	return nb[:offset]
}

var obtext = []byte("<span style= 'background-color: #ff0000;color: #fff;'>（混淆敏感词）</span>")

func highlightObuscateWords(matchedWords []MatchedSensitiveWord, page []byte) ([]byte, error) {

	insertPositions := make(map[int]struct{})
	for _, matchedWord := range matchedWords {
		if matchedWord.FromOcr {
			continue
		}

		position := matchedWord.Position
		word := []byte(matchedWord.Word)
		wordDigit := replaceNums(word, false)
		endPosition := position + len(wordDigit)
		if len(page) < endPosition {
			err := errors.New("page content does not match which shall be with message")
			log.Error(err)
			return []byte{}, err
		}
		tmpWord := page[endPosition-len(word) : endPosition]
		if !bytes.Equal(bytes.ToLower(tmpWord), bytes.ToLower(word)) {

			insertPositions[endPosition] = struct{}{}
		}
	}

	if len(insertPositions) > 0 {
		var outPage bytes.Buffer
		orderedPositions := make([]int, 0, len(insertPositions))
		for k := range insertPositions {
			orderedPositions = append(orderedPositions, k)
		}
		sort.Ints(orderedPositions)

		pre, post := 0, 0
		for _, v := range orderedPositions {
			pre, post = post, v
			outPage.Write(page[pre:post])
			outPage.Write(obtext)
		}
		outPage.Write(page[post:])
		return outPage.Bytes(), nil
	}
	return page, nil
}
