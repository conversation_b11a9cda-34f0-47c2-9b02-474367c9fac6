package collectors

import (
	"context"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (collector *Collector) AddSensitiveWord(val *schema.FinalSensitiveWordResult) {
	id, err := collector.saveToMongo(consts.CollectionFoundSensitiveWords, val)
	if err != nil {
		return
	}
	val.ID = id
	collector.producer.Produce(consts.TopicSensitiveWordResults, val)
}

func (collector *Collector) AddOcrSensitiveWord(val *schema.FinalOcrSensitiveWordResult) {
	id, err := collector.saveToMongo(consts.CollectionFoundOcrSensitiveWords, val)
	if err != nil {
		return
	}
	val.ID = id
	collector.producer.Produce(consts.TopicOcrSensitiveWordResults, val)
}

func (collector *Collector) AddBlackLink(val *schema.FinalBlackLinkResult) {
	id, err := collector.saveToMongo(consts.CollectionFoundBlackLinks, val)
	if err != nil {
		return
	}
	val.ID = id
	collector.producer.Produce(consts.TopicBlackLinkResults, val)
}

func (collector *Collector) AddContentChange(val *schema.FinalContentChangeResult) {
	id, err := collector.saveToMongo(consts.CollectionFoundContentChange, val)
	if err != nil {
		log.Errorln("------------------ producer.go AddContentChange Error:", err)
		return
	}
	val.ID = id
	collector.producer.Produce(consts.TopicContentChangeResults, val)
}

func (collector *Collector) AddlVul(val *schema.FoundVulDoc) {
	log.Info("collect vuls", val.AssetID)
	id, err := collector.saveToMongo(consts.CollectionFoundVuls, val)
	if err != nil {
		return
	}
	val.ID = id
	if val.Context == nil {
		val.Context = map[string]interface{}{}
	}
	method, ok := val.Context["method"]
	if !ok || method == "" {
		val.Context["method"] = val.Scan.Method
	}
	collector.producer.Produce(consts.TopicVulResults, val)
}

func (collector *Collector) AddTrojan(val *schema.FinalTrojanResult) {
	id, err := collector.saveToMongo(consts.CollectionFoundTrojans, val)
	if err != nil {
		return
	}
	val.ID = id
	collector.producer.Produce(consts.TopicTrojanResults, val)
}

func (collector *Collector) AddPhishing(val *schema.FinalPhishingResult) {
	id, err := collector.saveToMongo(consts.CollectionFoundPhishing, val)
	if err != nil {
		return
	}
	val.ID = id
	collector.producer.Produce(consts.TopicPhishingResults, val)
}

func (collector *Collector) AddSnapshotSensitiveWord(val *schema.SnapshotSensitiveWordDoc) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	id, err := primitive.ObjectIDFromHex(val.SnapshotID)
	if err != nil {
		log.Errorln("add snapshot sensitiveword error:", err)
		return
	}

	res, err := collector.dbConnection.GetMongoDatabase().Collection(consts.CollectionSnapshotSensitiveWord).UpdateOne(ctx,
		bson.M{"_id": id},
		bson.M{"$set": bson.M{
			"snapshot_url": val.SnapshotURL,
			"status":       val.Status},
		})
	if err != nil {
		log.Errorln("update error:", err)
		return
	}

	if res.ModifiedCount != 1 {
		log.Errorln("modified count != 1")
		return
	}
	collector.producer.Produce(consts.TopicSnapshotSensitiveWordResults, val)
}

func (collector *Collector) AddSnapshotContentChange(val *schema.FinalSnapshotContentChangeResult) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	id, err := primitive.ObjectIDFromHex(val.ID)
	if err != nil {
		log.Errorln("add snapshot contentchange error:", err)
		return
	}

	res, err := collector.dbConnection.GetMongoDatabase().Collection(consts.CollectionFoundContentChange).UpdateOne(ctx,
		bson.M{"_id": id},
		bson.M{"$set": bson.M{
			"new_snapshot": val.NewSnapshotURL,
			"old_snapshot": val.OldSnapshotURL,
			"status":       val.Status,
			"old_tool":     val.OldTool,
			"new_tool":     val.NewTool,
			"oldjs":        val.OldJS,
			"newjs":        val.NewJS,
			"snapshot_err": val.Err},
		})
	if err != nil || res.ModifiedCount != 1 {
		log.Errorln("update snapshot contentchange error:", err)
		return
	}
	collector.producer.Produce(consts.TopicSnapshotContentChangeResults, val)
}

func (collector *Collector) saveToMongo(collection string, val interface{}) (primitive.ObjectID, error) {
	const retry = 3
	var res *mongo.InsertOneResult
	var err error

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	for i := 1; i <= retry; i++ {
		res, err = collector.dbConnection.GetMongoDatabase().Collection(collection).InsertOne(ctx, val)

		if err != nil {
			log.Errorf("---------- producer.go saveToMongo error %s %v", collection, err)
		} else {
			newID := res.InsertedID.(primitive.ObjectID)

			switch collection {
			case consts.CollectionFoundSensitiveWords:
				if doc, ok := val.(*schema.FoundSensitiveWordsDoc); ok {
					doc.ID = newID
					// go func() {
					if err := collector.handleSensitiveWordSnapshot(newID, doc); err != nil {
						log.Errorln("handleSensitiveWordSnapshot error:", err)
					}
					// }()
				}
			case consts.CollectionFoundContentChange:
				if doc, ok := val.(*schema.FinalContentChangeResult); ok {
					doc.ID = newID
					// go func() {
					if err := collector.handleContentChangeSnapshot(newID, doc); err != nil {
						log.Errorln("handleContentChangeSnapshot error:", err)
					}
					// }()
				}
			}

			return newID, nil
		}

		time.Sleep(time.Millisecond * 10 * time.Duration(i))
	}

	return primitive.ObjectID{}, err
}
