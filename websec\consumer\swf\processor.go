package swf

import (
	"context"
	"sync"
	"time"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/consumer/swf/jpex"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/stream"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Options struct {
	Consumer     *stream.Consumer
	Producer     *stream.Producer
	JpexClient   *jpex.JpexClient
	DBConnection *db.DBConnection
	Concurrency  int64
}

type Processor struct {
	options      *Options
	consumer     *stream.Consumer
	producer     *stream.Producer
	dbConnection *db.DBConnection
	concurrency  int64
	jpexClient   *jpex.JpexClient

	consumeSema   *semaphore.Weighted
	semaWaitGroup sync.WaitGroup
}

func NewProcessor(options *Options) (*Processor, error) {
	processor := &Processor{
		options:      options,
		consumer:     options.Consumer,
		producer:     options.Producer,
		dbConnection: options.DBConnection,
		concurrency:  options.Concurrency,
		jpexClient:   options.JpexClient,
		consumeSema:  semaphore.NewWeighted(options.Concurrency),
	}
	return processor, nil
}

func (processor *Processor) Run() {
	processor.producer.Go()
	processor.consumer.SubscribeTopics(processor.Topics())
	processor.consumer.Go()

	var err error
	for msg := range processor.consumer.Messages() {
		if err = processor.consumeSema.Acquire(context.TODO(), 1); err == nil {
			processor.semaWaitGroup.Add(1)
			go processor.Process(msg, false)
		} else {
			processor.Process(msg, true)
		}
	}

	processor.producer.Close()
	processor.dbConnection.CloseThriftClient()
	log.Infoln("Ocr Processor Grace Exit")
}

func (processor *Processor) Stop() {
	processor.consumer.Close()
}

func (processor *Processor) Topics() []string {
	return []string{
		consts.TopicCrawledSwfURLs,
	}
}

func (processor *Processor) Process(msg *stream.Message, sync bool) error {
	defer func() {
		if !sync {
			processor.consumeSema.Release(1)
			processor.semaWaitGroup.Done()
		}
	}()

	topic := msg.Topic
	switch topic {
	case consts.TopicCrawledSwfURLs:
		return processor.processSwfURLsMessage(msg)
	default:
		log.Errorln("unknown message topic: ", topic)
	}
	return nil
}

func (processor *Processor) saveToMongo(ctx context.Context, collection string, val interface{}) (primitive.ObjectID, error) {

	retry := 3
	var res *mongo.InsertOneResult
	var err error
	for i := 1; i <= retry; i++ {
		res, err = processor.dbConnection.GetMongoDatabase().Collection(collection).InsertOne(ctx, val)
		if err != nil {
			log.Errorf("saveToMongo %s %v", collection, err)
		} else {
			return res.InsertedID.(primitive.ObjectID), nil
		}
		time.Sleep(time.Millisecond * 10 * time.Duration(i))
	}

	return primitive.ObjectID{}, err
}

func (processor *Processor) AddFoundSwfWithURLResult(ctx context.Context, val *schema.FoundSwfWithURLDoc) error {
	id, err := processor.saveToMongo(ctx, consts.CollectionFoundBlackLinks, val) //just save this to found_black_links collection
	if err != nil {
		return err
	}
	val.ID = id
	processor.producer.Produce(consts.TopicSwfWithURLResults, val)
	return nil
}
