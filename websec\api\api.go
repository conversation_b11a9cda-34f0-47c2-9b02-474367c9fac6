package api

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"time"
	"websec/common"
	"websec/common/db"
	"websec/common/schema"
	"websec/config"
	"websec/utils/chrome"
	"websec/utils/log"
	"websec/utils/stream"

	"github.com/gorilla/mux"
)

const (
	SUCCESS = "success"
	FAIL    = "fail"
)

func newSuccessResponse(data interface{}) *schema.Response {
	return &schema.Response{
		Status:  SUCCESS,
		Message: "",
		Data:    data,
	}
}

func newFailResponse(message string) *schema.Response {
	return &schema.Response{
		Status:  FAIL,
		Message: message,
	}
}

func newFailDataResponse(data interface{}) *schema.Response {
	return &schema.Response{
		Status:  FAIL,
		Message: "",
		Data:    data,
	}
}

type Options struct {
	Address      string
	DBConnection *db.DBConnection
	Producer     *stream.Producer
}

type API struct {
	options  *Options
	server   *http.Server
	producer *stream.Producer
	settings *config.Config
}

func NewAPI(options *Options, settings *config.Config) *API {
	api := &API{
		options: options,
		server:  nil,
	}
	producer, err := stream.NewProducer(&stream.ProducerOptions{
		Brokers:         settings.Kafka.Brokers,
		MessageMaxBytes: settings.Kafka.MessageMaxBytes,
	})
	if err != nil {
		log.Fatalln("failed to setup producer:", err)
		return nil
	}
	producer.Go()
	api.producer = producer
	api.settings = settings

	api.initServer()
	return api
}

func (api *API) StartServer() {
	listener, err := net.Listen("tcp", api.options.Address)
	if err != nil {
		log.Fatalln("net.Listen error, ", err.Error())
	}

	log.Infoln(api.server.Serve(listener))
}

func (api *API) initServer() {
	handler := api.buildHandler()
	api.server = &http.Server{
		Handler:      handler,
		ReadTimeout:  60 * time.Second,
		WriteTimeout: 60 * time.Second,
	}
	api.producer.Go()
}

func (api *API) writeResponse(rw http.ResponseWriter, response *schema.Response) {
	rw.Header().Set("Access-Control-Allow-Origin", "*")
	data, _ := json.Marshal(response)
	_, err := rw.Write(data)
	if err != nil {
		log.Errorln(err, response)
	}
}

func costTimeHandler(handler func(rw http.ResponseWriter, req *http.Request)) func(rw http.ResponseWriter, req *http.Request) {
	return func(rw http.ResponseWriter, req *http.Request) {
		startTime := time.Now()
		handler(rw, req)
		costTime := time.Since(startTime)
		log.Infof("%v %v ---> cost: %v ms.", req.Method, req.RequestURI, costTime.Seconds()*1000)
	}
}

func (api *API) buildHandler() http.Handler {
	handler := mux.NewRouter()

	handler.HandleFunc("/api/source-code/sensitive-word/{source_id:[0-9a-f]{24}}", costTimeHandler(api.sensitiveWordSourceCodeHandler)).Methods(http.MethodGet)
	handler.HandleFunc("/api/source-code/blacklink/{source_id}", costTimeHandler(api.blacklinkSourceCodeHandler)).Methods(http.MethodGet)
	handler.HandleFunc("/api/source-code/vuls/", costTimeHandler(api.vulSourceCodeHandler)).Methods(http.MethodPost)

	handler.HandleFunc("/api/tasks/", costTimeHandler(api.taskStatusHandler)).Methods(http.MethodGet)

	handler.HandleFunc("/api/result/{type}", costTimeHandler(api.resultHandler)).Methods(http.MethodGet)

	handler.HandleFunc("/api/breakcontinue", costTimeHandler(api.breakContinueHandler)).Methods(http.MethodPost)

	handler.HandleFunc("/api/finish/snapshot/fghk", costTimeHandler(api.fghkSnapshotHandler)).Methods(http.MethodGet)

	handler.HandleFunc("/api/snapshot/content-change/{id:[0-9a-f]{24}}", costTimeHandler(api.contentChangeSnapshotHandler)).Methods(http.MethodGet)
	handler.HandleFunc("/api/snapshot/sensitive-word", costTimeHandler(api.sensitiveWordSnapshotHandler)).Methods(http.MethodPost)

	handler.HandleFunc("/media/{id:[0-9a-f]{24}.png|[0-9a-f]{24}.jpg}", costTimeHandler(api.getImageHandler)).Methods(http.MethodGet)
	handler.HandleFunc("/media/upload", costTimeHandler(api.uploadImageHandler)).Methods(http.MethodPost)

	handler.HandleFunc("/api/sensitivewords/", costTimeHandler(api.sensitiveWordsHandler))
	handler.HandleFunc("/api/blackwords/", costTimeHandler(api.blackwordsHandler))

	handler.HandleFunc("/api/vul/smart/", costTimeHandler(api.vulSmartScanHandler)).Methods(http.MethodPost)

	handler.HandleFunc("/api/asset/", costTimeHandler(api.assetHandler))
	handler.HandleFunc("/api/asset/{id:[0-9a-f]{24}}", costTimeHandler(api.assetHandler))

	handler.HandleFunc("/api/asset/urls/{id:[0-9a-f]{24}}", costTimeHandler(api.assetUrlsHandler))

	handler.HandleFunc("/api/asset/WSDL/", costTimeHandler(api.assetWsdlHandler))
	handler.HandleFunc("/api/asset/WSDL/{id:[0-9a-f]{24}}", costTimeHandler(api.assetWsdlHandler))

	handler.HandleFunc("/api/sensitive-words/reload", costTimeHandler(api.reloadSensitiveWords))

	handler.HandleFunc("/api/snapshot/capture", costTimeHandler(CaptureScreenshot)).Methods(http.MethodPost)

	return handler
}

type SnapshotPayload struct {
	Filename string `json:"filename"`
}

// CaptureScreenshot 通过传入的 filename 参数，在 /tmp 下查找 HTML 文件，并使用 headless Chrome 渲染，生成截图保存到 /home/<USER>
func CaptureScreenshot(w http.ResponseWriter, r *http.Request) {
	// 从请求体中解析 JSON 数据，格式要求：{ "filename": "test.html" }
	payload := new(SnapshotPayload)
	err := json.Unmarshal(getHttpBody(r), payload)
	if err != nil {
		log.Errorln("get request body error: ", err)
		http.Error(w, "invalid JSON payload", http.StatusBadRequest)
		return
	}

	if payload.Filename == "" {
		http.Error(w, "missing 'filename' field", http.StatusBadRequest)
		return
	}
	filename := payload.Filename

	filePath := filename
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.Error(w, fmt.Sprintf("file %s not found", filePath), http.StatusNotFound)
		return
	}

	injectJS := &common.InjectJavaScript{
		InjectAfterLoad:  `window.alert = function () { return false; };window.prompt = function (msg, input) { return input; };window.confirm = function () { return true; };window.close = function () { return false; };console.log("inject after load.");`,
		InjectBeforeLoad: `console.log("inject after load.");`,
	}

	// 创建 headless chrome 客户端（此处使用默认地址，必要时请调整）
	client := chrome.NewClient(&chrome.Options{
		Addr:   "127.0.0.1:9888",
		Script: injectJS,
	})

	// 生成 chrome 可访问的文件 URL
	chromeURL := "file://" + filePath
	// 调用 chrome 客户端截图接口
	screenshotData, err := client.ScreenShot(chromeURL)
	if err != nil {
		http.Error(w, fmt.Sprintf("failed to capture screenshot: %v", err), http.StatusInternalServerError)
		return
	}

	// 生成截图文件名（包含当前时间戳防止重名）
	imageName := fmt.Sprintf("%d_%s.jpg", time.Now().Unix(), filepath.Base(filename))
	imagePath := filepath.Join("/home/<USER>", imageName)
	// 保存截图
	if err := ioutil.WriteFile(imagePath, screenshotData, 0644); err != nil {
		http.Error(w, fmt.Sprintf("failed to save screenshot: %v", err), http.StatusInternalServerError)
		return
	}

	// 返回结果，假设前端通过 /media/ 访问 /home/<USER>
	response := map[string]interface{}{
		"status":     "success",
		"image_path": imagePath,
		"url":        "/media/" + imageName,
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
