package schema

import (
	"fmt"
	"strings"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SiteURL struct {
	ID             primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	AssetID        string             `json:"asset_id" bson:"asset_id,omitempty"`
	JobID          string             `json:"job_id" bson:"job_id,omitempty"`
	ReferURL       string             `json:"refer_url" bson:"refer_url"`
	RefererHost    string             `json:"referer_host" bson:"referer_host"`
	RefererURLHash string             `json:"referer_url" bson:"referer_url_hash"`
	Methods        int32              `json:"methods" bson:"methods"`
	Scheme         string             `json:"scheme" bson:"scheme"`
	Host           string             `json:"host" bson:"host"`
	URI            string             `json:"uri" bson:"uri"`
	URLHash        string             `json:"url_hash" bson:"url_hash"`
	Depth          int32              `json:"depth" bson:"depth"`
	MediaType      string             `json:"media_type" bson:"media_type"`
	PostData       string             `json:"post_data,omitempty" bson:"post_data,omitempty"`
	URL            string             `json:"url" bson:"url"`

	url string `json:"-" bson:"-"`
}

func (u *SiteURL) String() string {
	if u.url == "" {
		u.url = fmt.Sprintf("%v://%v%v", u.Scheme, u.Host, u.URI)
	}
	return u.url
}

func (u *SiteURL) IsStatic() bool {
	return !strings.HasPrefix(u.MediaType, "text")
}

type URLReferee struct {
	ID             primitive.ObjectID `bson:"_id,omitempty"`
	URLId          primitive.ObjectID `bson:"url_id"`
	RefereeHost    string             `bson:"referee_host"`
	RefereeURLHash string             `bson:"referee_url_hash"`
}
