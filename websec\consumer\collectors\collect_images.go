package collectors

import (
	"context"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (collector *Collector) saveNewImg(saveURLs []*schema.SiteURL) error {
	rawCount := len(saveURLs)
	models := make([]mongo.WriteModel, rawCount)
	var index int
	for _, v := range saveURLs {

		update := bson.M{
			"$setOnInsert": bson.M{
				"host":           v.Host,
				"url_hash":       v.URLHash,
				"refer_host":     v.<PERSON><PERSON>er<PERSON>ost,
				"refer_url_hash": v.RefererURLHash,
			},
			"$currentDate": bson.M{
				"updated_at": true,
			},
		}
		models[index] = mongo.NewUpdateOneModel().SetFilter(bson.M{
			"host":           v.Host,
			"url_hash":       v.URLHash,
			"refer_url_hash": v.RefererURLHash,
		}).SetUpdate(update).SetUpsert(true)
		index++
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	res, err := collector.dbConnection.GetMongoDatabase().Collection(consts.CollectionImgRefer).
		BulkWrite(ctx, models, options.BulkWrite().SetOrdered(false))
	if err != nil {
		log.Errorf("saveNewImg error %v", err)
	} else {
		log.Infoln("saveNewImg result:", res.InsertedCount, res.ModifiedCount, res.UpsertedCount)
		if res.UpsertedCount != int64(rawCount) {
			log.Warnf("saveNewImg not all UpsertedCount%d rawCount%d modifiedCount%d", res.UpsertedCount, rawCount, res.ModifiedCount)
		} else {
			log.Infoln("saveNewImg sucess all", res.UpsertedCount, rawCount)
		}
	}

	return nil
}
