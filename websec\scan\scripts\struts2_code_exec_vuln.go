package scripts

import (
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"
)

var structs2GetPattern = regexp.MustCompile(`(?i)<a\s+?[^>]*?href="([^"]+?\.action|[^"]+?\.do)[^"]*?"[^>]*?>`)
var structs2PostPattern = regexp.MustCompile(`(?i)<form\s*?[^>]+?\s*?action="([^"]+?\.action|[^"]+?\.do)"\s+?method="post">`)

func Struts2CodeExecVul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	rawurl := constructURL(args, "/")
	_, body, err := httpClient.GetTimeout(nil, rawurl, 10*time.Second)
	if err != nil {
		return nil, err
	}
	groups := structs2GetPattern.FindAllSubmatch(body, -1)
	if groups == nil {
		groups = structs2PostPattern.FindAllSubmatch(body, -1)
	}

	for _, group := range groups {
		newurl := string(group[1])
		if strings.HasPrefix(newurl, "http://") || strings.HasPrefix(newurl, "https://") {
			parts, err := url.Parse(newurl)
			if err != nil || parts.Hostname() != args.Host {
				continue
			}
		} else {
			newurl = constructURL(args, newurl)
		}
		newurl += "?redirect:http://scan.websec.cn"

		req, err := http.NewRequest("GET", newurl, nil)
		if err != nil {
			continue
		}
		req.Header.Set("User-Agent", scriptUserAgent)
		resp, err := goHTTPClient.Do(req)
		if err != nil {
			continue
		}
		defer resp.Body.Close()
		if resp.Request.URL.String() == "http://scan.websec.cn/" {
			return &ScriptScanResult{Vulnerable: true, Output: newurl}, nil
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("struts2_action_code_exec_vuln.xml", Struts2CodeExecVul)
}
