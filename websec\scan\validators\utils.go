package validators

import (
	"net/http"
	"strings"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

func DoHTTPRequest(rawURL string) (string, int, error) {
	client := utils.NewHTTPClient("")
	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.SetRequestURI(rawURL)
	header := &request.Header
	header.SetMethod(http.MethodGet)

	err := utils.DoHTTPRequest(client, request, response)
	if err != nil {
		return "", 0, err
	}

	body, err := utils.GetUtf8Body(response)
	if err != nil {
		return "", 0, err
	}

	bodyStr := string(body)
	return bodyStr, response.StatusCode(), nil
}

func UnQuote(s string) string {
	if strings.HasPrefix(s, "\"") && strings.HasSuffix(s, "\"") || strings.HasPrefix(s, "'") && strings.HasSuffix(s, "'") {
		return s[1 : len(s)-1]
	}
	return s
}
