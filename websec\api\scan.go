package api

import (
	"context"
	"encoding/json"
	"net/http"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/go-redis/redis"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	ActionCodeSuccess   int = iota
	ActionCodeHaveStop      //已经停止了
	ActionCodeAdding        //已经开启了一个立即扫描任务
	ActionCodeNotStop       //没有停止，不需要继续
	ActionAssetNotExist     //资产不存在
	ActionSysError          //服务出错
)

type scanBreakContinueRequest struct {
	AssetID string `json:"asset_id"`
	Host    string `json:"host"`
	Action  int    `json:"action"`
}

type scanBreakContinueResponse struct {
	Code       int    `json:"code"`
	Msg        string `json:"msg"`
	Action     int    `json:"action"`      //目前的action
	TaskStatus int    `json:"task_status"` //扫描任务执行状态
	TaskPause  int    `json:"task_pause"`  //扫描任务是否暂停,0 没有暂停,1暂停
}

func (api *API) breakContinueHandler(rw http.ResponseWriter, req *http.Request) {
	reqData := new(scanBreakContinueRequest)
	err := json.Unmarshal(getHttpBody(req), reqData)
	if err != nil {
		log.Errorln("get request body error:", err)
		api.writeResponse(rw, newFailResponse("get request body error"))
		return
	}

	if reqData.Action == consts.ActionStop || reqData.Action == consts.ActionContinue ||
		reqData.Action == consts.ActionNone {
		api.scanStopContinue(rw, reqData)
	} else if reqData.Action == consts.ActionTODO {
		api.scanAtOnce(rw, reqData.AssetID)
	} else {
		api.writeResponse(rw, newFailResponse("action not support"))
	}
}

func (api *API) scanStopContinue(rw http.ResponseWriter, reqData *scanBreakContinueRequest) {
	log.Infoln("scan breakcontinue action", reqData.Host, reqData.Action)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	result := new(scanBreakContinueResponse)
	task := new(schema.Task)
	err := api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionTasks).FindOne(ctx,
		bson.M{
			"host": reqData.Host,
			"type": "scan",
		}).Decode(task)
	if err != nil {
		log.Errorln("get task from mongo error:", err)
		result.Code = ActionAssetNotExist
		result.Msg = "asset not exist"
		api.writeResponse(rw, newFailDataResponse(result))
		return
	}

	scanAction := new(schema.BreakScanAction)
	err = api.options.DBConnection.HGetObject(consts.RedisBreakContinueScan, reqData.Host, scanAction)
	if err != nil && err != redis.Nil {
		log.Errorln("get breakscan from redis error:", err)
		result.Code = ActionSysError
		result.Msg = "redis error" + err.Error()
		api.writeResponse(rw, newFailDataResponse(result))
		return
	}

	if reqData.Action == consts.ActionStop {
		if scanAction.Action == consts.ActionStop {
			result.Code = ActionCodeHaveStop
			result.Msg = "already stop"
			result.Action = scanAction.Action
			api.writeResponse(rw, newSuccessResponse(result))
			return
		} else {
			scanAction.Action = consts.ActionStop
			err = api.options.DBConnection.HSetObject(consts.RedisBreakContinueScan, reqData.Host, scanAction)
			if err != nil {
				result.Code = ActionSysError
				result.Msg = "redis error" + err.Error()
				api.writeResponse(rw, newFailDataResponse(result))
				return
			} else {
				result.Action = scanAction.Action
			}
		}
	} else if reqData.Action == consts.ActionContinue {
		if scanAction.Action == consts.ActionStop {
			err = api.options.DBConnection.HDel(consts.RedisBreakContinueScan, reqData.Host)
			if err != nil {
				result.Code = ActionSysError
				result.Msg = "redis error" + err.Error()
				api.writeResponse(rw, newFailDataResponse(result))
				return
			} else {
				result.Action = consts.ActionNone
			}
		} else {
			result.Code = ActionCodeNotStop
			result.Msg = "have not stop"
			result.Action = scanAction.Action
			api.writeResponse(rw, newSuccessResponse(result))
			return
		}
	} else if reqData.Action == consts.ActionNone {
		result.Action = scanAction.Action
		result.TaskStatus = int(task.Status)
		if !task.Track.LastTaskID.IsZero() {
			result.TaskPause = 1
		}
	}

	log.Infoln("scan breakcontinue success", reqData.Host, reqData.Action)
	result.Code = ActionCodeSuccess
	api.writeResponse(rw, newSuccessResponse(result))
}

func (api *API) scanAtOnce(rw http.ResponseWriter, assetID string) {
	result := new(scanBreakContinueResponse)
	val, err := api.options.DBConnection.HGetInt64(consts.RedisStartAtOnceScan, assetID)
	if err != nil && err != redis.Nil {
		result.Code = ActionSysError
		result.Msg = "redis error" + err.Error()
		api.writeResponse(rw, newFailDataResponse(result))
		return
	}

	if val == 1 {
		result.Code = ActionCodeAdding
		result.Msg = "already add a atonce scan in queue"
		api.writeResponse(rw, newFailDataResponse(result))
		return
	}

	objectID, err := primitive.ObjectIDFromHex(assetID)
	if err != nil {
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	res, err := api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionTasks).FindOne(ctx,
		bson.M{
			"asset_id":     objectID,
			"schedule.tag": consts.TaskTagScanAtOnce,
		}).DecodeBytes()
	if err != nil && err != mongo.ErrNoDocuments {
		result.Code = ActionSysError
		result.Msg = "mongo error" + err.Error()
		api.writeResponse(rw, newFailDataResponse(result))
		return
	}

	if err == nil && res != nil {
		result.Code = ActionCodeAdding
		result.Msg = "task is added"
		api.writeResponse(rw, newFailDataResponse(result))
		return
	}

	api.options.DBConnection.HSet(consts.RedisStartAtOnceScan, assetID, 1)
	result.Code = ActionCodeSuccess
	result.Msg = "add atonce scan success"
	api.writeResponse(rw, newSuccessResponse(result))
}
