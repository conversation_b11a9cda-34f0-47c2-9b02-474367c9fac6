package scripts

import (
	"crypto/tls"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"regexp"
	"strings"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

type ScriptScanArgs struct {
	Host    string
	Port    uint16
	IsHTTPS bool
}

type ScriptScanResult struct {
	Vulnerable bool
	Output     string
	Payload    string
	Body       []byte
}

var invulnerableResult = ScriptScanResult{Vulnerable: false}

var logger = log.New(os.Stdout, "", log.Ldate|log.Ltime|log.Lshortfile)

type ScriptScanFunc func(args *ScriptScanArgs) (*ScriptScanResult, error)

var scriptHandlers = map[string]ScriptScanFunc{}

func SupportedScriptNames() []string {
	result := []string{}
	for name := range scriptHandlers {
		result = append(result, name)
	}
	return result
}

func GetScanFunc(xmlName string) ScriptScanFunc {
	if f, ok := scriptHandlers[xmlName]; ok {
		return f
	}
	return nil
}

var scriptUserAgent = "Mozilla/5.0 (Windows NT 6.2; rv:30.0) Gecko/20150101 Firefox/32.0"

var httpClient = fasthttp.Client{
	Name:                          scriptUserAgent,
	ReadTimeout:                   20 * time.Second,
	WriteTimeout:                  20 * time.Second,
	MaxResponseBodySize:           1024 * 1024 * 2,
	DisableHeaderNamesNormalizing: true,
	Dial: func(addr string) (net.Conn, error) {
		return fasthttp.DialTimeout(addr, 5*time.Second)
	},
	TLSConfig: &tls.Config{

		InsecureSkipVerify: true,
	},
}

var headerNormalizingHTTPClient = fasthttp.Client{
	Name:                scriptUserAgent,
	ReadTimeout:         20 * time.Second,
	WriteTimeout:        20 * time.Second,
	MaxResponseBodySize: 1024 * 1024 * 2,
	Dial: func(addr string) (net.Conn, error) {
		return fasthttp.DialTimeout(addr, 5*time.Second)
	},
	TLSConfig: &tls.Config{

		InsecureSkipVerify: true,
	},
}

var goHTTPClient = http.Client{
	Timeout: 10 * time.Second,
}

var ipRegex = regexp.MustCompile(`((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)`)

func constructURL(args *ScriptScanArgs, uri string) string {
	var rawurl string
	if !strings.HasPrefix(uri, "/") {
		uri = "/" + uri
	}
	var scheme string
	if args.IsHTTPS {
		scheme = "https"
	} else {
		scheme = "http"
	}
	if args.Port == 80 || args.Port == 443 {
		rawurl = fmt.Sprintf("%v://%v%v", scheme, args.Host, uri)
	} else {
		rawurl = fmt.Sprintf("%v://%v:%v%v", scheme, args.Host, args.Port, uri)
	}
	return rawurl
}

func httpGet(url string) (int, []byte, error) {

	return httpDoTimeout("GET", url, 5*time.Second)
}

func httpGetTimeout(url string, timeout time.Duration) (int, []byte, error) {

	return httpDoTimeout("GET", url, timeout)
}

func httpDoTimeout(method string, url string, timeout time.Duration) (int, []byte, error) {

	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.SetRequestURI(url)
	request.Header.SetMethod(method)
	err := httpClient.DoTimeout(request, response, 5*time.Second)
	if err != nil {
		return 0, nil, err
	}
	body, err := utils.GetOriginalBody(response)
	return response.StatusCode(), body, err
}

func registerHandler(name string, handler ScriptScanFunc) {
	if _, ok := scriptHandlers[name]; ok {
		logger.Panic("duplicated handler name:", name)
	}
	scriptHandlers[name] = handler
}

func SetLogger(newLogger *log.Logger) {
	logger = newLogger
}
