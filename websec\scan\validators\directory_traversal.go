package validators

import (
	"bytes"
)

func ValidateDirectoryTraversal(args *ValidationArgs) (*ValidationResult, error) {
	result := ValidationResult{
		Status: VulIsInvalid,
	}
	body := bytes.ToLower(args.Body)
	if (bytes.Contains(body, []byte("index of /")) && bytes.Contains(body, []byte("parent directory"))) ||
		bytes.Contains(body, []byte("[to parent directory]")) ||
		bytes.Contains(body, []byte("[转到父目录]")) {
		result.Status = VulIsValid
	}
	return &result, nil
}
