package validators

import (
	"context"
	"net/http"
	"os/exec"
	"strings"
	"time"
)

func ValidateNginxExecution(args *ValidationArgs) (*ValidationResult, error) {
	resp, err := http.Head(args.VulURL)
	if err != nil {
		return nil, err
	}
	contentType := strings.ToLower(resp.Header.Get("Content-Type"))
	if strings.Contains(contentType, "text/html") {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		cmd := exec.CommandContext(ctx, "curl", "-I", "-k", args.VulURL)
		output, err := cmd.CombinedOutput()
		if err != nil {
			return nil, err
		}

		return &ValidationResult{
			Status:       VulIsValid,
			Command:      strings.Join(cmd.Args, " "),
			Output:       string(output),
			Highlight:    "text/html",
			NeedSnapshot: true,
		}, nil
	}
	return &ValidationResult{Status: VulIsInvalid}, nil
}
