package ocr

import (
	"context"
	"crypto/tls"
	"net/http"
	"sync"
	"time"
	"websec/common/consts"
	"websec/common/db"
	"websec/detect"
	"websec/utils/acsmx"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/stream"
)

type Options struct {
	Consumer         *stream.Consumer
	Producer         *stream.Producer
	DBConnection     *db.DBConnection
	SensitiveMatcher *acsmx.Matcher
	FghkMatcher      *detect.FghkMatcher
	Concurrency      int64
	OcrAPIAddress    string
	OcrAPIKey        string
	OcrAppKey        string
}

type Processor struct {
	options      *Options
	consumer     *stream.Consumer
	producer     *stream.Producer
	dbConnection *db.DBConnection
	client       *http.Client
	concurrency  int64
	Acc          *AccInfo

	MuAcc         *sync.RWMutex
	consumeSema   *semaphore.Weighted
	semaWaitGroup sync.WaitGroup
}

func NewProcessor(options *Options) (*Processor, error) {
	processor := &Processor{
		options:      options,
		consumer:     options.Consumer,
		producer:     options.Producer,
		dbConnection: options.DBConnection,
		concurrency:  options.Concurrency,
		client: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
		},
		MuAcc:       &sync.RWMutex{},
		consumeSema: semaphore.NewWeighted(options.Concurrency),
	}
	return processor, nil
}

func (processor *Processor) Run() {
	processor.producer.Go()
	processor.consumer.SubscribeTopics(processor.Topics())
	processor.consumer.Go()

	processor.LoadAcc()

	var err error
	for msg := range processor.consumer.Messages() {
		if err = processor.consumeSema.Acquire(context.TODO(), 1); err == nil {
			processor.semaWaitGroup.Add(1)
			go processor.Process(msg, false)
		} else {
			processor.Process(msg, true)
		}
	}

	processor.producer.Close()
	processor.dbConnection.CloseThriftClient()
	log.Infoln("Ocr Processor Grace Exit")
}

func (processor *Processor) Stop() {
	processor.consumer.Close()
}

func (processor *Processor) Topics() []string {
	return []string{
		consts.TopicCrawledImageURLS,
	}
}

func (processor *Processor) Process(msg *stream.Message, sync bool) error {
	defer func() {
		if !sync {
			processor.consumeSema.Release(1)
			processor.semaWaitGroup.Done()
		}
	}()

	topic := msg.Topic

	switch topic {
	case consts.TopicCrawledImageURLS:
		return processor.processImages(msg)
	default:
		log.Errorln("unknown message topic: ", topic)
	}
	return nil
}

func (processor *Processor) LoadAcc() {
	processor.loadAcc()
	go func() {
		ticker := time.NewTicker(12 * time.Hour)
		for t := range ticker.C {
			log.Infoln("---- load Acc ---- at:", t.String())
			processor.loadAcc()
		}
	}()
}

func (processor *Processor) loadAcc() {
	accInfo, err := processor.getToken()
	if err != nil {
		log.Errorln(err)
		return
	}
	processor.MuAcc.Lock()
	processor.Acc = accInfo
	processor.MuAcc.Unlock()

}
