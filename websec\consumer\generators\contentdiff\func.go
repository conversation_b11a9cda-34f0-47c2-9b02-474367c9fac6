package contentdiff

import (
	"bytes"
	"io"
	url_ "net/url"
	"regexp"
	"strings"
	"websec/common/consts"
	"websec/utils/acsmx"
	"websec/utils/log"

	"golang.org/x/net/html"
	"golang.org/x/net/publicsuffix"
	xmlpath "gopkg.in/xmlpath.v2"
)

func getDomainNameofURL(url string) string {
	u, err := url_.Parse(url)
	if err != nil {
		log.Errorf("Failed to parse url: %s err: %v", url, err)
		return ""
	}
	return strings.ToLower(u.Hostname())
}

func CheckHTMLBody(page string) bool {
	pageLowerCase := strings.ToLower(page)
	return strings.Contains(pageLowerCase, "<body") && strings.Contains(pageLowerCase, "</body")
}

var urlRegex = regexp.MustCompile(`(?i)(?:[a-zA-Z]+:)?//[^\s\'\"\.]+\.[^\s\'\"]*`)

func CheckOuterLinksInContent(url string, content string) ([]string, bool) {
	originDomainName := getDomainNameofURL(url)
	originDomainRoot, err := publicsuffix.EffectiveTLDPlusOne(originDomainName)
	if err != nil {
		log.Errorln(err, originDomainName)
		return nil, false
	}

	allLinks := urlRegex.FindAllString(content, -1)
	var outerLinks []string
	for _, link := range allLinks {
		domain := getDomainNameofURL(link)
		if domain == "" {
			continue
		}
		domainRoot, err := publicsuffix.EffectiveTLDPlusOne(domain)
		if err != nil {
			continue
		}
		if domainRoot != originDomainRoot {
			outerLinks = append(outerLinks, link)
		}
	}
	return outerLinks, len(outerLinks) > 0
}

func CheckBlackWordsInDiffContent(diff []byte, acMatcher *acsmx.Matcher) ([]string, bool) {
	var matchedWords []string
	changeLst, _ := getChangedContent(diff) //don't check err here, the changeLst might not be empty
	if len(changeLst) == 0 {
		return matchedWords, false
	}
	joinedString := strings.Join(changeLst, " ")
	_, ACmatchedWords := acMatcher.Search([]byte(joinedString))

	for _, word := range ACmatchedWords {
		matchedWords = append(matchedWords, word.Word)
	}
	return matchedWords, len(matchedWords) > 0
}

func CheckNoneInlcudedContent(old, new string) ([]byte, bool) {
	outerOld := getOuterContent(old)
	outerNew := getOuterContent(new)

	cleanOuterOld := removeCommentFromPage([]byte(outerOld))
	cleanOuterNew := removeCommentFromPage([]byte(outerNew))

	if len(cleanOuterNew) > 0 && bytes.Compare(cleanOuterOld, cleanOuterNew) != 0 {
		return cleanOuterNew, true
	}
	return []byte{}, false
}

func getChangedContent(diff []byte) ([]string, error) {

	var ret []string
	patterns := []string{`//span[@class="chg"]`, `//span[@class="add"]`} //WARNING: class "add" needs better filter, But acceptable
	node, err := xmlpath.ParseHTML(bytes.NewBuffer(diff))
	if err != nil {
		return ret, err
	}
	for _, pattern := range patterns {
		path, err := xmlpath.Compile(pattern)
		if err != nil {
			log.Errorln("Error happend while getChangedContent", err)
			continue
		}
		iter := path.Iter(node)
		for iter.Next() {
			s := iter.Node()
			ret = append(ret, s.String())
		}
	}
	return ret, nil
}

var htmlRe = regexp.MustCompile(`(?i)<html[\S\s]*/html>`)
var doctypeRe = regexp.MustCompile(`(?i)<!DOCTYPE[\S\s]*?>`)

func getOuterContent(page string) string {
	outer := htmlRe.ReplaceAllString(page, "")
	outer = strings.TrimSpace(outer)
	if len(outer) > 0 {
		outer = doctypeRe.ReplaceAllString(outer, "")
		outer = strings.TrimSpace(outer)
	}
	return outer
}

func removeCommentNodes(n *html.Node) {
	if n == nil {
		return
	}
	for c := n.FirstChild; c != nil; {
		p := c
		c = c.NextSibling
		if p.Type == html.CommentNode {
			n.RemoveChild(p)
		} else {
			removeCommentNodes(p)
		}
	}
}

func removeCommentFromPage(page []byte) []byte {
	doc, err := html.Parse(bytes.NewReader(page))
	if err != nil {
		log.Error(err)
		return []byte{}
	}
	if doc == nil {
		return []byte{}
	}
	removeCommentNodes(doc)
	var buf bytes.Buffer
	html.Render(io.Writer(&buf), doc)
	return buf.Bytes()
}

func GetExceptionTypeAndExceptionContent(url string, diff []byte, changedContent []string, old, neW []byte, matcher *acsmx.Matcher) (string, string) {
	wordsLst, found := CheckBlackWordsInDiffContent(diff, matcher)
	if found {
		return consts.BlackwordsException, strings.Join(wordsLst, " ")
	}

	outerLinks, found := CheckOuterLinksInContent(url, strings.Join(changedContent, " "))
	if found {
		return consts.OuterLinksException, strings.Join(outerLinks, " ")
	}

	nonInclude, found := CheckNoneInlcudedContent(string(old), string(neW))
	if found {
		return consts.NonIncludedContentException, string(nonInclude)
	}

	found = CheckHTMLBody(string(neW))
	if !found {
		return consts.NonBodyElementException, ""
	}

	return consts.NoException, ""
}

func GetContentDiffWithExceptionType(url string, diff, oldContent, newContent []byte,
	blackMatcher *acsmx.Matcher) ([]byte, string, string) {
	blackWords, found := CheckBlackWordsInDiffContent(diff, blackMatcher)
	if found {
		return diff, consts.BlackwordsException, strings.Join(blackWords, " ")
	}

	changedContent, _ := getChangedContent(diff)
	outerLinks, found := CheckOuterLinksInContent(url, strings.Join(changedContent, " "))
	if found {
		return diff, consts.OuterLinksException, strings.Join(outerLinks, " ")
	}

	nonInclude, found := CheckNoneInlcudedContent(string(oldContent), string(newContent))
	if found {
		return diff, consts.NonIncludedContentException, string(nonInclude)
	}

	found = CheckHTMLBody(string(newContent))
	if !found {
		return nil, consts.NonBodyElementException, ""
	}

	return nil, consts.NoException, ""
}

var GetChangedContent = getChangedContent
