package validators

import (
	"fmt"
	"regexp"
	"strings"
)

var (
	rePasswd = regexp.MustCompile(`(?i)/etc/(./)*passwd\b`)
	reWebXML = regexp.MustCompile(`(?i)/web-inf/web\.xml\b`)
)

func validatePasswd(rawURL string) (string, string, string) {
	key := "root:x:0:0"
	cmdToShow := fmt.Sprintf(`curl %s | grep "%s" -A 20`, rawURL, key)

	bodyStr, _, err := DoHTTPRequest(rawURL)
	if err != nil {
		return "", "", ""
	}

	bodyStrs := strings.Split(bodyStr, "\n")
	foundKey := false
	outStrs := []string{}
	for i := range bodyStrs {
		if !foundKey {
			if strings.Contains(bodyStrs[i], key) {
				foundKey = true
				outStrs = append(outStrs, bodyStrs[i])
			}
		} else {
			if len(outStrs) > 21 {
				break
			}
			outStrs = append(outStrs, bodyStrs[i])
		}
	}

	outStr := strings.Join(outStrs, "\n")
	return cmdToShow, outStr, key
}

func validateWebXML(rawURL string) (string, string, string) {
	start := "<web-app"
	end := "</web-app>"
	cmdToShow := fmt.Sprintf("curl %s", rawURL)
	outStr := ""

	bodyStr, _, err := DoHTTPRequest(rawURL)
	if err != nil {
		return "", "", ""
	}

	if strings.Contains(bodyStr, start) && strings.Contains(bodyStr, end) {
		startIndex := strings.Index(bodyStr, start)
		endIndex := strings.Index(bodyStr, end) + len(end)
		outStr = "...\n" + bodyStr[startIndex:endIndex] + "\n..."
	}

	return cmdToShow, outStr, "web-app"
}

func ValidateFileDownload(args *ValidationArgs) (*ValidationResult, error) {
	matchPasswd := rePasswd.FindString(args.VulURL)
	matchWebXML := reWebXML.FindString(args.VulURL)
	cmd, out, highlight := "", "", ""
	if len(matchPasswd) > 0 {
		cmd, out, highlight = validatePasswd(args.VulURL)
	} else if len(matchWebXML) > 0 {
		cmd, out, highlight = validateWebXML(args.VulURL)
	}

	if len(out) > 0 {
		return &ValidationResult{
			Status:       VulIsValid,
			Output:       out,
			Command:      cmd,
			NeedSnapshot: true,
			Highlight:    highlight,
		}, nil
	}
	return &ValidationResult{Status: VulIsInvalid}, nil
}
