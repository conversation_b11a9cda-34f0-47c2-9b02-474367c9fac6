package validators

import (
	"bytes"
	"io/ioutil"
	"net/http"
)

func ValidatePHPMisconfiguration(args *ValidationArgs) (*ValidationResult, error) {
	resp, err := http.Get(args.VulURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode == http.StatusOK && bytes.Contains(bytes.ToLower(body), []byte("php license")) {
		return &ValidationResult{
			Status: VulIsValid,
		}, nil
	}
	return &ValidationResult{Status: VulIsInvalid}, nil
}
