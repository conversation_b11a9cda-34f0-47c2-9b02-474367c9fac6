package scripts

import (
	"bytes"
	"fmt"
	"net/http"
	"net/url"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

func getViewstatAdminConsole(page []byte) []byte {

	newPage := bytes.Replace(page, []byte("\\n"), []byte("\n"), -1)
	pageList := bytes.Split(newPage, []byte("\n"))
	var subStr = []byte("javax.faces.ViewState")

	for _, value := range pageList {
		if bytes.Contains(value, subStr) {
			valueNum := bytes.Count(value, []byte(`value="`))
			if valueNum == 1 {
				return bytes.Split(bytes.Split(value, []byte(`value="`))[1], []byte(`"`))[0]
			} else if valueNum > 1 {
				return bytes.Split(bytes.Split(value, []byte(`value="`))[2], []byte(`"`))[0]
			}
		}

	}
	return []byte{}
}

func JBossAdminConsoleWeakPass(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var username = "admin"
	var password = "admin"
	rawurl := constructURL(args, "/")
	rawurl = fmt.Sprintf("%v/admin-console/login.seam", rawurl)

	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	cookie := fasthttp.AcquireCookie()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)
	defer fasthttp.ReleaseCookie(cookie)
	request.SetRequestURI(rawurl)
	request.Header.Set("Connection", "keep-alive")
	request.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")

	err := httpClient.DoTimeout(request, response, 10*time.Second)
	if err != nil {

		return nil, err
	}

	if response.StatusCode() != 200 {
		return &invulnerableResult, nil
	}

	response.Header.VisitAllCookie(func(key, value []byte) {
		err := cookie.ParseBytes(value)
		if err == nil {
			request.Header.SetCookie(string(key), string(cookie.Value()))
		}
	})

	value, err := utils.GetOriginalBody(response)
	state := getViewstatAdminConsole(value)
	var payload = bytes.Buffer{}
	payload.WriteString("login_form=login_form&login_form%3Aname=")
	payload.WriteString(username)
	payload.WriteString("&login_form%3Apassword=")
	payload.WriteString(password)
	payload.WriteString("&login_form%3Asubmit=Login&javax.faces.ViewState=")
	payload.WriteString(url.QueryEscape(string(state)))

	request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	request.Header.SetMethod(http.MethodPost)
	request.SetBody(payload.Bytes())
	response.Reset()

	err = httpClient.DoTimeout(request, response, 5*time.Second)
	if err != nil {

		return nil, err
	}

	if response.StatusCode() == 302 {
		request.ResetBody()
		response.Header.VisitAllCookie(func(key, value []byte) {
			err := cookie.ParseBytes(value)
			if err == nil {
				request.Header.SetCookie(string(key), string(cookie.Value()))
			}
		})
		request.SetRequestURIBytes(response.Header.Peek("Location"))
		response.Reset()

		err = httpClient.DoTimeout(request, response, 5*time.Second)
		if err != nil {

			return nil, err
		}
		value, err = utils.GetOriginalBody(response)
		if err != nil {
			return nil, err
		}
		if bytes.Contains(value, []byte("Welcome admin")) {
			return &ScriptScanResult{Vulnerable: true, Output: fmt.Sprintf("%v:%v", username, password), Body: value}, nil
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("jboss_admin_console_weakpass.xml", JBossAdminConsoleWeakPass)
}
