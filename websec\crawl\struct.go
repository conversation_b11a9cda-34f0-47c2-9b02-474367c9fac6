package crawl

import (
	"context"
	"sync"
	"time"
	"websec/common"
	"websec/common/schema"
	"websec/utils/semaphore"
)

type FingerPrintFunc func(common.Link) string

type Options struct {
	Links<PERSON>han             chan *common.Link
	Task                  *schema.Task
	Host                  string
	AssetID               string
	JobID                 string
	Offset                string
	ByChrome              bool
	CrawlOuterLinks       bool
	IgnoreContent         bool
	MaxDepth              int32
	MaxLinkNum            uint64
	ExternalScanDepth     int32
	Concurrency           int64
	ShouldCrawlFoundLinks bool
	LoadSensitivePaths    bool
	FPFunc                FingerPrintFunc
	GetLastGetAt          common.GetLastGetAtFunc
	SetLastGetAt          common.SetLastGetAtFunc
	SetCrawlingProgress   common.SetCrawlingProgressFunc
	Timeout               time.Duration
	Cookies               map[string]string
	Headers               common.HttpHeaders
	UserAgent             string
	ChromeHost            string
	FilterReg             string
	ChromePort            uint16
	MaxResponseLength     int
	ExtractEntryImages    bool
	ExtractEntrySwfs      bool

	SpecificXMLs []string

	SpecificAffects         map[string]bool
	IgnoredBlackLinkDomains *common.IgnoredBlackLinkDomains
	Semaphores              *sync.Map
}

func (options *Options) setDefaultValue() {
	if options.Concurrency == 0 {
		options.Concurrency = 10
	}
	if options.UserAgent == "" {
		options.UserAgent = "Mozilla/5.0 (Windows NT 6.2; rv:30.0) Gecko/20150101 Firefox/32.0"
	}
	if options.ChromeHost == "" {
		options.ChromeHost = "***********"
	}
	if options.ChromePort == 0 {
		options.ChromePort = 9224
	}
	if options.Timeout == 0 {
		options.Timeout = 60 * 5 * time.Second
	}
	if options.MaxResponseLength == 0 {
		options.MaxResponseLength = 1024 * 1024
	}
	if options.IgnoredBlackLinkDomains == nil {
		options.IgnoredBlackLinkDomains = common.NewIgnoredBlackLinkDomains()
	}
}

type RequestSemaphore struct {
	Sema *semaphore.Weighted
	Ctx  context.Context
}

func NewRequestSemaphore(concurrency int64) *RequestSemaphore {
	return &RequestSemaphore{
		Sema: semaphore.NewWeighted(concurrency),
		Ctx:  context.TODO(),
	}
}
