package log

import (
	"fmt"
	"log"
	"os"
)

const (
	calldepth = 3
)

var l Logger = &normalLogger{log.New(os.Stdout, "", log.LstdFlags|log.Lshortfile)}

type Logger interface {
	Debug(v ...interface{})
	Debugln(v ...interface{})
	Debugf(format string, v ...interface{})

	Info(v ...interface{})
	Infoln(v ...interface{})
	Infof(format string, v ...interface{})

	Warn(v ...interface{})
	Warnln(v ...interface{})
	Warnf(format string, v ...interface{})

	Error(v ...interface{})
	Errorln(v ...interface{})
	Errorf(format string, v ...interface{})

	Fatal(v ...interface{})
	Fatalln(v ...interface{})
	Fatalf(format string, v ...interface{})

	Panic(v ...interface{})
	Panicln(v ...interface{})
	Panicf(format string, v ...interface{})
}

type Handler interface {
	Handle(v ...interface{})
}

func SetLogger(logger Logger) {
	l = logger
}

func Debug(v ...interface{}) {
	l.Debug(v...)
}
func Debugln(v ...interface{}) {
	l.Debugln(v...)
}

func Debugf(format string, v ...interface{}) {
	l.Debugf(format, v...)
}

func Info(v ...interface{}) {
	l.Info(v...)
}

func Infoln(v ...interface{}) {
	l.Infoln(v...)
}

func Infof(format string, v ...interface{}) {
	l.Infof(format, v...)
}

func Warn(v ...interface{}) {
	l.Warn(v...)
}

func Warnln(v ...interface{}) {
	l.Warnln(v...)
}

func Warnf(format string, v ...interface{}) {
	l.Warnf(format, v...)
}

func Error(v ...interface{}) {
	l.Error(v...)
}

func Errorln(v ...interface{}) {
	l.Errorln(v...)
}
func Errorf(format string, v ...interface{}) {
	l.Errorf(format, v...)
}

func Fatal(v ...interface{}) {
	l.Fatal(v...)
}
func Fatalln(v ...interface{}) {
	l.Fatalln(v...)
}

func Fatalf(format string, v ...interface{}) {
	l.Fatalf(format, v...)
}

func Panic(v ...interface{}) {
	l.Panic(v...)
}

func Panicln(v ...interface{}) {
	l.Panicln(v...)
}

func Panicf(format string, v ...interface{}) {
	l.Panicf(format, v...)
}

func Handle(v ...interface{}) {
	if handle, ok := l.(Handler); ok {
		handle.Handle(v...)
	}
}

func header(lvl, msg string) string {
	return fmt.Sprintf("[%s] %s", lvl, msg)
}
