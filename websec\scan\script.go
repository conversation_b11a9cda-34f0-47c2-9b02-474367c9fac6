package scan

import (
	"net/url"
	"strconv"
	"time"
	"websec/scan/rules"
	"websec/scan/scripts"
	"websec/utils/log"
)

func (scanner *WSScanner) scanVulScript(link *AffectLink, vul *rules.Vulnerability, args *scripts.ScriptScanArgs) {
	defer scanner.goroutines.Done()
	defer scanner.requestSema.Release(1)

	xmlName := vul.VulXML
	scanFunc := scripts.GetScanFunc(xmlName)
	foundVuls := []*FoundVul{}
	var errorCount int64
	var start time.Time
	var timeCost time.Duration

	if scanFunc == nil {
		log.Errorln("failed to get script func for", xmlName)
		errorCount++
	} else {
		log.Infoln("script scan:", link.URL, xmlName)

		start = time.Now()
		result, err := scanFunc(args)
		if err != nil {
			log.Errorln("script scan failed:", link.URL, xmlName, " err:", err)
		}
		if result != nil && result.Vulnerable {
			foundVul := &FoundVul{
				Link:     link,
				Vul:      vul,
				VulURL:   result.Output,
				Severity: vul.Severity,
				Context: VulContext{
					"html":     result.Output,
					"xml_name": vul.VulXML,
					"body":     result.Body,
					"payload":  result.Payload,
				},
			}
			foundVuls = append(foundVuls, foundVul)
		}

		timeCost = time.Now().Sub(start)
		if timeCost < time.Second*1 {
			time.Sleep(time.Second*1 - timeCost)
		}
	}
	scanner.outputResult(&ScanResult{FoundVuls: foundVuls, RequestCount: 0, ErrorCount: errorCount})
}

func (scanner *WSScanner) scanScript(link *AffectLink) {
	modules := scanner.affectModules[link.Affect]
	parts, err := url.Parse(link.URL)
	if err != nil {
		log.Errorln("failed to parse url in script scan:", link.URL)
		return
	}
	var port uint16
	isHTTPS := parts.Scheme == "https"
	portStr := parts.Port()
	if portStr == "" {
		if isHTTPS {
			port = 443
		} else {
			port = 80
		}
	} else {
		intValue, err := strconv.Atoi(portStr)
		if err != nil {
			log.Errorln("failed to convert port to int:", portStr)
			return
		}
		port = uint16(intValue)
	}
	args := &scripts.ScriptScanArgs{
		Host:    parts.Hostname(),
		Port:    port,
		IsHTTPS: isHTTPS,
	}

Scanning:
	for i := range modules {
		for j := range modules[i].Vulnerabilities {
			vul := &(modules[i].Vulnerabilities[j])
			scanner.waitIfPaused()
			if scanner.shouldStop() {
				break Scanning
			}
			if scanner.specificVulXMLs != nil {
				if _, ok := scanner.specificVulXMLs[vul.VulXML]; !ok {
					continue
				}
			}
			linkCopy := link.DeepCopy()
			if !scanner.shouldScanLink(linkCopy, vul) {
				continue
			}
			err = scanner.requestSema.Acquire(scanner.requestCtx, 1)
			if err != nil {
				log.Errorln("failed to acquire requestSema")
				break
			}
			scanner.goroutines.Add(1)
			go scanner.scanVulScript(linkCopy, vul, args)
		}
	}
}
