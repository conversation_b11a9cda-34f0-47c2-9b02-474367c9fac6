package scripts

import (
	"bytes"
	"net"
	"time"
)

func MemcachedUDPUnauthority(args *ScriptScanArgs) (*ScriptScanResult, error) {
	serverAddr, err := net.ResolveUDPAddr("udp", args.Host+":11222")
	if err != nil {
		return nil, err
	}
	conn, err := net.DialUDP("udp", nil, serverAddr)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	conn.SetWriteDeadline(time.Now().Add(3 * time.Second))
	_, err = conn.Write([]byte("\x00\x00\x00\x00\x00\x01\x00\x00stats\r\n"))
	if err != nil {
		return nil, err
	}

	time.Sleep(1 * time.Second)

	conn.SetReadDeadline(time.Now().Add(3 * time.Second))
	data := make([]byte, 2048)
	_, err = conn.Read(data)
	if err != nil {
		return nil, err
	}
	if bytes.Contains(data, []byte("STAT pid")) {
		return &ScriptScanResult{Vulnerable: true, Output: args.Host + ":11211", Body: data}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("memcached_udp_unauthority.xml", MemcachedUDPUnauthority)
}
