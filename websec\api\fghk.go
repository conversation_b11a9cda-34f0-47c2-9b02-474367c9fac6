package api

import (
	"context"
	"encoding/base64"
	"encoding/binary"
	"net/http"
	"strconv"
	"strings"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FGHKSnapshot struct {
	SourceID       string `json:"source_id"`
	URL            string `json:"url"`
	SensitiveWords string `json:"sensitive_words"`
	Snapshot       string `json:"snapshot"`
	Remark         string `json:"remark"`
	PublishedAt    string `json:"published_at"`
}

type FGHKSnapshotResponse struct {
	Result      []FGHKSnapshot `json:"result"`
	ResultCount int            `json:"result_count"`
}

func (api *API) fghkSnapshotHandler(rw http.ResponseWriter, req *http.Request) {
	query := req.URL.Query()

	timeStr := query.Get("timestamp")
	var timeStamp int64
	var err error
	if timeStr == "" {
		timeStamp = time.Now().UTC().Unix() - 86400
	} else {
		timeStamp, err = strconv.ParseInt(timeStr, 10, 64)
		if err != nil {
			log.Errorln(err)
			api.writeResponse(rw, newFailResponse("timestamp not int"))
			return
		}
	}

	id := newObjectID(uint32(timeStamp))
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	cursor, err := api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionDetectFGHK).Find(ctx,
		bson.M{"_id": bson.M{"$gte": id}})
	if err != nil {
		log.Errorln(err)
		api.writeResponse(rw, newFailResponse("get from mongo err:"+err.Error()))
		return
	}
	defer cursor.Close(ctx)

	var result FGHKSnapshotResponse
	for cursor.Next(ctx) {
		doc := new(schema.ContentDetectFGHK)
		err = cursor.Decode(doc)
		if err != nil {
			log.Errorln(err)
			continue
		}

		remark, _ := base64.StdEncoding.DecodeString(doc.Remark)
		tmp := FGHKSnapshot{
			SourceID:       doc.ID.Hex(),
			URL:            doc.URL,
			SensitiveWords: doc.SensitiveWords,
			Snapshot:       doc.Snapshot,
			Remark:         string(remark),
			PublishedAt:    getPublishedAt(string(remark)),
		}
		if tmp.PublishedAt == "" {
			tmp.PublishedAt = doc.UpdateAt.Format(time.RFC3339)
		}

		result.Result = append(result.Result, tmp)
	}

	result.ResultCount = len(result.Result)
	api.writeResponse(rw, newSuccessResponse(result))
}

func getPublishedAt(remark string) string {
	ss := strings.Split(string(remark), "\n")
	if len(ss) == 0 {
		return ""
	}

	publish := ss[len(ss)-1]
	if strings.HasPrefix(publish, "发布时间:") {
		return publish[len("发布时间:"):]
	}
	return ""
}

func newObjectID(t uint32) primitive.ObjectID {
	id := primitive.NewObjectID()
	binary.BigEndian.PutUint32(id[0:4], t)
	return id
}
