package log

import (
	"fmt"
	"log"
	"os"
)

type normalLogger struct {
	*log.Logger
}

func (l *normalLogger) Debug(v ...interface{}) {
	l.Output(calldepth, header("DEBUG", fmt.Sprintln(v...)))
}

func (l *normalLogger) Debugln(v ...interface{}) {
	l.Output(calldepth, header("DEBUG", fmt.Sprintln(v...)))
}

func (l *normalLogger) Debugf(format string, v ...interface{}) {
	l.Output(calldepth, header("DEBUG", fmt.Sprintf(format, v...)))
}

func (l *normalLogger) Info(v ...interface{}) {
	l.Output(calldepth, header("INFO", fmt.Sprintln(v...)))
}

func (l *normalLogger) Infoln(v ...interface{}) {
	l.Output(calldepth, header("INFO", fmt.Sprintln(v...)))
}

func (l *normalLogger) Infof(format string, v ...interface{}) {
	l.Output(calldepth, header("INFO", fmt.Sprintf(format, v...)))
}

func (l *normalLogger) Warn(v ...interface{}) {
	l.Output(calldepth, header("WARN", fmt.Sprintln(v...)))
}

func (l *normalLogger) Warnln(v ...interface{}) {
	l.Output(calldepth, header("WARN", fmt.Sprintln(v...)))
}

func (l *normalLogger) Warnf(format string, v ...interface{}) {
	l.Output(calldepth, header("WARN", fmt.Sprintf(format, v...)))
}

func (l *normalLogger) Error(v ...interface{}) {
	l.Output(calldepth, header("ERROR", fmt.Sprintln(v...)))
}

func (l *normalLogger) Errorln(v ...interface{}) {
	l.Output(calldepth, header("ERROR", fmt.Sprintln(v...)))
}

func (l *normalLogger) Errorf(format string, v ...interface{}) {
	l.Output(calldepth, header("ERROR", fmt.Sprintf(format, v...)))
}

func (l *normalLogger) Fatal(v ...interface{}) {
	l.Output(calldepth, header("FATAL", fmt.Sprintln(v...)))
	os.Exit(1)
}

func (l *normalLogger) Fatalln(v ...interface{}) {
	l.Output(calldepth, header("FATAL", fmt.Sprintln(v...)))
	os.Exit(1)
}

func (l *normalLogger) Fatalf(format string, v ...interface{}) {
	l.Output(calldepth, header("FATAL", fmt.Sprintf(format, v...)))
	os.Exit(1)
}

func (l *normalLogger) Panic(v ...interface{}) {
	l.Logger.Panic(v...)
}

func (l *normalLogger) Panicln(v ...interface{}) {
	l.Logger.Panic(v...)
}

func (l *normalLogger) Panicf(format string, v ...interface{}) {
	l.Logger.Panicf(format, v...)
}
