package detect

import (
	"net"
	"time"
	"websec/common"
	"websec/utils/ocr"
	"websec/utils/semaphore"

	"github.com/valyala/fasthttp"
	"golang.org/x/net/publicsuffix"
)

type Detecter interface {
	Detect(*common.Webpage) (*DetectResult, error)
	DetectV1(*common.Webpage) (*DetectResult, error)
	GetStats() *common.DetectStats
	ClearUp() error
	DetectOCRSensitivewordsAsSW(*common.Webpage) []*SensitiveWordResult
}

func NewDetecter(options *Options) (Detecter, error) {
	rootDomain, err := publicsuffix.EffectiveTLDPlusOne(options.Domain)
	if err != nil {
		return nil, err
	}

	if options.OcrSema == nil {
		options.OcrSema = semaphore.NewWeighted(defaultConCurrency)
	}
	detecter := &WSDetecter{
		Options:    options,
		rootDomain: rootDomain,
		ocrClient: &fasthttp.Client{
			ReadTimeout: 60 * time.Second,
			Dial: func(addr string) (net.Conn, error) {
				return fasthttp.DialTimeout(addr, 60*time.Second)
			},
		},
		ocrv1Client: &ocr.OCRClientV1{
			URL: options.OcrAPIAddress,
		},
	}

	ocr.SetDefaultOCRClient(detecter.ocrv1Client)
	return detecter, nil
}
