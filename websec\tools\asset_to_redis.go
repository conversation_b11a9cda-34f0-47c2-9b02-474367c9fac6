package main

import (
	"context"
	"encoding/json"
	"time"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/config"
	"websec/utils/log"
	"websec/utils/semaphore"
)

func main() {
	settings, _ := config.ParseConfig()
	dbConnection, err := db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis))
	if err != nil {
		log.Errorln(err)
		return
	}

	cursor, err := dbConnection.GetMongoDatabase().Collection(consts.CollectionAssets).Find(context.Background(), nil)
	if err != nil {
		log.Errorln(err)
		return
	}
	defer cursor.Close(nil)

	sema := semaphore.NewWeighted(1000)
	start := time.Now()
	num := 0
	var asset schema.Asset
	for cursor.Next(nil) {
		err := cursor.Decode(&asset)
		if err != nil {
			log.Errorln(err)
			continue
		}
		bin, err := json.Marshal(asset)
		if err != nil {
			log.Errorln(err)
			continue
		}
		num++
		if err = sema.Acquire(context.TODO(), 1); err == nil {
			go func() {
				defer sema.Release(1)
				dbConnection.HSet(consts.RedisAssets, asset.ID.Hex(), bin)
			}()
		} else {
			log.Errorln(err)
		}
	}
	log.Infoln("cost:", time.Now().Sub(start).Seconds(), "num:", num)
}
