package crawl

import (
	"context"
	"fmt"
	"net"
	"regexp"
	"sync"
	"time"
	"websec/common"
	"websec/common/schema"

	"websec/utils"
	bf "websec/utils/bloomfilter"
	"websec/utils/log"
	"websec/utils/semaphore"

	"github.com/valyala/fasthttp"
)

type Crawler interface {
	Go() (chan *common.Webpage, error)

	Stop()

	SetMaxDepth(depth int32)

	SetMaxLinkNum(uint64)

	SetByChrome(byChrome bool)

	SetCrawlOuterLinks(crawl bool)

	SetConcurrency(concurrency int64)

	SetShouldCrawlFoundLinks(should bool)

	SetFingerPrintFunc(fpFunc FingerPrintFunc)

	SetTimeout(timeout time.Duration)

	SetChromeHost(host string)

	SetChromePort(port uint16)

	SetHeaders(headers common.HttpHeaders)

	SetUserAgent(userAgent string)

	GetStats() Stats

	SetCookies(map[string]string)

	GetOffset() string
	RunDirScanner(baseurl string, reqMethod string, ext []string, agent string,
		cookie string, only200 bool, handler *DirHandler, depth int32)
	GetDirVuls() ([]*schema.FoundVulDoc, error)

	IsCanceldActive() bool
}

type FilterInterface interface {
	Add([]byte)
	Contains([]byte) bool
}

func NewCrawler(options *Options, h VulHandler) (Crawler, error) {
	var err error
	options.setDefaultValue()

	crawler := &WSCrawler{}
	crawler.VulHandler = h
	crawler.Options = options
	crawler.Host = options.Host
	crawler.AssetID = options.AssetID
	crawler.JobID = options.JobID
	crawler.crawledOuterLinksFilter = make(map[string]bool)
	filter, err := bf.NewBloomFilter(1000000, 0.00000001)
	if err != nil {
		return nil, err
	}
	crawler.foundLinksFilter = filter
	crawler.mutexCrawledOuterLinksFilter = &sync.Mutex{}
	crawler.mutexFoundLinksFilter = &sync.Mutex{}
	crawler.linksQueue = utils.NewQueue()
	crawler.webPageChan = make(chan *common.Webpage, 20)
	crawler.chromeClient = &fasthttp.Client{
		ReadTimeout: 60 * time.Second,
		Dial: func(addr string) (net.Conn, error) {
			return fasthttp.DialTimeout(addr, 60*time.Second)
		},
	}
	crawler.chromeURI = fmt.Sprintf("http://%v:%v", options.ChromeHost, options.ChromePort)

	crawler.httpClient = newHTTPClient(options.UserAgent)

	crawler.cookiejar = utils.NewMyCookieJar()
	if options.Cookies != nil {
		for key, value := range options.Cookies {
			crawler.cookiejar.SetCookie(key, value)
		}
	}
	var r *regexp.Regexp

	if crawler.Options.FilterReg != "" {
		r, err = regexp.Compile(crawler.Options.FilterReg)
		if err != nil {
			log.Errorln("failed to load filer_reg:", err)
		}
		crawler.FilterReg = r
	}

	crawler.initDirDisclosure()
	crawler.isCanceled = false
	crawler.isAborting = false
	crawler.loadLinksFinished = false
	crawler.ProcessingCount = 0

	crawler.semaphores = options.Semaphores
	crawler.defaultSemaphore = NewRequestSemaphore(options.Concurrency)
	crawler.linkSema = semaphore.NewWeighted(options.Concurrency)
	crawler.linkCtx, crawler.linkCtxCancel = context.WithCancel(context.Background())
	crawler.outerLinkSema = semaphore.NewWeighted(options.Concurrency)
	crawler.outerLinkCtx, crawler.outerLinkCtxCancel = context.WithCancel(context.Background())
	crawler.startedAt = time.Now()
	crawler.expiredAt = crawler.startedAt.Add(options.Timeout)
	crawler.MaxDirFindDepth = 10

	return crawler, nil
}
