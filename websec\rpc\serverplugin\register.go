package serverplugin

import (
	"net"
	"sync"
)

type ClientManagerPlugin struct {
	clients map[string]net.Conn
	clock   sync.RWMutex
}

var clientmanagerPlugin = &ClientManagerPlugin{
	clients: make(map[string]net.Conn),
}

func GetClientManagerPlugin() *ClientManagerPlugin {
	return clientmanagerPlugin
}

func (c *ClientManagerPlugin) AddClient(host string, conn net.Conn) {
	c.clock.Lock()
	c.clients[host] = conn
	c.clock.Unlock()
}

func (c *ClientManagerPlugin) RemoveClient(host string) {
	c.clock.Lock()
	delete(c.clients, host)
	c.clock.Unlock()
}

func (c *ClientManagerPlugin) GetClientConn(host string) net.Conn {
	c.clock.RLock()
	defer c.clock.RUnlock()
	if conn, ok := c.clients[host]; ok {
		return conn
	}
	return nil
}

func (c *ClientManagerPlugin) HandleConnAccept(conn net.Conn) (net.Conn, bool) {
	host := conn.RemoteAddr().String()
	c.AddClient(host, conn)

	return conn, true
}

func (c *ClientManagerPlugin) HandleConnClose(conn net.Conn) bool {
	host := conn.RemoteAddr().String()
	c.RemoveClient(host)

	return true
}
