package scripts

import (
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"strings"
	"time"
)

func WeblogicWls9(args *ScriptScanArgs) (*ScriptScanResult, error) {
	client := &http.Client{Timeout: 5 * time.Second}

	vulPaths := make(map[string]string)
	vulPaths["wls-wsat/CoordinatorPortType"] = "WSAT10Service"
	vulPaths["_async/AsyncResponseService"] = "WSDL page"
	for k, v := range vulPaths {
		rawURL := constructURL(args, k)
		res, err := client.Get(rawURL)
		if err != nil {
			log.Println("WeblogicWls9 http error:", err)
			continue
		}

		if res.StatusCode != http.StatusOK {
			continue
		}

		body, err := ioutil.ReadAll(res.Body)
		if err != nil {
			res.Body.Close()
			log.Println(err)
			continue
		}
		res.Body.Close()

		if strings.Contains(string(body), v) {
			return &ScriptScanResult{
				Vulnerable: true,
				Output:     fmt.Sprintf("%s|Weblogic wls9 Async", rawURL),
				Body:       body,
			}, nil
		}
	}

	return nil, nil
}

func init() {
	registerHandler("weblogic_wls9_async.xml", WeblogicWls9)
}
