package validators

import (
	"log"
	"os"
)

var logger = log.New(os.Stdout, "", log.Lshortfile|log.Ldate|log.Ltime)

type ValidationArgs struct {
	VulXML string
	Method string
	URL    string
	VulURL string
	Body   []byte
}

type ValidationStatus = int

const (
	VulIsValid   ValidationStatus = 1
	VulIsInvalid                  = 2
	VulIsUnsure                   = 3
)

type ValidationResult struct {
	Status       ValidationStatus
	Command      string
	Output       string
	Highlight    string
	NeedSnapshot bool
}

type ValidationFunc func(*ValidationArgs) (*ValidationResult, error)

var typeFuncMapping = map[string]ValidationFunc{
	GenericDirectoryTraversal:               ValidateDirectoryTraversal,
	GenericApacheCGI:                        ValidateApacheCGI,
	GenericCvsSourceLeak:                    DoNothing,
	GenericSvnSourceLeak:                    DoNothing,
	GenericGitSourceLeak:                    DoNothing,
	GenericPHPInfo:                          DoNothing,
	GenericWebdavWrite:                      ValidateWebDavWrite,
	GenericFileDownload:                     ValidateFileDownload,
	GenericFileBackup:                       ValidateFileBackup,
	GenericErrorMessage:                     ValidateErrorPage,
	GenericStruts032:                        ValidateStruts,
	GenericStruts016:                        ValidateStruts,
	GenericNginxExecute:                     ValidateNginxExecution,
	GenericPHPErrorsEnabled:                 ValidatePHPMisconfiguration,
	GenericPHPAllowURLFopen:                 ValidatePHPMisconfiguration,
	GenericPHPAllowURLInclude:               ValidatePHPMisconfiguration,
	GenericPHPOpenBaseDir:                   ValidatePHPMisconfiguration,
	GenericFTPAnonymous:                     ValidateFTPAnonymous,
	GenericDNSZoneTransfer:                  ValidateDNSZoneTransfer,
	GenericShellShock:                       ValidateBashExecution,
	GenericCompressedFile:                   ValidateCompressedFile,
	GenericSensitiveFileExposed:             ValidateSensitiveFileExposed,
	GenericServerPageNotProcessedSourceLeak: ValidateFileBackup,
	GenericWeblogicPasswordCracked:          DoNothing,
	GenericWeblogicSsrf:                     DoNothing,
	GenericLocalPathDisclosure:              ValidateErrorPage,

	TrivialHTAccessReadable:     DoNothing,
	TrivialApacheServerStatus:   DoNothing,
	TrivialJetbrainProjectFiles: DoNothing,
	TrivialWebXMLReadable:       DoNothing,
	TrivialResinCauchoStatus:    DoNothing,
	TrivialFlashCrossDomain:     DoNothing,
	TrivialWeblogicConsoleFound: DoNothing,
	TrivialRobotsTxt:            DoNothing,

	ApplicationSiteServerFiletreeDirTraversal: DoNothing,
}

func getValidationFunc(XMLName string) ValidationFunc {
	typeName := getVulTypeName(XMLName)
	if typeName == VulTypeUnknown {
		return nil
	}
	if f, ok := typeFuncMapping[typeName]; ok {
		return f
	}
	return nil
}

func Validate(args *ValidationArgs) (*ValidationResult, error) {
	f := getValidationFunc(args.VulXML)
	if f == nil {
		return nil, nil
	}
	logger.Println("validating ", args.VulXML, args.VulURL)
	return f(args)
}
