package collectors

import (
	"encoding/json"
	"websec/common/db"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/stream"
)

func (collector *Collector) processPageArchive(msg *stream.Message) error {
	var doc = new(schema.FoundPageArchiveDoc)
	err := json.Unmarshal(msg.Value, doc)
	if err != nil {
		return err
	}

	headerBin, err := json.Marshal(doc.Header)
	if err != nil {
		return err
	}

	if doc.Host == "" || doc.URL == "" {
		return nil
	}

	/*
		esDoc := &schema.PageArchiveES{
			Host:           doc.Host,
			URL:            doc.URL,
			URLHash:        doc.URLHash,
			MainFrameURL:   doc.MainFrameURL,
			StatusCode:     doc.StatusCode,
			Header:         doc.Header,
			VersionTime:    doc.VersionTime,
			OldVersionTime: doc.OldVersionTime,
			ContentHash:    doc.ContentHash,
		}
		collector.saver.AddPageArchiveToEs(esDoc)
	*/

	hBaseDoc := &schema.PageArchiveHBase{
		Host:           doc.Host,
		AssetID:        doc.AssetID,
		JobID:          doc.JobID,
		MainFrameURL:   doc.MainFrameURL,
		StatusCode:     doc.StatusCode,
		URL:            doc.URL,
		URLHash:        doc.URLHash,
		Header:         string(headerBin),
		VersionTime:    doc.VersionTime,
		OldVersionTime: doc.OldVersionTime,
		ContentHash:    doc.ContentHash,
		Content:        doc.Content,
	}
	collector.saver.AddPageArchiveToHBase(hBaseDoc)

	return nil
}

func (collector *Collector) processHotPageArchive(msg *stream.Message) error {
	var doc = new(schema.FoundPageArchiveDoc)
	err := json.Unmarshal(msg.Value, doc)
	if err != nil {
		log.Errorln("save soucecode error:", err)
		return err
	}

	headerBin, err := json.Marshal(doc.Header)
	if err != nil {
		log.Errorln("save soucecode error:", err)
		return err
	}

	if doc.Host == "" || doc.URL == "" {
		log.Errorln("save soucecode error host or url empty", doc.Host, doc.URL)
		return nil
	}

	mongoDoc := &schema.PageArchiveHBase{
		Host:           doc.Host,
		AssetID:        doc.AssetID,
		JobID:          doc.JobID,
		MainFrameURL:   doc.MainFrameURL,
		StatusCode:     doc.StatusCode,
		URL:            doc.URL,
		URLHash:        doc.URLHash,
		Header:         string(headerBin),
		VersionTime:    doc.VersionTime,
		OldVersionTime: doc.OldVersionTime,
		ContentHash:    doc.ContentHash,
		Content:        doc.Content,
	}

	mongoDoc.RowKey = db.GenerateHBaseContentKey(doc.URL, doc.URLHash, doc.VersionTime)
	log.Infoln("save soucecode to mongo rowkey", mongoDoc.RowKey)
	collector.saver.AddPageArchiveToMongo(mongoDoc)
	return nil
}
