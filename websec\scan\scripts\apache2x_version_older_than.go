package scripts

import (
	"strconv"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
)

func Apache2xVerionOlderThan(args *ScriptScanArgs) (*ScriptScanResult, error) {
	rawurl := constructURL(args, "/")
	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.SetRequestURI(rawurl)

	err := headerNormalizingHTTPClient.DoTimeout(request, response, 10*time.Second)
	if err != nil {

		return nil, err
	}

	var apacheVersion string
	value := response.Header.Peek("server")
	if len(value) != 0 {
		groups := apacheVersionPattern.FindSubmatch(value)
		if groups != nil {
			apacheVersion = string(groups[1])
		}
	}
	if apacheVersion == "" {

		_, body, err := httpGetTimeout(rawurl+"/noteverexist1gdlx", 10*time.Second)
		if err != nil {
			return nil, err
		}
		groups := apacheVersionPattern.FindSubmatch(body)
		if groups != nil {
			apacheVersion = string(groups[1])
		} else {
			return &invulnerableResult, nil
		}
	}
	apacheVersion = strings.Trim(apacheVersion, " ")

	if strings.HasPrefix(apacheVersion, "2") {
		parts := strings.Split(apacheVersion, ".")
		if len(parts) == 3 {
			secondVersion, err := strconv.Atoi(parts[1])
			if err != nil {
				return nil, err
			}
			var vulnerable bool
			if secondVersion < 2 {
				vulnerable = true
			} else if secondVersion == 2 {
				thirdVersion, err := strconv.Atoi(parts[2])
				if err != nil {
					return nil, err
				}
				if thirdVersion < 16 {
					vulnerable = true
				}
			}
			if vulnerable {
				return &ScriptScanResult{Vulnerable: true, Output: "Apache version: " + apacheVersion}, nil
			}
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("apache2x_version_older_than.xml", Apache2xVerionOlderThan)
}
