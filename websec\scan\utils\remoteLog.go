package utils

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"net/http"
)

const logAddr = "http://e.scan.websec.cn"
const dnsLogDomain = ".d.megadns.com"

var existsStr = []byte("Vulnerabilities exist")

func GetLogResult(sign string, _type string) (isVerify bool, Content []byte, err error) {
	verify_url := fmt.Sprintf("%s/%s-verify.php?verify&rmd=%s", logAddr, _type, sign)
	resp, err := http.Get(verify_url)
	if err != nil {
		return false, nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return false, nil, err
	}
	if bytes.Contains(body, existsStr) {
		return true, body, nil
	}
	return false, nil, nil
}

func GetDNSLogResult(sign string) (isVerify bool, Content []byte, err error) {
	return GetLogResult(sign, "dns")
}

func GetWebLogResult(sign string) (isVerify bool, Content []byte, err error) {
	return GetLogResult(sign, "vul")
}

func GenDNSLogDomain(l int) (domain, sign string) {
	sign = RandLowLetterNumber(l)
	return sign + dnsLogDomain, sign
}

func GenDNSLogDomainFull(l int) (domain, sign string) {
	sign_length := l - len(dnsLogDomain)
	return GenDNSLogDomain(sign_length)
}

func GenWebLogUrl(l int) (url, sign string) {
	sign = RandLowLetterNumber(l)
	url = fmt.Sprintf("%s/vul-verify.php?rmd=%s", logAddr, sign)
	return url, sign
}

func GenWebLogStaticUrl(l int) (url, sign string) {
	sign = RandLowLetterNumber(l)
	url = fmt.Sprintf("%s/check-%s", logAddr, sign)
	return sign, url
}
