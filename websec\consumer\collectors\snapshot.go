package collectors

import (
	"context"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/consumer/snapshot"
	"websec/utils"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// rollback

const (
	ContentChangePrefix = "snapshot_cc_"
	SensitiveWordPrefix = "snapshot_sw_"
)

type SensitiveWordSnapshotResponse struct {
	Status      string `json:"status"`
	SnapshotID  string `json:"snapshot_id,omitempty"`
	SnapshotURL string `json:"snapshot,omitempty"`
}

type ContentChangeSnapshotResponse struct {
	Status      string `json:"status"`
	NewSnapshot string `json:"new_snapshot,omitempty"`
	OldSnapshot string `json:"old_snapshot,omitempty"`
}

type SnapshotSensitiveWord struct {
	ID               primitive.ObjectID `bson:"_id,omitempty"`
	SrcID            primitive.ObjectID `bson:"src_id"`
	Words            string             `bson:"words"`
	Status           uint8              `bson:"status"`
	ContentWordsHash string             `bson:"content_words_hash"`
	SnapshotURL      string             `bson:"snapshot_url"`
}

func (collector *Collector) handleSensitiveWordSnapshot(sourceID primitive.ObjectID, doc *schema.FoundSensitiveWordsDoc) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 遍历doc.Results， 将Position从小到大排序, 并将 doc.Results中的每一个 word 保存为一个分号分隔的字符串
	sort.Slice(doc.Results, func(i, j int) bool {
		return doc.Results[i].Position < doc.Results[j].Position
	})
	words := ""
	for _, v := range doc.Results {
		words += v.Word + ","
	}
	words = strings.TrimRight(words, ",")

	snapshotWord := new(SnapshotSensitiveWord)

	err := collector.dbConnection.GetMongoDatabase().Collection(consts.CollectionSnapshotSensitiveWord).FindOne(ctx,
		bson.M{
			"src_id": sourceID,
		}).Decode(snapshotWord)

	if err != nil {
		if err != mongo.ErrNoDocuments {
			log.Errorln("get snapshot_sensitiveword error:", err)
			return err
		} else {
			log.Debugf("src_id %s not found in snapshot_sensitiveword", sourceID.Hex())
		}
	}

	contentWordsHash := utils.Md5(doc.ContentHash + words)
	result := &SensitiveWordSnapshotResponse{}

	if err == mongo.ErrNoDocuments {
		wordResult := getWordResult(doc, strings.Split(words, ","))
		if len(wordResult) == 0 {
			log.Errorln("get snapshot_sensitiveword len==0 error:", err)
			return err
		}

		snapshotWord.SrcID = sourceID
		snapshotWord.Words = words
		snapshotWord.ContentWordsHash = contentWordsHash
		snapshotWord.Status = consts.SnapshotRunning

		res, err := collector.dbConnection.GetMongoDatabase().Collection(consts.CollectionSnapshotSensitiveWord).InsertOne(ctx, snapshotWord)
		if err != nil {
			log.Errorln("insert snapshot_sensitiveword collection error", err)
			return err
		}
		snapshotID := res.InsertedID.(primitive.ObjectID).Hex()

		msg := &schema.SnapshotSensitiveWordMessage{
			ID:          sourceID.Hex(),
			Host:        doc.Host,
			AssetID:     doc.AssetID,
			JobID:       doc.JobID,
			SnapshotID:  snapshotID,
			URL:         doc.URL,
			URLHash:     doc.URLHash,
			VersionTime: doc.VersionTime,
			Words:       wordResult,
		}

		// TODO
		// collector.producer.Produce(consts.TopicSnapshotSensitiveWordTodo, msg)
		collector.handleSnapshotSensitiveWord(sourceID, msg)

		result.Status = "running"
		result.SnapshotID = snapshotID
	} else {
		if snapshotWord.Status == consts.SnapshotRunning {
			result.Status = "running"
			result.SnapshotID = snapshotWord.ID.Hex()
		} else if snapshotWord.Status == consts.SnapshotFinished {
			result.Status = "finished"
			result.SnapshotID = snapshotWord.ID.Hex()
			result.SnapshotURL = snapshotWord.SnapshotURL
		} else {
			result.Status = "error"
		}
	}

	log.Infof("sensitiveword snapshot result %v", result)

	return nil
}

func (collector *Collector) handleContentChangeSnapshot(sourceID primitive.ObjectID, doc *schema.FoundContentChangeDoc) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var result ContentChangeSnapshotResponse

	switch uint8(doc.Status) {
	case consts.ToBeSnapshot:
		msg := &schema.SnapshotContentChangeMessage{
			ID:             sourceID.Hex(),
			Host:           doc.Host,
			AssetID:        doc.AssetID,
			JobID:          doc.JobID,
			URL:            doc.URL,
			URLHash:        doc.URLHash,
			OldVersionTime: doc.OldVersionTime,
			NewVersionTime: doc.NewVersionTime,
		}

		// TODO
		collector.handleSnapshotContentChange(msg)

		_, err := collector.dbConnection.GetMongoDatabase().Collection(consts.CollectionFoundContentChange).UpdateOne(ctx,
			bson.M{"_id": doc.ID}, bson.M{"$set": bson.M{"status": consts.SnapshotRunning}})
		if err != nil {
			log.Errorln(err)
			return err
		}
		result.Status = "running"
	case consts.SnapshotRunning:
		result.Status = "running"
	case consts.SnapshotFinished:
		result.Status = "finished"
		result.NewSnapshot = doc.NewSnapshot
		result.OldSnapshot = doc.OldSnapshot
	default:
		result.Status = "error"
	}

	log.Infof("contentchange snapshot result %v", result)

	return nil
}

func (collector *Collector) handleSnapshotContentChange(message *schema.SnapshotContentChangeMessage) error {
	dir, err := snapshot.CreateTempDir(ContentChangePrefix)
	if err != nil {
		collector.handleContentSnapshotError(message, err)
		return err
	}

	var oldErr, newErr error
	var oldPath, newPath string
	var oldLines, newLines []string
	var oldPage, newPage []byte
	var wg sync.WaitGroup

	log.Infof("handleSnapshotContentChange  %v %v %v %v %v", message.JobID, message.URL, message.URLHash, message.OldVersionTime, message.NewVersionTime)

	wg.Add(2)
	go func() {
		defer wg.Done()
		for i := 0; i < 5; i++ { // Try up to 3 times
			// oldPage, oldErr = collector.dbConnection.GetHBaseContent(message.JobID, message.URL, message.URLHash, message.OldVersionTime)
			oldPage, oldErr = collector.dbConnection.GetOldReferPageContent(message.JobID, message.AssetID, message.Host, message.URL, message.URLHash, message.OldVersionTime)
			if oldErr == nil {
				break
			}
			log.Errorf("Failed to get old HBase content (attempt %d/3): %v", i+1, oldErr)
			if i < 2 { // Don't sleep after the last attempt
				time.Sleep(5 * time.Second)
			}
		}
	}()
	go func() {
		defer wg.Done()
		for i := 0; i < 3; i++ { // Try up to 3 times
			newPage, newErr = collector.dbConnection.GetHBaseContent(message.JobID, message.URL, message.URLHash, message.NewVersionTime)
			if newErr == nil {
				break
			}
			log.Errorf("Failed to get new HBase content (attempt %d/3): %v", i+1, newErr)
			if i < 2 { // Don't sleep after the last attempt
				time.Sleep(5 * time.Second)
			}
		}
	}()
	wg.Wait()

	if oldErr != nil {
		collector.handleContentSnapshotError(message, oldErr)
		return oldErr
	}

	if newErr != nil {
		collector.handleContentSnapshotError(message, newErr)
		return newErr
	}

	oldLines, oldErr = bytesToStringLines(oldPage)
	if oldErr != nil {
		log.Error(oldErr)
		collector.handleContentSnapshotError(message, oldErr)
		return oldErr
	}

	newLines, newErr = bytesToStringLines(newPage)
	if newErr != nil {
		log.Error(oldErr)
		collector.handleContentSnapshotError(message, newErr)
		return newErr
	}

	highlightOldLines, highlightNewLines := visualDiff(oldLines, newLines)
	oldPage = []byte(strings.Join(highlightOldLines, ""))
	newPage = []byte(strings.Join(highlightNewLines, ""))

	oldPage = snapshot.MakeLinksAbsolute(oldPage, []byte(message.URL))
	newPage = snapshot.MakeLinksAbsolute(newPage, []byte(message.URL))

	oldPath, oldErr = snapshot.CreateFile(oldPage, dir)
	newPath, newErr = snapshot.CreateFile(newPage, dir)

	if oldErr != nil {
		collector.handleContentSnapshotError(message, oldErr)
		return oldErr
	}

	if newErr != nil {
		collector.handleContentSnapshotError(message, newErr)
		return newErr
	}

	chromeMsg := &snapshot.ContentChangeChrome{
		ID:          message.ID,
		Host:        message.Host,
		OldFilePath: oldPath,
		NewFilePath: newPath,
	}

	collector.screenshotTool.AddContentChangeChrome(chromeMsg)

	return nil
}

func (collector *Collector) handleSnapshotSensitiveWord(sourceID primitive.ObjectID, message *schema.SnapshotSensitiveWordMessage) error {

	log.Debugf("handleSnapshotSensitiveWord message id: %v, url: %v, urlhash: %v", message.ID, message.URL, message.URLHash)

	dir, err := snapshot.CreateTempDir(SensitiveWordPrefix)
	if err != nil {
		collector.handleSensitiveSnapshotError(message, err)
		return err
	}

	page, err := collector.dbConnection.GetSensitiveWordContent(sourceID)

	log.Infof("sensitiveword content: %s, length: %d", string(page[:50])+"...", len(string(page)))

	if err != nil {
		collector.handleSensitiveSnapshotError(message, err)
		return err
	}

	page, err = highlightObuscateWords(message.Words, page)
	if err != nil {
		collector.handleSensitiveSnapshotError(message, err)
		return err
	}

	page, err = highlightSensitiveWords(message.Words, page)
	if err != nil {
		collector.handleSensitiveSnapshotError(message, err)
		return err
	}

	page = snapshot.EnsureCharsetUTF8(page)

	page = snapshot.MakeLinksAbsolute(page, []byte(message.URL))

	filePath, err := snapshot.CreateFile(page, dir)
	if err != nil {
		collector.handleSensitiveSnapshotError(message, err)
		return err
	}

	chromeMsg := &snapshot.SensitiveWordChrome{
		ID:         message.ID,
		Host:       message.Host,
		SnapshotID: message.SnapshotID,
		FilePath:   filePath,
	}

	collector.screenshotTool.AddSensitiveWordChrome(chromeMsg)
	return nil
}

func (collector *Collector) handleContentSnapshotError(message *schema.SnapshotContentChangeMessage, err error) {
	log.Errorf("Failted to process ContentChangeSnapshotTask message: %s %v", message.ID, err)
	// srt := schema.FinalSnapshotContentChangeResult{
	// 	ID:     message.ID,
	// 	Host:   message.Host,
	// 	Status: consts.SnapshotError,
	// }
	// collector.producer.Produce(consts.TopicFinalSnapshotContentChangeResults, &srt)
}

func (collector *Collector) handleSensitiveSnapshotError(message *schema.SnapshotSensitiveWordMessage, err error) {
	log.Errorf("Failted to process SensitiveWordSnapshotTask message: %s %v", message.ID, err)
	// srt := schema.SnapshotSensitiveWordDoc{
	// 	ID:         message.ID,
	// 	Host:       message.Host,
	// 	SnapshotID: message.SnapshotID,
	// 	Status:     consts.SnapshotError,
	// }
	// collector.producer.Produce(consts.TopicFinalSnapshotSensitiveWordResults, &srt)
}

// 定期清理过期的截图
func cleanupScreenshots(dir string, maxAge time.Duration) {
	ticker := time.NewTicker(24 * time.Hour)
	for range ticker.C {
		threshold := time.Now().Add(-maxAge)
		filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			if !info.IsDir() && info.ModTime().Before(threshold) {
				os.Remove(path)
			}
			return nil
		})
	}
}

func init() {
	go cleanupScreenshots("/home/<USER>", 100*24*time.Hour)
}
