package schedule

import (
	"errors"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

var (
	sqlDB           *gorm.DB
	ErrSQLDBNotInit = errors.New("sqlDB not init")
)

type CollectOpt struct {
	DB *gorm.DB
}

func InitWorkerStatCollect(opt *CollectOpt) {
	sqlDB = opt.DB
}

type WorkerTaskStat struct {
	Time                  time.Time `gorm:"index"`
	Hostname              string
	Name                  string
	TaskCountInProcessing int32
	TaskCountFinished     int32
}

func (status *WorkerTaskStat) SaveToDB() error {
	if sqlDB == nil {
		return ErrSQLDBNotInit
	}
	return sqlDB.Save(status).Error
}

func NewWorkerTaskStat(ws *workerStat) *WorkerTaskStat {
	t := time.Now().UTC().Truncate(time.Minute * 5)
	name := ws.Name
	hostName := name
	hostPart := strings.Split(name, ":")
	if len(hostPart) > 0 {
		hostName = hostPart[0]
	}

	return &WorkerTaskStat{
		Hostname:              hostName,
		Name:                  name,
		Time:                  t,
		TaskCountInProcessing: ws.GetTaskCount(),
		TaskCountFinished:     ws.GetTaskFinishedCount(),
	}
}
