package generators

import (
	"websec/common/db"
	"websec/config"
	"websec/utils/semaphore"
)

type OptionFn func(*Generator) error

func WithDetectConfig(settings config.DetecterConfig) OptionFn {
	return func(generator *Generator) error {
		generator.settings = settings
		return nil
	}
}

func WithDBConnection(dbConnection *db.DBConnection) OptionFn {
	return func(generator *Generator) error {
		generator.dbConnection = dbConnection
		return nil
	}
}

func WithConsumeConcurrency(concurrency int64) OptionFn {
	return func(generator *Generator) error {
		generator.consumeSem = semaphore.NewWeighted(concurrency)
		return nil
	}
}

func WithSaveContentDepth(depth int) OptionFn {
	return func(generator *Generator) error {
		generator.saveDepth = int32(depth)
		return nil
	}
}

func WithUseHBase(clientType int) OptionFn {
	return func(generator *Generator) error {
		if clientType == db.HBaseOriginal || clientType == db.HBaseThrift {
			generator.useHBase = true
		} else {
			generator.useHBase = false
		}
		return nil
	}
}

func WithNewURL(v bool) OptionFn {
	return func(generator *Generator) error {
		generator.newURL = v
		return nil
	}
}

func WithDetectOcrPath(ocrPath string) OptionFn {
	return func(generator *Generator) error {
		generator.ocrPath = ocrPath
		return nil
	}
}
