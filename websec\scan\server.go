package scan

import (
	"net/url"
	"strings"
	"websec/scan/rules"
	"websec/utils"
	"websec/utils/log"
)

func genServerTestURL(URL string, param *rules.Param) string {
	parts, err := url.Parse(URL)
	if err != nil {
		log.Errorln("failed to parse url for directory test:", URL)
		return ""
	}
	paths := strings.Split(parts.Path, "/")
	path := strings.Join(paths[:len(paths)-1], "/")
	if strings.HasSuffix(path, "/") {
		path = path[:len(path)-1]
	}
	parts.RawPath = path + param.Value
	return utils.ToString(parts)
}
