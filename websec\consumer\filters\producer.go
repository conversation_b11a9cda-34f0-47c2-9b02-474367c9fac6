package filters

import (
	"websec/common/consts"
	"websec/common/schema"
)

func (filter *Filter) AddFinalSensitiveWord(val *schema.FinalSensitiveWordResult) {
	filter.producer.Produce(consts.TopicFinalSensitiveWordResults, val)
}

func (filter *Filter) AddFinalBlackLink(val *schema.FinalBlackLinkResult) {
	filter.producer.Produce(consts.TopicFinalBlackLinkResults, val)
}

func (filter *Filter) AddFinalContentChange(val *schema.FinalContentChangeResult) {
	filter.producer.Produce(consts.TopicFinalContentChangeResults, val)
}

func (filter *Filter) AddFinalVul(val *schema.FoundVulDoc) {
	filter.producer.Produce(consts.TopicFinalVulResults, val)
}

func (filter *Filter) AddFinalTrojan(val *schema.FinalTrojanResult) {
	filter.producer.Produce(consts.TopicFinalTrojanResults, val)
}

func (filter *Filter) AddFinalPhishing(val *schema.FinalPhishingResult) {
	filter.producer.Produce(consts.TopicFinalPhishingResults, val)
}
