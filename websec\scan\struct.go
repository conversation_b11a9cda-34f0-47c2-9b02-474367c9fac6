package scan

import (
	"context"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"websec/common"
	"websec/common/schema"
	"websec/detect"
	"websec/scan/rules"
	"websec/utils"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/tuchuang"

	"github.com/valyala/fasthttp"
)

type Affect = string

const (
	AffectContent        Affect = "text"
	AffectDirectory             = "directory"
	AffectFile                  = "file"
	AffectParameter             = "parameter"
	AffectSQLInject             = "sqlinject"
	AffectServer                = "server"
	AffectScript                = "script"
	AffectNmap                  = "nmap"
	AffectWeakPass              = "weakpass"
	AffectCookeSQLInject        = "cookie_sqlinject"
	AffectFuzzy                 = "fuzzy" //jiabao's python scritp server
	AffectXray                  = "xray"
)

const (
	AffectOptionContent         int = 1
	AffectOptionDirectory           = 2
	AffectOptionFile                = 4
	AffectOptionParameter           = 8
	AffectOptionSQLInject           = 16
	AffectOptionServer              = 32
	AffectOptionScript              = 64
	AffectOptionNmap                = 128
	AffectOptionWeakPass            = 256
	AffectOptionCookieSQLInject     = 512
	AffectOptionFuzzy               = 1024

	AffectOptionXray = 2048 // 通过xray 检测出的xss 等漏洞
	AffectOptionAll  = 65535
)

const MaxVulCountPerTextXML = 10

type Options struct {
	Tag             string
	Entry           string
	UserAgent       string
	SpecificXMLs    []string
	SpecificAffects []Affect

	FilterReg   string
	Concurrency int64
	ExpiredAt   time.Time
	Timeout     time.Duration
	Headers     http.Header
	Cookies     map[string]string

	Blueprint            *rules.BlueprintType
	ValidateSQLInjection bool
	ValidateXSS          bool
	PhantomJSPath        string
	PythonPath           string
	SqlmapPath           string
	SqlmapScreenshotPath string
	XSSJSPath            string
	XSSJSScreenshotPath  string
	XssPayLoadPath       string
	PangolinAddress      string
	CaptchaService       string

	WeakPassCheckURL      string
	PythonScriptsCheckURL string
	AssetID               string
	JobID                 string
	Task                  *schema.Task

	Sema *semaphore.Weighted
	Ctx  context.Context

	BucketSelect     *tuchuang.BucketSelect
	NmapHydraService string
}

func (options *Options) setDefaultValue() {
	if options.UserAgent == "" {
		options.UserAgent = "Mozilla/5.0 (Windows NT 6.2; rv:30.0) Gecko/20150101 Firefox/32.0"
	}
	if options.Concurrency == 0 {
		options.Concurrency = 10
	}
	if options.PhantomJSPath == "" {
		options.PhantomJSPath = "/usr/local/bin/phantomjs"
	}
	if options.PythonPath == "" {
		options.PythonPath = "/usr/bin/python"
	}
	if options.SqlmapPath == "" {
		options.SqlmapPath = "/opt/projects/sqlmap/"
	}
	if options.SqlmapScreenshotPath == "" {
		options.SqlmapScreenshotPath = "/opt/projects/websec/websec/scanner/js/shell.js"
	}
	if options.XSSJSPath == "" {
		options.XSSJSPath = "/opt/projects/websec/websec/scanner/js/xss.js"
	}
	if options.XSSJSScreenshotPath == "" {
		options.XSSJSScreenshotPath = "/opt/projects/websec/websec/scanner/js/xss4.js"
	}
	if options.PangolinAddress == "" {
		options.PangolinAddress = "127.0.0.1:45500"
	}
}

type ScanLink struct {
	UrlID    string
	Method   string
	URL      string
	Data     string
	IsStatic bool
	Headers  common.HttpHeaders
}

type AffectLink struct {
	UrlID   string
	Affect  string
	Method  string
	URL     string
	Data    string
	Headers common.HttpHeaders

	fp string // fingerprint
}

func keys(m url.Values) []string {
	ks := make([]string, len(m))
	i := 0
	for k := range m {
		ks[i] = k
		i++
	}
	return ks
}

var digitalPattern = regexp.MustCompile(`\d+`)

func (link *AffectLink) DeepCopy() *AffectLink {
	rlink := &AffectLink{
		Affect:  link.Affect,
		Method:  link.Method,
		URL:     link.URL,
		Data:    link.Data,
		Headers: make(common.HttpHeaders),
		fp:      link.fp,
	}

	for k, vs := range link.Headers {
		for i := range vs {
			rlink.Headers.Add(k, vs[i])
		}
	}
	return rlink
}

func (link *AffectLink) Fingerprint() string {
	if link.fp == "" {
		parts, err := url.Parse(link.URL)
		if err != nil {
			log.Errorln("failed to parse URL:", link.URL)
			return ""
		}

		hasher := sha1.New()
		hasher.Write([]byte(link.Method))
		hasher.Write([]byte(link.Affect))
		hasher.Write([]byte(digitalPattern.ReplaceAllString(parts.Path, "d+")))

		ks := keys(parts.Query())
		if len(ks) > 1 {
			sort.SliceStable(ks, func(i, j int) bool { return strings.Compare(ks[i], ks[j]) < 0 })
		}
		for i := range ks {
			hasher.Write([]byte(ks[i]))
		}
		if link.Method == "POST" {
			if values, err := url.ParseQuery(link.Data); err == nil {
				ds := keys(values)
				if len(ds) > 1 {
					sort.SliceStable(ds, func(i, j int) bool { return strings.Compare(ds[i], ds[j]) < 0 })
				}
				for i := range ds {
					hasher.Write([]byte(ds[i]))
				}
			}
		}
		link.fp = base64.RawURLEncoding.EncodeToString(hasher.Sum(nil))
	}
	return link.fp
}

type ScanRequest struct {
	Method  string
	URL     string
	Headers http.Header
	Data    string
}

type VulContext = map[string]interface{}

type FoundVul struct {
	Link     *AffectLink
	Vul      *rules.Vulnerability
	VulURL   string
	Severity string
	Context  VulContext
}

type ScanResult struct {
	FoundVuls    []*FoundVul
	RequestCount int64
	ErrorCount   int64
}

func (a *ScanResult) Merge(b *ScanResult) *ScanResult {
	if b != nil {
		a.FoundVuls = append(a.FoundVuls, b.FoundVuls...)
		a.RequestCount += b.RequestCount
		a.ErrorCount += b.ErrorCount
	}
	return a
}

type lazyContext struct {
	response *fasthttp.Response
	values   map[string]interface{}
}

func (context lazyContext) Get(key string) (interface{}, bool) {
	value, ok := context.values[key]
	if ok {
		return value, ok
	}
	response := context.response

	switch key {
	case "body":
		body, err := utils.GetOriginalBody(response)
		if err != nil {
			log.Errorln("failed to decode body:", err)
			return nil, false
		}
		body, _ = utils.ForceHtmlUtf8(body, string(response.Header.ContentType()))
		value = body

	case "response_code":
		value = response.StatusCode()

	case "response_length":
		contentLength := response.Header.ContentLength()
		if contentLength < 0 {

			if body, ok := context.Get("body"); ok {
				value = len(body.([]byte))
			} else {
				value = contentLength
			}
		} else {
			value = contentLength
		}

	case "header":
		value = response.Header.String()

	default:
		return nil, false
	}
	context.values[key] = value

	return value, true
}

func (context lazyContext) Set(key string, value interface{}) {
	context.values[key] = value
}

func (context lazyContext) Values() map[string]interface{} {
	return context.values
}

type Stats struct {
	FinishStatus   int64
	RequestCount   int64
	FoundVulsCount int64
	ScanCount      int64
	ErrorCount     int64
	ErrorReason    string
}

type WsdlResponse struct {
	URL           string              `json:"url"`
	Args          map[string][]string `json:"args,omitempty"`
	InterfaceName string              `json:"interface_name,omitempty"`
	Response      *http.Response      `json:"response,omitempty"`
}

type WsdlRequest struct {
	URL           string   `json:"wsdl"`
	InterfaceName string   `json:"interface_name"`
	Args          []string `json:"args,omitempty"`
}

type wsdlContext struct {
	response *WsdlResponse
	values   map[string]interface{}
}

func (context wsdlContext) Get(key string) (interface{}, bool) {
	value, ok := context.values[key]
	if ok {
		return value, ok
	}
	response := context.response.Response
	switch key {
	case "body":
		body, err := ioutil.ReadAll(response.Body)
		if err != nil {
			log.Errorln("failed to decode body:", err)
			return nil, false
		}

		value = string(body)
	case "response_code":
		value = response.StatusCode
	case "response_length":
		contentLength := response.ContentLength
		if contentLength < 0 {

			if body, ok := context.Get("body"); ok {
				value = len(body.([]byte))
			} else {
				value = contentLength
			}
		} else {
			value = contentLength
		}
	case "header":
		headerStr, err := json.Marshal(response.Header)
		if err != nil {
			log.Error("marshal resp header err", err)
			return nil, false
		}
		value = string(headerStr)
	default:
		return nil, false
	}
	context.values[key] = value
	return value, true
}

func (context wsdlContext) Set(key string, value interface{}) {
	context.values[key] = value
}

func (context wsdlContext) Values() map[string]interface{} {
	return context.values
}

type Website struct {
	CreateTime int64  `json:"create_time"`
	Detail     Detail `json:"detail"`
	Plugin     string `json:"plugin"`
	Target     Target `json:"target"`
}

type Target struct {
	Params interface{} `json:"params,omitempty"`
	URL    string      `json:"url"`
}

type Detail struct {
	Addr     string      `json:"addr"`
	PayLoad  string      `json:"payload"`
	SnapShot [][]string  `json:"snapshot"`
	Extra    interface{} `json:"extra"`
}

func (w Website) ToFoundVul(oldLink *AffectLink) (vul *FoundVul) {
	plugins := strings.Split(w.Plugin, "/")
	vulType := plugins[0]
	link := oldLink.DeepCopy()
	switch vulType {
	case "xss":

		link.Affect = AffectParameter

		vul = &FoundVul{
			Link:     link,
			Severity: "high",
			Vul: &rules.Vulnerability{
				VulXML:   XMLXSS,
				Severity: "high",
			},

			VulURL: w.Detail.Addr,
		}

	case "dirscan":
		vul = &FoundVul{
			Link:     link,
			Severity: "info",
			Vul: &rules.Vulnerability{
				VulXML:   detect.VulDirDisclosure,
				Severity: "info",
			},
			VulURL: w.Detail.Addr,
		}

	case "brute-force":
		vul = &FoundVul{
			Link:     link,
			Severity: weakPassVul.Severity,
			Vul:      &weakPassVul,
			VulURL:   w.Detail.Addr,
		}
	default:
		return
	}

	vulContext := make(map[string]interface{})

	if len(w.Detail.SnapShot) > 0 {
		requestResults := w.Detail.SnapShot[0]

		vulContext["method"] = link.Method

		reusltLen := len(requestResults)
		if reusltLen > 0 {
			vulContext["request_data"] = requestResults[0]
			vul.VulURL = requestResults[0]
		}
		if reusltLen > 1 {
			vulContext["body"] = []byte(requestResults[1])
		}
		extraMap := w.Detail.Extra.(map[string]interface{})
		if len(extraMap) > 0 {
			for name, v := range extraMap {
				vulContext[name] = v
			}
		}
	}
	vul.Context = vulContext
	vB, _ := json.Marshal(vul)
	fmt.Println(string(vB))
	return
}
