package validators

import (
	"bytes"
	"regexp"
)

var sqlCheckPatterns = []*regexp.Regexp{
	regexp.MustCompile(`(?i)\bselect\b.*?(?:\*|[a-z0-9]+).*?\bfrom\b`), // select
	regexp.MustCompile(`(?i)\bupdate\b.+?\bset\b.+?=`),                 // update
	regexp.MustCompile(`(?i)\bdelete from\b.+?\bwhere\b`),              // delete
	regexp.MustCompile(`(?i)\binsert into\b.+?\b(?:values|select)\b`),  // insert
}

var (
	exts             = `(?:php|asp|aspx|cs|jsp|inc|conf|ashx|asmx)`
	pathCheckPattern = regexp.MustCompile(
		`(?)(` +
			`\b[CDEFG]:\\(?:[^\\\n]+\\)+\S+?(?:\.` + exts + `)+\b` +
			`|` +
			`[\n \t\'"]` + `(?:/[^/\n]+?)*/(?:var|data|usr|opt|wwwroot|www|htdocs|apache2?|httpd)/(?:[^/\n]+?/).+?\.` + exts + `[\n \t\'"])`)
)

func ValidateErrorPage(args *ValidationArgs) (*ValidationResult, error) {
	for _, p := range sqlCheckPatterns {
		m := p.Find(args.Body)
		if len(m) > 0 {
			return &ValidationResult{Status: VulIsValid, Output: string(m)}, nil
		}
	}
	paths := pathCheckPattern.FindAll(args.Body, -1)
	if len(paths) < 1 {
		body := bytes.ToLower(args.Body)
		if bytes.Contains(body, []byte("warning")) && bytes.Contains(body, []byte("on line")) {
			return &ValidationResult{Status: VulIsValid, Output: "`warning` and `on line` found."}, nil
		}
	} else {
		for _, path := range paths {
			path = bytes.ToLower(path)
			if !bytes.Contains(path, []byte(`windows\microsoft.net`)) {
				return &ValidationResult{Status: VulIsValid, Output: string(path)}, nil
			}
		}
	}
	return &ValidationResult{Status: VulIsInvalid, Output: ""}, nil
}
