package utils

import (
	"bufio"
	"bytes"
	"fmt"
	"net/http"
	"net/textproto"
	"strings"
	"websec/utils/log"

	"github.com/valyala/fasthttp"
)

func GetOriginalBody(response *fasthttp.Response) ([]byte, error) {
	contentEncoding := strings.ToLower(string(response.Header.Peek("Content-Encoding")))
	var body []byte
	var err error
	switch contentEncoding {
	case "", "none", "identity":
		body, err = response.Body(), nil
	case "gzip":
		body, err = response.BodyGunzip()
	case "deflate":
		body, err = response.BodyInflate()
	default:

		body, err = []byte{}, fmt.Errorf("unsupported Content-Encoding: %v", contentEncoding)
	}
	return body, err
}

func GetUtf8Body(response *fasthttp.Response) ([]byte, error) {
	body, err := GetOriginalBody(response)
	if err != nil {
		return nil, err
	}
	body, _ = ForceHtmlUtf8(body, string(response.Header.ContentType()))
	return body, nil
}

func HeadersToString(headers http.Header) (string, error) {
	result := bytes.Buffer{}
	err := headers.Write(&result)
	if err != nil {
		return "", err
	}
	return result.String(), nil
}

func ParseHeader(headerStr string) http.Header {
	reader := bufio.NewReader(strings.NewReader(headerStr + "\r\n\r\n"))
	tp := textproto.NewReader(reader)

	mimeHeader, err := tp.ReadMIMEHeader()
	if err != nil {
		log.Errorln("failed to parse header:", headerStr, err)
		return http.Header{}
	}
	return http.Header(mimeHeader)
}
