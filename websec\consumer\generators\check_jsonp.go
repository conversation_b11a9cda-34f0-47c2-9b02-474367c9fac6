package generators

import (
	"crypto/tls"
	"io/ioutil"
	"net/http"
	"net/url"
	"regexp"
	"time"

	"github.com/robertkrimen/otto/ast"
	"github.com/robertkrimen/otto/parser"
)

var goHTTPClient = http.Client{

	Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}},
	Timeout:   10 * time.Second,

	CheckRedirect: func(req *http.Request, via []*http.Request) error {
		return http.ErrUseLastResponse
	},
}

var ContentJS = map[string]bool{
	"application/ecmascript":   true,
	"application/javascript":   true,
	"application/x-ecmascript": true,
	"application/x-javascript": true,
	"text/ecmascript":          true,
	"text/javascript":          true,
	"text/javascript1.0":       true,
	"text/javascript1.1":       true,
	"text/javascript1.2":       true,
	"text/javascript1.3":       true,
	"text/javascript1.4":       true,
	"text/javascript1.5":       true,
	"text/jscript":             true,
	"text/livescript":          true,
	"text/x-ecmascript":        true,
	"text/x-javascript":        true,
}

func CheckSenseJsonp(jsUrl string) (bool, error) {
	queryMap, domainString, err := UrlParser(jsUrl)
	if err != nil {
		return false, err
	}

	isCallback, callbackFuncName, err := CheckJSIsCallback(queryMap)

	if isCallback {

		normalRespContent, err := GetJsResponse(jsUrl, domainString)
		if err != nil {
			return false, err
		}
		isJsonpNormal, err := CheckJsRespAst(normalRespContent, callbackFuncName)
		if err != nil {
			return false, err
		}

		if isJsonpNormal {
			noRefererContent, err := GetJsResponse(jsUrl, "")
			if err != nil {
				return false, err
			}
			isJsonp, err := CheckJsRespAst(noRefererContent, callbackFuncName)
			if err != nil {
				return false, err
			}
			return isJsonp, nil
		}

	}
	return false, nil
}

func UrlParser(jsUrl string) (url.Values, string, error) {
	urlParser, err := url.Parse(jsUrl)
	if err != nil {
		return nil, "", err
	}

	domainString := urlParser.Scheme + "://" + urlParser.Host
	return urlParser.Query(), domainString, nil
}

func CheckJSIsCallback(queryMap url.Values) (bool, string, error) {
	var re = regexp.MustCompile(`(?m)(?i)(callback)|(jsonp)|(^cb$)|(function)`)
	for k, v := range queryMap {
		regResult := re.FindAllString(k, -1)
		if len(regResult) > 0 && len(v) > 0 {
			return true, v[0], nil
		}
	}
	return false, "", nil
}

func CheckIsSensitiveKey(key string) (bool, error) {
	var re = regexp.MustCompile(`(?m)(?i)(uid)|(userid)|(user_id)|(nin)|(name)|(username)|(nick)`)
	regResult := re.FindAllString(key, -1)
	if len(regResult) > 0 {
		return true, nil
	}
	return false, nil
}

func GetJsResponse(jsUrl string, referer string) (string, error) {
	req, err := http.NewRequest("GET", jsUrl, nil)
	if err != nil {
		return "", nil
	}
	req.Header.Set("Referer", referer)
	resp, err := goHTTPClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode == 200 {
		return string(body), nil
	}
	return "", nil
}

func CheckJsRespAst(content string, funcName string) (bool, error) {

	program, err := parser.ParseFile(nil, "", content, 0)
	if err != nil {
		return false, err
	}
	if len(program.Body) > 0 {
		statement := program.Body[0]
		expression := statement.(*ast.ExpressionStatement).Expression
		expName := expression.(*ast.CallExpression).Callee.(*ast.Identifier).Name

		if funcName != expName {
			return false, err
		}
		argList := expression.(*ast.CallExpression).ArgumentList
		for _, arg := range argList {
			result := DealAstExpression(arg)
			if result != true {
				continue
			}
			return result, nil
		}
	}

	return false, nil
}

func DealAstExpression(expression ast.Expression) bool {
	objectLiteral, isObjectLiteral := expression.(*ast.ObjectLiteral)
	if isObjectLiteral {
		values := objectLiteral.Value
		for _, value := range values {
			result := DealAstProperty(value)
			if result != true {
				continue
			}
			return result
		}
	}
	return false
}
func DealAstProperty(value ast.Property) bool {
	secondLevelValue := value.Value

	objectLiteral, isObjectLiteral := secondLevelValue.(*ast.ObjectLiteral)
	arrayLiteral, isArrayLiteral := secondLevelValue.(*ast.ArrayLiteral)
	stringLiteral, isStringLiteral := secondLevelValue.(*ast.StringLiteral)
	numberLiteral, isNumberLiteral := secondLevelValue.(*ast.NumberLiteral)
	if isObjectLiteral {
		thirdLevelValue := objectLiteral.Value
		for _, v := range thirdLevelValue {
			DealAstProperty(v)
		}
	} else if isArrayLiteral {
		thirdLevelValue := arrayLiteral.Value
		for _, v := range thirdLevelValue {
			DealAstExpression(v)
		}
	} else if isStringLiteral {

		thirdLevelValue := stringLiteral.Value
		isSensitiveKey, _ := CheckIsSensitiveKey(value.Key)
		if isSensitiveKey && thirdLevelValue != "" {
			return true
		}
	} else if isNumberLiteral {
		thirdLevelValue := numberLiteral.Value
		isSensitiveKey, _ := CheckIsSensitiveKey(value.Key)
		if isSensitiveKey && thirdLevelValue != 0 {
			return true
		}
	}
	return false
}
