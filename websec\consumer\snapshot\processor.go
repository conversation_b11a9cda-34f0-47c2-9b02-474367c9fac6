package snapshot

import (
	"context"
	"encoding/json"
	"strings"
	"sync"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/config"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/stream"
	"websec/utils/tuchuang"
)

type Processor struct {
	consumer     *stream.Consumer
	producer     *stream.Producer
	dbConnection *db.DBConnection

	snapshotConfig *config.SnapshotConfig

	consumeSema *semaphore.Weighted
	wg          sync.WaitGroup

	imgUploader *tuchuang.BucketSelect

	uploadService    *UploadService
	chromeScreenShot *ChromeScreenShot

	ignoredHostsMap map[string]struct{}
}

func (processor *Processor) Process(msg *stream.Message, sync bool) error {
	if !sync {
		defer processor.consumeSema.Release(1)
		defer processor.wg.Done()
	}

	switch msg.Topic {
	case consts.TopicSnapshotSensitiveWordTodo:
		return processor.processSnapshotSensitiveMessage(msg)
	case consts.TopicSnapshotContentChangeTodo:
		return processor.processSnapshotContentChange(msg)
	default:
		log.Errorln("unknown message topic:", msg.Topic)
	}

	return nil
}

func (processor *Processor) Run() {
	processor.producer.Go()
	processor.consumer.SubscribeTopics(subscribedTopics)
	processor.consumer.Go()
	processor.uploadService.Start()
	processor.chromeScreenShot.Start()

	for msg := range processor.consumer.Messages() {
		if err := processor.consumeSema.Acquire(context.TODO(), 1); err == nil {
			processor.wg.Add(1)
			go processor.Process(msg, false)
		} else {
			processor.Process(msg, true)
		}
	}
	processor.wg.Wait()
	processor.producer.Close()
	processor.chromeScreenShot.Stop()
	processor.uploadService.Stop()
	log.Infoln("Processor Grace Exit")
}

func (processor *Processor) Stop() {
	processor.consumer.Close()
}

func (processor *Processor) processSnapshotContentChange(msg *stream.Message) error {
	var message = new(schema.SnapshotContentChangeMessage)
	err := json.Unmarshal(msg.Value, message)
	if err != nil {
		log.Error(err)
		return err
	}

	dir, err := CreateTempDir(ContentChangePrefix)
	if err != nil {
		processor.handleSnapshotError(message, err)
		return err
	}

	var oldErr, newErr error
	var oldPath, newPath string
	var oldLines, newLines []string
	var oldPage, newPage []byte
	var wg sync.WaitGroup

	wg.Add(2)
	go func() {
		defer wg.Done()
		oldPage, oldErr = processor.dbConnection.GetOldReferPageContent(message.JobID, message.AssetID, message.Host, message.URL, message.URLHash, message.OldVersionTime)
	}()
	go func() {
		defer wg.Done()
		newPage, newErr = processor.dbConnection.GetHBaseContent(message.JobID, message.URL, message.URLHash, message.NewVersionTime)
	}()
	wg.Wait()

	if oldErr != nil {
		processor.handleSnapshotError(message, oldErr)
		return oldErr
	}

	if newErr != nil {
		processor.handleSnapshotError(message, newErr)
		return newErr
	}

	oldLines, oldErr = bytesToStringLines(oldPage)
	if oldErr != nil {
		log.Error(oldErr)
		processor.handleSnapshotError(message, oldErr)
		return oldErr
	}

	newLines, newErr = bytesToStringLines(newPage)
	if newErr != nil {
		log.Error(oldErr)
		processor.handleSnapshotError(message, newErr)
		return newErr
	}

	highlightOldLines, highlightNewLines := visualDiff(oldLines, newLines)
	oldPage = []byte(strings.Join(highlightOldLines, ""))
	newPage = []byte(strings.Join(highlightNewLines, ""))

	oldPage = MakeLinksAbsolute(oldPage, []byte(message.URL))
	newPage = MakeLinksAbsolute(newPage, []byte(message.URL))

	oldPath, oldErr = CreateFile(oldPage, dir)
	newPath, newErr = CreateFile(newPage, dir)
	if oldErr != nil && newErr != nil {
		processor.handleSnapshotError(message, oldErr)
		return oldErr
	}

	chromeMsg := &ContentChangeChrome{
		ID:          message.ID,
		Host:        message.Host,
		OldFilePath: oldPath,
		NewFilePath: newPath,
	}
	processor.chromeScreenShot.AddContentChangeChrome(chromeMsg)

	return nil
}

func (processor *Processor) handleSnapshotError(message *schema.SnapshotContentChangeMessage, err error) {
	log.Errorf("Failted to process SnapshotTask message: %s %v", message.ID, err)
	srt := schema.FinalSnapshotContentChangeResult{
		ID:     message.ID,
		Host:   message.Host,
		Status: consts.SnapshotError,
	}
	processor.producer.Produce(consts.TopicFinalSnapshotContentChangeResults, &srt)
}

func initSnapShotConfig(snapshotConfig *config.SnapshotConfig) {
	chromeEndpoint = snapshotConfig.ChromeEndpoint
	phantomjsConfig = snapshotConfig.PhantomjsConfig
	phantomjsScript = snapshotConfig.PhantomjsScript
	taskTimeout = snapshotConfig.TaskTimeout

	log.Infof(`initSnapShotConfig:
				 chromeEndpoint  = %s
				 phantomjsConfig = %s
				 phantomjsScript = %s
				 taskTimeout = %v`,
		chromeEndpoint,
		phantomjsConfig,
		phantomjsScript,
		taskTimeout,
	)
}

func NewProcessor(consumer *stream.Consumer, producer *stream.Producer, options ...OptionFn) (*Processor, error) {
	var err error
	processor := &Processor{
		consumer:        consumer,
		producer:        producer,
		consumeSema:     semaphore.NewWeighted(20),
		ignoredHostsMap: make(map[string]struct{}),
	}

	for _, opt := range options {
		err = opt(processor)
		if err != nil {
			return nil, err
		}
	}

	log.Infof("snapshot processor.ignoredHostsMap %v", processor.ignoredHostsMap)

	initSnapShotConfig(processor.snapshotConfig)

	processor.uploadService = NewUploadService(processor)
	processor.chromeScreenShot = NewChromeScreenShot(processor, processor.snapshotConfig.ChromeAddr)

	return processor, nil
}
