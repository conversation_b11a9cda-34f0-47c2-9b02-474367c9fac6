package scripts

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"net/http"
)

func TomcatWeakPass(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var rawurl = constructURL(args, "/")
	var fl = []string{"Application Manager", "Welcome to Tomcat"}
	var wl = []string{"admin:admin", "tomcat:tomcat", "admin:123456", "admin:", "root:root",
		"root:", "tomcat:", "tomcat:s3cret"}
	var buf bytes.Buffer
	buf.WriteString(rawurl)
	buf.WriteString("/manager/html")
	loginurl := buf.String()

	for _, value := range wl {
		authstr := base64.StdEncoding.EncodeToString([]byte(value))
		authstr = "Basic " + authstr
		req, err := http.NewRequest(http.MethodGet, loginurl, nil)

		if err != nil {
			return nil, err
		}

		req.Header.Set("Authorization", authstr)
		response, err := goHTTPClient.Do(req)

		if err != nil {
			return nil, err
		}

		if response.StatusCode == 401 || response.StatusCode == 403 {
			continue
		}

		if response.StatusCode == 404 {

			return &invulnerableResult, nil
		}

		defer response.Body.Close()
		reshtml, err := ioutil.ReadAll(response.Body)
		for _, flag := range fl {
			if bytes.Contains(reshtml, []byte(flag)) {
				return &ScriptScanResult{Vulnerable: true, Output: fmt.Sprintf("%v|%v", loginurl, value), Body: reshtml}, nil
			}
		}
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("tomcat_weakpass.xml", TomcatWeakPass)
}
