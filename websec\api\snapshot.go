package api

import (
	"context"
	"encoding/json"
	"net/http"
	"sort"
	"strings"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils"
	"websec/utils/log"

	"github.com/gorilla/mux"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ContentChangeSnapshotResponse struct {
	Status      string `json:"status"`
	NewSnapshot string `json:"new_snapshot,omitempty"`
	OldSnapshot string `json:"old_snapshot,omitempty"`
}

func (api *API) contentChangeSnapshotHandler(rw http.ResponseWriter, req *http.Request) {
	vars := mux.Vars(req)
	sourceID := vars["id"]

	id, err := primitive.ObjectIDFromHex(sourceID)
	if err != nil {
		log.Errorln(err)
		api.writeResponse(rw, newFailResponse("id error"))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	doc := new(schema.FoundContentChangeDoc)
	err = api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionFoundContentChange).FindOne(ctx,
		bson.M{"_id": id}).Decode(doc)
	if err != nil {
		log.Errorln(err)
		api.writeResponse(rw, newFailResponse("the snapshot is not exist,"+err.Error()))
		return
	}

	var result ContentChangeSnapshotResponse
	switch uint8(doc.Status) {
	case consts.ToBeSnapshot:
		msg := &schema.SnapshotContentChangeMessage{
			ID:             doc.ID.Hex(),
			Host:           doc.Host,
			AssetID:        doc.AssetID,
			JobID:          doc.JobID,
			URL:            doc.URL,
			URLHash:        doc.URLHash,
			OldVersionTime: doc.OldVersionTime,
			NewVersionTime: doc.NewVersionTime,
		}
		api.options.Producer.Produce(consts.TopicSnapshotContentChangeTodo, msg)
		_, err = api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionFoundContentChange).UpdateOne(ctx,
			bson.M{"_id": doc.ID}, bson.M{"$set": bson.M{"status": consts.SnapshotRunning}})
		if err != nil {
			log.Errorln(err)
			api.writeResponse(rw, newFailResponse("update to mongo fail,"+err.Error()))
			return
		}
		result.Status = "running"
	case consts.SnapshotRunning:
		result.Status = "running"
	case consts.SnapshotFinished:
		result.Status = "finished"
		result.NewSnapshot = doc.NewSnapshot
		result.OldSnapshot = doc.OldSnapshot
	default:
		result.Status = "error"
	}

	api.writeResponse(rw, newSuccessResponse(result))
}

type SnapshotSensitiveWord struct {
	ID               primitive.ObjectID `bson:"_id,omitempty"`
	SrcID            primitive.ObjectID `bson:"src_id"`
	Words            string             `bson:"words"`
	Status           uint8              `bson:"status"`
	ContentWordsHash string             `bson:"content_words_hash"`
	SnapshotURL      string             `bson:"snapshot_url"`
}

type SensitiveWordSnapshotRequest struct {
	SourceID string   `json:"src_id"`
	Words    []string `json:"words"`
	Host     string   `json:"host"`
}

type SensitiveWordSnapshotResponse struct {
	Status      string `json:"status"`
	SnapshotID  string `json:"snapshot_id,omitempty"`
	SnapshotURL string `json:"snapshot,omitempty"`
}

func (api *API) sensitiveWordSnapshotHandler(rw http.ResponseWriter, req *http.Request) {
	body := getHttpBody(req)
	reqData := new(SensitiveWordSnapshotRequest)
	err := json.Unmarshal(body, reqData)
	if err != nil {
		log.Errorln("Unmarshal error:", err)
		api.writeResponse(rw, newFailResponse("param error"))
		return
	}

	id, err := primitive.ObjectIDFromHex(reqData.SourceID)
	if err != nil {
		log.Errorln("ObjectIDFromHex error:", err)
		api.writeResponse(rw, newFailResponse("param error"))
		return
	}

	queryOpts := bson.M{"_id": id}
	if reqData.Host != "" {
		queryOpts["host"] = reqData.Host
	}
	log.Debugf("sensitive word snapshot query opts: %v", queryOpts)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	doc := new(schema.FoundSensitiveWordsDoc)
	err = api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionFoundSensitiveWords).FindOne(ctx, queryOpts).Decode(doc)
	if err != nil {
		log.Errorln("get sensitiveword from mongo error:", err)
		api.writeResponse(rw, newFailResponse("sensitiveword not found "+err.Error()))
		return
	}

	sort.Strings(reqData.Words)
	words := strings.Join(reqData.Words, ",")

	snapShotWord := new(SnapshotSensitiveWord)
	contentWordsHash := utils.Md5(doc.ContentHash + words)
	if doc.ContentHash == "" {
		err = api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionSnapshotSensitiveWord).FindOne(ctx,
			bson.M{
				"src_id": doc.ID,
				"words":  words,
			}).Decode(snapShotWord)
	} else {
		err = api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionSnapshotSensitiveWord).FindOne(ctx,
			bson.M{
				"content_words_hash": contentWordsHash,
			}).Decode(snapShotWord)
	}

	if err != nil && err != mongo.ErrNoDocuments {
		log.Errorln("get snapshot_sensitiveword error:", err)
		api.writeResponse(rw, newFailResponse("sensitiveword not found "+err.Error()))
		return
	}

	result := &SensitiveWordSnapshotResponse{}

	if err == mongo.ErrNoDocuments {
		wordResult := getWordResult(doc, reqData.Words)
		if len(wordResult) == 0 {
			log.Errorln("get snapshot_sensitiveword len==0 error:", err)
			api.writeResponse(rw, newFailResponse("sensitive word error "+err.Error()))
			return
		}

		snapShotWord.SrcID = doc.ID
		snapShotWord.Words = words
		snapShotWord.ContentWordsHash = contentWordsHash
		snapShotWord.Status = consts.SnapshotRunning

		res, err := api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionSnapshotSensitiveWord).InsertOne(ctx, snapShotWord)
		if err != nil {
			log.Errorln("insert snapshot_sensitiveword collection error", err)
			api.writeResponse(rw, newFailResponse("sensitive word error "+err.Error()))
			return
		}
		snapshotID := res.InsertedID.(primitive.ObjectID).Hex()

		snapShotMsg := &schema.SnapshotSensitiveWordMessage{
			ID:          reqData.SourceID,
			Host:        doc.Host,
			AssetID:     doc.AssetID,
			JobID:       doc.JobID,
			SnapshotID:  snapshotID,
			URL:         doc.URL,
			URLHash:     doc.URLHash,
			VersionTime: doc.VersionTime,
			Words:       wordResult,
		}

		api.options.Producer.Produce(consts.TopicSnapshotSensitiveWordTodo, snapShotMsg)
		result.Status = "running"
		result.SnapshotID = snapshotID
	} else {
		if snapShotWord.Status == consts.SnapshotRunning {
			result.Status = "running"
			result.SnapshotID = snapShotWord.ID.Hex()
		} else if snapShotWord.Status == consts.SnapshotFinished {
			result.Status = "finished"
			result.SnapshotID = snapShotWord.ID.Hex()
			result.SnapshotURL = snapShotWord.SnapshotURL
		} else {
			result.Status = "error"
		}
	}

	api.writeResponse(rw, newSuccessResponse(result))
}

func getWordResult(doc *schema.FoundSensitiveWordsDoc, words []string) []schema.WordResult {
	result := make([]schema.WordResult, 0, len(words))
	for _, v := range words {
		for _, vv := range doc.Results {
			if vv.Word == v {
				tmp := schema.WordResult{
					Word:         vv.Word,
					IsCustomized: vv.IsCustomized,
					Position:     vv.Position,
				}
				result = append(result, tmp)
				break
			}
		}
	}
	return result
}
