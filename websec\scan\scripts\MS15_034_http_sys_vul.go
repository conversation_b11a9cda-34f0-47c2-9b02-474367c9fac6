package scripts

import (
	"bytes"
	"crypto/tls"
	"math/rand"
	"net"
	"regexp"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

var xUserAgent = "Mozilla/5.0 (Windows NT 6.2; rv:30.0) Gecko/20150101 Firefox/32.0"

func xNewHTTPClient() *fasthttp.Client {
	return &fasthttp.Client{
		Name:                          xUserAgent,
		ReadTimeout:                   20 * time.Second,
		WriteTimeout:                  20 * time.Second,
		MaxResponseBodySize:           1024 * 1024 * 2,
		DisableHeaderNamesNormalizing: true,
		Dial: func(addr string) (net.Conn, error) {
			return fasthttp.DialTimeout(addr, 5*time.Second)
		},
		TLSConfig: &tls.Config{

			InsecureSkipVerify: true,
		},
	}
}

func xGeneralSetReqHeader(req *fasthttp.Request) *fasthttp.Request {
	req.Header.Set("User-Agent", xUserAgent)
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Connection", "keep-alive")
	return req
}

var srcRe = regexp.MustCompile(`href\s*=\s*"([^\"]+?\.(css)(\?[^\"]*)?)"`)
var hrefRe = regexp.MustCompile(`src="([^\"]+?\.(jpg|png|js)([@|\?][^\"]*)?)"`)

func foundLinks(body []byte) ([]string, error) {
	ret := make([]string, 0)
	match := srcRe.FindAllSubmatch([]byte(body), -1)
	for _, g := range match {
		ret = append(ret, string(g[1]))
	}
	match = hrefRe.FindAllSubmatch([]byte(body), -1)
	for _, g := range match {
		ret = append(ret, string(g[1]))
	}

	return ret, nil
}

func vulCheck(url string) (bool, error) {
	request := fasthttp.AcquireRequest()
	request.SetRequestURI(url)
	request = xGeneralSetReqHeader(request)
	request.Header.Set("Range", "bytes=0-18446744073709551615")
	request.Header.SetMethod("GET")
	defer fasthttp.ReleaseRequest(request)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	client := xNewHTTPClient()
	err := client.DoTimeout(request, resp, 5*time.Second)
	if err != nil {
		return false, err
	}

	body, err := utils.GetUtf8Body(resp)
	if err != nil {
		return false, err
	}

	server := resp.Header.Peek("Server")
	if len(server) != 0 {
		if !bytes.Contains(server, []byte("IIS")) {
			return false, nil
		}

		if resp.StatusCode() == 416 || bytes.Contains(body, []byte("Requested Range Not Satisfiable")) {
			return true, nil
		}
		return false, nil
	}
	return false, nil
}

func HTTPSysVul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	url := constructURL(args, "/")

	yes, err := vulCheck(url)
	if err != nil {
		return nil, err
	}
	if yes {
		return &ScriptScanResult{Vulnerable: true, Output: url}, nil
	}

	request := fasthttp.AcquireRequest()
	request.SetRequestURI(url)
	request = xGeneralSetReqHeader(request)
	request.Header.SetMethod("GET")
	defer fasthttp.ReleaseRequest(request)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	client := xNewHTTPClient()
	err = client.DoTimeout(request, resp, 5*time.Second)
	if err != nil {
		return nil, err
	}

	body, err := utils.GetUtf8Body(resp)
	if err != nil {
		return nil, err
	}
	links, err := foundLinks(body)
	if err != nil {
		return nil, err
	}

	var randomLinks []string
	num := 5
	if len(links) > num {
		randomLinks = make([]string, num)
		rand.Seed(time.Now().UTC().UnixNano())
		for i := range randomLinks {
			randomLinks[i] = links[rand.Intn(len(links))]
		}
	} else {
		randomLinks = links
	}

	for _, link := range randomLinks {
		yes, err := vulCheck(link)
		if err != nil {
			continue
		}
		if yes {
			return &ScriptScanResult{Vulnerable: true, Output: url}, nil
		}
	}
	return &ScriptScanResult{Vulnerable: false, Output: url}, nil
}

func init() {
	registerHandler("MS15-034_Http.sys_vul.xml", HTTPSysVul)
}
