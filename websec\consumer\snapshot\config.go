package snapshot

import (
	"websec/common/consts"
	"websec/common/schema"
)

var subscribedTopics = []string{
	consts.TopicSnapshotContentChangeTodo,
	consts.TopicSnapshotSensitiveWordTodo,
}

const TempDir = "/tmp"
const (
	ContentChangePrefix = "snapshot_cc_"
	SensitiveWordPrefix = "snapshot_sw_"
)

type MatchedSensitiveWord = schema.WordResult

type SensitiveWordChrome struct {
	ID         string
	Host       string
	AssetID    string
	SnapshotID string
	FilePath   string
}

type ContentChangeChrome struct {
	ID          string
	Host        string
	AssetID     string
	JobID       string
	OldFilePath string
	NewFilePath string
}

type SensitiveWordUpload struct {
	ID         string
	Host       string
	AssetID    string
	SnapshotID string
	ImageData  []byte
	Tool       string
	JS         bool
	Err        error
}

type ContentChangeUpload struct {
	ID           string
	Host         string
	AssetID      string
	JobID        string
	OldImageData []byte
	NewImageData []byte
	OldTool      string
	NewTool      string
	OldJS        bool
	NewJS        bool
	Err          error
}

var (
	chromeEndpoint = ""

	phantomjsExec   = "phantomjs"
	phantomjsConfig = ""
	phantomjsScript = ""
	taskTimeout     = int(10)
)
