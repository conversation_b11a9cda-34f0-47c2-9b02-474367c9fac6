package scripts

import (
	"bytes"
	"net/http"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
)

func WebRarAndZipDown(args *ScriptScanArgs) (*ScriptScanResult, error) {
	parts := strings.Split(args.Host, ".")

	var err error

	if len(parts) > 1 {
		name := parts[0]
		if name == "www" {
			name = parts[1]
		}
		request := fasthttp.AcquireRequest()
		response := fasthttp.AcquireResponse()
		defer fasthttp.ReleaseRequest(request)
		defer fasthttp.ReleaseResponse(response)

		var url string
		var contentType []byte
		for _, suffix := range []string{".rar", ".zip"} {
			url = constructURL(args, name+suffix)
			request.Header.SetMethod(http.MethodHead)
			request.SetRequestURI(url)
			err = httpClient.DoTimeout(request, response, time.Second*5)
			if err != nil {
				continue
			}
			contentType = response.Header.Peek("Content-Type")
			if response.StatusCode() == 200 && bytes.Contains(contentType, []byte("application/octet-stream")) && response.Header.ContentLength() > 0 {
				return &ScriptScanResult{Vulnerable: true, Output: url}, nil
			}
		}
	}
	if err != nil {
		return nil, err
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("web_rar_and_zip_down_vul.xml", WebRarAndZipDown)
}
