package scripts

import (
	"github.com/sergi/go-diff/diffmatchpatch"
)

func diffRatio(diffs []diffmatchpatch.Diff, totalLength int) float64 {
	var commonLength int
	for i := range diffs {
		if diffs[i].Type == diffmatchpatch.DiffEqual {
			commonLength += len(diffs[i].Text)
		}
	}
	return 2.0 * float64(commonLength) / float64(totalLength)
}

func textSimilarity(a, b string) float64 {
	totalLength := len(a) + len(b)
	if totalLength == 0 {
		return 1.0
	}

	var differ = diffmatchpatch.New()
	diffs := differ.DiffMain(a, b, false)
	return diffRatio(diffs, totalLength)
}
