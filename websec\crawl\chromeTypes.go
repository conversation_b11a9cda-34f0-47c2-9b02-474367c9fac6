package crawl

import (
	"websec/utils"
)

type CookieSameSite string

const (
	CookieSameSiteNotSet CookieSameSite = ""
	CookieSameSiteStrict CookieSameSite = "Strict"
	CookieSameSiteLax    CookieSameSite = "Lax"
)

func (e CookieSameSite) Valid() bool {
	switch e {
	case "Strict", "Lax":
		return true
	default:
		return false
	}
}

func (e CookieSameSite) String() string {
	return string(e)
}

type SetCookieArgs struct {
	Name     string         `json:"name"`               // Cookie name.
	Value    string         `json:"value"`              // Cookie value.
	URL      *string        `json:"url,omitempty"`      // The request-URI to associate with the setting of the cookie. This value can affect the default domain and path values of the created cookie.
	Domain   *string        `json:"domain,omitempty"`   // Cookie domain.
	Path     *string        `json:"path,omitempty"`     // Cookie path.
	Secure   *bool          `json:"secure,omitempty"`   // True if cookie is secure.
	HTTPOnly *bool          `json:"httpOnly,omitempty"` // True if cookie is http-only.
	SameSite CookieSameSite `json:"sameSite,omitempty"` // Cookie SameSite type.
	Expires  int64          `json:"expires,omitempty"`  // Cookie expiration date, session cookie if not set
}

func NewSetCookieArgs(name string, value string) *SetCookieArgs {
	args := new(SetCookieArgs)
	args.Name = name
	args.Value = value
	return args
}

type SetCookieReply struct {
	Success bool `json:"success"` // True if successfully set cookie.
}

type CookieParam struct {
	Name     string         `json:"name"`               // Cookie name.
	Value    string         `json:"value"`              // Cookie value.
	URL      string         `json:"url,omitempty"`      // The request-URI to associate with the setting of the cookie. This value can affect the default domain and path values of the created cookie.
	Domain   string         `json:"domain,omitempty"`   // Cookie domain.
	Path     string         `json:"path,omitempty"`     // Cookie path.
	Secure   bool           `json:"secure,omitempty"`   // True if cookie is secure.
	HTTPOnly bool           `json:"httpOnly,omitempty"` // True if cookie is http-only.
	SameSite CookieSameSite `json:"sameSite,omitempty"` // Cookie SameSite type.
	Expires  int64          `json:"expires,omitempty"`  // Cookie expiration date, session cookie if not set
}

type SetCookiesArgs struct {
	Cookies []CookieParam `json:"cookies"` // Cookies to be set.
}

func NewSetCookiesArgs(cookies []CookieParam) *SetCookiesArgs {
	args := new(SetCookiesArgs)
	args.Cookies = cookies
	return args
}

func GetCookieParams(cookiejar *utils.MyCookieJar) []CookieParam {
	cookies := cookiejar.GetCookies()

	cookiejar.Lock()
	defer cookiejar.Unlock()
	cookieParams := []CookieParam{}
	for key, value := range cookies {
		param := CookieParam{}
		param.Name = key
		param.Value = string(value.Value())
		param.Domain = string(value.Domain())
		param.Path = string(value.Path())
		param.Secure = value.Secure()
		param.HTTPOnly = value.HTTPOnly()
		if !value.Expire().IsZero() {
			param.Expires = value.Expire().Unix()
		}
		cookieParams = append(cookieParams, param)
	}
	return cookieParams
}

type evaluateParams struct {
	Expression    string `json:"expression"`
	ReturnByValue bool   `json:"returnByValue"`
}

type addScriptToEvaluateOnNewDocumentParams struct {
	Source string `json:"source"`
}

type setLifecycleEventsEnabledParams struct {
	Enabled bool `json:"enabled"`
}

type lifecycleEvent struct {
	FrameID   string  `json:"frameId"`
	LoaderID  string  `json:"loaderId"`
	Name      string  `json:"name"`
	Timestamp float64 `json:"timpstamp"`
}

type eventWindowOpen struct {
	URL            string   `json:"url"`
	WindowName     string   `json:"windowName"`
	WindowFeatures []string `json:"windowFeatures"`
	UserGesture    bool     `json:"userGesture"`
}

type targetInfo struct {
	TargetID         string `json:"targetId"`
	Type             string `json:"type"`
	Title            string `json:"title"`
	URL              string `json:"url"`
	Attached         bool   `json:"attached"`
	OpenerID         string `json:"openerId"`
	BrowserContextID string `json:"browserContextId"`
}

type eventTargetCreated struct {
	TargetInfo targetInfo `json:"targetInfo"`
}

type setDiscoverTargetsParams struct {
	Discover bool `json:"discover"`
}

type closeTargetParams struct {
	TargetID string `json:"targetId"`
}

type closeTargetReturns struct {
	Success bool `json:"success"`
}

type requestPattern struct {
	URLPattern        string `json:"urlPattern,omitempty"`
	ResourceType      string `json:"resourceType,omitempty"`
	InterceptionStage string `json:"interceptionStage,omitempty"`
}

type setRequestInterceptionParams struct {
	Patterns []requestPattern `json:"patterns"`
}

type authChallenge struct {
	Source string `json:"source,omitempty"`
	Origin string `json:"origin"`
	Scheme string `json:"scheme"`
	Realm  string `json:"realm"`
}

type eventRequestIntercepted struct {
	InterceptionID      string            `json:"interceptionId"`
	Request             *Request          `json:"request"`
	FrameID             string            `json:"frameId"`
	ResourceType        string            `json:"resourceType"`
	IsNavigationRequest bool              `json:"isNavigationRequest"`
	IsDownload          bool              `json:"isDownload,omitempty"`
	RedirectURL         string            `json:"redirectUrl,omitempty"`
	AuthChallenge       *authChallenge    `json:"authChallenge,omitempty"`
	ResponseErrorReason string            `json:"responseErrorReason,omitempty"`
	ResponseStatusCode  int               `json:"responseStatusCode,omitempty"`
	ResponseHeaders     map[string]string `json:"responseHeaders,omitempty"`
}

type authChallengeResponse struct {
	Response string `json:"response"`
	Username string `json:"username,omitempty"`
	Password string `json:"password,omitempty"`
}

type continueInterceptedRequestParams struct {
	InterceptionID        string                 `json:"interceptionId"`
	ErrorReason           string                 `json:"errorReason,omitempty"`
	RawResponse           string                 `json:"rawResponse,omitempty"`
	URL                   string                 `json:"url,omitempty"`
	Method                string                 `json:"method,omitempty"`
	PostData              string                 `json:"postData,omitempty"`
	Headers               map[string]string      `json:"headers,omitempty"`
	AuthChallengeResponse *authChallengeResponse `json:"authChallengeResponse,omitempty"`
}

type getBoxModelParams struct {
	NodeID        int64  `json:"nodeId,omitempty"`
	BackendNodeID int64  `json:"backendNodeId,omitempty"`
	ObjectID      string `json:"objectId,omitempty"`
}

type quad []float64

type boxModel struct {
	Content quad  `json:"content"`
	Padding quad  `json:"padding"`
	Border  quad  `json:"Border"`
	Margin  quad  `json:"margin"`
	Width   int64 `json:"width"`
	Height  int64 `json:"height"`
}

func (bm boxModel) getCenterCoordinate() (x, y int64) {
	x = int64(bm.Border[0]) + bm.Width/2
	y = int64(bm.Border[1]) + bm.Height/2
	return
}

type getBoxModelReturns struct {
	Model boxModel `json:"model"`
}

type dispatchMouseEventParams struct {
	Type       string `json:"type"`
	X          int64  `json:"x"`
	Y          int64  `json:"y"`
	Modifiers  int64  `json:"modifiers,omitempty"`
	Button     string `json:"button,omitempty"`
	ClickCount int64  `json:"clickCount,omitempty"`
}

type querySelectorAllParams struct {
	NodeID   int64  `json:"nodeId"`
	Selector string `json:"selector"`
}

type querySelectorAllReturns struct {
	NodeIDs []int64 `json:"nodeIds"`
}
