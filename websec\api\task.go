package api

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type tasksResponse struct {
	Tasks []*schema.Task `json:"tasks"`
	Page  int            `json:"page,omitempty"`
	Size  int            `json:"size,omitempty"`
	Total int            `json:"total,omitempty"`
}

func (api *API) taskStatusHandler(rw http.ResponseWriter, req *http.Request) {
	query := req.URL.Query()
	page := query.Get("page")
	size := query.Get("size")
	if size == "" {
		size = query.Get("per_page")
	}
	host := query.Get("host")
	assetID := query.Get("id")
	status := query.Get("status")
	sort := query.Get("sort")
	sortOpts := api.genSortOpts(sort)

	pageNum, sizeNum, err := api.checkPageAndSize(page, size)
	if err != nil {
		log.Errorln(err)
		api.writeResponse(rw, newFailResponse("page or size error."))
		return
	}

	statusNum := -1
	if status != "" {
		statusNum, err = strconv.Atoi(status)
		if err != nil {
			log.Errorln(err)
			api.writeResponse(rw, newFailResponse("status error."))
			return
		}
	}

	if host != "" {
		response := api.queryTaskByHost(host, statusNum, sortOpts)
		api.writeResponse(rw, response)
		return
	}

	if assetID != "" {
		response := api.queryTaskByID(assetID)
		api.writeResponse(rw, response)
		return
	}

	response := api.queryTaskPage(pageNum, sizeNum, statusNum, sortOpts)
	api.writeResponse(rw, response)
	return
}

func (api *API) genSortOpts(sort string) *options.FindOptions {
	if sort == "" {
		return options.Find().SetSort(bson.M{"_id": -1})
	}

	var order int
	var keyStr string
	if strings.HasPrefix(sort, "-") {
		order = -1
		keyStr = sort[1:len(sort)]
	} else {
		order = 1
		keyStr = sort
	}

	return options.Find().SetSort(bson.M{keyStr: order})
}

func (api *API) checkPageAndSize(page, size string) (int, int, error) {
	if page == "" {
		page = "1"
	}

	if size == "" {
		size = "10"
	}

	pageNum, err := strconv.Atoi(page)
	if err != nil {
		return 0, 0, err
	}

	sizeNum, err := strconv.Atoi(size)
	if err != nil {
		return 0, 0, err
	}
	return pageNum, sizeNum, nil
}

func (api *API) queryTaskByHost(host string, statusNum int, opts *options.FindOptions) *schema.Response {
	mdb := api.options.DBConnection.GetMongoDatabase()
	query := bson.M{
		"host": bson.M{"$regex": host},
	}
	if statusNum != -1 {
		query["status"] = statusNum
	}

	tasks, err := api.queryTasks(mdb, query, opts)
	if err != nil {
		return newFailResponse("query tasks error")
	}

	data := tasksResponse{
		Tasks: tasks,
	}
	return newSuccessResponse(data)
}

func (api *API) queryTaskByID(assetID string) *schema.Response {
	mdb := api.options.DBConnection.GetMongoDatabase()
	objectID, err := primitive.ObjectIDFromHex(assetID)
	if err != nil {
		log.Errorln("object id error:", err)
		return newFailResponse("object id error")
	}

	query := bson.M{
		"_id": objectID,
	}

	tasks, err := api.queryTasks(mdb, query, nil)
	if err != nil {
		return newFailResponse("query tasks error")
	}

	data := tasksResponse{
		Tasks: tasks,
	}
	return newSuccessResponse(data)
}

func (api *API) queryTaskPage(pageNum int, sizeNum int, statusNum int, opts *options.FindOptions) *schema.Response {
	mdb := api.options.DBConnection.GetMongoDatabase()
	query := bson.M{}
	if statusNum != -1 {
		query["status"] = statusNum
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	skipNum := int64((pageNum - 1) * sizeNum)
	if opts != nil {
		opts = opts.SetSkip(skipNum).SetLimit(int64(sizeNum))
	} else {
		opts = options.Find().SetSkip(skipNum).SetLimit(int64(sizeNum))
	}

	tasks, err := api.queryTasks(mdb, query, opts)
	if err != nil {
		return newFailResponse("query tasks error")
	}

	total, err := mdb.Collection(consts.CollectionTasks).EstimatedDocumentCount(ctx)
	if err != nil {
		log.Errorln("failed to get tasks count:", err)
		return newFailResponse("failed to get tasks count")
	}

	data := tasksResponse{
		Tasks: tasks,
		Page:  pageNum,
		Size:  sizeNum,
		Total: int(total),
	}
	return newSuccessResponse(data)
}

func (api *API) queryTasks(mdb *mongo.Database, query bson.M, opts *options.FindOptions) ([]*schema.Task, error) {
	tasks := make([]*schema.Task, 0, 10)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cursor, err := mdb.Collection(consts.CollectionTasks).Find(ctx, query, opts)
	if err != nil {
		log.Errorln("failed to find task:", err)
		return tasks, err
	}

	for cursor.Next(ctx) {
		var task schema.Task
		err := cursor.Decode(&task)
		if err != nil {
			log.Errorln("failed to decode task:", err)
			continue
		}
		tasks = append(tasks, &task)
	}
	return tasks, nil
}

type vulSmartScanData struct {
	ID string `json:"id"`
}

func (api *API) vulSmartScanHandler(rw http.ResponseWriter, req *http.Request) {
	var err error
	reqData := new(vulSmartScanData)
	err = json.Unmarshal(getHttpBody(req), reqData)
	if err != nil {
		log.Errorln("get request body error: ", err)
		api.writeResponse(rw, newFailResponse("get request body error"))
		return
	}

	asset, err := api.getAsset(reqData.ID)
	if err != nil {
		log.Errorf("get asset error: %v %v ", err, reqData.ID)
		api.writeResponse(rw, newFailResponse("get asset error"))
		return
	}

	task := schema.NewSmartScanTask(asset)
	existTask := api.checkSmartScanTaskExists(asset.Host)

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var data vulSmartScanData
	if existTask != nil {
		data = vulSmartScanData{
			ID: existTask.ID.Hex(),
		}
		if existTask.Status != 0 && existTask.Status != 21 {
			_, err = mdb.Collection(consts.CollectionTasks).UpdateOne(
				ctx, bson.M{"_id": existTask.ID}, bson.M{"$set": task})
		}
	} else {
		var result *mongo.InsertOneResult
		result, err = mdb.Collection(consts.CollectionTasks).InsertOne(ctx, task)
		data = vulSmartScanData{
			ID: result.InsertedID.(primitive.ObjectID).Hex(),
		}
	}

	if err != nil {
		log.Errorln(err)
		api.writeResponse(rw, newFailResponse("smart scan task add error"))
	} else {
		api.writeResponse(rw, newSuccessResponse(data))
	}
}

func (api *API) checkSmartScanTaskExists(host string) *schema.Task {
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var task schema.Task
	err := mdb.Collection(consts.CollectionTasks).FindOne(
		ctx,
		bson.M{
			"host":         host,
			"schedule.tag": consts.TaskTagScanSmart,
		},
	).Decode(&task)

	if err != nil {
		log.Errorf("checkSmartScanTaskExists: %v, %v", host, err)
	}

	if task.Host == "" {
		return nil
	}
	return &task
}
