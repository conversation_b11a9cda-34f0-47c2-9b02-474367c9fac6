package utils

import (
	"bytes"
	"sync"
	"time"

	"github.com/valyala/fasthttp"
)

type MyCookieJar struct {
	entries map[string]*fasthttp.Cookie
	mu      sync.RWMutex
}

func NewMyCookieJar() *MyCookieJar {
	return &MyCookieJar{
		entries: map[string]*fasthttp.Cookie{},
		mu:      sync.RWMutex{},
	}
}

func (jar *MyCookieJar) GetCookie(key string) (*fasthttp.Cookie, bool) {
	jar.mu.RLock()
	defer jar.mu.RUnlock()

	cookie, ok := jar.entries[key]
	return cookie, ok
}

func (jar *MyCookieJar) GetCookies() map[string]*fasthttp.Cookie {
	jar.mu.RLock()
	defer jar.mu.RUnlock()

	return jar.entries
}

func (jar *MyCookieJar) SetCookie(key, value string) {
	jar.mu.Lock()
	defer jar.mu.Unlock()

	if cookie, ok := jar.entries[key]; ok {
		if cookie.String() != value {
			cookie.Parse(value)
		}
	} else {
		cookie := fasthttp.AcquireCookie()
		cookie.Parse(value)
		jar.entries[key] = cookie
	}
}

func (jar *MyCookieJar) CopyToRequest(request *fasthttp.Request) {
	jar.mu.RLock()
	defer jar.mu.RUnlock()

	now := time.Now()
	for key, cookie := range jar.entries {
		if cookie.Expire().Before(now) {
			request.Header.SetCookie(key, string(cookie.Value()))
		}
	}
}

func (jar *MyCookieJar) CopyFromResponse(response *fasthttp.Response) {
	jar.mu.Lock()
	defer jar.mu.Unlock()

	response.Header.VisitAllCookie(func(key, value []byte) {
		keyStr := string(key)
		if cookie, ok := jar.entries[keyStr]; ok {
			if !bytes.Equal(cookie.Value(), value) {
				cookie.ParseBytes(value)
			}
		} else {
			cookie = fasthttp.AcquireCookie()
			cookie.ParseBytes(value)
			jar.entries[keyStr] = cookie
		}
	})
}

func (jar *MyCookieJar) Release() {
	jar.mu.Lock()
	defer jar.mu.Unlock()

	for key, cookie := range jar.entries {
		fasthttp.ReleaseCookie(cookie)
		delete(jar.entries, key)
	}
}

func (jar *MyCookieJar) Lock() {
	jar.mu.Lock()
}

func (jar *MyCookieJar) Unlock() {
	jar.mu.Unlock()
}
