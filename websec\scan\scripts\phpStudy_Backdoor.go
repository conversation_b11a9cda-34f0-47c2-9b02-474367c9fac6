package scripts

import (
	"bytes"
	"compress/gzip"
	"io/ioutil"
	"net/http"
	"time"
)

func PHPStudyBackdoor(args *ScriptScanArgs) (*ScriptScanResult, error) {
	baseURL := constructURL(args, "/index.php")
	client := &http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
		Timeout: 15 * time.Second,
	}
	defer client.CloseIdleConnections()

	req, _ := http.NewRequest("GET", baseURL, nil)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.100 Safari/537.36")
	req.Header.Set("accept-charset", "ZWNobygxMTIyMyoyMDExNyk7")
	req.Header.Set("Accept-Encoding", "gzip,deflate")

	resp, err := client.Do(req)
	if err != nil {
		return &invulnerableResult, err
	}
	defer resp.Body.Close()
	reader, err := gzip.NewReader(resp.Body)
	if err != nil {
		return &invulnerableResult, err
	}
	defer reader.Close()
	content, _ := ioutil.ReadAll(reader)

	if bytes.Contains(content, []byte("225773091")) {
		return &ScriptScanResult{
			Vulnerable: true,
			Output:     constructURL(args, "/") + " Site has PHPStudy BackDoor",
			Body:       nil,
		}, nil
	}

	return &invulnerableResult, err
}
func init() {
	registerHandler("PHPStudy_Backdoor.xml", PHPStudyBackdoor)
}
