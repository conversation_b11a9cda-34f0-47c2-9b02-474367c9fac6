package api

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"
	"websec/common/consts"
	"websec/utils"
	"websec/utils/log"

	"github.com/gorilla/mux"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ImageData struct {
	ID     primitive.ObjectID `bson:"_id,omitempty"`
	Data   []byte             `bson:"data"`
	Suffix string             `bson:"suffix"`
	Md5    string             `bson:"md5"`
}

type UploadRequest struct {
	Data   string `json:"data"`
	Suffix string `json:"suffix"`
}

type UploadResponse struct {
	URL string `json:"url"`
}

func (api *API) getImageHandler(rw http.ResponseWriter, req *http.Request) {
	vars := mux.Vars(req)
	id, ok := vars["id"]
	if !ok {
		api.writeResponse(rw, newFailResponse("id param error"))
		return
	}

	fields := strings.Split(id, ".")
	if len(fields) != 2 {
		api.writeResponse(rw, newFailResponse("id param error"))
		return
	}

	objID, err := primitive.ObjectIDFromHex(fields[0])
	if err != nil {
		api.writeResponse(rw, newFailResponse("id param error"))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	doc := new(ImageData)
	err = api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionImageSave).FindOne(ctx,
		bson.M{"_id": objID}).Decode(doc)
	if err != nil {
		log.Errorln(err)
		api.writeResponse(rw, newFailResponse("key not exist"))
		return
	}

	if !strings.HasSuffix(doc.Suffix, fields[1]) {
		api.writeResponse(rw, newFailResponse("suffix not equal"))
		return
	}

	rw.Header().Add("Accept-Ranges", "bytes")
	rw.Header().Add("Content-Length", strconv.Itoa(len(doc.Data)))
	rw.Header().Add("Content-Type", "image/jpeg")
	_, err = rw.Write(doc.Data)
	if err != nil {
		log.Errorln("write error", err)
	}
}

func (api *API) uploadImageHandler(rw http.ResponseWriter, req *http.Request) {
	body := getHttpBody(req)
	reqData := new(UploadRequest)
	err := json.Unmarshal(body, reqData)
	if err != nil {
		log.Errorln(err)
		api.writeResponse(rw, newFailResponse("param error"))
		return
	}

	imageData, err := base64.StdEncoding.DecodeString(reqData.Data)
	if err != nil {
		log.Errorln(err)
		api.writeResponse(rw, newFailResponse("param data error"))
		return
	}

	md5 := utils.Md5(string(imageData))
	doc := new(ImageData)
	var idStr string

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err = api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionImageSave).FindOne(ctx,
		bson.M{"md5": md5}).Decode(doc)
	if err == nil {
		idStr = doc.ID.Hex()
	} else {
		doc.Data = imageData
		doc.Suffix = reqData.Suffix
		doc.Md5 = md5

		res, err := api.options.DBConnection.GetMongoDatabase().Collection(consts.CollectionImageSave).InsertOne(ctx, doc)
		if err != nil {
			log.Errorln(err)
			api.writeResponse(rw, newFailResponse("upload insert failed"))
			return
		}

		idStr = res.InsertedID.(primitive.ObjectID).Hex()
	}

	finalURL := fmt.Sprintf("%s%s", idStr, doc.Suffix)
	log.Infoln("finalURL", finalURL)
	response := &UploadResponse{
		URL: finalURL,
	}
	api.writeResponse(rw, newSuccessResponse(response))
}
