package schema

import (
	"time"
	"websec/common"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type RawSensitiveWordResult = FoundSensitiveWordsDoc

type FinalSensitiveWordResult = FoundSensitiveWordsDoc

type FinalOcrSensitiveWordResult = FoundOcrSensitiveWordDoc

type WordResult struct {
	Word         string `json:"word"`
	IsCustomized bool   `json:"is_customized"`
	Position     int    `json:"position"`
	Context      string `json:"context,omitempty"`
	FromOcr      bool   `json:"from_ocr"`
}

type RawOcrSensitiveWordResult struct {
	Mainframe   string           `json:"mainframe"`
	URL         string           `json:"url"`
	ContentHash string           `json:"content_hash"`
	Results     []*OcrWordResult `json:"results"`
	FoundAt     time.Time        `json:"found_at"`
}

type OcrWordResult struct {
	ImageURL     string        `json:"image_url"`
	ImageURLHash string        `json:"image_url_hash"`
	ImageAddress string        `json:"image_address"`
	Content      string        `json:"content"`
	Results      []*WordResult `json:"results"`
}

type CrawledImageURLS struct {
	AssetID string    `json:"asset_id"`
	JobID   string    `json:"job_id"`
	URL     string    `json:"url"`
	Host    string    `json:"host"`
	FoundAt time.Time `json:"found_at"`
	Images  []string  `json:"images"`
}

type CrawledSwfURLs struct {
	AssetID string    `json:"asset_id"`
	JobID   string    `json:"job_id"`
	URL     string    `json:"url"`
	Host    string    `json:"host"`
	FoundAt time.Time `json:"found_at"`
	Swfs    []string  `json:"swf_url_lst"`
}

type SearchURL struct {
	URL string `json:"url"`
}

type RawContentChangeResult = FoundContentChangeDoc

type FinalContentChangeResult = FoundContentChangeDoc

type RawBlackLinkResult = FoundBlackLinksDoc

type FinalBlackLinkResult = FoundBlackLinksDoc

type RawTrojanResult = FoundTrojanDoc

type FinalTrojanResult = FoundTrojanDoc

type RawPhishingResult = FoundPhishingDoc

type FinalPhishingResult = FoundPhishingDoc

type FoundNewURLs struct {
	URLs []*SiteURL `json:"urls"`
}

type FoundPageArchiveDoc struct {
	Host           string             `json:"host"`
	AssetID        string             `json:"asset_id"`
	JobID          string             `json:"job_id" bson:"job_id"`
	URL            string             `json:"url" bson:"url"`
	URLHash        string             `json:"url_hash" bson:"url_hash"`
	MainFrameURL   string             `json:"main_frame_url"`
	StatusCode     int16              `json:"status_code"`
	Header         common.HttpHeaders `json:"header"`
	Content        []byte             `json:"content,omitempty" bson:"content,omitempty"`
	ContentHash    string             `json:"content_hash" bson:"content_hash"`
	VersionTime    int64              `json:"version_time" bson:"version_time"`
	OldVersionTime int64              `json:"old_version_time"`
}

type SnapshotContentChangeMessage struct {
	ID             string `json:"id"`
	Host           string `json:"host" bson:"host"`
	AssetID        string `json:"asset_id" bson:"asset_id"`
	JobID          string `json:"job_id" bson:"job_id"`
	URL            string `json:"url"`
	URLHash        string `json:"url_hash"`
	OldVersionTime int64  `json:"old_version_time"`
	NewVersionTime int64  `json:"new_version_time"`
}

type FinalSnapshotContentChangeResult struct {
	ID             string `json:"id"`
	Host           string `json:"host" bson:"host"`
	AssetID        string `json:"asset_id" bson:"asset_id"`
	JobID          string `json:"job_id" bson:"job_id"`
	OldSnapshotURL string `json:"old_snapshot"`
	NewSnapshotURL string `json:"new_snapshot"`
	Status         uint8  `json:"status"`
	OldTool        string `json:"oldtool"`
	NewTool        string `json:"newtool"`
	OldJS          bool   `json:"old_js_enabled"`
	NewJS          bool   `json:"new_js_enabled"`
	Err            string `json:"err"`
}

type SnapshotSensitiveWordMessage struct {
	ID          string       `json:"id"`
	Host        string       `json:"host" bson:"host"`
	AssetID     string       `json:"asset_id" bson:"asset_id"`
	JobID       string       `json:"job_id" bson:"job_id"`
	URL         string       `json:"url"`
	URLHash     string       `json:"url_hash"`
	VersionTime int64        `json:"version_time"`
	Words       []WordResult `json:"words"`
	SnapshotID  string       `json:"snapshot_id"`
}

type SnapshotSensitiveWordDoc struct {
	ID          string `json:"id" bson:"id"`
	Host        string `json:"host" bson:"host"`
	AssetID     string `json:"asset_id" bson:"asset_id"`
	JobID       string `json:"job_id" bson:"job_id"`
	SnapshotID  string `json:"snapshot_id" bson:"snapshot_id"`
	SnapshotURL string `json:"snapshot_url" bson:"snapshot_url"`
	Tool        string `json:"tool" bson:"tool"`
	JS          bool   `json:"js_enabled" bson:"js_enabled"`
	Status      uint8  `json:"status" bson:"status"`
	Err         string `json:"err" bson:"err"`
}

type PageArchiveES struct {
	Host           string             `json:"host"`
	AssetID        string             `json:"asset_id" bson:"asset_id"`
	JobID          string             `json:"job_id" bson:"job_id"`
	MainFrameURL   string             `json:"main_frame_url"`
	URL            string             `json:"url"`
	URLHash        string             `json:"url_hash"`
	StatusCode     int16              `json:"status_code"`
	Header         common.HttpHeaders `json:"header"`
	VersionTime    int64              `json:"version_time"`
	OldVersionTime int64              `json:"old_version_time"`
	ContentHash    string             `json:"content_hash"`
}

type PageArchiveHBase struct {
	ID             primitive.ObjectID `bson:"_id,omitempty"`
	RowKey         string             `json:"-" bson:"row_key"`
	Host           string             `json:"host" bson:"host"`
	AssetID        string             `json:"asset_id" bson:"asset_id"`
	JobID          string             `json:"job_id" bson:"job_id"`
	MainFrameURL   string             `json:"main_frame_url" bson:"main_frame_url"`
	StatusCode     int16              `json:"status_code" bson:"status_code"`
	URL            string             `json:"url" bson:"url"`
	URLHash        string             `json:"url_hash" bson:"url_hash"`
	Header         string             `json:"header" bson:"header"`
	OldVersionTime int64              `json:"old_version_time" bson:"old_version_time"`
	VersionTime    int64              `json:"version_time" bson:"version_time"`
	Content        []byte             `json:"content" bson:"content"`
	ContentHash    string             `json:"content_hash" bson:"content_hash"`
}

type WebpageSnapshot struct {
	ID              primitive.ObjectID `bson:"_id,omitempty"`
	RowKey          string             `json:"-" bson:"row_key"`
	Host            string             `json:"host" bson:"host"`
	AssetID         string             `json:"asset_id" bson:"asset_id"`
	JobID           string             `json:"job_id" bson:"job_id"`
	MainFrameURL    string             `json:"main_frame_url" bson:"main_frame_url"`
	StatusCode      int16              `json:"status_code" bson:"status_code"`
	URL             string             `json:"url" bson:"url"`
	URLHash         string             `json:"url_hash" bson:"url_hash"`
	Header          string             `json:"header" bson:"header"`
	LastVersionTime int64              `json:"last_version_time" bson:"last_version_time"`
	VersionTime     int64              `json:"version_time" bson:"version_time"`
	Content         []byte             `json:"content" bson:"content"`
	ContentHash     string             `json:"content_hash" bson:"content_hash"`
}

type TaskStats struct {
	TaskID  string    `json:"task_id"`
	Host    string    `json:"host"`
	AssetID string    `json:"asset_id"`
	RunAt   time.Time `json:"run_at"`
	Stats   taskStats `json:"stats"`
}

type TaskFinishedResult struct {
	TaskID  string `json:"task_id"`
	AssetID string `json:"asset_id"`
	Status  int64  `json:"status"`
	Reason  string `json:"reason"`
}
