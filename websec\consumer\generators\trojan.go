package generators

import (
	"websec/common"
	"websec/common/schema"
	"websec/detect"
)

func getTrojanResult(page *common.Webpage, result *detect.DetectResult) []*schema.RawTrojanResult {
	if len(result.Trojans) == 0 {
		return nil
	}

	results := make([]*schema.RawTrojanResult, len(result.Trojans))

	for index, v := range result.Trojans {
		trojanResults := make([]schema.FoundTrojan, 1)
		trojanResults[0].Confidence = v.Confidence
		trojanResults[0].Evidence = v.Evidence
		trojanResults[0].Info = v.Info
		trojanResults[0].PlatformType = v.PlatformType
		trojanResults[0].ThreatType = v.ThreatType
		trojanResults[0].ThreatEntryType = v.ThreatEntryType
		trojanResults[0].Source = v.Source

		r := &schema.RawTrojanResult{
			AssetID: page.AssetID,
			JobID:   page.JobID,
			Host:    page.Host,
			URL:     page.URL,
			URLHash: page.URLHash(),
			Results: trojanResults,
			FoundAt: page.CrawledAt,
		}
		results[index] = r
	}

	return results
}
