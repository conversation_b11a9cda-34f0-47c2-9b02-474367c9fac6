package scripts

import (
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"time"
)

func SSLCcsInjection(args *ScriptScanArgs) (*ScriptScanResult, error) {
	host := args.Host

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()

	var cmd *exec.Cmd
	var output []byte
	cmd = exec.CommandContext(ctx, "sh", "-c", fmt.Sprintf(
		"`which nmap` -p 443 --script ssl-ccs-injection %v", host))
	output, err := cmd.CombinedOutput()

	if err != nil {
		fmt.Printf("err:%v", err)
		return nil, err
	}

	if bytes.Contains(output, []byte("443/tcp open")) {
		if bytes.Contains(output, []byte("Risk factor: High")) || bytes.Contains(output, []byte("CVE-2014-0224")) {
			return &ScriptScanResult{Vulnerable: true, Output: fmt.Sprintf("http://%v:443", host), Body: output}, nil
		}
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("ssl_ccs_injection.xml", SSLCcsInjection)
}
