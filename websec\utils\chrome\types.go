package chrome

type LayoutViewport struct {
	PageX        int64 `json:"pageX"`        // Horizontal offset relative to the document (CSS pixels).
	PageY        int64 `json:"pageY"`        // Vertical offset relative to the document (CSS pixels).
	ClientWidth  int64 `json:"clientWidth"`  // Width (CSS pixels), excludes scrollbar if present.
	ClientHeight int64 `json:"clientHeight"` // Height (CSS pixels), excludes scrollbar if present.
}

type VisualViewport struct {
	OffsetX      float64 `json:"offsetX"`      // Horizontal offset relative to the layout viewport (CSS pixels).
	OffsetY      float64 `json:"offsetY"`      // Vertical offset relative to the layout viewport (CSS pixels).
	PageX        float64 `json:"pageX"`        // Horizontal offset relative to the document (CSS pixels).
	PageY        float64 `json:"pageY"`        // Vertical offset relative to the document (CSS pixels).
	ClientWidth  float64 `json:"clientWidth"`  // Width (CSS pixels), excludes scrollbar if present.
	ClientHeight float64 `json:"clientHeight"` // Height (CSS pixels), excludes scrollbar if present.
	Scale        float64 `json:"scale"`        // Scale relative to the ideal viewport (size at width=device-width).
}

type Viewport struct {
	X      float64 `json:"x"`      // X offset in CSS pixels.
	Y      float64 `json:"y"`      // Y offset in CSS pixels
	Width  float64 `json:"width"`  // Rectangle width in CSS pixels
	Height float64 `json:"height"` // Rectangle height in CSS pixels
	Scale  float64 `json:"scale"`  // Page scale factor.
}

type Rect struct {
	X      float64 `json:"x"`      // X coordinate
	Y      float64 `json:"y"`      // Y coordinate
	Width  float64 `json:"width"`  // Rectangle width
	Height float64 `json:"height"` // Rectangle height
}

type OrientationType string

type ScreenOrientation struct {
	Type  OrientationType `json:"type"`  // Orientation type.
	Angle int64           `json:"angle"` // Orientation angle.
}

type RGBA struct {
	R int64   `json:"r"`           // The red component, in the [0-255] range.
	G int64   `json:"g"`           // The green component, in the [0-255] range.
	B int64   `json:"b"`           // The blue component, in the [0-255] range.
	A float64 `json:"a,omitempty"` // The alpha component, in the [0-1] range (default: 1).
}
