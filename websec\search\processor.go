package search

import (
	"encoding/json"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"
	"websec/common"
	"websec/common/consts"
	"websec/common/schema"
	"websec/crawl"
	"websec/utils/log"
	"websec/utils/stream"
)

type Options struct {
	Consumer    *stream.Consumer
	Producer    *stream.Producer
	Concurrency int64
	ChromeHost  string
	ChromePort  uint16
	UserAgent   string
}

type Processor struct {
	options    *Options
	consumer   *stream.Consumer
	producer   *stream.Producer
	linksChan  chan *common.Link
	semaphores *sync.Map
}

func NewProcessor(options *Options) (*Processor, error) {
	semaphores := &sync.Map{}
	processor := &Processor{
		options:    options,
		consumer:   options.Consumer,
		producer:   options.Producer,
		linksChan:  make(chan *common.Link, 10),
		semaphores: semaphores,
	}

	return processor, nil
}

func (processor *Processor) Run() {
	processor.producer.Go()
	processor.consumer.SubscribeTopics(processor.Topics())
	processor.consumer.Go()

	setupSignal(processor.Stop)

	var err error

	crawler, err := processor.createCrawler()
	if err != nil {
		log.Errorln(err)
		return
	}

	go processor.watchCrawler(crawler)

	for msg := range processor.consumer.Messages() {
		processor.process(msg)
	}

	close(processor.linksChan)
	processor.producer.Close()
	log.Infoln("search urls crawler grace exit")
}

func (processor *Processor) Stop() {
	processor.consumer.Close()
}

func (processor *Processor) Topics() []string {
	return []string{consts.TopicSearchURLS}
}

func (processor *Processor) process(msg *stream.Message) error {
	topic := msg.Topic

	switch topic {
	case consts.TopicSearchURLS:
		var searchURL schema.SearchURL
		err := json.Unmarshal(msg.Value, &searchURL)
		if err != nil {
			log.Errorln("Unmarshal failed: ", err)
			return err
		}
		link := common.GenLink(searchURL.URL, http.MethodGet, "", "", 1)
		link.NotProcessWebPage = true
		processor.linksChan <- link
	default:
		log.Errorln("unknown message topic: ", topic)
	}
	return nil
}

func (processor *Processor) createCrawler() (crawl.Crawler, error) {
	crawlOptions := &crawl.Options{
		ByChrome:              true,
		UserAgent:             processor.options.UserAgent,
		Semaphores:            processor.semaphores,
		ChromeHost:            processor.options.ChromeHost,
		ChromePort:            processor.options.ChromePort,
		Timeout:               24 * time.Hour,
		Concurrency:           processor.options.Concurrency,
		LinksChan:             processor.linksChan,
		ShouldCrawlFoundLinks: true,
		MaxDepth:              3,
	}

	return crawl.NewCrawler(crawlOptions, nil)
}

func (processor *Processor) watchCrawler(crawler crawl.Crawler) {

	crawlChan, err := crawler.Go()
	if err != nil {
		log.Errorln(err)
		return
	}

	for webPage := range crawlChan {
		if webPage.URL == "" {
			continue
		}

		log.Infof("webPage %s %d", webPage.URL, webPage.Depth)
		processor.producer.Produce(consts.TopicCrawledWebpages, webPage)
	}

	log.Infoln("finish watch crawler.")
}

func setupSignal(closeCb func()) {
	go func() {
		ch := make(chan os.Signal, 1)
		signal.Notify(ch, syscall.SIGINT, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGHUP)
		for si := range ch {
			log.Infoln("receive signal", si)
			switch si {
			case syscall.SIGHUP:
				log.Infoln("signal SIGHUP.")
			case syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT:
				closeCb()
				break
			default:
				log.Errorln("invalid sig", si)
			}
		}
	}()
}
