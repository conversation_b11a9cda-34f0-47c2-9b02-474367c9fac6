package snapshot

import (
	"encoding/json"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/stream"
)

// rollback

func (processor *Processor) processSnapshotSensitiveMessage(msg *stream.Message) error {
	message := new(schema.SnapshotSensitiveWordMessage)
	err := json.Unmarshal(msg.Value, message)
	if err != nil {
		log.Error(err)
		return err
	}

	_, ignored := processor.ignoredHostsMap[message.Host]
	if ignored {
		log.Warnf("ignored sensitive message id %v with host %v", message.ID, message.Host)
		return nil
	}

	log.Debugf("begin sensitive message id %v", message.ID)

	dir, err := CreateTempDir(SensitiveWordPrefix)
	if err != nil {
		processor.handleSensitiveSnapshotError(message, err)
		return err
	}

	page, err := processor.dbConnection.GetHBaseContent(message.AssetID, message.URL, message.URLHash, message.VersionTime)
	if err != nil {
		processor.handleSensitiveSnapshotError(message, err)
		return err
	}

	page, err = highlightObuscateWords(message.Words, page)
	if err != nil {
		processor.handleSensitiveSnapshotError(message, err)
		return err
	}

	page, err = highlightSensitiveWords(message.Words, page)
	if err != nil {
		processor.handleSensitiveSnapshotError(message, err)
		return err
	}

	page = EnsureCharsetUTF8(page)

	page = MakeLinksAbsolute(page, []byte(message.URL))

	filePath, err := CreateFile(page, dir)
	if err != nil {
		processor.handleSensitiveSnapshotError(message, err)
		return err
	}

	chromeMsg := &SensitiveWordChrome{
		ID:         message.ID,
		Host:       message.Host,
		AssetID:    message.AssetID,
		SnapshotID: message.SnapshotID,
		FilePath:   filePath,
	}

	processor.chromeScreenShot.AddSensitiveWordChrome(chromeMsg)
	return nil
}

func (processor *Processor) handleSensitiveSnapshotError(message *schema.SnapshotSensitiveWordMessage, err error) {
	log.Errorf("Failted to process SnapshotSensitiveWordMessage message: %s %v", message.ID, err)
	srt := schema.SnapshotSensitiveWordDoc{
		ID:         message.ID,
		Host:       message.Host,
		AssetID:    message.AssetID,
		SnapshotID: message.SnapshotID,
		Status:     consts.SnapshotError,
	}
	processor.producer.Produce(consts.TopicFinalSnapshotSensitiveWordResults, &srt)
}
