package scripts

import (
	"bytes"
	"fmt"
	"time"
	"websec/utils"

	"github.com/valyala/fasthttp"
)

func DockerRemoteunauthExecVul(args *ScriptScanArgs) (*ScriptScanResult, error) {

	rawurl := fmt.Sprintf("http://%v:2375/version", args.Host)
	request := fasthttp.AcquireRequest()
	response := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.SetRequestURI(rawurl)

	err := httpClient.DoTimeout(request, response, 5*time.Second)
	if err != nil {

		return nil, err
	}
	body, err := utils.GetOriginalBody(response)
	if err != nil {

		return nil, err
	}
	if bytes.Contains(body, []byte("ApiVersion")) {
		return &ScriptScanResult{Vulnerable: true, Output: rawurl, Body: body}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHand<PERSON>("docker_remote_unauth_exec_vul.xml", DockerRemoteunauthExecVul)
}
