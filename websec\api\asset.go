package api

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"net/http"
	"path/filepath"
	"regexp"
	"strings"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/scan"
	"websec/utils/log"
	"websec/utils/semaphore"

	"websec/scan/rules"

	"github.com/gorilla/mux"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	DefaultMaxDepth    int32 = 10000
	DefaultConcurrency       = 5
	DefaultScanPeriod        = 7 * 24 * 3600
	DefaultCrawlPeriod       = 300
	AssetStatusEnable        = 1
	AssetStatusDisable       = 0
	AssetStatusDelete        = -1

	DatetimeFormat    string = "2006-01-02 15:04:05"
	DefaultMaxLinkNum uint64 = 9999999
)

var (
	reScheduleRange = regexp.MustCompile(`\d\d:\d\d`)
)

type assetData struct {
	ID string `json:"id"`
}

type AssetURL struct {
	ID   primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty" codec:"_id,omitempty"`
	URI  string             `json:"uri" bson:"uri" codec:"uri"`
	Host string             `json:"host" bson:"host" codec:"host"`
}

type assetsResponse struct {
	Assets []*schema.Asset `json:"assets"`
	Page   int             `json:"page,omitempty"`
	Size   int             `json:"size,omitempty"`
	Total  int             `json:"total,omitempty"`
}

type assetURLResult struct {
	Name string
	Dir  string
	URLS []string

	Child map[string]*assetURLResult
}

type urlSendResults struct {
	Name string            `json:"name,omitempty"`
	URLS []string          `json:"urls,omitempty"`
	Dirs []*urlSendResults `json:"dirs,omitempty"`
}

func (api *API) assetHandler(rw http.ResponseWriter, req *http.Request) {
	method := req.Method
	switch method {
	case http.MethodGet:
		api.assetGetHandler(rw, req)
	case http.MethodPost:
		api.assetPostHandler(rw, req)
	case http.MethodDelete:
		api.assetDeleteHandler(rw, req)
	case http.MethodPatch:
		api.assetPatchHandler(rw, req)
	default:
		api.writeResponse(rw, newFailResponse("method not allowed"))
	}
}

func (api *API) assetWsdlHandler(rw http.ResponseWriter, req *http.Request) {
	method := req.Method
	switch method {
	case http.MethodGet:
		api.assetWsdlGetHandler(rw, req)
	case http.MethodPost:
		api.assetWsdlPostHandler(rw, req)
	case http.MethodDelete:
		api.assetWsdlDeleteHandler(rw, req)
	case http.MethodPatch:
		api.assetWsdlPatchHandler(rw, req)
	default:
		api.writeResponse(rw, newFailResponse("method not allowed"))
	}
}

func (api *API) assetUrlsHandler(rw http.ResponseWriter, req *http.Request) {
	method := req.Method
	switch method {
	case http.MethodGet:
		api.assetUrlsGetHandler(rw, req)
	default:
		api.writeResponse(rw, newFailResponse("method not allowed"))
	}
}

func (api *API) assetUrlsGetHandler(rw http.ResponseWriter, req *http.Request) {
	vars := mux.Vars(req)
	assetID := vars["id"]
	query := req.URL.Query()
	urlType := query.Get("scan_type")
	if assetID != "" {

		assetUrls, err := api.getAssetUrls(assetID, urlType)
		if err != nil {
			log.Errorln("get asset  urls error: ", err)
			api.writeResponse(rw, newFailResponse("get asset urls error"))
			return
		}
		api.writeResponse(rw, newSuccessResponse(assetUrls))
	} else {

		api.writeResponse(rw, newFailResponse("not set asset id"))
	}
}

func (api *API) assetGetHandler(rw http.ResponseWriter, req *http.Request) {
	vars := mux.Vars(req)
	assetID := vars["id"]
	if assetID != "" {

		asset, err := api.getAsset(assetID)
		if err != nil {
			log.Errorln("get asset error: ", err)
			api.writeResponse(rw, newFailResponse("get asset error"))
			return
		}
		api.writeResponse(rw, newSuccessResponse(asset))
	} else {

		response := api.getAssetList(req)
		api.writeResponse(rw, response)
	}
}

func (api *API) getAssetList(req *http.Request) *schema.Response {
	query := req.URL.Query()
	page := query.Get("page")
	size := query.Get("size")
	if size == "" {
		size = query.Get("per_page")
	}
	host := query.Get("host")
	sort := query.Get("sort")
	sortOpts := api.genSortOpts(sort)

	pageNum, sizeNum, err := api.checkPageAndSize(page, size)
	if err != nil {
		log.Errorln(err)
		return newFailResponse("page or size error.")
	}
	skipNum := int64((pageNum - 1) * sizeNum)
	sortOpts = sortOpts.SetSkip(skipNum).SetLimit(int64(sizeNum))

	queryOpts := bson.M{}
	if host != "" {
		queryOpts["host"] = bson.M{"$regex": host}
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cursor, err := mdb.Collection(consts.CollectionAssets).Find(
		ctx, queryOpts, sortOpts)
	if err != nil {
		log.Errorln(err)
		return newFailResponse("find assets error")
	}

	assets := make([]*schema.Asset, 0, 10)
	for cursor.Next(ctx) {
		var tmpAsset schema.Asset
		err := cursor.Decode(&tmpAsset)
		if err != nil {
			log.Errorln(err)
			continue
		}
		assets = append(assets, &tmpAsset)
	}

	total, err := mdb.Collection(consts.CollectionAssets).EstimatedDocumentCount(ctx)
	if err != nil {
		log.Errorln("failed to get assets count: ", err)
		return newFailResponse("failed to get assets count")
	}

	resp := &assetsResponse{
		Assets: assets,
		Page:   pageNum,
		Size:   sizeNum,
		Total:  int(total),
	}
	return newSuccessResponse(resp)
}

func (api *API) assetPostHandler(rw http.ResponseWriter, req *http.Request) {
	var err error
	asset := new(schema.Asset)
	err = json.Unmarshal(getHttpBody(req), asset)
	if err != nil {
		log.Errorln("get request body error: ", err, req.Method, req.Body)
		api.writeResponse(rw, newFailResponse("get request body error"))
		return
	}

	log.Info("post handler asset", asset)
	err = api.validateAsset(asset)
	if err != nil {
		log.Errorln("validate asset error: ", err)
		api.writeResponse(rw, newFailResponse("validate asset error"))
		return
	}

	asset = api.processAsset(asset)
	assetID, err := api.insertAsset(asset)
	if err != nil {
		log.Errorln("insert asset error: ", err)
		api.writeResponse(rw, newFailResponse("insert asset error"))
		return
	}

	data := assetData{
		ID: assetID,
	}
	api.writeResponse(rw, newSuccessResponse(data))
}

func (api *API) assetPatchHandler(rw http.ResponseWriter, req *http.Request) {
	var err error
	vars := mux.Vars(req)
	assetID := vars["id"]

	objectID, err := primitive.ObjectIDFromHex(assetID)
	if err != nil {
		log.Errorln("convert object id error: ", err)
		api.writeResponse(rw, newFailResponse("convert object id error"))
		return
	}

	existAsset, err := api.getAsset(assetID)
	if err != nil {
		log.Errorln("get asset error: ", err)
		api.writeResponse(rw, newFailResponse("get asset error"))
		return
	}

	asset := map[string]interface{}{}
	err = json.Unmarshal(getHttpBody(req), &asset)
	if err != nil {
		log.Errorln("unmarshal request body error: ", err)
		api.writeResponse(rw, newFailResponse("unmarshal request body error"))
		return
	}

	for key := range asset {
		log.Info("asset key", key)

	}
	updateData := bson.M{}
	if rawName, ok := asset["name"]; ok {
		updateData["name"] = ""
		if name, ok := rawName.(string); ok {
			if name != "null" && name != "none" {
				updateData["name"] = name
			}
		}
	}
	if importantURLS, ok := asset["important_urls"]; ok {
		updateData["important_urls"] = importantURLS.([]interface{})
	}
	if entries, ok := asset["entries"]; ok {
		updateData["entries"] = entries.([]interface{})
	}
	if monitorTypes, ok := asset["monitor_types"]; ok {
		updateData["monitor_types"] = monitorTypes.([]interface{})
	}
	if status, ok := asset["status"]; ok {
		updateData["status"] = int(status.(float64))
	}

	if postOptions, ok := asset["options"]; ok {
		options := postOptions.(map[string]interface{})
		if rawScheduleRanges, ok := options["schedule_ranges"]; ok {
			scheduleRanges := rawScheduleRanges.([]interface{})
			for i := range scheduleRanges {
				sRange := scheduleRanges[i].(map[string]interface{})
				if _, ok := sRange["start"]; !ok {
					log.Errorln("asset schedule ranges start validate error.")
					api.writeResponse(rw, newFailResponse("asset schedule ranges validate error"))
					return
				}
				if _, ok := sRange["end"]; !ok {
					log.Errorln("asset schedule ranges end validate error.")
					api.writeResponse(rw, newFailResponse("asset schedule ranges validate error"))
					return
				}
				if !reScheduleRange.MatchString(sRange["start"].(string)) || !reScheduleRange.MatchString(sRange["end"].(string)) {
					log.Errorln("asset schedule ranges validate error.")
					api.writeResponse(rw, newFailResponse("asset schedule ranges validate error"))
					return
				}

				if _, ok := sRange["concurrency"]; !ok {
					scheduleRanges[i].(map[string]interface{})["concurrency"] = existAsset.Options.Concurrency
				}
			}
			updateData["options.schedule_ranges"] = scheduleRanges
		}
		if quickMonitorHomepage, ok := options["quick_monitor_homepage"]; ok {
			updateData["options.quick_monitor_homepage"] = int64(quickMonitorHomepage.(float64))
		}
		if quickMonitorSecondpage, ok := options["quick_monitor_secondpage"]; ok {
			updateData["options.quick_monitor_secondpage"] = int64(quickMonitorSecondpage.(float64))
		}
		if scheduleTag, ok := options["schedule_tag"]; ok {
			updateData["options.schedule_tag"] = scheduleTag.(string)
		}
		if priority, ok := options["priority"]; ok {
			updateData["options.priority"] = int(priority.(float64))
		}
		if concurrency, ok := options["concurrency"]; ok {
			value := int(concurrency.(float64))
			if value > 10 {
				log.Warnf("asset %v concurrency great than 10, %v", existAsset.Host, value)
			}
			updateData["options.concurrency"] = value
		}
		if maxDepth, ok := options["max_depth"]; ok {
			updateData["max_depth"] = int(maxDepth.(float64))
			updateData["options.max_depth"] = int(maxDepth.(float64))
		}

		if maxLinkNum, ok := options["max_link_num"]; ok {
			updateData["max_link_num"] = int(maxLinkNum.(float64))
			updateData["options.max_link_num"] = int(maxLinkNum.(float64))
		}

		if expiredAt, ok := options["expired_at"]; ok {
			t, err := time.ParseInLocation(time.RFC3339, expiredAt.(string), time.Local)
			if err != nil {
				log.Errorln("expired_at time error: ", err)
				api.writeResponse(rw, newFailResponse("expired_at time error"))
				return
			}
			updateData["options.expired_at"] = t
		}
		if rawPeriods, ok := options["periods"]; ok {
			periods := rawPeriods.(map[string]interface{})
			for k, v := range periods {
				updateData["options.periods."+k] = int(v.(float64))
			}
		}

		if vul, ok := options["vul"]; ok {
			updateData["options.vul"] = vul
		}
		if blackLink, ok := options["black_link"]; ok {
			updateData["options.black_link"] = blackLink
		}
		if rawSensitiveWord, ok := options["sensitive_word"]; ok {
			sensitiveWord := rawSensitiveWord.(map[string]interface{})
			for k, v := range sensitiveWord {
				updateData["options.sensitive_word."+k] = v
			}
		}

		if rawContentChange, ok := options["content_change"]; ok {
			contentChange := rawContentChange.(map[string]interface{})
			if rawCustomizedXpath, ok := contentChange["customized_xpath"]; ok {
				customizedXpath := rawCustomizedXpath.(map[string]interface{})
				newXpath := map[string]interface{}{}
				for k, v := range customizedXpath {
					newKey := base64.URLEncoding.EncodeToString([]byte(k))
					newXpath[newKey] = v
				}
				updateData["options.content_change.customized_xpath"] = newXpath
			}
		}

		if headers, ok := options["headers"]; ok {
			updateData["options.headers"] = headers.(string)
		}

		if scanScheduleAt, ok := options["scan_schedule_at"]; ok {

			t, err := time.ParseInLocation(time.RFC3339, scanScheduleAt.(string), time.Local)
			if err != nil {
				log.Errorln("scan_schedule_at time error: ", err)
				api.writeResponse(rw, newFailResponse("scan_schedule_at time error"))
				return
			}
			updateData["options.scan_schedule_at"] = t
		}

		if crawlScheduleAt, ok := options["crawl_schedule_at"]; ok {

			t, err := time.ParseInLocation(time.RFC3339, crawlScheduleAt.(string), time.Local)
			if err != nil {
				log.Errorln("crawl_schedule_at time error: ", err)
				api.writeResponse(rw, newFailResponse("crawl_schedule_at time error"))
				return
			}
			updateData["options.crawl_schedule_at"] = t
		}
	}

	log.Debugln("asset patch data: ", updateData)
	err = api.delAssetFromRedis(existAsset.Host)
	if err != nil {
		log.Errorf("delete asset form redis error, %v %v", existAsset.Host, err)
		api.writeResponse(rw, newFailResponse("delete asset from redis error"))
		return
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err = mdb.Collection(consts.CollectionAssets).UpdateOne(
		ctx, bson.M{"_id": objectID}, bson.M{"$set": updateData})
	if err != nil {
		log.Errorf("udpate asset error, %v %v", existAsset.Host, err)
		api.writeResponse(rw, newFailResponse("udpate asset error"))
		return
	}
	api.writeResponse(rw, newSuccessResponse("success"))
}

func (api *API) insertAsset(asset *schema.Asset) (string, error) {
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var err error

	var result *mongo.InsertOneResult
	result, err = mdb.Collection(consts.CollectionAssets).InsertOne(ctx, asset)
	if err != nil {
		return "", err
	}
	return result.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (api *API) processAsset(asset *schema.Asset) *schema.Asset {
	customizedXpath := asset.Options.ContentChange.Customized

	if len(customizedXpath) > 0 {
		newXPaths := make(map[string][]string)
		for k, v := range customizedXpath {
			k = base64.URLEncoding.EncodeToString([]byte(k))
			newXPaths[k] = v
		}
		asset.Options.ContentChange.Customized = newXPaths
	}

	if asset.Options.MaxDepth == 0 {
		asset.Options.MaxDepth = DefaultMaxDepth
	}

	if asset.Options.MaxLinkNum == 0 {
		asset.Options.MaxLinkNum = DefaultMaxLinkNum
	}

	if asset.Options.Concurrency == 0 {
		asset.Options.Concurrency = DefaultConcurrency
	}

	if asset.Options.Concurrency > 10 {
		log.Warnln("asset %s concurrency %d greater than 10", asset.Host, asset.Options.Concurrency)
	}

	if asset.Options.ScheduleTag == "" {
		asset.Options.ScheduleTag = "default"
	}

	if asset.Options.Periods.Vul == 0 {
		log.Warnln("asset options.periods.vul == 0", asset.Host)
		asset.Options.Periods.Vul = DefaultScanPeriod
	}
	if asset.Options.Periods.SensitiveWord == 0 {
		log.Warnln("asset options.periods.sensitiveword == 0", asset.Host)
		asset.Options.Periods.SensitiveWord = DefaultCrawlPeriod
	}
	if asset.Options.Periods.BlackLink == 0 {
		log.Warnln("asset options.periods.blacklink == 0", asset.Host)
		asset.Options.Periods.BlackLink = DefaultCrawlPeriod
	}
	if asset.Options.Periods.ContentChange == 0 {
		log.Warnln("asset options.periods.contentchange == 0", asset.Host)
		asset.Options.Periods.ContentChange = DefaultCrawlPeriod
	}

	now := time.Now()
	asset.CreatedAt = now
	asset.UpdatedAt = now

	return asset
}

func (api *API) validateAsset(asset *schema.Asset) error {
	if asset.Host == "" {
		return errors.New("host validator error")
	}

	for _, u := range asset.Entries {
		u = strings.TrimSpace(u)
		if !strings.HasPrefix(u, "http://") && !strings.HasPrefix(u, "https://") {
			return errors.New("entries validate error")
		}
	}

	for _, u := range asset.ImportantURLS {
		u = strings.TrimSpace(u)
		if !strings.HasPrefix(u, "http://") && !strings.HasPrefix(u, "https://") {
			return errors.New("importanturl validate error")
		}
	}

	if len(asset.MonitorTypes) == 0 {
		return errors.New("monitor_types validate error")
	}

	if asset.Status == 0 {
		return errors.New("status validate error")
	}

	if len(asset.Options.ScheduleRanges) > 0 {
		ranges := asset.Options.ScheduleRanges
		for _, sRange := range ranges {
			if !reScheduleRange.MatchString(sRange.Start) || !reScheduleRange.MatchString(sRange.End) {
				return errors.New("schedule ranges validate error")
			}
		}
	}
	return nil
}

func (api *API) assetDeleteHandler(rw http.ResponseWriter, req *http.Request) {
	vars := mux.Vars(req)
	assetID := vars["id"]

	objectID, err := primitive.ObjectIDFromHex(assetID)
	if err != nil {
		log.Errorln("convert object id error: ", err)
		api.writeResponse(rw, newFailResponse("convert object id error"))
		return
	}

	asset := new(schema.Asset)
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err = mdb.Collection(consts.CollectionAssets).FindOne(ctx, bson.M{"_id": objectID}).Decode(asset)
	if err != nil {
		log.Errorln("find asset error: ", err)
		api.writeResponse(rw, newFailResponse("find asset error"))
		return
	}

	if asset.Host == "" {
		log.Errorf("asset not exist %v", assetID)
		api.writeResponse(rw, newFailResponse("asset not exist"))
		return
	}

	err = api.delAssetFromRedis(asset.Host)
	if err != nil {
		log.Errorln("delete asset from redis error: ", err)
		api.writeResponse(rw, newFailResponse("delete asset from redis error"))
		return
	}

	err = api.delToBeScheduleTasks(asset.Host)
	if err != nil {
		log.Errorln("delete tobe schedule tasks error: ", err)
		api.writeResponse(rw, newFailResponse("delete tobe schedule tasks error"))
		return
	}

	err = api.disableAsset(asset.ID)
	if err != nil {
		log.Errorln("disable asset error: ", err)
		api.writeResponse(rw, newFailResponse("disable asset error"))
		return
	}

	api.writeResponse(rw, newSuccessResponse("asset delete success"))
}

func (api *API) delAssetFromRedis(host string) error {
	exists, err := api.options.DBConnection.HExists("assets", host)
	if err != nil {
		return err
	}

	if exists {
		err := api.options.DBConnection.HDel("assets", host)
		if err != nil {
			return err
		}
	}
	return nil
}

func (api *API) delToBeScheduleTasks(host string) error {
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err := mdb.Collection(consts.CollectionTasks).DeleteMany(
		ctx, bson.M{"host": host, "status": 0})
	return err
}

func (api *API) disableAsset(assetID primitive.ObjectID) error {
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err := mdb.Collection(consts.CollectionAssets).UpdateOne(
		ctx, bson.M{"_id": assetID}, bson.M{"$set": bson.M{"status": AssetStatusDisable}})
	return err
}

func (api *API) getAsset(rawID string) (*schema.Asset, error) {
	objectID, err := primitive.ObjectIDFromHex(rawID)
	if err != nil {
		return nil, err
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	asset := new(schema.Asset)

	err = mdb.Collection(consts.CollectionAssets).FindOne(ctx, bson.M{"_id": objectID}).Decode(&asset)
	if err != nil {
		return nil, err
	}

	if asset == nil {
		return nil, errors.New("asset not exists")
	}

	return asset, nil
}

func (api *API) getAssetUrls(rawID, urlType string) ([]*urlSendResults, error) {
	objectID, err := primitive.ObjectIDFromHex(rawID)
	if err != nil {
		return nil, err
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	asset := new(schema.Asset)

	err = mdb.Collection(consts.CollectionAssets).FindOne(ctx, bson.M{"_id": objectID}).Decode(&asset)
	if err != nil {
		return nil, err
	}

	if asset == nil {
		return nil, errors.New("asset not exists")
	}

	ctx, ctxCancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer ctxCancel()

	var searchFIlter bson.M
	assetTree := []*assetURLResult{}

	urls := make(map[string]*assetURLResult)

	if urlType == consts.AllDomain {
		searchFIlter = bson.M{
			"asset_id": rawID,
			"depth":    bson.M{"$lte": asset.Options.MaxDepth},
		}

	} else {
		searchFIlter = bson.M{
			"asset_id": rawID,
			"host":     asset.Host,
			"depth":    bson.M{"$lte": asset.Options.MaxDepth},
		}
	}

	cursor, err := mdb.Collection(consts.CollectionURLs).Find(ctx, searchFIlter)
	if err != nil {
		log.Errorln("failed to fetch finished tasks:", err)
		return nil, errors.New("asset has no urls")
	}

	urlMap := make(map[string]*AssetURL)

	for cursor.Next(ctx) {
		var curUrl = new(AssetURL)
		err := cursor.Decode(curUrl)
		if err != nil {
			log.Errorln("failed to decode:", err)
			continue
		}

		uri := filepath.Join(curUrl.Host, curUrl.URI)
		if _, ok := urlMap[uri]; ok {
			log.Debugln("this url is in urls", uri)
			continue
		}
		urlMap[uri] = curUrl

		if asset.Host == curUrl.Host {
			dir, _ := filepath.Split(curUrl.URI)
			pathList := strings.Split(dir, "/")
			assetTree = updateUrls(urls, pathList, uri, assetTree, false)
		} else {
			pathList := []string{curUrl.Host, curUrl.URI}
			assetTree = append(assetTree)
			assetTree = updateUrls(urls, pathList, uri, assetTree, true)
		}
	}
	cursor.Close(ctx)

	log.Info("assetTree", assetTree)
	results := []*urlSendResults{}
	for i := range assetTree {
		curItem := assetTree[i]
		result := GetDirs(curItem)
		results = append(results, result)
	}

	return results, nil
}

func GetDirs(results *assetURLResult) *urlSendResults {
	dirs := &urlSendResults{
		Name: results.Name,
		URLS: results.URLS,
	}
	for i := range results.Child {
		curDir := GetDirs(results.Child[i])
		dirs.Dirs = append(dirs.Dirs, curDir)
	}

	return dirs
}

func updateUrls(urls map[string]*assetURLResult, pathList []string, uri string,
	tree []*assetURLResult, outer bool) []*assetURLResult {

	var parent *assetURLResult

	log.Info("path list", pathList)
	for i := range pathList {
		curPath := filepath.Join(pathList[:i]...)
		if curPath == "" {
			if !outer {
				curPath = "/"
			} else {
				curPath = "外链"
			}
		}
		if curNode, exists := urls[curPath]; !exists {

			curNode = &assetURLResult{
				Name:  curPath,
				Dir:   curPath,
				URLS:  []string{},
				Child: make(map[string]*assetURLResult),
			}

			log.Info("curPath", curPath)

			if parent == nil {

				tree = append(tree, curNode)

			} else {

				if _, ok := parent.Child[curPath]; !ok {
					parent.Child[curPath] = curNode
				}
			}
			urls[curPath] = curNode

			parent = curNode
		} else {
			parent = curNode
		}
	}
	parent.URLS = append(parent.URLS, uri)
	return tree
}

func (api *API) assetWsdlGetHandler(rw http.ResponseWriter, req *http.Request) {
	vars := mux.Vars(req)
	assetID := vars["id"]
	if assetID != "" {

		asset, err := api.getWsdlAsset(assetID)
		if err != nil {
			log.Errorln("get asset error: ", err)
			api.writeResponse(rw, newFailResponse("get asset error"))
			return
		}
		api.writeResponse(rw, newSuccessResponse(asset))
	} else {

		response := api.getWsdlAssetList(req)
		api.writeResponse(rw, response)
	}
}

func (api *API) getWsdlAsset(rawID string) (*schema.Asset, error) {
	objectID, err := primitive.ObjectIDFromHex(rawID)
	if err != nil {
		return nil, err
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	asset := new(schema.Asset)

	err = mdb.Collection(consts.CollectionWsdlAssets).FindOne(ctx, bson.M{"_id": objectID}).Decode(&asset)
	if err != nil {
		return nil, err
	}

	if asset == nil {
		return nil, errors.New("asset not exists")
	}

	return asset, nil
}

func (api *API) getWsdlAssetList(req *http.Request) *schema.Response {
	query := req.URL.Query()
	page := query.Get("page")
	size := query.Get("size")
	if size == "" {
		size = query.Get("per_page")
	}
	host := query.Get("host")
	sort := query.Get("sort")
	sortOpts := api.genSortOpts(sort)

	pageNum, sizeNum, err := api.checkPageAndSize(page, size)
	if err != nil {
		log.Errorln(err)
		return newFailResponse("page or size error.")
	}
	skipNum := int64((pageNum - 1) * sizeNum)
	sortOpts = sortOpts.SetSkip(skipNum).SetLimit(int64(sizeNum))

	queryOpts := bson.M{}
	if host != "" {
		queryOpts["host"] = bson.M{"$regex": host}
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cursor, err := mdb.Collection(consts.CollectionWsdlAssets).Find(
		ctx, queryOpts, sortOpts)
	if err != nil {
		log.Errorln(err)
		return newFailResponse("find assets error")
	}

	assets := make([]*schema.Asset, 0, 10)
	for cursor.Next(ctx) {
		var tmpAsset schema.Asset
		err := cursor.Decode(&tmpAsset)
		if err != nil {
			log.Errorln(err)
			continue
		}
		assets = append(assets, &tmpAsset)
	}

	total, err := mdb.Collection(consts.CollectionWsdlAssets).EstimatedDocumentCount(ctx)
	if err != nil {
		log.Errorln("failed to get assets count: ", err)
		return newFailResponse("failed to get assets count")
	}

	resp := &assetsResponse{
		Assets: assets,
		Page:   pageNum,
		Size:   sizeNum,
		Total:  int(total),
	}
	return newSuccessResponse(resp)
}

func (api *API) assetWsdlPostHandler(rw http.ResponseWriter, req *http.Request) {
	var err error
	asset := new(schema.Asset)
	err = json.Unmarshal(getHttpBody(req), asset)
	if err != nil {
		log.Errorln("get request body error: ", err)
		api.writeResponse(rw, newFailResponse("get request body error"))
		return
	}

	log.Info("post handler asset", asset)

	asset = api.processAsset(asset)
	assetID, err := api.insertWsdlAsset(asset)
	if err != nil {
		log.Errorln("insert asset error: ", err)
		api.writeResponse(rw, newFailResponse("insert asset error"))
		return
	}

	go api.processWsdl(asset, assetID)
	data := assetData{
		ID: assetID,
	}
	api.writeResponse(rw, newSuccessResponse(data))
}

func (api *API) insertWsdlAsset(asset *schema.Asset) (string, error) {
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var existAsset schema.Asset
	var err error

	_ = mdb.Collection(consts.CollectionWsdlAssets).FindOne(ctx, bson.M{"host": asset.Host}).Decode(&existAsset)

	assetID := ""
	if existAsset.Host != "" {
		assetID = existAsset.ID.Hex()
		if existAsset.Status != AssetStatusEnable {

			_, err = mdb.Collection(consts.CollectionWsdlAssets).UpdateOne(
				ctx, bson.M{"_id": existAsset.ID}, bson.M{"$set": asset})
		}
		return assetID, err
	}

	var result *mongo.InsertOneResult
	result, err = mdb.Collection(consts.CollectionWsdlAssets).InsertOne(ctx, asset)
	if err != nil {
		return assetID, err
	}
	return result.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (api *API) assetWsdlDeleteHandler(rw http.ResponseWriter, req *http.Request) {
	vars := mux.Vars(req)
	assetID := vars["id"]

	objectID, err := primitive.ObjectIDFromHex(assetID)
	if err != nil {
		log.Errorln("convert object id error: ", err)
		api.writeResponse(rw, newFailResponse("convert object id error"))
		return
	}

	asset := new(schema.Asset)
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err = mdb.Collection(consts.CollectionWsdlAssets).FindOne(ctx, bson.M{"_id": objectID}).Decode(asset)
	if err != nil {
		log.Errorln("find asset error: ", err)
		api.writeResponse(rw, newFailResponse("find asset error"))
		return
	}

	if asset.Host == "" {
		log.Errorf("asset not exist %v", assetID)
		api.writeResponse(rw, newFailResponse("asset not exist"))
		return
	}

	err = api.delAssetFromRedis(asset.Host)
	if err != nil {
		log.Errorln("delete asset from redis error: ", err)
		api.writeResponse(rw, newFailResponse("delete asset from redis error"))
		return
	}

	err = api.disableAsset(asset.ID)
	if err != nil {
		log.Errorln("disable asset error: ", err)
		api.writeResponse(rw, newFailResponse("disable asset error"))
		return
	}

	api.writeResponse(rw, newSuccessResponse("asset delete success"))
}

func (api *API) disableWsdlAsset(assetID primitive.ObjectID) error {
	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err := mdb.Collection(consts.CollectionWsdlAssets).UpdateOne(
		ctx, bson.M{"_id": assetID}, bson.M{"$set": bson.M{"status": AssetStatusDisable}})
	return err
}

func (api *API) assetWsdlPatchHandler(rw http.ResponseWriter, req *http.Request) {
	var err error
	vars := mux.Vars(req)
	assetID := vars["id"]

	objectID, err := primitive.ObjectIDFromHex(assetID)
	if err != nil {
		log.Errorln("convert object id error: ", err)
		api.writeResponse(rw, newFailResponse("convert object id error"))
		return
	}

	existAsset, err := api.getWsdlAsset(assetID)
	if err != nil {
		log.Errorln("get asset error: ", err)
		api.writeResponse(rw, newFailResponse("get asset error"))
		return
	}

	asset := map[string]interface{}{}
	err = json.Unmarshal(getHttpBody(req), &asset)
	if err != nil {
		log.Errorln("unmarshal request body error: ", err)
		api.writeResponse(rw, newFailResponse("unmarshal request body error"))
		return
	}

	for key := range asset {
		log.Info("asset key", key)

	}
	updateData := bson.M{}
	if rawName, ok := asset["name"]; ok {
		updateData["name"] = ""
		if name, ok := rawName.(string); ok {
			if name != "null" && name != "none" {
				updateData["name"] = name
			}
		}
	}
	if importantURLS, ok := asset["important_urls"]; ok {
		updateData["important_urls"] = importantURLS.([]interface{})
	}
	if entries, ok := asset["entries"]; ok {
		updateData["entries"] = entries.([]interface{})
	}
	if monitorTypes, ok := asset["monitor_types"]; ok {
		updateData["monitor_types"] = monitorTypes.([]interface{})
	}
	if status, ok := asset["status"]; ok {
		updateData["status"] = int(status.(float64))
	}

	if postOptions, ok := asset["options"]; ok {
		options := postOptions.(map[string]interface{})
		if rawScheduleRanges, ok := options["schedule_ranges"]; ok {
			scheduleRanges := rawScheduleRanges.([]interface{})
			for i := range scheduleRanges {
				sRange := scheduleRanges[i].(map[string]interface{})
				if _, ok := sRange["start"]; !ok {
					log.Errorln("asset schedule ranges start validate error.")
					api.writeResponse(rw, newFailResponse("asset schedule ranges validate error"))
					return
				}
				if _, ok := sRange["end"]; !ok {
					log.Errorln("asset schedule ranges end validate error.")
					api.writeResponse(rw, newFailResponse("asset schedule ranges validate error"))
					return
				}
				if !reScheduleRange.MatchString(sRange["start"].(string)) || !reScheduleRange.MatchString(sRange["end"].(string)) {
					log.Errorln("asset schedule ranges validate error.")
					api.writeResponse(rw, newFailResponse("asset schedule ranges validate error"))
					return
				}

				if _, ok := sRange["concurrency"]; !ok {
					scheduleRanges[i].(map[string]interface{})["concurrency"] = existAsset.Options.Concurrency
				}
			}
			updateData["options.schedule_ranges"] = scheduleRanges
		}
		if quickMonitorHomepage, ok := options["quick_monitor_homepage"]; ok {
			updateData["options.quick_monitor_homepage"] = int64(quickMonitorHomepage.(float64))
		}
		if quickMonitorSecondpage, ok := options["quick_monitor_secondpage"]; ok {
			updateData["options.quick_monitor_secondpage"] = int64(quickMonitorSecondpage.(float64))
		}
		if scheduleTag, ok := options["schedule_tag"]; ok {
			updateData["options.schedule_tag"] = scheduleTag.(string)
		}
		if priority, ok := options["priority"]; ok {
			updateData["options.priority"] = int(priority.(float64))
		}
		if concurrency, ok := options["concurrency"]; ok {
			value := int(concurrency.(float64))
			if value > 10 {
				log.Warnf("asset %v concurrency great than 10, %v", existAsset.Host, value)
			}
			updateData["options.concurrency"] = value
		}
		if maxDepth, ok := options["max_depth"]; ok {
			updateData["max_depth"] = int(maxDepth.(float64))
		}
		if maxLinkNum, ok := options["max_link_num"]; ok {
			updateData["max_link_num"] = int(maxLinkNum.(float64))
		}
		if expiredAt, ok := options["expired_at"]; ok {
			t, err := time.ParseInLocation(time.RFC3339, expiredAt.(string), time.Local)
			if err != nil {
				log.Errorln("expired_at time error: ", err)
				api.writeResponse(rw, newFailResponse("expired_at time error"))
				return
			}
			updateData["options.expired_at"] = t
		}
		if rawPeriods, ok := options["periods"]; ok {
			periods := rawPeriods.(map[string]interface{})
			for k, v := range periods {
				updateData["options.periods."+k] = int(v.(float64))
			}
		}

		if vul, ok := options["vul"]; ok {
			updateData["options.vul"] = vul
		}
		if blackLink, ok := options["black_link"]; ok {
			updateData["options.black_link"] = blackLink
		}
		if rawSensitiveWord, ok := options["sensitive_word"]; ok {
			sensitiveWord := rawSensitiveWord.(map[string]interface{})
			for k, v := range sensitiveWord {
				updateData["options.sensitive_word."+k] = v
			}
		}

		if rawContentChange, ok := options["content_change"]; ok {
			contentChange := rawContentChange.(map[string]interface{})
			if rawCustomizedXpath, ok := contentChange["customized_xpath"]; ok {
				customizedXpath := rawCustomizedXpath.(map[string]interface{})
				newXpath := map[string]interface{}{}
				for k, v := range customizedXpath {
					newKey := base64.URLEncoding.EncodeToString([]byte(k))
					newXpath[newKey] = v
				}
				updateData["options.content_change.customized_xpath"] = newXpath
			}
		}

		if headers, ok := options["headers"]; ok {
			updateData["options.headers"] = headers.(string)
		}

		if scheduleAt, ok := options["schedule_at"]; ok {
			updateData["options.schedule_at"] = scheduleAt.(string)
		}
	}

	log.Debugln("asset patch data: ", updateData["options.max_depth"])
	err = api.delAssetFromRedis(existAsset.Host)
	if err != nil {
		log.Errorf("delete asset form redis error, %v %v", existAsset.Host, err)
		api.writeResponse(rw, newFailResponse("delete asset from redis error"))
		return
	}

	mdb := api.options.DBConnection.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	_, err = mdb.Collection(consts.CollectionWsdlAssets).UpdateOne(
		ctx, bson.M{"_id": objectID}, bson.M{"$set": updateData})
	if err != nil {
		log.Errorf("udpate asset error, %v %v", existAsset.Host, err)
		api.writeResponse(rw, newFailResponse("udpate asset error"))
		return
	}
	api.writeResponse(rw, newSuccessResponse("success"))
}

func (api *API) processWsdl(asset *schema.Asset, assetID string) {

	settings := api.settings
	blueprint, err := rules.LoadBlueprint(settings.Scanner.BlueprintPath, settings.Scanner.VulsPath)
	if err != nil {
		log.Errorln("failed to load blueprint:", err)
		return
	}
	defaultBlueprint := &blueprint

	ctx, _ := context.WithCancel(context.Background())

	scanOption := &scan.Options{
		Sema:      semaphore.NewWeighted(10),
		Blueprint: defaultBlueprint,
		ExpiredAt: time.Now().Add(time.Hour * 12),
		Ctx:       ctx,
	}

	scanner, err := scan.NewScanner(scanOption)
	if err != nil {
		log.Error("create scanner err", err)
		return
	}

	log.Info("prepare to scan:", asset.ID.Hex(), asset.Entries)
	go scanner.GetWsdlVul(asset.Entries)
	scanChan, err := scanner.GetResultChan()
	if err != nil {
		log.Error("GetResultChan err", err)
		return
	}
Watching:
	for {
		timer := time.NewTimer(time.Second * 5)
		select {

		case scanResult, ok := <-scanChan:
			timer.Stop()
			if ok {
				for _, vul := range scanResult.FoundVuls {

					xmlName := vul.Vul.VulXML

					doc := schema.FoundVulDoc{
						Host: asset.Host,
						Scan: schema.FoundVulsScan{
							Affect:      vul.Link.Affect,
							Method:      vul.Link.Method,
							URL:         vul.Link.URL,
							Data:        vul.Link.Data,
							Headers:     vul.Link.Headers,
							Fingerprint: vul.Link.Fingerprint(),
						},
						Vul: schema.FoundVulsVul{
							VulXML:   xmlName,
							Severity: vul.Severity,
							VulURL:   vul.VulURL,
							From:     "暂无相关信息",
						},
						Context: vul.Context,
						FoundAt: time.Now(),
						AssetID: assetID,
					}
					log.Info("vul:", doc)
					api.producer.Produce(consts.TopicFinalVulResults, &doc)
				}

			} else {
				break Watching
			}
		case <-timer.C:
			continue
		}
	}

}
