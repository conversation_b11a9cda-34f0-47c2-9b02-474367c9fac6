package collectors

import (
	"context"
	"sync"
	"websec/common/consts"
	"websec/common/db"
	"websec/consumer/snapshot"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/stream"
)

type Collector struct {
	consumer     *stream.Consumer
	producer     *stream.Producer
	dbConnection *db.DBConnection
	saver        *Saver

	consumeSema   *semaphore.Weighted
	semaWaitGroup sync.WaitGroup

	newURL bool

	screenshotTool *snapshot.ScreenshotTool
}

func (collector *Collector) TopicKafka() []string {
	return []string{
		consts.TopicFinalSensitiveWordResults,
		consts.TopicFinalContentChangeResults,
		consts.TopicFinalBlackLinkResults,
		consts.TopicFinalTrojanResults,
		consts.TopicFinalPhishingResults,
		consts.TopicFinalVulResults,
		consts.TopicFinalSnapshotContentChangeResults,
		consts.TopicFinalSnapshotSensitiveWordResults,
		consts.TopicFinalOcrSensitiveWordResults,
	}
}

func (collector *Collector) TopicMongo() []string {
	return []string{
		consts.TopicFinalNewURLResults,
		consts.TopicTaskStats,
		consts.TopicFinalHotPageArchiveResults,
	}
}

func (collector *Collector) TopicHBase() []string {
	return []string{
		consts.TopicFinalPageArchiveResults,
	}
}

func (collector *Collector) TopicSensitiveWord() []string {
	return []string{
		consts.TopicFinalSensitiveWordResults,
	}
}

func (collector *Collector) Topics() []string {
	return []string{
		consts.TopicFinalSensitiveWordResults,
		consts.TopicFinalContentChangeResults,
		consts.TopicFinalBlackLinkResults,
		consts.TopicFinalTrojanResults,
		consts.TopicFinalPhishingResults,
		consts.TopicFinalVulResults,
		consts.TopicFinalNewURLResults,
		consts.TopicFinalPageArchiveResults,
		consts.TopicFinalSnapshotContentChangeResults,
		consts.TopicFinalSnapshotSensitiveWordResults,
		consts.TopicTaskStats,
		consts.TopicFinalOcrSensitiveWordResults,
		consts.TopicFinalHotPageArchiveResults,
	}
}

func (collector *Collector) Process(msg *stream.Message, sync bool) error {
	defer func() {
		if !sync {
			collector.consumeSema.Release(1)
			collector.semaWaitGroup.Done()
		}
	}()

	var err error

	topic := msg.Topic

	switch topic {
	case consts.TopicFinalSensitiveWordResults:
		err = collector.processSensitiveWord(msg)
	case consts.TopicFinalContentChangeResults:
		err = collector.processContentChange(msg)
	case consts.TopicFinalBlackLinkResults:
		err = collector.processBlackLink(msg)
	case consts.TopicFinalTrojanResults:
		err = collector.processTrojan(msg)
	case consts.TopicFinalPhishingResults:
		err = collector.processPhishing(msg)
	case consts.TopicFinalVulResults:
		err = collector.processVul(msg)
	case consts.TopicFinalNewURLResults:
		err = collector.processNewURL(msg)
	case consts.TopicFinalPageArchiveResults:
		err = collector.processPageArchive(msg)
	case consts.TopicFinalSnapshotContentChangeResults:
		err = collector.processSnapshotContentChange(msg)
	case consts.TopicFinalSnapshotSensitiveWordResults:
		err = collector.processSnapshotSensitiveWord(msg)
	case consts.TopicTaskStats:
		err = collector.processTaskStats(msg)
	case consts.TopicFinalOcrSensitiveWordResults:
		err = collector.processOcrSensitiveWord(msg)
	case consts.TopicFinalHotPageArchiveResults:
		err = collector.processHotPageArchive(msg)
	default:
		log.Errorln("unknown message topic:", topic)
	}
	if err != nil {
		log.Errorln("failed to process message:", msg.Topic, err)
	}
	return err
}

func (collector *Collector) Run(collectorType string) {
	switch collectorType {
	case "kafka":
		collector.consumer.SubscribeTopics(collector.TopicKafka())
		collector.producer.Go()
	case "mongo":
		collector.consumer.SubscribeTopics(collector.TopicMongo())
	case "hbase":
		collector.consumer.SubscribeTopics(collector.TopicHBase())
	case "all":
		collector.consumer.SubscribeTopics(collector.Topics())
		collector.producer.Go()
	default:
		log.Fatalln("collector Type is error", collectorType)
	}

	collector.consumer.Go()
	collector.saver.Go()

	var err error
	for msg := range collector.consumer.Messages() {
		if err = collector.consumeSema.Acquire(context.TODO(), 1); err == nil {
			collector.semaWaitGroup.Add(1)
			go collector.Process(msg, false)
		} else {
			collector.Process(msg, true)
		}
	}

	collector.semaWaitGroup.Wait()
	collector.producer.Close()
	collector.saver.Close()
	collector.dbConnection.CloseThriftClient()
	log.Infoln("Collector Grace Exit")
}

func (collector *Collector) Stop() {
	collector.consumer.Close()
}

func (collector *Collector) DBConnection() *db.DBConnection {
	return collector.dbConnection
}

func NewCollector(consumer *stream.Consumer, producer *stream.Producer, options ...OptionFn) (*Collector, error) {
	var err error

	collector := &Collector{
		consumer:       consumer,
		producer:       producer,
		consumeSema:    semaphore.NewWeighted(20),
		screenshotTool: snapshot.NewScreenshotTool("127.0.0.1:9888"),
	}

	for _, opt := range options {
		err = opt(collector)
		if err != nil {
			return nil, err
		}
	}

	if collector.dbConnection != nil {
		collector.screenshotTool.SetDBConnection(collector.dbConnection)
	}

	return collector, nil
}
