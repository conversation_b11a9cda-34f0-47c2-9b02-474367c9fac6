package detect

import (
	"bytes"
	"context"
	"os/exec"
	"path"
	"strings"
	"time"
	"websec/utils/log"
	"websec/utils/semaphore"
)

type SuspiciousContent struct {
	Title   string
	Matches []string
	Content string
}

type JSUnpackSection struct {
	Title string
	Lines []string
}

type JSUnpackResult struct {
	Output       []byte
	Maliciouses  []SuspiciousContent
	Suspiciouses []SuspiciousContent
}

var (
	jsunpackSema        = semaphore.NewWeighted(8)
	jsunpackSemaContext = context.Background()
)

func parseJSUnpackOutput(output []byte) []*JSUnpackSection {

	result := make([]*JSUnpackSection, 0, 2)

	lines := bytes.Split(output, []byte("\n"))
	for i := 0; i < len(lines); i++ {
		line := lines[i]
		if bytes.HasPrefix(line, []byte("[")) {
			section := JSUnpackSection{
				Title: string(line),
				Lines: []string{},
			}
			for i < len(lines)-1 {
				nextLine := lines[i+1]
				if bytes.HasPrefix(nextLine, []byte("\t")) {
					nextLine = bytes.Trim(nextLine, "\t ")
					if len(nextLine) > 0 {
						section.Lines = append(section.Lines, string(nextLine))
					}
					i++
				} else {
					break
				}
			}
			result = append(result, &section)
		}
	}
	return result
}

func JSUnpackDetect(jsunpackPath string, URL string) (*JSUnpackResult, error) {
	err := jsunpackSema.Acquire(jsunpackSemaContext, 1)
	if err != nil {
		log.Errorln("failed to acquire jsunpackSema for:", URL)
		return nil, err
	}
	defer jsunpackSema.Release(1)

	python := path.Join(jsunpackPath, "env/bin/python")
	jsunpackFile := path.Join(jsunpackPath, "jsunpackn.py")
	configFile := path.Join(jsunpackPath, "options.config")

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, python, jsunpackFile, "-c", configFile, "-u", URL)
	cmd.Dir = jsunpackPath
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, err
	}

	result := JSUnpackResult{}

	sections := parseJSUnpackOutput(output)
	for _, section := range sections {

		matches := []string{}
		for _, line := range section.Lines {
			if strings.HasPrefix(line, "suspicious:") || strings.HasPrefix(line, "malicious:") {
				matches = append(matches, line)
			}
		}
		content := SuspiciousContent{
			Title:   section.Title,
			Matches: matches,
			Content: strings.Join(section.Lines, "\n"),
		}

		if strings.HasPrefix(section.Title, "[malicious") {
			result.Maliciouses = append(result.Maliciouses, content)
		} else if strings.HasPrefix(section.Title, "[suspicious") {
			result.Suspiciouses = append(result.Suspiciouses, content)
		}
	}
	return &result, nil
}
