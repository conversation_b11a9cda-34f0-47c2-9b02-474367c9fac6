package tuchuang

import (
	"errors"
	"io/ioutil"
	"path"
	"path/filepath"
)

type LocalFSBucket struct {
	Dir    string // path dir for local fs
	Prefix string
}

func NewLocalFSBucket(dir string, defaultPrefix string) *LocalFSBucket {
	if len(dir) == 0 {
		dir = "/home/<USER>"
	}
	return &LocalFSBucket{
		Dir:    dir,
		Prefix: defaultPrefix,
	}
}

func (b *LocalFSBucket) UploadImg(imgData []byte, ext string, prefix string) (string, error) {
	if len(imgData) == 0 {
		return "", errors.New("imgData len == 0")
	}

	if len(prefix) == 0 {
		prefix = b.Prefix
	}

	imageName := sha1sum(imgData) + ext
	path := path.Join(b.Dir, imageName)
	err := ioutil.WriteFile(path, imgData, 0644)
	if err != nil {
		return "", err
	}

	return prefix + imageName, nil

}

func (b *LocalFSBucket) UploadImgFile(imgPath string, prefix string) (string, error) {
	imgData, err := ioutil.ReadFile(imgPath)
	if err != nil {
		return "", err
	}
	ext := filepath.Ext(imgPath)
	return b.UploadImg(imgData, ext, prefix)
}
