package schedule

import (
	"context"
	"fmt"
	"math/rand"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const SCHEDULE_PERIOD = 60

func (scheduler *Scheduler) loadExistedTasks() (map[string][]*schema.Task, error) {
	ctx, ctxCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer ctxCancel()

	cursor, err := scheduler.mongodb.Collection(consts.CollectionTasks).Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	tasks := map[string][]*schema.Task{}

	for cursor.Next(ctx) {
		var result schema.Task
		err := cursor.Decode(&result)
		if err != nil {
			log.Errorln("failed to decode:", err)
			continue
		}
		tasks[result.Host] = append(tasks[result.Host], &result)
	}
	if err = cursor.Err(); err != nil {
		return nil, err
	}
	return tasks, nil
}

func (scheduler *Scheduler) generateTaskForNextPeriod(currentTask *schema.Task) *schema.Task {
	if currentTask.Schedule.Tag == consts.TaskTagScanAtOnce {
		return nil
	}

	if currentTask.Schedule.Period == 0 {
		log.Warnln("generateTask Period==0", currentTask)
		return nil
	}

	asset, ok := scheduler.assets[currentTask.AssetID.Hex()]
	if !ok {
		log.Warnln("maybe this asset is expired:", currentTask.ID)
		return nil
	}

	var newTask *schema.Task
	allTasks := scheduler.generateTasksForAsset(asset, nil)
	for _, task := range allTasks {
		if task.Schedule.Tag == currentTask.Schedule.Tag {
			now := time.Now().UTC()

			if task.Type == consts.TaskTypeScan {

				log.Debug("task originalPlan ", currentTask.ID, currentTask.Host, currentTask.Schedule.OriginalPlan)
				if currentTask.Schedule.OriginalPlan.IsZero() {

					currentTask.Schedule.OriginalPlan = now
					log.Debug("task originalPlan iszero", currentTask.ID, currentTask.Host, currentTask.Schedule.OriginalPlan)
				}

				switch currentTask.FinishedStatus {
				case consts.TaskResultPaused:
					if now.Sub(currentTask.Schedule.OriginalPlan) >= time.Second*time.Duration(task.Schedule.Period) {

						task.Schedule.OriginalPlan = currentTask.Schedule.OriginalPlan.Add(time.Second * time.Duration(task.Schedule.Period))
						task.Schedule.Plan = task.Schedule.OriginalPlan
						log.Warnln("task not finished in one period:", currentTask.ID, currentTask.Host, currentTask.Schedule.OriginalPlan)
					} else {
						if len(asset.Options.ScheduleRanges) > 0 {

							mergtime := asset.Options.ScheduleRanges.Merge()
							log.Info("merge time", mergtime, mergtime.ToTimeRange(now), mergtime.ToTimeRange(now).StartTime(now))

							task.Schedule.Plan = asset.Options.ScheduleRanges.Merge().ToTimeRange(now).StartTime(now)
							task.Track.LastTaskID = currentTask.ID
							task.Schedule.OriginalPlan = currentTask.Schedule.OriginalPlan
							log.Debug("task have scheduleranges ", task.Host, task.Schedule.Plan)
						} else {
							task.Schedule.OriginalPlan = currentTask.Schedule.OriginalPlan.Add(time.Second * time.Duration(task.Schedule.Period))
							task.Schedule.Plan = task.Schedule.OriginalPlan
							log.Debug("task not scheduleranges ", task.Host, task.Schedule.Plan)
						}
					}
				case consts.TaskResultCanceled:
					task.Schedule.OriginalPlan = currentTask.Schedule.OriginalPlan
					task.Track.LastTaskID = currentTask.ID
					task.Schedule.Plan = now
				}
			} else {
				plan := currentTask.Schedule.Plan.Add(time.Second * time.Duration(task.Schedule.Period))
				if plan.Before(now) {
					plan = now
					log.Warnln("task was not scheduled on time:", currentTask.ID, currentTask.Host)
				}
				task.Schedule.Plan = plan
			}

			if task.Schedule.Plan.After(asset.Options.ExpiredAt) {
				log.Warnln("next plan is after expired date:", currentTask.Host, &task.Schedule.Plan, asset.Options.ExpiredAt)
			}

			log.Debug("generate task success", task.Host, task.Type, task.Schedule.Plan)
			newTask = task
			break
		}
	}
	return newTask
}

func (scheduler *Scheduler) generateTasksVul(asset *schema.Asset) []*schema.Task {
	task := schema.NewTask(asset, consts.TaskTypeCrawl)
	task.Schedule.Period = asset.Options.Periods.Vul

	task.Schedule.Plan = time.Now().Add(time.Minute * 10)
	task.Schedule.Priority = asset.Options.Priority
	task.Schedule.Tag = consts.TaskTagCrawlAll
	task.Crawl.MaxDepth = asset.Options.MaxDepth
	task.Crawl.MaxLinkNum = asset.Options.MaxLinkNum
	task.Crawl.Timeout = asset.Options.Periods.Vul
	task.Crawl.FilterReg = asset.Options.Vul.Specific.FilterReg

	task.ExternalScanDepth = asset.Options.ExternalScanPolicy.ScanDepth

	scanTask := schema.NewTask(asset, consts.TaskTypeScan)
	scanTask.Scan.Timeout = asset.Options.Periods.Vul
	scanTask.Schedule.Period = asset.Options.Periods.Vul
	scanTask.Crawl.MaxDepth = asset.Options.MaxDepth

	scanTask.Scan.SpecificXMLs = asset.Options.Vul.Specific.XMLs
	scanTask.Scan.FilterReg = asset.Options.Vul.Specific.FilterReg

	if len(asset.Options.ScheduleRanges) > 0 && asset.Options.ScheduleRanges[0].Start != "08:00" && asset.Options.ScheduleRanges[0].End != "07:59" { // 随机延后，随机的范围从爬虫任务之后 2 小时至 6天

		startTime := asset.Options.ScheduleRanges.Merge().ToTimeRange(task.Schedule.Plan).StartTime(task.Schedule.Plan)
		scanTask.Schedule.OriginalPlan = startTime
	} else {
		scanTask.Schedule.OriginalPlan = task.Schedule.Plan.Add(time.Minute * time.Duration(60+rand.Int63n(10)))
	}

	if asset.Options.Headers != "" {

		scanTask.ExtraOptions.Headers = asset.Options.Headers
		task.ExtraOptions.Headers = asset.Options.Headers
	}

	scanTask.Schedule.Plan = scanTask.Schedule.OriginalPlan
	scanTask.Schedule.Priority = asset.Options.Priority
	scanTask.Schedule.Tag = consts.TaskTagScanAll
	scanTask.ExternalScanDepth = asset.Options.ExternalScanPolicy.ScanDepth
	return []*schema.Task{task, scanTask}
}

func (scheduler *Scheduler) generateTasksBlackLink(asset *schema.Asset) []*schema.Task {
	task := schema.NewTask(asset, consts.TaskTypeCrawl)
	task.Schedule.Priority = asset.Options.Priority
	task.Schedule.Period = SCHEDULE_PERIOD

	task.Schedule.Plan = time.Now().Add(time.Second * 5)
	task.Schedule.Tag = consts.TaskTagCrawlActive
	task.Crawl.MaxDepth = asset.Options.MaxDepth
	task.Crawl.Timeout = 10000

	task.Crawl.FilterReg = asset.Options.Vul.Specific.FilterReg
	if asset.Options.Headers != "" {
		task.ExtraOptions.Headers = asset.Options.Headers
	}

	task.ExternalScanDepth = asset.Options.ExternalScanPolicy.ScanDepth

	return []*schema.Task{task}
}

func (scheduler *Scheduler) generateTasksSensitiveWord(asset *schema.Asset) []*schema.Task {
	task := schema.NewTask(asset, consts.TaskTypeCrawl)
	task.Schedule.Priority = asset.Options.Priority
	task.Schedule.Period = SCHEDULE_PERIOD
	task.Schedule.Plan = time.Now().Add(time.Second * 5)
	task.Schedule.Tag = consts.TaskTagCrawlActive
	task.Crawl.MaxDepth = asset.Options.MaxDepth
	task.Crawl.MaxLinkNum = asset.Options.MaxLinkNum
	task.Crawl.Timeout = 10000
	task.Crawl.FilterReg = asset.Options.Vul.Specific.FilterReg

	if asset.Options.Headers != "" {
		task.ExtraOptions.Headers = asset.Options.Headers
	}

	task.ExternalScanDepth = asset.Options.ExternalScanPolicy.ScanDepth

	return []*schema.Task{task}
}

func (scheduler *Scheduler) generateTasksContentChange(asset *schema.Asset) []*schema.Task {
	task := schema.NewTask(asset, consts.TaskTypeCrawl)
	task.Schedule.Priority = asset.Options.Priority
	task.Schedule.Period = SCHEDULE_PERIOD

	task.Schedule.Plan = time.Now().Add(time.Second * 5)
	task.Schedule.Tag = consts.TaskTagCrawlActive
	task.Crawl.MaxDepth = asset.Options.MaxDepth
	task.Crawl.Timeout = 10000

	if asset.Options.Headers != "" {
		task.ExtraOptions.Headers = asset.Options.Headers
	}

	task.ExternalScanDepth = asset.Options.ExternalScanPolicy.ScanDepth

	return []*schema.Task{task}
}

func (scheduler *Scheduler) generateTaskAtOnceScan(asset *schema.Asset) *schema.Task {
	task := schema.NewTask(asset, consts.TaskTypeScan)

	task.Schedule.Priority = 10
	task.Schedule.Period = 0
	task.Schedule.Plan = time.Now()
	task.Schedule.Tag = consts.TaskTagScanAtOnce
	task.Scan.Timeout = asset.Options.Periods.Vul

	task.Scan.FilterReg = asset.Options.Vul.Specific.FilterReg

	if asset.Options.Headers != "" {
		task.ExtraOptions.Headers = asset.Options.Headers
	}

	task.ExternalScanDepth = asset.Options.ExternalScanPolicy.ScanDepth

	return task
}

func (scheduler *Scheduler) generateTasksForAsset(asset *schema.Asset, existedTasks []*schema.Task) []*schema.Task {
	newTasks := []*schema.Task{}

	for _, typ := range asset.MonitorTypes {
		switch typ {
		case consts.MonitorTypeVul:
			newTasks = append(newTasks, scheduler.generateTasksVul(asset)...)
		case consts.MonitorTypeBlackLink:
			newTasks = append(newTasks, scheduler.generateTasksBlackLink(asset)...)
		case consts.MonitorTypeSensitiveWord:
			newTasks = append(newTasks, scheduler.generateTasksSensitiveWord(asset)...)
		case consts.MonitorTypeContentChange:
			newTasks = append(newTasks, scheduler.generateTasksContentChange(asset)...)
		case consts.MonitorTypeTrojan:
			newTasks = append(newTasks, scheduler.generateTasksContentChange(asset)...)
		default:
			continue
		}
	}

filterNewTasks:
	for _, task := range newTasks {
		for _, existed := range existedTasks {
			if task.Schedule.Tag == existed.Schedule.Tag {
				if existed.Schedule.Period > task.Schedule.Period {
					existed.Schedule.Period = task.Schedule.Period
				}
				continue filterNewTasks
			}
		}
		existedTasks = append(existedTasks, task)
	}
	return existedTasks
}

func (scheduler *Scheduler) insertTasks(tasks []*schema.Task) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	collection := scheduler.mongodb.Collection(consts.CollectionTasks)
	for _, task := range tasks {
		if task.ID != primitive.NilObjectID {
			continue
		}
		res, err := collection.InsertOne(ctx, task)
		if err != nil {
			log.Errorln("failed to insert task:", err)
			continue
		}
		task.ID = res.InsertedID.(primitive.ObjectID)
	}
}

func (scheduler *Scheduler) generateTasks() {
	log.Infoln("generating tasks...")

	assets, err := scheduler.loadAssets()
	if err != nil {
		log.Errorln("failed to load assets:", err)
		return
	}
	log.Infoln("total assets count:", len(assets))
	scheduler.assets = assets

	tasks, err := scheduler.loadExistedTasks()
	if err != nil {
		log.Errorln("failed to load existed tasks:", err)
		return
	}
	log.Infoln("total tasks count:", len(tasks))

	for assetID := range scheduler.assets {
		asset := scheduler.assets[assetID]
		existedTasks, ok := tasks[assetID]
		if !ok {
			existedTasks = []*schema.Task{}
		}
		hostTasks := scheduler.generateTasksForAsset(asset, existedTasks)

		scheduler.insertTasks(hostTasks)
	}
}

func (scheduler *Scheduler) generateCrawlThenScanTask(asset *schema.Asset) *schema.Task {
	task := schema.NewTask(asset, consts.TaskTypeCrawlThenScan)
	task.Schedule.Priority = asset.Options.Priority

	task.Crawl.MaxDepth = asset.Options.MaxDepth
	task.Crawl.MaxLinkNum = asset.Options.MaxLinkNum
	task.Crawl.Timeout = 300 //= asset.Options.Periods.Vul // yes, it's vul, it's crawl then scan.
	task.Crawl.FilterReg = asset.Options.Vul.Specific.FilterReg

	task.Scan.SpecificXMLs = asset.Options.Vul.Specific.XMLs
	task.Scan.FilterReg = asset.Options.Vul.Specific.FilterReg
	task.Scan.Timeout = 3600 // asset.Options.Periods.Vul

	if asset.Options.Headers != "" {
		task.ExtraOptions.Headers = asset.Options.Headers
	}

	task.Schedule.Plan = time.Now().UTC()
	task.Schedule.Tag = consts.TaskTagScanAtOnce

	return task
}

func (scheduler *Scheduler) dispatchCrawlThenScanTask(assetID string) error {
	ctx, ctxCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer ctxCancel()

	assetColl := scheduler.mongodb.Collection(consts.CollectionAssets)
	objectID, err := primitive.ObjectIDFromHex(assetID)
	if err != nil {
		return err
	}

	var asset schema.Asset
	err = assetColl.FindOne(ctx, bson.M{"_id": objectID}).Decode(&asset)
	if err != nil {
		return err
	}

	if len(asset.Host) == 0 {
		return fmt.Errorf("asset not found for id %s", assetID)
	}

	task := scheduler.generateCrawlThenScanTask(&asset)
	err = scheduler.dispatchSema.Acquire(context.TODO(), 1)
	if err != nil {
		return err
	}
	err = scheduler.dispatchTask(task)
	if err != nil {
		return err
	}
	return nil
}
