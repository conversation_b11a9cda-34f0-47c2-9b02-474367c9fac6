package generators

import (
	"context"
	"sync"
	"websec/common/consts"
	"websec/common/db"
	"websec/config"
	"websec/consumer/generators/matcher"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/stream"
)

type Generator struct {
	consumer     *stream.Consumer
	producer     *stream.Producer
	dbConnection *db.DBConnection
	sMatcher     *matcher.SensitiveWordMatcherManager
	bMatcher     *matcher.BlackWordMatcherManager
	settings     config.DetecterConfig

	consumeSem   *semaphore.Weighted
	semWaitGroup sync.WaitGroup

	saveDepth int32
	useHBase  bool
	newURL    bool
	ocrPath   string

	processedURLCounter map[string]ProcessedURLCounter
}

type ProcessedURLCounter struct {
	count uint64
}

func (generator *Generator) Topics() []string {
	return []string{
		consts.TopicCrawledWebpages,
		consts.TopicTaskFinished,
	}
}

func (generator *Generator) Process(msg *stream.Message, sync bool) error {
	defer func() {
		if !sync {
			generator.consumeSem.Release(1)
			generator.semWaitGroup.Done()
		}
	}()

	topic := msg.Topic

	switch topic {
	case consts.TopicCrawledWebpages:
		return generator.processWebPage(msg)
	case consts.TopicTaskFinished:
		return generator.processFinishedTask(msg)
	default:
		log.Errorln("unknown message topic:", topic)
	}
	return nil
}

func (generator *Generator) Run() {
	generator.producer.Go()

	generator.consumer.SubscribeTopics(generator.Topics())
	generator.consumer.Go()

	var err error
	for msg := range generator.consumer.Messages() {
		if err = generator.consumeSem.Acquire(context.TODO(), 1); err == nil {
			generator.semWaitGroup.Add(1)
			go generator.Process(msg, false)
		} else {
			generator.Process(msg, true)
		}
	}
	generator.semWaitGroup.Wait()
	generator.producer.Close()
	generator.dbConnection.CloseThriftClient()
	log.Infoln("Generator Grace Exit")
}

func (generator *Generator) Stop() {
	generator.consumer.Close()
}

func NewGenerator(consumer *stream.Consumer, producer *stream.Producer, options ...OptionFn) (*Generator, error) {
	var err error
	generator := &Generator{
		consumer:            consumer,
		producer:            producer,
		sMatcher:            matcher.NewSensitiveWordMatcherManager(60),
		bMatcher:            matcher.NewBlackWordMatcherManager(60),
		consumeSem:          semaphore.NewWeighted(20),
		processedURLCounter: map[string]ProcessedURLCounter{},
	}

	for _, opt := range options {
		err = opt(generator)
		if err != nil {
			return nil, err
		}
	}
	return generator, nil
}
