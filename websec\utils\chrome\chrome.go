package chrome

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"
	"websec/common"
	"websec/utils/log"

	"github.com/gorilla/websocket"
)

var StaticResourceType = map[string]bool{
	"Document": true, "Stylesheet": true, "Image": true,
	"Media": true, "Font": true, "Script": true,
}

func decode(resp *http.Response, v interface{}) error {
	defer resp.Body.Close()
	err := json.NewDecoder(resp.Body).Decode(v)

	return err
}

func unmarshal(payload []byte) (map[string]interface{}, error) {
	var response map[string]interface{}
	err := json.Unmarshal(payload, &response)
	if err != nil {
		log.Errorln("unmarshal", string(payload), len(payload), err)
	}
	return response, err
}

func convertHeaders(headers map[string]string) common.HttpHeaders {
	ret := make(common.HttpHeaders)
	for key, value := range headers {

		ret.Add(key, value)
	}
	return ret
}

const (
	EventClosed = "Tab.closed"
)

var (
	ErrorNoWsURL = errors.New("no websocket URL")

	MaxReadBufferSize = 0

	MaxWriteBufferSize = 100 * 1024
)

type chromeHeaders = map[string]string

func toChromeHeaders(headers common.HttpHeaders) chromeHeaders {
	cHeaders := chromeHeaders{}
	for k, v := range headers {
		cHeaders[k] = strings.Join(v, " ")
	}
	return cHeaders
}

type MethodReqParams struct {
	ID     int         `json:"id"`
	Method string      `json:"method"`
	Params interface{} `json:"params"`
}

type Params map[string]interface{}

func (p Params) String(k string) string {
	return p[k].(string)
}

func (p Params) Int(k string) int {
	return int(p[k].(float64))
}

func (p Params) Map(k string) map[string]interface{} {
	return p[k].(map[string]interface{})
}

type EventCallback func(params json.RawMessage)

type wsMessage struct {
	ID     int             `json:"id"`
	Result json.RawMessage `json:"result"`

	Method string          `json:"method"`
	Params json.RawMessage `json:"params"`
}

type FetchResponse struct {
	URL          string            `json:"url"`
	EffectiveURL string            `json:"effective_url"`
	StatusCode   int               `json:"status_code"`
	Headers      map[string]string `json:"headers"`
	Content      []byte            `json:"content"`
	Time         int64             `json:"time"`
	CaptureURLS  []string          `json:"capture_urls"`
}

type Tab struct {
	ID               string `json:"id"`
	Type             string `json:"type"`
	Description      string `json:"description"`
	Title            string `json:"title"`
	URL              string `json:"url"`
	WsURL            string `json:"webSocketDebuggerUrl"`
	DevURL           string `json:"devtoolsFrontendUrl"`
	InjectJavaScript *common.InjectJavaScript

	ws         *websocket.Conn
	reqID      int
	finished   bool
	goroutines sync.WaitGroup

	frameID      string
	requestID    string
	requestURL   string
	pageURL      string
	loaded       chan bool
	refreshed    chan bool
	targetIDChan chan string

	networkIdled    bool
	lifecycleEvents sync.Map
	recResponses    map[string]*Response
	sentRequests    map[string][]eventRequestWillBeSent
	abortedURLS     map[string]bool

	requests  chan json.RawMessage
	responses map[int]chan json.RawMessage
	callbacks map[string]EventCallback
	events    chan wsMessage

	mutexCallbacks    *sync.Mutex
	mutexResponses    *sync.Mutex
	mutexRecResponses *sync.Mutex
	mutexAbortedURLS  *sync.Mutex
}

func (t *Tab) Init() {
	t.reqID = 0
	t.frameID = ""
	t.requestURL = ""
	t.pageURL = ""
	t.finished = false
	t.loaded = make(chan bool, 2)
	t.refreshed = make(chan bool, 2)
	t.targetIDChan = make(chan string, 20)
	t.recResponses = map[string]*Response{}
	t.sentRequests = map[string][]eventRequestWillBeSent{}
	t.requests = make(chan json.RawMessage, 30)
	t.responses = map[int]chan json.RawMessage{}
	t.callbacks = map[string]EventCallback{}
	t.events = make(chan wsMessage, 256)
	t.mutexCallbacks = &sync.Mutex{}
	t.mutexResponses = &sync.Mutex{}
	t.mutexRecResponses = &sync.Mutex{}
	t.mutexAbortedURLS = &sync.Mutex{}
	t.lifecycleEvents = sync.Map{}
	t.abortedURLS = map[string]bool{}
}

func (t *Tab) InitProtocol() error {
	if err := t.networkEvents(true); err != nil {
		log.Errorln(err)
		return err
	}
	if err := t.pageEvents(true); err != nil {
		log.Errorln(err)
		return err
	}
	if err := t.domEvents(true); err != nil {
		log.Errorln(err)
		return err
	}
	if err := t.securityEvents(true); err != nil {
		log.Errorln(err)
		return err
	}
	/*
		if err := t.runtimeEvents(true); err != nil {
			logger.Println(err)
			return err
		}
		if err := t.targetEvents(true); err != nil {
			logger.Println(err)
			return err
		}
		if err := t.setCacheDisabled(true); err != nil {
			log.Errorln(err)
			return err
		}
		if err := t.ClearBrowserCache(); err != nil {
			log.Errorln(err)
			return err
		}
		if err := t.ClearBrowserCookies(); err != nil {
			log.Errorln(err)
			return err
		}
	*/
	if err := t.SetOverrideCertificateErrors(true); err != nil {
		log.Errorln(err)
		return err
	}
	if err := t.setAdBlockingEnabled(true); err != nil {
		log.Errorln(err)
		return err
	}
	if err := t.SetLifecycleEventsEnabled(true); err != nil {
		log.Errorln(err)
		return err
	}
	/*
		if err := t.setDiscoverTargets(true); err != nil {
			logger.Println(err)
			return err
		}
		if err := t.setRequestInterception("*", "Request"); err != nil {
			logger.Println(err)
			return err
		}
	*/

	t.CallbackEvent("Network.requestWillBeSent", t.requestWillBeSentCB)
	t.CallbackEvent("Network.responseReceived", t.responseReceivedCB)
	t.CallbackEvent("Page.javascriptDialogOpening", t.javascriptDialogOpeningCB)
	t.CallbackEvent("Security.certificateError", t.certificateErrorCB)
	t.CallbackEvent("Page.lifecycleEvent", t.lifecycleEventCB)

	return nil
}

func (t *Tab) Connect() error {
	err := t.connectWS()
	if err != nil {
		return err
	}

	t.goroutines.Add(2)
	go t.sendMessage()
	go t.processEvents()

	return nil
}

func (t *Tab) connectWS() error {
	if len(t.WsURL) == 0 {
		return ErrorNoWsURL
	}

	d := &websocket.Dialer{
		ReadBufferSize:  MaxReadBufferSize,
		WriteBufferSize: MaxWriteBufferSize,
	}

	ws, _, err := d.Dial(t.WsURL, nil)
	if err != nil {
		return err
	}

	t.ws = ws

	err = t.ws.SetReadDeadline(time.Now().Add(time.Second * 40))
	if err != nil {
		log.Errorln("SetReadDeadline failed.", err)
		return err
	}
	err = t.ws.SetWriteDeadline(time.Now().Add(time.Second * 40))
	if err != nil {
		log.Errorln("SetWriteDeadline failed.", err)
		return err
	}

	t.goroutines.Add(1)
	go t.readMessage()
	return nil
}

func (t *Tab) Close() {
	t.finished = true

	if t.ws != nil {
		err := t.ws.Close()
		if err != nil {
			log.Errorln(err)
		}
		t.goroutines.Wait()
	}
}

func permanentError(err error) bool {
	if strings.Contains(err.Error(), "i/o timeout") {
		return true
	}

	if websocket.IsUnexpectedCloseError(err) {
		return true
	}

	if neterr, ok := err.(net.Error); ok && !neterr.Temporary() {
		return true
	}

	log.Errorln(err)
	return false
}

func (t *Tab) readMessage() {
	defer t.goroutines.Done()
	defer func() {
		if err := recover(); err != nil {
			log.Errorln("readMessage recover error.", err)
		}
	}()

reading:
	for {
		if t.finished {
			break reading
		}

		_, bytes, err := t.ws.ReadMessage()
		if err != nil {
			if permanentError(err) {
				break reading
			}
		} else {

			var message wsMessage

			if err := json.Unmarshal(bytes, &message); err != nil {
				log.Errorln("unmarshal", string(bytes), len(bytes), err)
			} else if message.Method != "" {
				t.mutexCallbacks.Lock()
				_, ok := t.callbacks[message.Method]
				t.mutexCallbacks.Unlock()

				if !ok {
					continue // don't queue unrequested events
				}

				t.events <- message

			} else {

				t.mutexResponses.Lock()
				ch := t.responses[message.ID]
				t.mutexResponses.Unlock()

				if ch != nil {
					ch <- message.Result
				}
			}
		}
	}
}

func (t *Tab) sendMessage() {
	defer t.goroutines.Done()

sending:
	for {
		if t.finished {
			break sending
		}

		select {
		case message := <-t.requests:
			err := t.ws.WriteMessage(websocket.TextMessage, message)
			if err != nil {
				log.Errorln("write message:", err, string(message))
				if strings.Contains(err.Error(), "i/o timeout") {
					break sending
				}
			}
		default:
			time.Sleep(time.Millisecond * 10)
		}
	}
}

func (t *Tab) SendRequest(method string, mParams interface{}) (json.RawMessage, error) {
	rawReply, err := t.sendRawReplyRequest(method, mParams)
	if err != nil || rawReply == nil {
		return nil, err
	}
	return rawReply, nil
}

func (t *Tab) sendRawReplyRequest(method string, mParams interface{}) (json.RawMessage, error) {
	responseChan := make(chan json.RawMessage, 1)

	t.mutexResponses.Lock()
	reqID := t.reqID
	t.reqID++
	t.responses[reqID] = responseChan
	t.mutexResponses.Unlock()

	defer t.delRespChan(reqID)

	command := MethodReqParams{
		ID:     reqID,
		Method: method,
		Params: mParams,
	}

	rawCmd, err := json.Marshal(command)
	if err != nil {
		return []byte{}, err
	}

	if t.finished {
		log.Infoln("Chrome navigate finished. rawCmd: ", string(rawCmd))
		return []byte{}, errors.New("chrome navigate finished")
	}

	timer := time.NewTimer(time.Second * 5)
	select {
	case t.requests <- rawCmd:
		timer.Stop()
	case <-timer.C:
		log.Errorln("Send Request to t.requests error", t.requestURL, string(rawCmd))
		return nil, errors.New("send request to t.requests error")
	}

	timer = time.NewTimer(time.Second * 25)
	select {
	case reply := <-responseChan:
		return reply, nil
	case <-timer.C:
		log.Errorln(string(rawCmd), ", receive reply timeout.")
		return nil, errors.New("receive reply timeout")
	}
}

func (t *Tab) delRespChan(reqID int) {
	t.mutexResponses.Lock()
	delete(t.responses, reqID)
	t.mutexResponses.Unlock()
}

func (t *Tab) processEvents() {
	defer t.goroutines.Done()

processing:
	for {
		if t.finished {
			break processing
		}

		select {
		case ev := <-t.events:
			t.mutexCallbacks.Lock()
			cb := t.callbacks[ev.Method]
			t.mutexCallbacks.Unlock()

			if cb != nil {
				cb(ev.Params)
			}
		default:
			time.Sleep(time.Millisecond * 10)
		}
	}
}

func (t *Tab) closeOpenedTargets() {
	defer t.goroutines.Done()

close:
	for {
		if t.finished {
			break close
		}

		select {
		case targetID := <-t.targetIDChan:
			success, err := t.closeTarget(targetID)
			if err != nil {
				log.Errorln(err)
				break
			}
			log.Infoln("close target:", targetID, "success:", success)
		default:
			time.Sleep(time.Millisecond * 50)
		}
	}
}

func (t *Tab) CallbackEvent(method string, cb EventCallback) {
	t.mutexCallbacks.Lock()
	t.callbacks[method] = cb
	t.mutexCallbacks.Unlock()
}

func (t *Tab) Navigate(url string) error {
	frameID, err := t.navigate(url)
	if err != nil {
		log.Errorln(err)
		return err
	}
	t.frameID = frameID

	t.waitNetworkIdle()

	if err := t.Evaluate(t.InjectJavaScript.InjectAfterLoad, true); err != nil {
		log.Errorln(err)
	}
	return err
}

func (t *Tab) domainEvents(domain string, enable bool) error {
	method := domain

	if enable {
		method += ".enable"
	} else {
		method += ".disable"
	}

	_, err := t.SendRequest(method, nil)
	return err
}

func (t *Tab) pageEvents(enable bool) error {
	return t.domainEvents("Page", enable)
}

func (t *Tab) domEvents(enable bool) error {
	return t.domainEvents("DOM", enable)
}

func (t *Tab) networkEvents(enable bool) error {
	return t.domainEvents("Network", enable)
}

func (t *Tab) securityEvents(enable bool) error {
	return t.domainEvents("Security", enable)
}

func (t *Tab) runtimeEvents(enable bool) error {
	return t.domainEvents("Runtime", enable)
}

func (t *Tab) targetEvents(enable bool) error {
	return t.domainEvents("Target", enable)
}

type navigateParams struct {
	URL     string `json:"url"`
	Referer string `json:"referer,omitempty"`
}

type navigateReturns struct {
	FrameID   string `json:"frameId,omitempty"`
	LoaderID  string `json:"loaderId,omitempty"`
	ErrorText string `json:"errorText,omitempty"`
}

func (t *Tab) navigate(u string) (string, error) {
	res, err := t.SendRequest("Page.navigate", navigateParams{
		URL: u,
	})

	if err != nil {
		return "", err
	}

	var ret navigateReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return "", err
	}
	return ret.FrameID, nil
}

type captureScreenShotParams struct {
	Format      string   `json:"format"`
	Quality     int      `json:"quality"`
	Chip        Viewport `json:"chip"`
	FromSurface bool     `json:"fromSurface,omitempty"`
}

type GetLayoutMetricsReturns struct {
	LayoutViewport *LayoutViewport `json:"layoutViewport,omitempty"` // Metrics relating to the layout viewport.
	VisualViewport *VisualViewport `json:"visualViewport,omitempty"` // Metrics relating to the visual viewport.
	ContentSize    *Rect           `json:"contentSize,omitempty"`    // Size of scrollable area.
}

type CaptureScreenShotReturns struct {
	Data string `json:"data,omitempty"` // Base64-encoded image data.
}

func (t *Tab) screenShot(format string, quality int, fullPage bool, chip Viewport) ([]byte, error) {
	if fullPage {
		res, err := t.SendRequest("Page.getLayoutMetrics", nil)
		if err != nil {
			log.Errorln(err)
			return nil, err
		}

		metricsRet := new(GetLayoutMetricsReturns)
		err = json.Unmarshal(res, metricsRet)
		if err != nil {
			log.Errorln(err)
			return nil, err
		}

		chip.X = 0
		chip.Y = 0
		chip.Width = metricsRet.ContentSize.Width
		chip.Height = metricsRet.ContentSize.Height
		chip.Scale = 1

		if err = t.SetDeviceMetricsOverride(int64(chip.Width), int64(chip.Height), 1, false); err != nil {
			log.Errorln(err)
			return nil, err
		}
	}

	err := t.SetDefaultBackgroundColorOverride(&RGBA{R: 255, G: 255, B: 255, A: 1})
	if err != nil {
		log.Errorln(err)
		return nil, err
	}

	if err := t.waitForPageLoad(1 * time.Second); err != nil {
		log.Errorln("等待页面渲染完成失败:", err)
		return nil, err
	}

	res, err := t.SendRequest("Page.captureScreenshot", captureScreenShotParams{
		Format:  format,
		Quality: quality,
		Chip:    chip,
	})
	if err != nil {
		log.Errorln(err)
		return nil, err
	}

	data := new(CaptureScreenShotReturns)
	err = json.Unmarshal(res, data)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}

	var dec []byte
	dec, err = base64.StdEncoding.DecodeString(data.Data)
	if err != nil {
		return nil, err
	}
	return dec, nil
}

type SetDeviceMetricsOverrideParams struct {
	Width              int64              `json:"width"`                        // Overriding width value in pixels (minimum 0, maximum 10000000). 0 disables the override.
	Height             int64              `json:"height"`                       // Overriding height value in pixels (minimum 0, maximum 10000000). 0 disables the override.
	DeviceScaleFactor  float64            `json:"deviceScaleFactor"`            // Overriding device scale factor value. 0 disables the override.
	Mobile             bool               `json:"mobile"`                       // Whether to emulate mobile device. This includes viewport meta tag, overlay scrollbars, text autosizing and more.
	Scale              float64            `json:"scale,omitempty"`              // Scale to apply to resulting view image.
	ScreenWidth        int64              `json:"screenWidth,omitempty"`        // Overriding screen width value in pixels (minimum 0, maximum 10000000).
	ScreenHeight       int64              `json:"screenHeight,omitempty"`       // Overriding screen height value in pixels (minimum 0, maximum 10000000).
	PositionX          int64              `json:"positionX,omitempty"`          // Overriding view X position on screen in pixels (minimum 0, maximum 10000000).
	PositionY          int64              `json:"positionY,omitempty"`          // Overriding view Y position on screen in pixels (minimum 0, maximum 10000000).
	DontSetVisibleSize bool               `json:"dontSetVisibleSize,omitempty"` // Do not set visible view size, rely upon explicit setVisibleSize call.
	ScreenOrientation  *ScreenOrientation `json:"screenOrientation,omitempty"`  // Screen orientation override.
	Viewport           *Viewport          `json:"viewport,omitempty"`           // If set, the visible area of the page will be overridden to this viewport. This viewport change is not observed by the page, e.g. viewport-relative elements do not change positions.
}

func (t *Tab) SetDeviceMetricsOverride(width int64, height int64, deviceScaleFactor float64, mobile bool) error {
	_, err := t.SendRequest("Emulation.setDeviceMetricsOverride", &SetDeviceMetricsOverrideParams{
		Width:             width,
		Height:            height,
		DeviceScaleFactor: deviceScaleFactor,
		Mobile:            mobile,
	})

	return err
}

type SetDefaultBackgroundColorOverrideParams struct {
	Color *RGBA `json:"color,omitempty"` // RGBA of the default background color. If not specified, any existing override will be cleared.
}

func (t *Tab) SetDefaultBackgroundColorOverride(color *RGBA) error {
	_, err := t.SendRequest("Emulation.setDefaultBackgroundColorOverride", &SetDefaultBackgroundColorOverrideParams{
		Color: color,
	})
	return err
}

type setUserAgentParams struct {
	UserAgent string `json:"userAgent"`
}

func (t *Tab) SetUserAgent(userAgent string) error {
	_, err := t.SendRequest("Network.setUserAgentOverride", setUserAgentParams{
		UserAgent: userAgent,
	})
	return err
}

type setBlockedURLSParams struct {
	Urls []string `json:"urls"`
}

func (t *Tab) SetBlockedURLs(urls ...string) error {
	_, err := t.SendRequest("Network.setBlockedURLs", setBlockedURLSParams{
		Urls: urls,
	})
	return err
}

func (t *Tab) ClearBrowserCache() error {
	_, err := t.SendRequest("Network.clearBrowserCache", nil)
	return err
}

func (t *Tab) ClearBrowserCookies() error {
	_, err := t.SendRequest("Network.clearBrowserCookies", nil)
	return err
}

type setOverrideCertificateErrorsParams struct {
	Override bool `json:"override"`
}

func (t *Tab) SetOverrideCertificateErrors(override bool) error {
	_, err := t.SendRequest("Security.setOverrideCertificateErrors", setOverrideCertificateErrorsParams{
		Override: override,
	})
	return err
}

type setExtraHTTPHeadersParams struct {
	Headers chromeHeaders `json:"headers"`
}

func (t *Tab) SetExtraHTTPHeaders(headers common.HttpHeaders) error {
	_, err := t.SendRequest("Network.setExtraHTTPHeaders", setExtraHTTPHeadersParams{
		Headers: toChromeHeaders(headers),
	})
	return err
}

type Node struct {
	NodeID           int64    `json:"nodeId"`
	ParentID         int64    `json:"parentId,omitempty"`
	NodeType         int64    `json:"nodeType"`
	NodeName         string   `json:"nodeName"`
	LocalName        string   `json:"localName"`
	NodeValue        string   `json:"nodeValue"`
	ChildNodeCount   int64    `json:"childNodeCount,omitempty"`
	Children         []*Node  `json:"children,omitempty"`
	Attributes       []string `json:"attributes,omitempty"`
	DocumentURL      string   `json:"documentURL,omitempty"`
	BaseURL          string   `json:"baseURL,omitempty"`
	PublicID         string   `json:"publicId,omitempty"`
	SystemID         string   `json:"systemId,omitempty"`
	InternalSubset   string   `json:"internalSubset,omitempty"`
	XMLVersion       string   `json:"xmlVersion,omitempty"`
	Name             string   `json:"name,omitempty"`
	Value            string   `json:"value,omitempty"`
	PseudoType       string   `json:"pseudoType,omitempty"`
	ShadowRootType   string   `json:"shadowRootType,omitempty"`
	FrameID          string   `json:"frameId,omitempty"`
	ContentDocument  *Node    `json:"contentDocument,omitempty"`
	ShadowRoots      []*Node  `json:"shadowRoots,omitempty"`
	TemplateContent  *Node    `json:"templateContent,omitempty"`
	PseudoElements   []*Node  `json:"pseudoElements,omitempty"`
	ImportedDocument *Node    `json:"importedDocument,omitempty"`
	IsSVG            bool     `json:"isSVG,omitempty"`
}

type getDocumentReturns struct {
	Root *Node `json:"root,omitempty"`
}

func (t *Tab) GetDocument() (*Node, error) {
	rawResult, err := t.SendRequest("DOM.getDocument", nil)
	if err != nil {
		return nil, err
	}
	var root getDocumentReturns
	err = json.Unmarshal(rawResult, &root)
	if err != nil {
		return nil, err
	}
	return root.Root, nil
}

func (t *Tab) getRootNodeID() (int64, error) {
	res, err := t.GetDocument()
	if err != nil {
		return 0, err
	}

	return res.NodeID, nil
}

type getOuterHTMLParams struct {
	NodeID int64 `json:"nodeId"`
}

type getOuterHTMLReturns struct {
	OuterHTML string `json:"outerHTML,omitempty"`
}

func (t *Tab) GetOuterHTML(nodeID int64) ([]byte, error) {
	res, err := t.SendRequest("DOM.getOuterHTML", getOuterHTMLParams{
		NodeID: nodeID,
	})
	if err != nil {
		log.Errorln(err)
		return []byte{}, err
	}

	var oHTML getOuterHTMLReturns
	err = json.Unmarshal(res, &oHTML)
	if err != nil {
		log.Errorln(err)
		return []byte{}, err
	}

	return []byte(oHTML.OuterHTML), nil
}

type handleJavaScriptDialogParams struct {
	Accept     bool   `json:"accept"`
	PromptText string `json:"promptText"`
}

func (t *Tab) HandleJavaScriptDialog(accept bool, promptText string) error {
	_, err := t.SendRequest("Page.handleJavaScriptDialog", handleJavaScriptDialogParams{
		Accept:     accept,
		PromptText: promptText,
	})

	return err
}

func (t *Tab) javascriptDialogOpeningCB(params json.RawMessage) {
	err := t.HandleJavaScriptDialog(true, "")
	if err != nil {
		log.Errorln(err)
	}

}

type getResponseBodyParams struct {
	RequestID string `json:"requestId"`
}

type getResponseBodyReturns struct {
	Body          string `json:"body,omitempty"`
	Base64encoded bool   `json:"base64Encoded,omitempty"`
}

func (t *Tab) GetResponseBody(requestID string) ([]byte, error) {

	res, err := t.SendRequest("Network.getResponseBody", getResponseBodyParams{
		RequestID: requestID,
	})
	if err != nil {
		return []byte{}, err
	}

	var ret getResponseBodyReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return []byte{}, err
	}

	if ret.Base64encoded {
		return base64.StdEncoding.DecodeString(ret.Body)
	}
	return []byte(ret.Body), nil
}

type setCacheDisableedParams struct {
	CacheDisabled bool `json:"cacheDisabled"`
}

func (t *Tab) setCacheDisabled(cacheDisabled bool) error {
	_, err := t.SendRequest("Network.setCacheDisabled", setCacheDisableedParams{
		CacheDisabled: cacheDisabled,
	})

	return err
}

func (t *Tab) GetResourceTree() (*FrameTree, error) {
	res, err := t.SendRequest("Page.getResourceTree", nil)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}

	var ret GetResourceTreeReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}

	return &ret.FrameTree, nil
}

type getResourceContentParams struct {
	FrameID string `json:"frameId"`
	URL     string `json:"url"`
}

type getResourceContentReturns struct {
	Content       string `json:"content,omitempty"`
	Base64encoded bool   `json:"base64Encoded,omitempty"`
}

func (t *Tab) GetResourceContent(frameID, u string) ([]byte, error) {
	res, err := t.SendRequest("Page.getResourceContent", getResourceContentParams{
		FrameID: frameID,
		URL:     u,
	})

	if err != nil {
		return nil, err
	}

	var ret getResourceContentReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return nil, err
	}

	var dec []byte
	if ret.Base64encoded {
		dec, err = base64.StdEncoding.DecodeString(ret.Content)
		if err != nil {
			return nil, err
		}
	} else {
		dec = []byte(ret.Content)
	}
	return dec, nil
}

type handleCertificateErrorParams struct {
	Action  string `json:"action"`
	EventID int64  `json:"eventId"`
}

func (t *Tab) handleCertificateError(action string, eventID int64) error {
	_, err := t.SendRequest("Security.handleCertificateError", handleCertificateErrorParams{
		Action:  action,
		EventID: eventID,
	})

	return err
}

type eventCertificateError struct {
	EventID    int64  `json:"eventId"`
	ErrorType  string `json:"errorType"`
	RequestURL string `json:"requestURL"`
}

func (t *Tab) certificateErrorCB(params json.RawMessage) {
	var ev eventCertificateError
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
	}
	eventID := ev.EventID
	err = t.handleCertificateError("continue", eventID)
	if err != nil {
		log.Errorln(err)
	}
}

func (t *Tab) loadEventFiredCB(params json.RawMessage) {
	timer := time.NewTimer(5 * time.Second)
	select {
	case t.loaded <- true:
		return
	case <-timer.C:
		log.Infoln("write loadEventFired to chan failed.", t.requestURL)
		return
	}
}

func (t *Tab) responseReceivedCB(params json.RawMessage) {
	var ev eventResponseReceived
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
	}
	t.mutexRecResponses.Lock()
	defer t.mutexRecResponses.Unlock()
	t.recResponses[ev.Response.URL] = ev.Response
}

func noSchemeTailURL(u string) string {
	if strings.HasPrefix(u, "http://") {
		u = u[7:]
	}

	if strings.HasPrefix(u, "https://") {
		u = u[8:]
	}

	if strings.HasSuffix(u, "/") {
		u = u[:len(u)-1]
	}
	return u
}

func (t *Tab) requestWillBeSentCB(params json.RawMessage) {
	var ev eventRequestWillBeSent
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
		return
	}

	frameID := ev.FrameID
	request := ev.Request
	curReqURL := request.URL
	tmpReqURL := noSchemeTailURL(t.requestURL)
	if tmpReqURL != noSchemeTailURL(curReqURL) {
		if _, ok := t.sentRequests[frameID]; ok {
			t.sentRequests[frameID] = append(t.sentRequests[frameID], ev)
		} else {
			t.sentRequests[frameID] = []eventRequestWillBeSent{ev}
		}
	}
}

func (t *Tab) waitNetworkIdle() {
	timer := time.NewTimer(30 * time.Second)
	reCheck := false

checking:
	for {
		time.Sleep(1 * time.Second)

		select {
		case <-timer.C:
			break checking
		default:
		}

		tmpNetworkIdle := true
		t.lifecycleEvents.Range(func(k, v interface{}) bool {
			tmpIdle := false
			for _, vv := range v.([]string) {
				if vv == "networkIdle" {
					tmpIdle = true
					break
				}
			}
			if !tmpIdle {
				tmpNetworkIdle = false
				return false
			}
			return true
		})

		if tmpNetworkIdle && !reCheck {
			reCheck = true
			continue
		} else if !tmpNetworkIdle && !reCheck {
			continue
		} else if tmpNetworkIdle && reCheck {
			load := true
			t.lifecycleEvents.Range(func(k, v interface{}) bool {
				tmpLoad := false
				for _, vv := range v.([]string) {
					if vv == "load" {
						tmpLoad = true
						break
					}
				}
				if !tmpLoad {
					load = false
					return false
				}
				return true
			})

			if load {
				break checking
			} else {
				continue
			}
		}
	}
	t.networkIdled = true
}

func (t *Tab) waitLoaded() {
	timer := time.NewTimer(30 * time.Second)
	select {
	case <-t.loaded:
		return
	case <-t.refreshed:
		return
	case <-timer.C:
		return
	}
}

func (t *Tab) waitRefreshed() {
	timer := time.NewTimer(1 * time.Second)
	select {
	case <-t.refreshed:
		return
	case <-timer.C:
		return
	}
}

func (t *Tab) getHTML(nodeID int64) []byte {
	html, err := t.GetOuterHTML(nodeID)
	if err != nil {
		log.Errorln(err)
		return []byte("")
	}
	return html
}

func (t *Tab) getHeaders(u string) (int64, map[string]string, string) {
	var status int64 = 599
	headers := map[string]string{}
	pageURL := ""

	t.mutexRecResponses.Lock()
	defer t.mutexRecResponses.Unlock()
	if resp, ok := t.recResponses[u]; ok {
		status = resp.Status
		headers = resp.Headers
		pageURL = resp.URL
	}
	return status, headers, pageURL
}

type setAdBlockingEnabledParams struct {
	Enabled bool `json:"enabled"`
}

func (t *Tab) setAdBlockingEnabled(enabled bool) error {
	_, err := t.SendRequest("Page.setAdBlockingEnabled", setAdBlockingEnabledParams{
		Enabled: enabled,
	})

	return err
}

func (t *Tab) SetCookie(args *SetCookieArgs) error {
	_, err := t.SendRequest("Network.setCookie", args)
	return err
}

func (t *Tab) SetCookies(args *SetCookiesArgs) error {
	_, err := t.SendRequest("Network.setCookies", args)
	return err
}

func (t *Tab) Evaluate(expression string, returnByValue bool) error {
	if len(expression) == 0 {
		return errors.New("expression is nil")
	}

	_, err := t.SendRequest("Runtime.evaluate", evaluateParams{
		Expression:    expression,
		ReturnByValue: returnByValue,
	})
	return err
}

func (t *Tab) AddScriptToEvaluateOnNewDocument(source string) error {
	if len(source) == 0 {
		return errors.New("source is nil")
	}

	_, err := t.SendRequest(
		"Page.addScriptToEvaluateOnNewDocument",
		addScriptToEvaluateOnNewDocumentParams{
			Source: source,
		})
	return err
}

func (t *Tab) SetLifecycleEventsEnabled(enabled bool) error {
	_, err := t.SendRequest(
		"Page.setLifecycleEventsEnabled",
		setLifecycleEventsEnabledParams{
			Enabled: enabled,
		})
	return err
}

func (t *Tab) lifecycleEventCB(params json.RawMessage) {
	var ev lifecycleEvent
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
		return
	}

	frameID := ev.FrameID
	if _, ok := t.lifecycleEvents.Load(frameID); ok {
		if ev.Name == "init" {

			t.lifecycleEvents.Store(frameID, []string{})
		} else {
			v, _ := t.lifecycleEvents.Load(frameID)
			v = append(v.([]string), ev.Name)
			t.lifecycleEvents.Store(frameID, v)
		}
	} else {
		v := []string{ev.Name}
		t.lifecycleEvents.Store(frameID, v)
	}
}

func (t *Tab) windowOpenCB(params json.RawMessage) {
	var ev eventWindowOpen
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
		return
	}

	log.Infoln(ev.URL, ev.WindowName, ev.WindowFeatures, ev.UserGesture)
}

func (t *Tab) targetCreatedCB(params json.RawMessage) {
	var ev eventTargetCreated
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
		return
	}

	info := ev.TargetInfo
	log.Infoln(info.URL, info.Type, info.Title, info.TargetID, info.OpenerID, info.BrowserContextID, info.Attached)

	timer := time.NewTimer(1 * time.Second)
	select {
	case t.targetIDChan <- info.TargetID:
		return
	case <-timer.C:
		return
	}
}

func (t *Tab) setDiscoverTargets(discover bool) error {
	_, err := t.SendRequest(
		"Target.setDiscoverTargets",
		setDiscoverTargetsParams{
			Discover: discover,
		})
	return err
}

func (t *Tab) closeTarget(targetID string) (bool, error) {
	res, err := t.SendRequest(
		"Target.closeTarget",
		closeTargetParams{
			TargetID: targetID,
		})

	if err != nil {
		log.Errorln("close target error, targetId:", targetID, err)
		return false, err
	}

	var ret closeTargetReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return false, err
	}

	return ret.Success, nil
}

func (t *Tab) setRequestInterception(pattern, interceptionStage string) error {
	_, err := t.SendRequest(
		"Network.setRequestInterception",
		setRequestInterceptionParams{
			Patterns: []requestPattern{
				requestPattern{
					URLPattern:        pattern,
					InterceptionStage: interceptionStage,
				},
			},
		})
	return err
}

func (t *Tab) requestInterceptedCB(params json.RawMessage) {
	var ev eventRequestIntercepted
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
		return
	}

	if ev.IsNavigationRequest && t.networkIdled {

		if err := t.continueInterceptedRequest(ev.InterceptionID, "Aborted"); err == nil {
			t.mutexAbortedURLS.Lock()
			defer t.mutexAbortedURLS.Unlock()
			t.abortedURLS[ev.Request.URL] = true
		} else {
			log.Errorln(ev, ev.Request.URL, "abort failed.")
		}
	} else {
		if err := t.continueInterceptedRequest(ev.InterceptionID, ""); err != nil {
			log.Errorln(ev, ev.Request.URL, err, "continue failed.")
		}
	}
}

func (t *Tab) continueInterceptedRequest(interceptionID, errorReason string) error {
	_, err := t.SendRequest(
		"Network.continueInterceptedRequest",
		continueInterceptedRequestParams{
			InterceptionID: interceptionID,
			ErrorReason:    errorReason,
		})
	return err
}

func (t *Tab) getNodeCenterCoordinate(nodeID int64) (int64, int64, error) {
	res, err := t.SendRequest(
		"DOM.getBoxModel",
		getBoxModelParams{
			NodeID: nodeID,
		})
	if err != nil {
		return 0, 0, err
	}

	var ret getBoxModelReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return 0, 0, err
	}

	x, y := ret.Model.getCenterCoordinate()
	return x, y, nil
}

func (t *Tab) dispatchMouseEvent(typ string, x, y int64) error {

	_, err := t.SendRequest(
		"Input.dispatchMouseEvent",
		dispatchMouseEventParams{
			Type:       typ,
			X:          x,
			Y:          y,
			Button:     "left",
			ClickCount: 1,
		})
	return err
}

func (t *Tab) querySelectorAll(nodeID int64, selector string) ([]int64, error) {
	res, err := t.SendRequest(
		"DOM.querySelectorAll",
		querySelectorAllParams{
			NodeID:   nodeID,
			Selector: selector,
		})
	if err != nil {
		return []int64{}, err
	}

	var ret querySelectorAllReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return []int64{}, err
	}

	return ret.NodeIDs, nil
}

func (t *Tab) simulatedClick(rootNodeID int64) {
	nodeIDs, err := t.querySelectorAll(rootNodeID, "a:not([href])")
	if err != nil {
		log.Errorln(err)
		return
	}

	timer := time.NewTimer(5 * time.Second)
	for i := range nodeIDs {

		select {
		case <-timer.C:
			return
		default:
			break
		}

		x, y, err := t.getNodeCenterCoordinate(nodeIDs[i])
		if err != nil {
			continue
		}

		err = t.dispatchMouseEvent("mousePressed", x, y)
		if err != nil {
			log.Errorln(err)
		}
		err = t.dispatchMouseEvent("mouseReleased", x, y)
		if err != nil {
			log.Errorln(err)
		}
	}
}

func (t *Tab) waitForRendering(expectedSelector string, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("等待指定元素(%s)超时", expectedSelector)
		default:
			err := t.Evaluate(fmt.Sprintf("document.querySelector('%s') != null", expectedSelector), true)
			if err != nil {
				time.Sleep(100 * time.Millisecond)
				continue
			}
			time.Sleep(100 * time.Millisecond)
		}
	}
}

func (t *Tab) waitForPageLoad(timeout time.Duration) error {
	deadline := time.Now().Add(timeout)
	for {
		err := t.Evaluate("document.readyState", true)
		if err == nil {
			return nil
		}
		if time.Now().After(deadline) {
			return fmt.Errorf("等待页面加载完成超时")
		}
		time.Sleep(100 * time.Millisecond)
	}
}
