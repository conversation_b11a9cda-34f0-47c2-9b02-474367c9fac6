package validators

import (
	"context"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

var extPatterns = []*regexp.Regexp{

	regexp.MustCompile(`(?is)\bCodeBehind=".*?\.aspx\.cs"`),
	regexp.MustCompile(`(?is)\bAutoEventWireup="(true|false)"`),
	regexp.MustCompile(`(?is)<%.*?%>`),
	regexp.MustCompile(`(?is)<(asp:[a-z]+)[^>]*/>`),

	regexp.MustCompile(`(?is)<%=?.+?%>`),

	regexp.MustCompile(`(?is)<\?(php)?.+?\?>`),

	regexp.MustCompile(`(?is)<%@\s*(page|include)\s.*?%>?`), // <%@page
	regexp.MustCompile(`(?is)<%=?.+?%>`),                    // <%...%> <%=...%>
	regexp.MustCompile(`(?is)<([sc]:[a-z]+)[^>]*/>`),        // <s:xxx1234/>
}

var extTwiceCheckPatterns = []*regexp.Regexp{
	regexp.MustCompile(`(?is)<([^\s]+)[^>]+runat="server"[^>]*>.*?</([^\s]+)>`), // <test runat="server">...</test>
	regexp.MustCompile(`(?is)<(asp:[a-z]+)[^>]*>.*?</(asp:[a-z]+)>`),            // <asp:xxx>...</asp:xxx>
	regexp.MustCompile(`(?is)<([sc]:[a-z]+)[^>]*>.*?</([sc]:[a-z]+)>`),          // <s:xxx>...</s:xxx> <c:xxx>...</c:xxx>
}

var validExts = []string{
	".jsp.bak", ".php.bak", ".asp.bak", ".aspx.bak",
}

func ValidateFileBackup(args *ValidationArgs) (*ValidationResult, error) {
	fileName := strings.ToLower(filepath.Base(args.VulURL))

	extCheckPassed := false
	for i := range validExts {
		if strings.HasSuffix(fileName, validExts[i]) {
			extCheckPassed = true
			break
		}
	}

	bodyStr, statusCode, err := DoHTTPRequest(args.VulURL)
	if err != nil {
		return nil, err
	}

	langCharacteristicsMatched := false
	if statusCode == 200 && extCheckPassed {
		for _, p := range extPatterns {
			m := p.FindString(bodyStr)
			if len(m) > 0 {
				langCharacteristicsMatched = true
				break
			}
		}

		if !langCharacteristicsMatched {
			for _, p := range extTwiceCheckPatterns {
				m := p.FindStringSubmatch(bodyStr)
				if len(m) == 3 {
					if strings.ToLower(m[1]) == strings.ToLower(m[2]) {
						langCharacteristicsMatched = true
						break
					}
				}
			}
		}

		if langCharacteristicsMatched {
			ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
			defer cancel()

			cmd := exec.CommandContext(ctx, "curl", "-I", args.VulURL)
			output, err := cmd.CombinedOutput()
			if err != nil {
				return nil, err
			}
			return &ValidationResult{
				Status:       VulIsValid,
				Command:      "curl -I " + args.VulURL,
				Output:       string(output),
				Highlight:    "200 OK",
				NeedSnapshot: true,
			}, nil
		}
	}
	return &ValidationResult{
		Status:       VulIsInvalid,
		NeedSnapshot: false,
	}, nil
}
