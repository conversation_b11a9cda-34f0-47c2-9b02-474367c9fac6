package main

import (
	"context"
	"flag"
	"fmt"
	"time"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/config"
	"websec/scan"
	"websec/scan/rules"
	"websec/utils"
	"websec/utils/log"
	"websec/utils/stream"

	"github.com/mhqiang/logger"
	"github.com/panjf2000/ants"
	"github.com/scylladb/go-set/strset"
	"go.mongodb.org/mongo-driver/bson"
)

var (
	keyWord = flag.String("name", "", "asset keyword")
	dstURL  = flag.String("dsturl", "http://***********/dvwa/vulnerabilities/xss_d/?default=English",
		"check_ur")
	duration = flag.Int("duration", 7, "duration")
	header   = flag.String("header", "", "request header")

	fishSrc = map[string]*strset.Set{}
)

var h *XssHandler

type XssHandler struct {
	producer     *stream.Producer
	dbConnection *db.DBConnection
	pool         *ants.PoolWithFunc
}

func main() {
	var lconfig logger.Config

	lconfig.MaxLogSize = 100
	lconfig.ServiceName = "test"
	lconfig.Level = "info"

	logger.Init(&lconfig)

	settings, err := config.ParseConfig()
	if err != nil {
		log.Errorln(err)
		return
	}

	dbConnection, err := db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis))
	if err != nil {
		log.Errorln(err)
		return
	}

	run(dbConnection, settings)

}

func run(dbConnection *db.DBConnection, settings *config.Config) {
	producer, err := stream.NewProducer(&stream.ProducerOptions{
		Brokers:         settings.Kafka.Brokers,
		MessageMaxBytes: settings.Kafka.MessageMaxBytes,
	})
	if err != nil {
		log.Errorln(err)
		return
	}
	producer.Go()
	defer producer.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	asset := new(schema.Asset)
	err = dbConnection.GetMongoDatabase().Collection(consts.CollectionAssets).FindOne(ctx,
		bson.M{"host": "***********"}).Decode(asset)
	if err != nil {
		log.Errorln(err)
		return
	}

	blueprint, err := rules.LoadBlueprint(settings.Scanner.BlueprintPath, settings.Scanner.VulsPath)
	if err != nil {
		log.Errorln("error while createScanner when load blueprint", err)
		return
	}

	ops := &scan.Options{
		Blueprint: &blueprint,
	}
	scannerWorker, _ := scan.NewScanner(ops)
	link := &scan.AffectLink{
		Method:  "GET",
		URL:     *dstURL,
		Headers: utils.ParseHeader(*header),
	}

	scanChan := scannerWorker.Result()

	go scannerWorker.ScanXray(link)
	timer := time.NewTicker(time.Second * 5)

Watching:

	for {
		select {
		case scanResult, ok := <-scanChan:
			if !ok {
				break Watching
			}
			logger.Info(scanResult)
			processResult(producer, asset, scanResult)
			break Watching
		case <-timer.C:
			fmt.Println("------")
		}
	}

	time.Sleep(10 * time.Second)

}

func processResult(producer *stream.Producer, asset *schema.Asset, result *scan.ScanResult) {
	for i := range result.FoundVuls {
		vul := result.FoundVuls[i]
		if vul == nil {
			continue
		}
		xmlName := vul.Vul.VulXML

		doc := schema.FoundVulDoc{
			Host: asset.Host,
			Scan: schema.FoundVulsScan{
				Affect: vul.Link.Affect,
				Method: vul.Link.Method,
				URL:    vul.Link.URL,

				Headers:     vul.Link.Headers,
				Fingerprint: vul.Link.Fingerprint(),
			},
			Vul: schema.FoundVulsVul{
				VulXML:   xmlName,
				Severity: vul.Severity,
				VulURL:   vul.VulURL,
				From:     "暂无相关信息",
			},
			Context: vul.Context,
			FoundAt: time.Now(),
			AssetID: asset.ID.Hex(),
		}

		producer.Produce(consts.TopicRawVulResults, &doc)
	}
}
