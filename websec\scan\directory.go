package scan

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"
	"websec/scan/rules"
	"websec/utils"
	"websec/utils/log"

	"github.com/valyala/fasthttp"
)

func genDirectoryTestURL(URL string, param *rules.Param) string {
	parts, err := url.Parse(URL)
	if err != nil {
		log.Errorln("failed to parse url for directory test:", URL)
		return ""
	}
	paths := strings.Split(parts.Path, "/")
	path := strings.Join(paths[:len(paths)-1], "/")
	if strings.HasSuffix(path, "/") {
		path = path[:len(path)-1]
	}
	parts.RawPath = path + param.Value
	return utils.ToString(parts)
}

func (scanner *WSScanner) doVulnerableTestDirectory(link *AffectLink, vul *rules.Vulnerability, param *rules.Param) (bool, *ScanResult) {
	vulnerable, scanResult := scanner.doVulnerableTest(link, vul, param)
	if vulnerable && (vul.VulXML == XMLPossibleSensitiveDirs ||
		vul.VulXML == XMLPossibleSensitiveFiles ||
		vul.VulXML == XMLPossibleSensitiveFiles2 ||
		vul.VulXML == XMLDirectoryBackupCheckSmall ||
		vul.VulXML == XMLDirectoryBackupCheck) {
		foundVuls := scanResult.FoundVuls
		if len(foundVuls) == 0 {
			log.Errorln("this should NOT happen: vulnerable but FoundVuls is empty:", link, vul, param)
			return vulnerable, scanResult
		}

		foundVul := foundVuls[len(foundVuls)-1]
		var lastURL = foundVul.VulURL
		var neverURL string

		if vul.VulXML == XMLPossibleSensitiveDirs {
			if strings.HasSuffix(lastURL, "/") {
				neverURL = fmt.Sprintf("%vnever/", lastURL[:len(lastURL)-1])
			} else {
				neverURL = fmt.Sprintf("%vnever/", lastURL)
			}
		} else if vul.VulXML != XMLDirectoryBackupCheck &&
			vul.VulXML != XMLDirectoryBackupCheckSmall && param != nil &&
			strings.Contains(param.Value, ".") {

			parts := strings.Split(lastURL, ".")
			if len(parts) < 2 {
				log.Errorln("this should NOT happen, wrong url:", lastURL)
				return vulnerable, scanResult
			}
			parts[len(parts)-2] += "never"
			neverURL = strings.Join(parts, ".")
		} else {
			neverURL = lastURL + "never"
		}

		for retry := 0; retry < 3; retry++ {
			if retry > 0 {
				time.Sleep(2 * time.Second)
			}
			request := &ScanRequest{Method: http.MethodGet, URL: neverURL}
			response, _, err := scanner.doRequest(request)
			scanResult.RequestCount++
			if err != nil {
				scanResult.ErrorCount++
				continue
			}
			defer fasthttp.ReleaseResponse(response)
			status := response.StatusCode()
			if status == 200 || status == 403 {
				vulnerable = false
				break
			}
			if retry == 2 {

				if status == 408 || status == 599 {

					vulnerable = false
					break
				}
			} else {

				if status == 408 || status == 599 {

					continue
				} else {

					retry = 3
				}
			}
			context := foundVul.Context
			body, _ := utils.GetUtf8Body(response)
			context["never"] = map[string]interface{}{
				"url":    neverURL,
				"status": status,
				"body":   body,
			}
		}
		if !vulnerable {
			scanResult.FoundVuls = foundVuls[:len(foundVuls)-1]
		}
	}
	return vulnerable, scanResult
}
