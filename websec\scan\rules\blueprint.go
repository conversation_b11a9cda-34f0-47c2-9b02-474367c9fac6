package rules

import (
	"encoding/xml"
	"io/ioutil"
	"os"
	"path"
	"websec/utils/log"
)

type Param struct {
	Name  string `xml:"name,attr"`
	Value string `xml:"value,attr"`
}

type Vulnerability struct {
	Description     string          `xml:"description,attr"`
	Name            string          `xml:"name,attr"`
	VulXML          string          `xml:"vul_xml,attr"`
	Params          []Param         `xml:"item>param"`
	Vulnerabilities []Vulnerability `xml:"vulnerability"`
	NotUseXML       bool            `xml:"noxml,attr"`
	Severity        string          `xml:"severity,attr"`
	Rule            *RuleDefinition
	Affects         string // 同 Moduel.Affects
	DLL             string // 同 Module.DLL

}

var thirdAPPMap = map[string]bool{
	"hanweb":        true,
	"yongyou":       true,
	"inspur":        true,
	"phpcms":        true,
	"ruvar":         true,
	"wanhu":         true,
	"fanwe":         true,
	"fsmcms":        true,
	"shopex":        true,
	"dedecms":       true,
	"b2bbuilder":    true,
	"jixian":        true,
	"cmseasy":       true,
	"metinfo":       true,
	"trueway":       true,
	"ecshop":        true,
	"maticsoft":     true,
	"strongsoft":    true,
	"tongda":        true,
	"discuz":        true,
	"eyou":          true,
	"51tek":         true,
	"bit-service":   true,
	"haitianoa":     true,
	"mymps":         true,
	"aspcms":        true,
	"siteserver":    true,
	"changedu":      true,
	"php168":        true,
	"phpok":         true,
	"zoomla":        true,
	"haitian oa":    true,
	"syc oa":        true,
	"74cms":         true,
	"qibosoft":      true,
	"shopnc":        true,
	"cnki":          true,
	"destoon":       true,
	"phpyun":        true,
	"espcms":        true,
	"live800":       true,
	"qibocms":       true,
	"qibomenhu":     true,
	"thinksns":      true,
	"turbomail":     true,
	"finecms":       true,
	"jcms":          true,
	"phpdisk":       true,
	"phpweb":        true,
	"yonyou nc":     true,
	"yongyou fe":    true,
	"i@Repor":       true,
	"i@Report":      true,
	"phpmyadmin":    true,
	"haitai oa":     true,
	"beidouxing oa": true,
	"threeoa":       true,
	"yijieoa":       true,
	"hcesoft oa":    true,
	"huatu oa":      true,
	"qiangzhi oa":   true,
	"BroadVision":   true,
	"edoas2":        true,
	"eoa":           true,
	"ip.board":      true,
	"khoa":          true,
	"oa169":         true,
	"qioa":          true,
	"zxoa":          true,
	"thinkphp":      true,
	"thinksaas":     true,
	"fckeditor":     true,
	"Discuz":        true,
}

func (vul *Vulnerability) loadRule(dir string) {
	if vul.VulXML == "" {
		log.Warnf("rule xml for `%s` is empty", vul.Name)
		return
	}
	if vul.NotUseXML {
		log.Infof("%s not need xml", vul.Name)
		return
	}

	filename := path.Join(dir, vul.VulXML)
	definition, err := loadVulXML(filename)
	if err != nil {
		log.Fatal("Failed to load rule xml", filename)
		return
	}
	vul.Rule = definition
}

func (vul *Vulnerability) getDescendants() []Vulnerability {
	descendants := make([]Vulnerability, 0, 10)
	if vul.VulXML != "BrowserXSS.xml" && vul.VulXML != "DomStoreXSS.xml" {
		if vul.VulXML != "" {
			descendants = append(descendants, *vul)
		}
		for i := range vul.Vulnerabilities {
			descendants = append(descendants, vul.Vulnerabilities[i].getDescendants()...)
		}
	}
	return descendants
}

type Module struct {
	Affects         string          `xml:"affects,attr"`
	Description     string          `xml:"description,attr"`
	DLL             string          `xml:"dll,attr"`
	ExeXML          string          `xml:"exe_xml,attr"`
	Name            string          `xml:"name,attr"`
	Vulnerabilities []Vulnerability `xml:"vulnerabilities>vulnerability"`
}

func (module *Module) getVulnerabilities() []Vulnerability {
	vuls := make([]Vulnerability, 0, 10)
	for i := range module.Vulnerabilities {
		vuls = append(vuls, module.Vulnerabilities[i].getDescendants()...)
	}
	return vuls
}

func (module *Module) loadRules(vulXmlsPath string) {
	filteredVuls := make([]Vulnerability, 0, 10)
	for _, vul := range module.getVulnerabilities() {
		vul.loadRule(vulXmlsPath)

		vul.DLL = module.DLL
		vul.Affects = module.Affects

		if vul.Rule == nil {

			if vul.Severity == "" {
				vul.Severity = "low"
			}
			filteredVuls = append(filteredVuls, vul)
		} else {
			metadata := vul.Rule.Metadata

			vul.Severity = metadata.Severity

			if metadata.Applicable.ThirdApp == "" {
				filteredVuls = append(filteredVuls, vul)
			} else {

				if _, exists := thirdAPPMap[metadata.Applicable.ThirdApp]; exists {
					filteredVuls = append(filteredVuls, vul)
				}
			}
		}
	}
	module.Vulnerabilities = filteredVuls
}

type All struct {
	Modules []Module `xml:"module"`
}

type BlueprintType map[string]map[string]*Module

func (blueprint BlueprintType) GetModules(affect string, dlls []string) []*Module {
	if modules, ok := blueprint[affect]; ok {
		var result []*Module
		if len(dlls) == 0 {
			result = make([]*Module, 0, len(modules))
			for _, module := range modules {
				result = append(result, module)
			}
		} else {
			result = make([]*Module, 0, len(dlls))
			for _, dll := range dlls {
				result = append(result, modules[dll])
			}
		}
		return result
	}
	return []*Module{}
}

func ParseAllXML(filename string) (*All, error) {
	f, err := os.Open(filename)
	if err != nil {
		log.Fatalln("Failed to open file:", filename)
		return nil, err
	}
	defer f.Close()
	buf, err := ioutil.ReadAll(f)
	if err != nil {
		log.Fatalln("Failed to read file:", filename)
		return nil, err
	}
	all := All{}
	err = xml.Unmarshal(buf, &all)
	if err != nil {
		return nil, err
	}
	return &all, nil
}

func LoadBlueprint(allXMLPath string, vulXmlsPath string) (BlueprintType, error) {
	all, err := ParseAllXML(allXMLPath)
	if err != nil {
		return nil, err
	}
	log.Infoln("load vuls ===========")
	bp := make(BlueprintType)
	for i := range all.Modules {
		module := &all.Modules[i]
		module.loadRules(vulXmlsPath)
		affects := module.Affects

		if bp[affects] == nil {
			bp[affects] = make(map[string]*Module)
		}
		bp[affects][module.DLL] = module
	}
	return bp, nil
}
