package share

import (
	"websec/rpc/codec"
	"websec/rpc/log"
	"websec/rpc/protocol"
)

var (
	Codecs = map[protocol.SerializeType]codec.Codec{
		protocol.SerializeNone: &codec.ByteCodec{},
		protocol.JSON:          &codec.JSONCodec{},
		protocol.ProtoBuffer:   &codec.PBCodec{},
		protocol.MsgPack:       &codec.MsgpackCodec{},
		protocol.Thrift:        &codec.ThriftCodec{},
	}
)

func RegisterCodec(t protocol.SerializeType, c codec.Codec) {
	Codecs[t] = c
}

type ContextKey string

var ReqMetaDataKey = ContextKey("__req_metadata")

var ResMetaDataKey = ContextKey("__res_metadata")

type Call struct {
	ServicePath   string            // The name of the service and method to call.
	ServiceMethod string            // The name of the service and method to call.
	Metadata      map[string]string //metadata
	ResMetadata   map[string]string
	Args          interface{} // The argument to the function (*struct).
	Reply         interface{} // The reply from the function (*struct).
	Error         error       // After completion, the error status.
	Done          chan *Call  // Strobes when call is complete.
	Raw           bool        // raw message or not
}

func (call *Call) CallDone() {
	select {
	case call.Done <- call:

	default:
		log.Debug("rpc: discarding Call reply due to insufficient Done chan capacity")

	}
}

type SeqKey struct{}
