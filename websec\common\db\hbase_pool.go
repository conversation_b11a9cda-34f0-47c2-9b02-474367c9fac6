package db

import (
	"sync"
	"websec/config"

	"github.com/tsuna/gohbase"
)

type hBaseManager struct {
	sessions map[string]gohbase.Client
	lock     *sync.RWMutex
}

func newHBaseManager() *hBaseManager {
	return &hBaseManager{
		sessions: make(map[string]gohbase.Client),
		lock:     new(sync.RWMutex),
	}
}

func (m *hBaseManager) Get(info config.HBaseConfig) gohbase.Client {
	m.lock.Lock()
	if session, ok := m.sessions[info.Addr]; ok {
		m.lock.Unlock()
		return session
	}
	m.lock.Unlock()

	c := gohbase.NewClient(info.Addr)
	m.lock.Lock()
	m.sessions[info.Addr] = c
	m.lock.Unlock()
	return c
}

var hBasePools = newHBaseManager()

func GetHBaseOriginal(info config.HBaseConfig) gohbase.Client {
	return hBasePools.Get(info)
}
