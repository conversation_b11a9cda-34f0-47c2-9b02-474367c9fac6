package detect

type Operation int

const (
	CheckBlackWords          Operation = 1
	CheckSensitiveWords                = 2
	CheckPageChange                    = 4
	CheckPrivateInfo                   = 8
	CheckUnsafeForms                   = 16
	CheckDirectoryList                 = 32
	CheckOceanLotus                    = 64
	CheckBTC                           = 128
	CheckSensitiveWordGroups           = 256
	Check<PERSON><PERSON><PERSON>                       = 512
	CheckTrojan                        = 1024
	CheckImageMagick                   = 2048
	CheckVuls                          = CheckPrivateInfo | CheckUnsafeForms | CheckDirectoryList | CheckOceanLotus | CheckBTC | CheckJenkins | CheckImageMagick
	CheckImage                         = 4096
)

const (
	VulPhoneDisclosure  string = "phone_disclosure.xml"
	VulIpDisclosure            = "ip_disclosure.xml"
	VulEmailDisclosure         = "email_disclosure.xml"
	VulUnsafeForms             = "User_credentials_are_sent_in_clear_text.xml"
	VulIdCardDisclosure        = "idcard_disclosure.xml"
	VulCsrfDisclosure          = "cross_site_request_forgery.xml"
	VulDirDisclosure           = "dir_disclosure.xml"
	VulNoDislay                = "vuls_hide.xml"
)

const (
	FileText   string = "text"
	FileScript        = "script"
)
