package db

import (
	"context"
	"sync"
	"time"
	"websec/config"
	"websec/utils/hbase"
	"websec/utils/log"

	"github.com/apache/thrift/lib/go/thrift"
)

type ThriftHBaseNode struct {
	Client        *hbase.HbaseClient
	transport     thrift.TTransport
	addr          string
	lastCheckTime int64
}

func (t *ThriftHBaseNode) Close() {
	t.transport.Close()
}

func (t *ThriftHBaseNode) MutateRow(tableName, row []byte, mutations []*hbase.Mutation) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var err error
	for i := 0; i < 3; i++ {
		err = t.Client.MutateRow(ctx, tableName, row, mutations, nil)
		if err != nil {
			log.Errorln("hbase MutateRow error:", err)
			if !t.checkError(err) {
				log.Errorln("MutateRow err:", err)
			}
		} else {
			break
		}
	}

	return err
}

func (t *ThriftHBaseNode) MutateRows(tableName []byte, rowBatches []*hbase.BatchMutation) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var err error
	for i := 0; i < 3; i++ {
		err = t.Client.MutateRows(ctx, tableName, rowBatches, nil)
		if err != nil {
			log.Errorln("hbase MutateRows error:", err)
			if !t.checkError(err) {
				log.Errorln("MutateRows err:", err)
			}
		} else {
			break
		}
	}

	return err
}

func (t *ThriftHBaseNode) Get(tableName, row, column []byte) ([]*hbase.TCell, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var r []*hbase.TCell
	var err error
	for i := 0; i < 3; i++ {
		r, err = t.Client.Get(ctx, tableName, row, column, nil)
		if err != nil {
			log.Errorln("hbase Get error:", err)
			if !t.checkError(err) {
				log.Errorln("Get err:", err)
			}
		} else {
			break
		}
	}

	return r, err
}

func (t *ThriftHBaseNode) checkError(errs error) bool {
	if time.Now().Unix() < t.lastCheckTime+5 {
		return false
	}

	t.lastCheckTime = time.Now().Unix()
	t.transport.Close()

	var err error
	for i := 0; i < 3; i++ {
		log.Infof("thrift client reconnect %s %d", t.addr, i+1)
		protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()
		t.transport, err = thrift.NewTSocket(t.addr)
		if err != nil {
			log.Errorln("error resolving address:", err)
			continue
		}

		t.transport = thrift.NewTBufferedTransport(t.transport, 20*1024*1024)
		t.Client = hbase.NewHbaseClientFactory(t.transport, protocolFactory)
		if err = t.transport.Open(); err != nil {
			log.Errorln(err)
			t.transport.Close()
			continue
		} else {
			break
		}
	}

	return true
}

func createNode(info config.HBaseConfig) (*ThriftHBaseNode, error) {
	node := new(ThriftHBaseNode)
	var err error
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()
	node.transport, err = thrift.NewTSocket(info.Addr)
	if err != nil {
		log.Errorln("error resolving address:", err)
		return nil, err
	}

	node.addr = info.Addr
	node.transport = thrift.NewTBufferedTransport(node.transport, 20*1024*1024)

	node.Client = hbase.NewHbaseClientFactory(node.transport, protocolFactory)
	if err = node.transport.Open(); err != nil {
		log.Errorln(err)
		return nil, err
	}

	return node, nil
}

type ThriftHBaseClient struct {
	pool       []*ThriftHBaseNode
	clientChan chan *ThriftHBaseNode
	poolSize   int
	config     config.HBaseConfig
}

func (c *ThriftHBaseClient) initNode() error {
	var err error
	var node *ThriftHBaseNode
	for i := 0; i < c.poolSize; i++ {
		for i := 0; i < 3; i++ {
			node, err = createNode(c.config)
			if err == nil {
				break
			}
		}

		if err != nil {
			return err
		}
		c.pool[i] = node
		c.clientChan <- node
	}

	return nil
}

func (c *ThriftHBaseClient) Get(tableName, row, column []byte) ([]*hbase.TCell, error) {
	node := <-c.clientChan
	defer func() {
		c.clientChan <- node
	}()
	return node.Get(tableName, row, column)
}

func (c *ThriftHBaseClient) MutateRows(tableName []byte, rowBatches []*hbase.BatchMutation) error {
	node := <-c.clientChan
	defer func() {
		c.clientChan <- node
	}()
	return node.MutateRows(tableName, rowBatches)
}

func (c *ThriftHBaseClient) MutateRow(tableName, row []byte, mutations []*hbase.Mutation) error {
	node := <-c.clientChan
	defer func() {
		c.clientChan <- node
	}()
	return node.MutateRow(tableName, row, mutations)
}

func (c *ThriftHBaseClient) Close() {
	for _, v := range c.pool {
		if v != nil {
			v.Close()
		}
	}
}

type hBaseThriftPool struct {
	clients map[string]*ThriftHBaseClient
	lock    *sync.RWMutex
}

func newHBaseThriftPool() *hBaseThriftPool {
	return &hBaseThriftPool{
		clients: make(map[string]*ThriftHBaseClient),
		lock:    new(sync.RWMutex),
	}
}

func getHBaseClient(info config.HBaseConfig) (*ThriftHBaseClient, error) {
	client := &ThriftHBaseClient{
		poolSize:   info.Concurrency,
		pool:       make([]*ThriftHBaseNode, info.Concurrency),
		clientChan: make(chan *ThriftHBaseNode, info.Concurrency),
		config:     info,
	}

	err := client.initNode()
	if err != nil {
		return nil, err
	}
	return client, nil
}

func (m *hBaseThriftPool) Get(info config.HBaseConfig) (*ThriftHBaseClient, error) {
	m.lock.Lock()
	if client, ok := m.clients[info.Addr]; ok {
		m.lock.Unlock()
		return client, nil
	}
	m.lock.Unlock()

	c, err := getHBaseClient(info)
	if err != nil {
		return nil, err
	}

	m.lock.Lock()
	m.clients[info.Addr] = c
	m.lock.Unlock()
	return c, nil
}

var hBaseThriftClients = newHBaseThriftPool()

func GetHBaseThrift(info config.HBaseConfig) (*ThriftHBaseClient, error) {
	return hBaseThriftClients.Get(info)
}
