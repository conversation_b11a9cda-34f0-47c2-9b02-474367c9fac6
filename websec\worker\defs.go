package main

type RunMode = string

const (
	ModeWorker            RunMode = "worker"
	ModeScheduler                 = "scheduler"
	ModeStandalone                = "standalone"
	ModeCrawler                   = "crawler"
	ModeScanner                   = "scanner"
	ModeScanSingle                = "scanner_single"
	ModeOuterPageSaver            = "outerPageSaver"
	ModeChromeServer              = "chromeServer"
	ModeOcrServer                 = "ocrServer"
	ModeAPI                       = "api"
	ModeSearchCrawler             = "searchCrawler"
	ModeStandaloneCrawler         = "standaloneCrawler"
	ModeXSSCollect                = "xss_collect"
)

const (
	HomepageStandaloneCrawler   string = "homepage"
	SecondpageStandaloneCrawler        = "secondpage"
)

const (
	NotUseChrome int64 = 1
)
