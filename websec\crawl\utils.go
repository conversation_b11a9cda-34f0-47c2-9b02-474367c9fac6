package crawl

import (
	"crypto/tls"
	"fmt"
	"net"
	"net/url"
	"regexp"
	"strings"
	"time"
	"unicode"

	"github.com/PuerkitoBio/goquery"
	"github.com/valyala/fasthttp"
	"golang.org/x/net/publicsuffix"
)

var StaticElemTag = map[string]bool{
	"link": true, "script": true, "img": true, "embed": true,
	"param": true, "audio": true, "video": true, "source": true,
}

var ignoredPrefix = []string{
	"data:", "\"data:", "javascript:", "mailto:",
}

var IgnoredExts = map[string]bool{
	"rar": true, "zip": true, "gz": true, "tar": true, "tgz": true, "cab": true, "7z": true, "bz": true, "bz2": true,
	"pdf": true, "ps": true, "doc": true, "docx": true, "ppt": true, "pptx": true, "xls": true, "xlsx": true, "rtf": true,
	"dot": true, "mpp": true, "mpt": true, "mpd": true, "mdb": true, "csv": true, "pps": true, "ppa": true, "dif": true,
	"rmvb": true, "avi": true, "mpg": true, "mpeg": true, "mov": true, "movie": true, "rm": true, "asf": true, "mpe": true,
	"asx": true, "m1v": true, "mpa": true, "wmv": true, "wav": true, "mp3": true, "ra": true, "au": true, "aiff": true,
	"mpga": true, "wma": true, "mid": true, "midi": true, "rmi": true, "m3u": true, "exe": true, "swf": true, "tif": true,
	"wmf": true, "tiff": true, "ico": true, "icon": true, "pcx": true, "dll": true, "apk": true, "jar": true, "sisx": true,
	"css": true, "mp4": true, // "jpeg": true, "png": true, "gif": true, ".bmp": true, "jpg": true,
}
var PictureExts = map[string]bool{
	"jpeg": true, "png": true, "gif": true, ".bmp": true, "jpg": true,
}

var linkAttr = []string{
	"action", "archive", "background", "cite", "classid",
	"codebase", "data", "href", "longdesc", "profile",
	"src", "usemap", "dynsrc", "lowsrc",
}

var BlockedHost = map[string]bool{
	"pos.baidu.com": true, "open.qzone.qq.com": true, "a1.alicdn.com": true,
	"adservice.google.com": true, "fonts.googleapis.com": true, "www.google-analytics.com": true,
	"fonts.gstatic.com": true,
}

func newHTTPClient(name string) *fasthttp.Client {
	return &fasthttp.Client{
		Name:                          name,
		ReadTimeout:                   20 * time.Second,
		WriteTimeout:                  20 * time.Second,
		MaxResponseBodySize:           1024 * 1024 * 2,
		DisableHeaderNamesNormalizing: true,
		Dial: func(addr string) (net.Conn, error) {
			return fasthttp.DialTimeout(addr, 20*time.Second)
		},
		TLSConfig: &tls.Config{

			InsecureSkipVerify: true,
		},
	}
}

var BlockedRegDomain = map[string]bool{
	"doubleclick.net": true, "googlesyndication.com": true,
}

func URLJoin(baseURL *url.URL, ref string) string {
	refURL, err := url.Parse(ref)
	if err != nil {
		return ""
	}

	return baseURL.ResolveReference(refURL).String()
}

func UnQuote(s string) string {
	if !HasLetterOrNumber(s) {

		return ""
	}

	if strings.HasPrefix(s, "\"") && strings.HasSuffix(s, "\"") || strings.HasPrefix(s, "'") && strings.HasSuffix(s, "'") {
		return s[1 : len(s)-1]
	}
	return s
}

type ExtractLink struct {
	TagName string
	URL     string
}

var (
	reRefresh    = regexp.MustCompile(`(?i)[^;=]*;\s*(?:url\s*=\s*)?(?P<url>.*)$`)
	reCSSURLS    = regexp.MustCompile(`url\(('+'["][^"]*["]|'+"['][^']*[']|[^)]*)\)`)
	reCSSImports = regexp.MustCompile(`@import "(.*?)"`)
	reLocation   = regexp.MustCompile(`location.href.+?=.+?"(.*?)"`)
	reJsessionid = regexp.MustCompile(`;jsessionid=[a-zA-Z0-9-]*`)
)

func ExtractLinksFromDoc(doc *goquery.Document, baseURL *url.URL) chan *ExtractLink {
	extractLinksChan := make(chan *ExtractLink, 20)
	go extractLinks(doc, baseURL, extractLinksChan)
	return extractLinksChan
}

func extractLinks(doc *goquery.Document, baseURL *url.URL, extractLinksChan chan *ExtractLink) {
	doc.Find("*").Each(func(i int, s *goquery.Selection) {
		tagName := goquery.NodeName(s)

		if tagName == "noscript" {
			s.SetHtml(s.Text())
			s.Find("object").Each(func(i int, sc *goquery.Selection) {
				codebase := sc.AttrOr("codebase", "")
				var codebaseURL *url.URL

				if codebase != "" {
					codebaseURL, _ = url.Parse(codebase)
					writeLinkToChan(tagName, codebase, baseURL, extractLinksChan)
				}

				sc.Find("embed").Each(func(i int, sc *goquery.Selection) {
					v := sc.AttrOr("src", "")
					if v != "" {
						if v = URLJoin(baseURL, v); v == "" {
							return
						}
						writeLinkToChan(tagName, v, baseURL, extractLinksChan)
					}

				})

				sc.Find("param[name='movie']").Each(func(i int, sc *goquery.Selection) {
					v := sc.AttrOr("value", "")
					if v != "" {
						if codebaseURL != nil {
							if v = URLJoin(codebaseURL, v); v == "" {
								return
							}
						}
						writeLinkToChan(tagName, v, baseURL, extractLinksChan)
					}

				})

				sc.Find("param[name='src']").Each(func(i int, sc *goquery.Selection) {
					v := sc.AttrOr("value", "")
					if v != "" {
						if codebaseURL != nil {
							if v = URLJoin(codebaseURL, v); v == "" {
								return
							}
						}
						writeLinkToChan(tagName, v, baseURL, extractLinksChan)
					}
				})
			})
		}

		if tagName == "object" {
			codebase := s.AttrOr("codebase", "")
			var codebaseURL *url.URL

			if codebase != "" {
				codebaseURL, _ = url.Parse(codebase)
				writeLinkToChan(tagName, codebase, baseURL, extractLinksChan)
			}

			for _, attr := range []string{"classid", "data"} {
				v := s.AttrOr(attr, "")
				if v != "" {
					if codebaseURL != nil {
						if v = URLJoin(codebaseURL, v); v == "" {
							continue
						}
					}
					writeLinkToChan(tagName, v, baseURL, extractLinksChan)
				}
			}

			archive := s.AttrOr("archive", "")
			if archive != "" {
				archives := strings.Split(archive, " ")
				for _, arch := range archives {
					arch = strings.TrimSpace(arch)
					if arch != "" {
						if codebaseURL != nil {
							if arch = URLJoin(codebaseURL, arch); arch == "" {
								continue
							}
						}
						writeLinkToChan(tagName, arch, baseURL, extractLinksChan)
					}
				}
			}

			s.Find("embed").Each(func(i int, sc *goquery.Selection) {
				v := sc.AttrOr("src", "")
				if v != "" {
					if v = URLJoin(baseURL, v); v == "" {
						return
					}
					writeLinkToChan(tagName, v, baseURL, extractLinksChan)
				}

			})

			s.Find("param[name='movie']").Each(func(i int, sc *goquery.Selection) {
				v := sc.AttrOr("value", "")
				if v != "" {
					if codebaseURL != nil {
						if v = URLJoin(codebaseURL, v); v == "" {
							return
						}
					}
					writeLinkToChan(tagName, v, baseURL, extractLinksChan)
				}

			})

			s.Find("param[name='src']").Each(func(i int, sc *goquery.Selection) {
				v := sc.AttrOr("value", "")
				if v != "" {
					if codebaseURL != nil {
						if v = URLJoin(codebaseURL, v); v == "" {
							return
						}
					}
					writeLinkToChan(tagName, v, baseURL, extractLinksChan)
				}
			})

		} else {
			for _, attr := range linkAttr {
				v := s.AttrOr(attr, "")
				if v != "" {
					writeLinkToChan(tagName, v, baseURL, extractLinksChan)
				}
			}
		}

		if tagName == "meta" {
			httpEquiv := s.AttrOr("http-equiv", "")
			httpEquiv = strings.ToLower(httpEquiv)
			if httpEquiv == "refresh" {
				content := s.AttrOr("content", "")
				match := reRefresh.FindStringSubmatch(content)
				if len(match) > 1 {
					u := match[1]
					u = UnQuote(u)
					writeLinkToChan(tagName, u, baseURL, extractLinksChan)
				}
			}
		} else if tagName == "param" {
			valueType := s.AttrOr("valuetype", "")
			valueType = strings.ToLower(valueType)
			if valueType == "ref" {
				value, _ := s.Attr("value")
				value = UnQuote(value)
				writeLinkToChan(tagName, value, baseURL, extractLinksChan)
			}
		} else if tagName == "style" {
			text := s.Text()

			if text != "" {
				match := reCSSURLS.FindAllStringSubmatch(text, -1)
				if match != nil {
					for _, item := range match {
						if len(item) > 1 {
							u := item[1]
							u = UnQuote(u)
							writeLinkToChan(tagName, u, baseURL, extractLinksChan)
						}
					}
				}

				match = reCSSImports.FindAllStringSubmatch(text, -1)
				if match != nil {
					for _, item := range match {
						if len(item) > 1 {
							u := item[1]
							u = UnQuote(u)
							writeLinkToChan(tagName, u, baseURL, extractLinksChan)
						}
					}
				}
			}
		} else if tagName == "script" {
			outerHTML, err := goquery.OuterHtml(s)
			if err == nil {
				match := reLocation.FindAllStringSubmatch(outerHTML, -1)
				if match != nil {
					for _, item := range match {
						if len(item) > 1 {
							u := item[1]
							u = UnQuote(u)
							writeLinkToChan(guessTagName(u), u, baseURL, extractLinksChan)
						}
					}
				}
			}
		} else if tagName == "img" {

		}

		if s.AttrOr("style", "") != "" {
			text, _ := s.Attr("style")
			match := reCSSURLS.FindAllStringSubmatch(text, -1)
			if match != nil {
				for _, item := range match {
					if len(item) > 1 {
						u := item[1]
						u = UnQuote(u)
						writeLinkToChan(tagName, u, baseURL, extractLinksChan)
					}
				}
			}
		}
	})
	close(extractLinksChan)
}

func writeLinkToChan(tagName, rawURL string, baseURL *url.URL, ch chan *ExtractLink) {
	rawURL = strings.TrimSpace(rawURL)
	if len(rawURL) == 0 {
		return
	}

	if rawURL = URLJoin(baseURL, rawURL); rawURL != "" {
		if len(reJsessionid.FindAllString(rawURL, 1)) > 0 {
			rawURL = reJsessionid.ReplaceAllString(rawURL, "") // 去除url中jsessionid
		}

		if extrackLinkCheck(tagName, rawURL) {
			link := &ExtractLink{tagName, rawURL}
			ch <- link
		}
	}
}

func extrackLinkCheck(tagName, rawURL string) bool {
	if len(rawURL) > 1024 || tagName == "v:imagedata" {
		return false
	}

	for i := range ignoredPrefix {
		if strings.HasPrefix(rawURL, ignoredPrefix[i]) {
			return false
		}
	}

	if tagName == "img" && strings.Contains(rawURL, "data:") {
		return false
	}

	return true
}

func guessTagName(rawURL string) string {
	if len(rawURL) > 1024 {
		return "link"
	}

	parts, err := url.Parse(rawURL)
	if err != nil {
		return "link"
	}

	pathParts := strings.Split(parts.Path, ".")

	if len(pathParts) > 1 {
		tail := pathParts[len(pathParts)-1]

		if _, ok := PictureExts[tail]; ok {
			return "img"
		}

		if _, ok := IgnoredExts[tail]; ok {
			return "link"
		}
	}

	return "a"
}

func IsValidURL(u string) bool {
	if len(u) > 1024 {
		return false
	}

	if !strings.HasPrefix(u, "http://") && !strings.HasPrefix(u, "https://") {
		return false
	}

	parts, err := url.Parse(u)
	if err != nil {
		return false
	}

	if parts.Host == "www.csa-wxpo.com" && parts.Path == "/ChangeLanguage" {
		return false
	}

	pathParts := strings.Split(parts.Path, "/")
	if len(pathParts) > 0 {
		ext := pathParts[len(pathParts)-1]
		extParts := strings.Split(ext, ".")
		if len(extParts) > 0 {
			ext = extParts[len(extParts)-1]
			ext = strings.ToLower(ext)
			if _, ok := IgnoredExts[ext]; ok {
				return false
			}
		}
	}
	return true
}

func IsDomainEqual(domain, u string) bool {
	parts, err := url.Parse(u)
	if err != nil {
		return false
	}
	host := parts.Host
	port := parts.Port()
	if port == "80" || port == "443" {
		host = parts.Hostname()
	}
	return domain == host
}

func IsInternalLink(domain, u string) bool {
	if IsValidURL(u) {
		return IsDomainEqual(domain, u)
	}
	return false
}

func IsOuterLink(domain, u string) bool {
	if IsValidURL(u) {
		return !IsDomainEqual(domain, u)
	}
	return false
}

func IsInMap(s string, m map[string]bool) bool {
	if _, ok := m[s]; ok {
		return true
	}
	return false
}

func NoSchemeTailURL(u string) string {
	if strings.HasPrefix(u, "http://") {
		u = u[7:]
	}

	if strings.HasPrefix(u, "https://") {
		u = u[8:]
	}

	if strings.HasSuffix(u, "/") {
		u = u[:len(u)-1]
	}
	return u
}

func GetDomain(u string) (string, error) {
	linkParts, err := url.Parse(u)
	if err != nil {
		return "", err
	}
	domain := linkParts.Host
	return domain, nil
}

func IsBlocked(u string) bool {
	parts, err := url.Parse(u)
	if err != nil {
		return true
	}

	host := parts.Host
	if _, ok := BlockedHost[host]; ok {
		return true
	}

	regDomain, err := publicsuffix.EffectiveTLDPlusOne(host)
	if err != nil {
		return true
	}
	if _, ok := BlockedRegDomain[regDomain]; ok {
		return true
	}
	return false
}

func GetRedirectURL(baseURL string, location []byte) string {
	u := fasthttp.AcquireURI()
	u.Update(baseURL)
	u.UpdateBytes(location)
	redirectURL := u.String()
	fasthttp.ReleaseURI(u)
	return redirectURL
}

func HasLetterOrNumber(s string) bool {
	for _, r := range s {
		if unicode.IsLetter(r) || unicode.IsNumber(r) || string(r) == "/" {
			return true
		}
	}
	return false
}

var urlPatternS string = `(^(http|https)://)(www.)?[a-zA-Z0-9@:%._\\+~#?&//=]{2,256}\.[a-z]{2,6}([-a-zA-Z0-9@:%._\\+~#?&//=]*)`
var urlPattern = regexp.MustCompile(urlPatternS)
var urlIpPatterns string = `(((http|https)://)((2[0-4]\d|25[0-5])|[0-1]?\d{0,2})(\.((2[0-4]\d|25[0-5])|[0-1]?\d{0,2})){3}(:(6553[0-5]|655[0-2]\d|65[0-4]\d{2}|6[0-4]\d{3}|[1-5]\d{4}|[1-9]\d{1,3}|[1-9]){1})?)`
var urlIpPattern = regexp.MustCompile(urlIpPatterns)

func findAllURLsInContent(content string, pattern *regexp.Regexp) []string {
	urls := make([]string, 0)
	for len(content) > 0 {
		l := strings.Index(content, "http://")
		if l == -1 {
			l = strings.Index(content, "https://")
			if l == -1 {
				return urls
			}
		}

		var tmpS string
		STEP := 256
		if len(content)-l < STEP {
			tmpS = content[l:]
		} else {
			tmpS = content[l : l+STEP]
		}
		u := pattern.Find([]byte(tmpS))
		if len(u) > 0 {
			urls = append(urls, string(u[:]))
			content = content[l+len(u):]
		} else {
			content = content[l+1:]
		}
	}
	return urls
}

func findAllOuterURLs(content string, myDomain string) []string {
	start := time.Now().UTC()
	outerURLs := make([]string, 0)

	regexs := []*regexp.Regexp{urlPattern, urlIpPattern}
	for _, pattern := range regexs {
		urls := findAllURLsInContent(content, pattern)
		for i := range urls {
			s := urls[i]
			u, err := url.Parse(s)
			if err != nil {
				continue
			}

			regDomain, err := publicsuffix.EffectiveTLDPlusOne(u.Host)
			if err != nil {
				continue
			}

			if regDomain != myDomain {
				outerURLs = append(outerURLs, s)
			}
		}
	}

	end := time.Now().UTC()
	fmt.Printf("parse urls cost: %d ms\n", end.Sub(start).Microseconds())
	return outerURLs
}
