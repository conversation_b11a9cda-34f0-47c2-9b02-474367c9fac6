package config

import (
	"flag"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-ini/ini"
)

type schedulerConfig struct {
	Host                string
	Port                uint16
	MongoDB             string
	MySQL               string
	FetchCount          int
	FetchInterval       time.Duration
	MaxTaskCount        int
	Tag                 string
	DispatchConcurrency int64
}

type ElasticsearchConfig struct {
	URLs     []string
	Username string
	Password string
}

type RedisConfig struct {
	Host     string
	Port     uint16
	Password string
	Database int
}

type MongoConfig struct {
	URI string
}

type MySQLConfig struct {
	URI string
}

type KafkaConfig struct {
	Brokers              string // 服务器内部使用的kafka
	MessageMaxBytes      int    // broker能接收消息的最大字节数
	FetchMessageMaxBytes int    // 消费者能读取的最大消息,这个值应该大于或等于message.max.bytes
}

type ChromeConfig struct {
	Host string
	Port uint16
}

type periodConfig struct {
	BlackSensitiveWords     time.Duration
	IgnoredBlackLinkDomains time.Duration
}

type DetecterConfig struct {
	SafeBrowsingURL             string
	JSUnpackPath                string
	TrojanRulePath              string
	WangDunBlackLinkDetecterURL string
}

type OcrConfig struct {
	Concurrency   int64
	OcrAPIAddress string
	OcrAPIKey     string
	OcrAppKey     string
}

type SensitiveImageConfig struct {
	Concurrency int64
}

type SwfConfig struct {
	Concurrency int64
	JpexURL     string
}

type ThirdPartyScanner struct {
	WeakCheckURL  string
	PythonScripts string
}

type scannerConfig struct {
	BlueprintPath        string
	VulsPath             string
	SqlmapPath           string
	SqlmapScreenshotPath string
	XSSJSPath            string
	XSSJSScreenshotPath  string
	CaptchaService       string
	XssPayloadPath       string

	Method  string
	Affects []string
	XMLs    []string
}

type JiebaConfig struct {
	DictPath      string
	HMMPath       string
	UserDictPath  string
	IDFPath       string
	StopWordsPath string
}

type webscanConfig struct {
	Host          string
	SQLInjectPort uint16
}

type crawlerConfig struct {
	ChromeClientPorts            string
	ChromeConcurrency            int32
	ChromeServerPort             uint16
	InjectJSPath                 string
	StandaloneConcurrency        int64
	StandaloneRequestConcurrency int64
	HomePageCycle                float64
	SecondPageCycle              float64
}

type consumerConfig struct {
	GeneratorConcurrency int64
	FilterConcurrency    int64
	CollectorConcurrency int64
	SaveDepth            int
	SourceCodeSaveTime   int
	NewURL               bool
}

type HBaseConfig struct {
	Addr        string
	ClientType  int // 0 原生hbase  1 thrift连接hbase 2 不使用hbase
	Concurrency int //不读配置，默认5个，可以在使用的地方修改，一般不要修改默认值
}

type apiConfig struct {
	Addr                     string //我们提供给平台的api地址
	SensitiveWordPredictAddr string //敏感词过滤API地址，禹庆华提供
}

type SnapshotConfig struct {
	ChromeEndpoint    string // http://127.0.0.1:9222/json
	PhantomjsConfig   string
	PhantomjsScript   string // render.js
	TaskTimeout       int
	Parallel          int
	MaxMessageKBytes  int
	QueuedMinMessages int
	ChromeAddr        string
	ChromeSema        int64
	IgnoredHosts      string
}

type S3BucketConfig struct {
	Endpoint   string
	SSL        bool
	Region     string
	AccessKey  string
	SecretKey  string
	BucketName string
	ACL        string
	URLPrefix  string
	MaxRetry   int
	BucketType int
	LocalDir   string
}

type LogSetting struct {
	Dir      string
	FileName string
}

type Config struct {
	Scheduler schedulerConfig

	MongoDB        MongoConfig
	Elastic        ElasticsearchConfig
	Redis          RedisConfig
	Kafka          KafkaConfig
	PlatformKafka  KafkaConfig
	Chrome         ChromeConfig
	Period         periodConfig
	Detecter       DetecterConfig
	Scanner        scannerConfig
	Jieba          JiebaConfig
	Webscan        webscanConfig
	Crawler        crawlerConfig
	Consumer       consumerConfig
	HBase          HBaseConfig
	API            apiConfig
	Snapshot       SnapshotConfig
	S3Bucket       S3BucketConfig
	Ocr            OcrConfig
	SensitiveImage SensitiveImageConfig
	SwfCheck       SwfConfig
	ThirdPartyScan ThirdPartyScanner
	RunMode        string
	Target         string
	UserAgent      string
	MonitorType    string
	CollectorType  string
	DebugHost      string
	CrawlerType    string
	CrawlerOffset  int64

	SpecificXml []string
	ScanUrl     string
	UrlMethod   string
	LinkAffect  string
	AssetIDStr  string

	BaizeXapikey string

	MySQL MySQLConfig

	NmapHydraService string
	Log              LogSetting
}

func ParseConfig() (*Config, error) {
	config := &Config{}

	var specialXml, scanUrl, method, linkAffect, assetIDStr string
	flag.StringVar(&specialXml, "xml", "", "vul_xml")
	flag.StringVar(&scanUrl, "url", "", "scanUrl")
	flag.StringVar(&method, "m", "GET", "request method")
	flag.StringVar(&linkAffect, "affect", "", "linkAffect")
	flag.StringVar(&assetIDStr, "asset_id", "", "asset_id")

	iniPtr := flag.String("ini", "/opt/projects/websec/config/dev.ini", "specify the path of config file.")
	runModePtr := flag.String("run", "worker", "which role should run as. current supported modes: scheduler/worker/standalone/crawler/scanner.")
	uaPtr := flag.String("user-agent", "Mozilla/5.0 (Windows NT 6.2; rv:30.0) Gecko/20150101 Firefox/32.0", "user agent")
	methodPtr := flag.String("method", "GET", "http request method.")
	monitorTypePtr := flag.String("monitor-type", "FULL", "monitor type: FULL/SMART/PAGE_CHANGE/BLACK_SENSITIVE.")
	targetPtr := flag.String("target", "", "target site.")
	affectsPtr := flag.String("affects", "", "affects (seperated by comma).")
	xmlsPtr := flag.String("xmls", "", "specific xmls (separated by comma).")
	collectorTypePtr := flag.String("collector-type", "all", "collector type: kafka/mongo/hbase/all")
	debugHost := flag.String("debug", "", "pprof debug host,127.0.0.1:6060")
	chromeAddr := flag.String("chrome-addr", "127.0.0.1:9888", "snapshot connect chrome debug address")
	crawlerType := flag.String("crawler-type", "homepage", "specify standalone crawler type.")
	crawlerOffset := flag.String("crawler-offset", "0", "specify standalone secondpage crawler asset offset.")

	logDir := flag.String("log-dir", "logs", "zap log save dir.")
	logName := flag.String("log-name", "backend.log", "log file name")

	flag.Parse()

	fmt.Println("parsing config file:", *iniPtr)

	cfg, err := ini.Load(*iniPtr)
	if err != nil {
		return config, err
	}

	config.SpecificXml = []string{specialXml}
	config.ScanUrl = scanUrl
	config.UrlMethod = method
	config.LinkAffect = linkAffect
	config.AssetIDStr = assetIDStr

	config.Scheduler.Host = cfg.Section("scheduler").Key("host").MustString("127.0.0.1")
	config.Scheduler.Port = uint16(cfg.Section("scheduler").Key("port").MustUint(8800))
	config.Scheduler.MongoDB = cfg.Section("scheduler").Key("mongodb").MustString("mongodb://127.0.0.1:27017/qunjiance")
	config.Scheduler.MySQL = cfg.Section("scheduler").Key("mysql").MustString("xxxx://127.0.0.1:3306/xxxxxx")
	config.Scheduler.FetchCount = cfg.Section("scheduler").Key("fetch_count").MustInt(1000)
	config.Scheduler.FetchInterval = time.Duration(cfg.Section("scheduler").Key("fetch_interval").MustInt(10)) * time.Second
	config.Scheduler.MaxTaskCount = cfg.Section("scheduler").Key("max_taskcount").MustInt(400)
	config.Scheduler.Tag = cfg.Section("scheduler").Key("tag").MustString("default")
	config.Scheduler.DispatchConcurrency = cfg.Section("scheduler").Key("dispatch_concurrency").MustInt64(20)

	config.MongoDB.URI = cfg.Section("mongodb").Key("uri").MustString("")

	config.Elastic.URLs = strings.Split(cfg.Section("es").Key("urls").MustString("http://127.0.0.1:9200"), ",")
	config.Elastic.Username = cfg.Section("es").Key("username").MustString("")
	config.Elastic.Password = cfg.Section("es").Key("password").MustString("")

	config.Redis.Host = cfg.Section("redis").Key("host").MustString("127.0.0.1")
	config.Redis.Port = uint16(cfg.Section("redis").Key("port").MustUint(6379))
	config.Redis.Password = cfg.Section("redis").Key("password").MustString("")
	config.Redis.Database = cfg.Section("redis").Key("Database").MustInt(0)

	config.Kafka.Brokers = cfg.Section("kafka").Key("brokers").MustString("localhost:9092")
	config.Kafka.MessageMaxBytes = cfg.Section("kafka").Key("message_max_bytes").MustInt(5120000)
	config.Kafka.FetchMessageMaxBytes = cfg.Section("kafka").Key("fetch_message_max_bytes").MustInt(5242880)
	config.PlatformKafka.Brokers = cfg.Section("out_kafka").Key("brokers").MustString("localhost:9092")
	config.PlatformKafka.MessageMaxBytes = cfg.Section("out_kafka").Key("message_max_bytes").MustInt(5120000)
	config.PlatformKafka.FetchMessageMaxBytes = cfg.Section("out_kafka").Key("fetch_message_max_bytes").MustInt(5242880)

	config.Chrome.Host = cfg.Section("chrome").Key("host").MustString("localhost")
	config.Chrome.Port = uint16(cfg.Section("chrome").Key("port").MustUint(19224))

	config.Period.BlackSensitiveWords = time.Duration(cfg.Section("period").Key("black_sensitive").MustInt(86400)) * time.Second
	config.Period.IgnoredBlackLinkDomains = time.Duration(cfg.Section("period").Key("ignored_blacklink_domains").MustInt(86400)) * time.Second

	config.Detecter.SafeBrowsingURL = cfg.Section("detecter").Key("safe_browsing_url").MustString("http://127.0.0.1:8080/v4/threatMatches:find")
	config.Detecter.JSUnpackPath = cfg.Section("detecter").Key("jsunpack_path").MustString("/opt/projects/jsunpack-n/")
	config.Detecter.TrojanRulePath = cfg.Section("detecter").Key("trojan_rule_path").MustString("/opt/projects/websec/detect_js_Trojan.json")
	config.Detecter.WangDunBlackLinkDetecterURL = cfg.Section("detecter").Key("wangdun_blacklink_api").MustString("")

	config.Scanner.BlueprintPath = cfg.Section("scanner").Key("blueprint_path").MustString("/opt/projects/websec/xml/All.xml")
	config.Scanner.VulsPath = cfg.Section("scanner").Key("vuls_xml_dir").MustString("/opt/projects/websec/xml/vuls/")
	config.Scanner.SqlmapPath = cfg.Section("scanner").Key("sqlmap_path").MustString("/opt/projects/sqlmap/")
	config.Scanner.SqlmapScreenshotPath = cfg.Section("scanner").Key("sqlmap_screenshot_path").MustString("/opt/projects/websec/websec/scanner/js/shell.js")
	config.Scanner.XSSJSScreenshotPath = cfg.Section("scanner").Key("xss_js_screenshot_path").MustString("/opt/projects/websec/websec/scanner/js/xss4.js")
	config.Scanner.CaptchaService = cfg.Section("scanner").Key("captcha_service").MustString("http://***********:8090/predict")
	config.Scanner.XssPayloadPath = cfg.Section("scanner").Key("xss_payload_path").MustString("/opt/projects/websec/config/payload.json")

	config.Jieba.DictPath = cfg.Section("jieba").Key("dict_path").MustString("/opt/projects/websec/jieba/jieba.dict.utf8")
	config.Jieba.HMMPath = cfg.Section("jieba").Key("hmm_path").MustString("/opt/projects/websec/jieba/hmm_model.utf8")
	config.Jieba.UserDictPath = cfg.Section("jieba").Key("user_dict_path").MustString("/opt/projects/websec/jieba/user.dict.utf8")
	config.Jieba.IDFPath = cfg.Section("jieba").Key("idf_path").MustString("/opt/projects/websec/jieba/idf.utf8")
	config.Jieba.StopWordsPath = cfg.Section("jieba").Key("stop_words_path").MustString("/opt/projects/websec/jieba/stop_words.utf8")

	config.Webscan.Host = cfg.Section("webscan").Key("host").MustString("127.0.0.1")
	config.Webscan.SQLInjectPort = uint16(cfg.Section("webscan").Key("pangolin_port").MustUint(45500))

	config.Crawler.ChromeClientPorts = cfg.Section("crawler").Key("chrome_client_ports").MustString("9225")
	config.Crawler.ChromeConcurrency = int32(cfg.Section("crawler").Key("chrome_concurrency").MustInt(20))
	config.Crawler.ChromeServerPort = uint16(cfg.Section("crawler").Key("chrome_server_port").MustUint(19222))
	config.Crawler.InjectJSPath = cfg.Section("crawler").Key("inject_javascript_path").MustString("/opt/projects/websec/chrome/inject.js")
	config.Crawler.StandaloneConcurrency = int64(cfg.Section("crawler").Key("standalone_concurrency").MustInt(100))
	config.Crawler.StandaloneRequestConcurrency = int64(cfg.Section("crawler").Key("standalone_request_concurrency").MustInt(2))
	config.Crawler.HomePageCycle = cfg.Section("crawler").Key("homepage_cycle").MustFloat64(300.0)
	config.Crawler.SecondPageCycle = cfg.Section("crawler").Key("secondpage_cycle").MustFloat64(900.0)

	config.Consumer.GeneratorConcurrency = cfg.Section("consumer").Key("generator_concurrency").MustInt64(40)
	config.Consumer.FilterConcurrency = cfg.Section("consumer").Key("filter_concurrency").MustInt64(40)
	config.Consumer.CollectorConcurrency = cfg.Section("consumer").Key("collector_concurrency").MustInt64(40)
	config.Consumer.SaveDepth = cfg.Section("consumer").Key("save_depth").MustInt(10)
	config.Consumer.SourceCodeSaveTime = cfg.Section("consumer").Key("sourcecode_savetime").MustInt(300)
	config.Consumer.NewURL = cfg.Section("consumer").Key("newurl").MustBool(true)

	config.HBase.Addr = cfg.Section("hbase").Key("addr").MustString("127.0.0.1")
	config.HBase.ClientType = cfg.Section("hbase").Key("client_type").MustInt(1)
	config.HBase.Concurrency = 5
	config.API.Addr = cfg.Section("api").Key("addr").MustString(":9090")
	config.API.SensitiveWordPredictAddr = cfg.Section("api").Key("sensitiveword_predict").MustString("http://***********:8060/api/predict2")

	config.RunMode = *runModePtr
	config.Target = *targetPtr
	config.MonitorType = strings.ToUpper(*monitorTypePtr)
	config.UserAgent = *uaPtr
	config.DebugHost = *debugHost
	config.Scanner.Method = *methodPtr
	if *affectsPtr != "" {
		config.Scanner.Affects = strings.Split(*affectsPtr, ",")
	}
	if *xmlsPtr != "" {
		config.Scanner.XMLs = strings.Split(*xmlsPtr, ",")
	}
	config.CollectorType = *collectorTypePtr
	config.CrawlerType = *crawlerType
	config.CrawlerOffset, err = strconv.ParseInt(*crawlerOffset, 10, 64)
	if err != nil {
		return config, err
	}

	config.Snapshot.ChromeEndpoint = cfg.Section("snapshot").Key("chrome_endpoint").MustString("")   //"http://127.0.0.1:9222/json"
	config.Snapshot.PhantomjsConfig = cfg.Section("snapshot").Key("phantomjs_config").MustString("") //"/opt/projects/websec/js/phantomjs/config.json"
	config.Snapshot.PhantomjsScript = cfg.Section("snapshot").Key("phantomjs_script").MustString("") //"/opt/projects/websec/js/phantomjs/render_singlepage.js"
	config.Snapshot.TaskTimeout = cfg.Section("snapshot").Key("task_timeout").MustInt(10)
	config.Snapshot.Parallel = cfg.Section("snapshot").Key("parallel").MustInt(20)
	config.Snapshot.MaxMessageKBytes = cfg.Section("snapshot").Key("max_message_kbytes").MustInt(1000)
	config.Snapshot.QueuedMinMessages = cfg.Section("snapshot").Key("queued_min_messags").MustInt(1000)
	config.Snapshot.ChromeAddr = *chromeAddr
	config.Snapshot.ChromeSema = cfg.Section("snapshot").Key("chromesema").MustInt64(20)
	config.Snapshot.IgnoredHosts = cfg.Section("snapshot").Key("ignored_hosts").MustString("")

	config.Ocr.Concurrency = cfg.Section("ocr").Key("concurrency").MustInt64(1)
	config.Ocr.OcrAPIAddress = cfg.Section("ocr").Key("ocr_api_address").MustString("http://127.0.0.1:8181/ocr")
	config.Ocr.OcrAPIKey = cfg.Section("ocr").Key("api_key").MustString("00a8d3c7bf8141d87ca68e3e15d9526c56edf6aa")
	config.Ocr.OcrAppKey = cfg.Section("ocr").Key("app_key").MustString("c959ead6528a559e250d97c6d6cf9225a499cd6e")

	config.S3Bucket.Endpoint = cfg.Section("s3bucket").Key("endpoint").MustString("")
	config.S3Bucket.SSL = cfg.Section("s3bucket").Key("ssl").MustBool(false)
	config.S3Bucket.ACL = cfg.Section("s3bucket").Key("acl").MustString("")
	config.S3Bucket.Region = cfg.Section("s3bucket").Key("region").MustString("")
	config.S3Bucket.AccessKey = cfg.Section("s3bucket").Key("access_key").MustString("")
	config.S3Bucket.SecretKey = cfg.Section("s3bucket").Key("secret_key").MustString("")
	config.S3Bucket.URLPrefix = cfg.Section("s3bucket").Key("url_prefix").MustString("")
	config.S3Bucket.BucketName = cfg.Section("s3bucket").Key("bucket_name").MustString("")
	config.S3Bucket.MaxRetry = cfg.Section("s3bucket").Key("max_retry").MustInt(1)
	config.S3Bucket.BucketType = cfg.Section("s3bucket").Key("bucket_type").MustInt(0)
	config.S3Bucket.LocalDir = cfg.Section("s3bucket").Key("local_dir").MustString("/home/<USER>")

	config.SensitiveImage.Concurrency = cfg.Section("sensitive_image").Key("concurrency").MustInt64(20)

	config.SwfCheck.Concurrency = cfg.Section("swf").Key("concurrency").MustInt64(5)
	config.SwfCheck.JpexURL = cfg.Section("swf").Key("jpex_url").MustString("")
	config.NmapHydraService = cfg.Section("nmaphydra_service").Key("url").MustString("")

	config.ThirdPartyScan.WeakCheckURL = cfg.Section("third_party_scan").Key("weakpass").MustString("")
	config.ThirdPartyScan.PythonScripts = cfg.Section("third_party_scan").Key("python_scripts").MustString("")

	config.Log.Dir = *logDir
	config.Log.FileName = *logName

	config.BaizeXapikey = cfg.Section("baize").Key("xapikey").MustString("")

	return config, nil
}
