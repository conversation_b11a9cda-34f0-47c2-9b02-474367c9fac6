package scripts

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"net"
	"regexp"
	"strconv"
	"time"
)

var eximRCEPattern = regexp.MustCompile(`Exim (\d+\.\d+\.\d+|\d+\.\d+_\d+|\d+\.\d+)`)

func EximOffByOneRCE(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var output string
	var err error

	for _, port := range []int{25, 26, 465, 587} {
		var conn net.Conn
		conn, err = net.DialTimeout("tcp", args.Host+":"+strconv.Itoa(port), time.Second*5)
		if err != nil {
			continue
		}
		if port == 465 {
			conn = net.Conn(tls.Client(conn, &tls.Config{InsecureSkipVerify: true}))
		}
		defer conn.Close()

		time.Sleep(100 * time.Millisecond)
		data := make([]byte, 1024)
		conn.SetReadDeadline(time.Now().Add(5 * time.Second))
		_, err = conn.Read(data)
		if err != nil {
			continue
		}
		if bytes.Contains(data, []byte("Exim")) && !bytes.Contains(data, []byte("4.90.1")) && !bytes.Contains(data, []byte("4.90_1")) {
			version := string(eximRCEPattern.Find(data))
			conn.SetWriteDeadline(time.Now().Add(5 * time.Second))
			conn.Write([]byte("EHLO test\n"))
			time.Sleep(100 * time.Millisecond)
			conn.SetReadDeadline(time.Now().Add(5 * time.Second))
			n, err := conn.Read(data)
			if err != nil {
				continue
			}
			if n > 0 {
				output += fmt.Sprintf("|%v:%v", port, version)
			}
		}
	}
	if output != "" {
		return &ScriptScanResult{Vulnerable: true, Output: args.Host + output}, nil
	}
	if err != nil {
		return nil, err
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("exim_off_by_one_rce.xml", EximOffByOneRCE)
}
