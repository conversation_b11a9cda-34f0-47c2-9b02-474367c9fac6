package scripts

import (
	"net"
	"strings"
	"websec/utils"

	"golang.org/x/net/publicsuffix"
)

func getRootDomains(domain string) ([]string, error) {
	rootDomain, err := publicsuffix.EffectiveTLDPlusOne(domain)
	if err != nil {
		return nil, err
	}
	results := []string{}
	parts := strings.Split(domain, ".")
	suffixParts := strings.Split(rootDomain, ".")

	start := 0
	if len(parts) > len(suffixParts)+1 {
		start = len(parts) - len(suffixParts) - 1
	}
	for i := start; i <= len(parts)-len(suffixParts); i++ {
		results = append(results, strings.Join(parts[i:], "."))
	}
	return results, nil
}

func FanJieXi(args *ScriptScanArgs) (*ScriptScanResult, error) {
	if ipRegex.MatchString(args.Host) {
		return &invulnerableResult, nil
	}
	var output string
	rootDomains, err := getRootDomains(args.Host)
	if err != nil {
		return nil, err
	}
	for _, rootDomain := range rootDomains {
		ipSet := map[string]bool{}
		for i := 0; i < 3; i++ {
			subDomain := utils.RandLetters(15)
			domainName := subDomain + "." + rootDomain
			ips, err := net.LookupIP(domainName)
			if err != nil {
				continue
			}
			for i := range ips {
				ipSet[ips[i].String()] = true
			}
		}
		ipList := []string{}
		for key := range ipSet {
			ipList = append(ipList, key)
		}
		if len(ipList) > 0 {
			output += "*." + rootDomain + "|" + strings.Join(ipList, "|")
		}
	}
	if output != "" {
		return &ScriptScanResult{Vulnerable: true, Output: output}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("fanjiexi.xml", FanJieXi)
}
