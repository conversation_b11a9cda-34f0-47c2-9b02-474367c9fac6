package resource

import (
	"context"
	"time"
	"websec/common"
	"websec/common/consts"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	ignoredBlackLinkDomains = common.NewIgnoredBlackLinkDomains()
)

type blackLinkDomainsIgnored struct {
	Domain string `bson:"domain"`
}

func LoadIgnoredBlackLinkDomains(mongodb *mongo.Database) error {
	log.Infoln("---- load ignored black domains. ----")

	var domains = make(map[string]bool)

	coll := mongodb.Collection(consts.CollectionBlackLinkDomainsIgnored)
	iter, err := coll.Find(context.Background(), bson.M{})
	if err != nil {
		log.Errorln("failed to load ignored black domains.", err)
		return err
	}

	var doc blackLinkDomainsIgnored
	for iter.Next(context.Background()) {
		iter.Decode(&doc)
		domains[doc.Domain] = true
	}
	ignoredBlackLinkDomains.UpdateDomains(domains)
	return nil
}

func DefaultIgnoredBlackLinkDomains() *common.IgnoredBlackLinkDomains {
	return ignoredBlackLinkDomains
}

func LoadIgnoredBlackLinkDomainsPeriodically(mongodb *mongo.Database, period time.Duration) {
	go func() {
		ticker := time.NewTicker(period)
		for t := range ticker.C {
			log.Infoln("---- load ignored black domains ---- at:", t.String())
			LoadIgnoredBlackLinkDomains(mongodb)
		}
	}()
}
