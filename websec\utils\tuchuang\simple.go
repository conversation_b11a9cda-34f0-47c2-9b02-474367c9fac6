package tuchuang

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"path"
	"path/filepath"
	"strings"
	"time"
)

type SimpleBucket struct {
	Addr   string
	Client *http.Client
	Prefix string
}

func NewSimpleBucket(addr, prefix string) *SimpleBucket {
	address := fmt.Sprintf("http://%s/media/upload", addr)
	return &SimpleBucket{
		Addr:   address,
		Client: &http.Client{Timeout: 5 * time.Second},
		Prefix: prefix,
	}
}

type UploadRequest struct {
	Data   string `json:"data"`
	Suffix string `json:"suffix"`
}

type UploadResponseData struct {
	URL string `json:"url"`
}

type UploadResponse struct {
	Status  string             `json:"status"`
	Message string             `json:"message,omitempty"`
	Data    UploadResponseData `json:"data,omitempty"`
}

func (s *SimpleBucket) UploadImg(imgData []byte, ext string, prefix string) (string, error) {
	imageStr := base64.StdEncoding.EncodeToString(imgData)
	request := &UploadRequest{
		Data:   imageStr,
		Suffix: ext,
	}

	data, err := json.Marshal(request)
	if err != nil {
		return "", err
	}

	resp, err := s.Client.Post(s.Addr, "application/json", strings.NewReader(string(data)))
	if err != nil {
		return "", err
	}

	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	res := new(UploadResponse)
	err = json.Unmarshal(body, res)
	if err != nil {
		return "", err
	}

	u, _ := url.Parse(s.Prefix)
	u.Path = path.Join(u.Path, res.Data.URL)
	return u.String(), nil
}

func (s *SimpleBucket) UploadImgFile(imgPath string, prefix string) (string, error) {
	imgData, err := ioutil.ReadFile(imgPath)
	if err != nil {
		return "", err
	}
	ext := filepath.Ext(imgPath)
	return s.UploadImg(imgData, ext, prefix)
}
