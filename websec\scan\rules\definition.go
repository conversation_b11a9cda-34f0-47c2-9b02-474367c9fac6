package rules

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"io/ioutil"
	"os"
	"regexp"
	"strconv"
	"strings"
	"websec/utils/log"
)

type Applicable struct {
	OS        string `xml:"OS"`
	WebServer string `xml:"webserver"`
	AppServer string `xml:"appserver"`
	ThirdApp  string `xml:"thirdapp"`
}

type RuleMetadata struct {
	Language              string     `xml:"language,attr"`
	Title                 string     `xml:"title"`
	Date                  string     `xml:"date"`
	Severity              string     `xml:"severity"`
	Tag                   string     `xml:"tag"`
	Description           string     `xml:"description"`
	Impact                string     `xml:"impact"`
	Recommendation        string     `xml:"recommendation"`
	Affects               string     `xml:"affects"`
	VulReportAmount       int        `xml:"vul_report_amount"`
	ScriptVulName         string     `xml:"script_vul_name"`
	Applicable            Applicable `xml:"applicable"`
	TestAllChild          bool       `xml:"testallchild"`
	TestFromModule        bool       `xml:"testfrommodule"`
	NeedExecuteJavascript bool       `xml:"need_execute_javascript"`
}

type RuleReference struct {
	Title  string `xml:"title,attr"`
	Source string `xml:",chardata"`
}

type RuleTestRequest struct {
	Method        string `xml:"method"`
	URL           string `xml:"url"`
	Version       string `xml:"version"`
	PostText      string `xml:"post_text"`
	Cookies       string `xml:"cookies"`
	CustomHeaders string `xml:"custom_headers"`
}

func (request *RuleTestRequest) Init() {
	if request.PostText != "" {
		request.PostText = strings.Replace(request.PostText, "\n", "\r\n", -1)
	}
	if request.CustomHeaders != "" {
		request.CustomHeaders = strings.Replace(request.CustomHeaders, "#_$", "\r\n", -1)
	}
}

type RuleTestResponseVariable struct {
	Description string `xml:"description,attr"`
	Name        string `xml:"name,attr"`
	Source      string `xml:"source,attr"`
	Value       string `xml:",chardata"`
	pattern     *regexp.Regexp
}

func (variable *RuleTestResponseVariable) Init() {
	variable.pattern = regexp.MustCompile(variable.Value)
}

type RuleTestResponse struct {
	Variables []RuleTestResponseVariable `xml:"var"`
}

func (res *RuleTestResponse) UpdateVariables(context RuleTestContext) {
}

type MatchFuncType func(*RuleCriterion, interface{}) bool

type RuleCriterion struct {
	Comment    string `xml:"comment,attr"`
	Operator   string `xml:"operator,attr"`
	Variable   string `xml:"variable,attr"`
	Value      string `xml:"value,attr"`
	pattern    *regexp.Regexp
	intValue   int
	bytesValue []byte
	matchFunc  MatchFuncType
}

type RuleTestContext interface {
	Get(string) (interface{}, bool)
	Set(string, interface{})
}

func (criterion *RuleCriterion) Match(values RuleTestContext) bool {
	responseValue, ok := values.Get(criterion.Variable)
	if !ok {
		log.Errorf("Failed to get %s from values %v", criterion.Variable, values)
		return false
	}
	return criterion.matchFunc(criterion, responseValue)
}

var variableRe = regexp.MustCompile("[^$()]+")

func (criterion *RuleCriterion) Init() {
	if criterion.Operator == "" {

		criterion.Operator = "equal"
	}

	criterion.matchFunc = map[string]MatchFuncType{
		"equal":         MatchEqual,
		"not equal":     MatchNotEqual,
		"contains":      MatchContains,
		"not contains":  MatchNotContains,
		"greater than":  MatchGreaterThan,
		"less than":     MatchLessThan,
		"pattern match": MatchPattern,
	}[criterion.Operator]

	criterion.Variable = variableRe.FindString(criterion.Variable)
	if criterion.Operator == "contains" && criterion.Value == "X-AspNet-Version" {

		criterion.Value = "X-Aspnet-Version"
	}

	criterion.bytesValue = []byte(criterion.Value)

	switch criterion.Operator {
	case "pattern match":
		/*
			if strings.HasSuffix(criterion.Value, "$?") {
				criterion.Value = strings.TrimSuffix(criterion.Value, "?")
			}
		*/
		criterion.pattern = regexp.MustCompile(criterion.Value)
	case "equal", "not equal", "greater than", "less than":
		intValue, err := strconv.Atoi(criterion.Value)
		if err != nil {

			intValue = -1
		}
		criterion.intValue = intValue
	}
}

func (criteria *RuleCriteria) Init() {
	if criteria.Operator == "" {

		criteria.Operator = "AND"
	}
	for i := range criteria.Criterias {
		criteria.Criterias[i].Init()
	}
	for i := range criteria.Criterions {
		criteria.Criterions[i].Init()
	}
}

func MatchEqual(criterion *RuleCriterion, responseValue interface{}) bool {
	switch responseValue.(type) {
	case string:
		stringValue, ok := responseValue.(string)
		if ok {
			return criterion.Value == stringValue
		}
		log.Errorf("Failed to convert `%v` to type string", responseValue)
	case []byte:
		bytesValue, ok := responseValue.([]byte)
		if ok {
			return bytes.Equal(criterion.bytesValue, bytesValue)
		}
		log.Errorf("Failed to convert `%v` to type []byte", responseValue)
	case int:
		if criterion.intValue != -1 {
			intValue, ok := responseValue.(int)
			if ok {
				return criterion.intValue == intValue
			}
		}
	}
	log.Warn("This should rarely happen. Unknown responseValue type or failed to convert, value is: ", responseValue)
	return criterion.Value == fmt.Sprintf("%v", responseValue)
}

func MatchNotEqual(criterion *RuleCriterion, responseValue interface{}) bool {
	return !MatchEqual(criterion, responseValue)
}

func MatchContains(criterion *RuleCriterion, responseValue interface{}) bool {
	switch responseValue.(type) {
	case string:
		stringValue, ok := responseValue.(string)
		if ok {
			return strings.Contains(stringValue, criterion.Value)
		}
	case []byte:
		bytesValue, ok := responseValue.([]byte)
		if ok {
			return bytes.Contains(bytesValue, criterion.bytesValue)
		}
	case int:
		intValue, ok := responseValue.(int)
		if ok {
			stringValue := strconv.Itoa(intValue)
			return strings.Contains(stringValue, criterion.Value)
		}
	}
	log.Warn("This should rarely happen. Unknown responseValue type or failed to convert, value is: ", responseValue)
	log.Info(criterion.Comment, criterion.Operator, criterion.Value, criterion.Variable)
	return strings.Contains(fmt.Sprintf("%v", responseValue), criterion.Value)
}

func MatchNotContains(criterion *RuleCriterion, responseValue interface{}) bool {
	return !MatchContains(criterion, responseValue)
}

func MatchGreaterThan(criterion *RuleCriterion, responseValue interface{}) bool {
	switch responseValue.(type) {
	case int:
		intValue, ok := responseValue.(int)
		if ok {
			return intValue > criterion.intValue
		}
	case string:
		stringValue, ok := responseValue.(string)
		if ok {
			intValue, err := strconv.Atoi(stringValue)
			if err == nil {
				return intValue > criterion.intValue
			}
		}
	}
	log.Warn("This should rarely happen. GreaterThan meets an invalid value: ", responseValue)
	return false
}

func MatchLessThan(criterion *RuleCriterion, responseValue interface{}) bool {
	switch responseValue.(type) {
	case int:
		intValue, ok := responseValue.(int)
		if ok {
			return intValue < criterion.intValue
		}
	case string:
		stringValue, ok := responseValue.(string)
		if ok {
			intValue, err := strconv.Atoi(stringValue)
			if err == nil {
				return intValue < criterion.intValue
			}
		}
	}
	log.Warn("This should rarely happen. LessThan meets an invalid value: ", responseValue)
	return false
}

func MatchPattern(criterion *RuleCriterion, responseValue interface{}) bool {
	switch responseValue.(type) {
	case string:
		stringValue, ok := responseValue.(string)
		if ok {
			return criterion.pattern.MatchString(stringValue)
		}
	case []byte:
		bytesValue, ok := responseValue.([]byte)
		if ok {
			return criterion.pattern.Match(bytesValue)
		}
	case int:
		intValue, ok := responseValue.(int)
		if ok {
			stringValue := strconv.Itoa(intValue)
			return criterion.pattern.MatchString(stringValue)
		}
	}
	log.Warn("This should rarely happen. Failed to pattern match: ", responseValue)
	return false
}

type RuleCriteria struct {
	Operator   string          `xml:"operator,attr"`
	Criterias  []RuleCriteria  `xml:"criteria"`
	Criterions []RuleCriterion `xml:"criterion"`
}

func (criteria *RuleCriteria) Match(values RuleTestContext) bool {
	switch criteria.Operator {
	case "AND":
		for i := range criteria.Criterions {
			if !criteria.Criterions[i].Match(values) {
				return false
			}
		}
		for i := range criteria.Criterias {
			if !criteria.Criterias[i].Match(values) {
				return false
			}
		}
		return true
	case "OR":
		for i := range criteria.Criterions {
			if criteria.Criterions[i].Match(values) {
				return true
			}
		}
		for i := range criteria.Criterias {
			if criteria.Criterias[i].Match(values) {
				return true
			}
		}
		return false
	default:
		log.Warn("Unknown Criteria Operator: ", criteria.Operator)
		return false
	}
}

type RuleTest struct {
	Request  RuleTestRequest  `xml:"request"`
	Response RuleTestResponse `xml:"response"`
	Criteria RuleCriteria     `xml:"criteria"`
	NeedUtf8 bool             // TODO: use this field to avoid unnecessary transcode
}

func (test *RuleTest) Init() {

	variables := test.Response.Variables
	for i := range variables {
		variables[i].Init()
	}
	test.Criteria.Init()
	test.Request.Init()
}

func (test *RuleTest) Match(values RuleTestContext) bool {
	variables := test.Response.Variables

	for i := range variables {
		key := variables[i].Name
		value := ""
		source := variables[i].Source

		switch source {
		case "statusline":
			continue
		default:

			if sourceValue, ok := values.Get(source); ok {

				switch sourceValue.(type) {
				case string:
					value = variables[i].pattern.FindString(sourceValue.(string))
				case []byte:
					value = string(variables[i].pattern.Find(sourceValue.([]byte)))
				}
			}
		}
		values.Set(key, value)
	}
	return test.Criteria.Match(values)
}

type RuleDefinition struct {
	Metadatas  []RuleMetadata  `xml:"metadatas>metadata"`
	References []RuleReference `xml:"references>reference"`
	Tests      []RuleTest      `xml:"tests>test"`
	Metadata   *RuleMetadata
}

func (rule *RuleDefinition) Init() {
	metadata, err := rule.GetMetadata("zh_CN")
	if err != nil {
		log.Fatal("failed to get zh_CN metadata from", rule)
	}
	rule.Metadata = metadata
}

func (rule *RuleDefinition) GetMetadata(lang string) (*RuleMetadata, error) {
	for i := range rule.Metadatas {
		if rule.Metadatas[i].Language == lang {
			return &rule.Metadatas[i], nil
		}
	}
	return nil, fmt.Errorf("failed to get %s metadata from rule %s", lang, rule.Metadatas[0].Description)
}

func loadVulXML(filename string) (*RuleDefinition, error) {
	f, err := os.Open(filename)
	if err != nil {
		log.Fatal("Failed to open file: ", filename)
		return nil, err
	}
	defer f.Close()
	buf, err := ioutil.ReadAll(f)
	if err != nil {
		log.Fatalln("Failed to read file: ", filename)
		return nil, err
	}
	buf, err = decrypt(buf)
	if err != nil {
		log.Fatalln("Failed to decrypt file: ", filename)
		return nil, err
	}

	buf = bytes.Replace(buf, []byte("]]>"), []byte("]]&gt;"), -1)
	definition := RuleDefinition{}
	err = xml.Unmarshal(buf, &definition)
	if err != nil {
		log.Fatalln("Failed to unmarshal file: ", filename, err)
		return nil, err
	}
	definition.Init()
	for i := range definition.Tests {
		definition.Tests[i].Init()
	}
	return &definition, nil
}
