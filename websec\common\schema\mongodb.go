package schema

import (
	"time"
	"websec/common"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FoundSensitiveWord struct {
	Word         string `json:"word" bson:"word"`
	Position     int    `json:"position" bson:"position"`
	Context      string `json:"context" bson:"context"`
	ContextHash  string `json:"context_hash" bson:"context_hash"`
	IsCustomized bool   `json:"is_customized" bson:"is_customized"`
	FromOcr      bool   `json:"from_ocr" bson:"from_ocr"`
}

type FoundSensitiveWordsDoc struct {
	ID           primitive.ObjectID   `json:"id,omitempty" bson:"_id,omitempty"`
	Host         string               `json:"host" bson:"host"`
	MainframeURL string               `json:"mainframe_url" bson:"mainframe_url"`
	URL          string               `json:"url" bson:"url"`
	URLHash      string               `json:"url_hash" bson:"url_hash"`
	Results      []FoundSensitiveWord `json:"results" bson:"results"`
	Strength     int                  `json:"strength" bson:"strength"`
	VersionTime  int64                `json:"version_time" bson:"version_time"`
	FoundAt      time.Time            `json:"found_at" bson:"found_at"`
	AssetID      string               `json:"asset_id,omitempty" bson:"asset_id"`
	JobID        string               `json:"job_id,omitempty" bson:"job_id"`
	Content      []byte               `json:"content" bson:"content"`
	ContentHash  string               `json:"content_hash" bson:"content_hash"`
	IsExternal   bool                 `json:"is_external,omitempty" bson:"is_external"`
}

type FoundContentChangeDoc struct {
	ID                primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	Host              string             `json:"host" bson:"host"`
	MainframeURL      string             `json:"mainframe_url" bson:"mainframe_url"`
	URL               string             `json:"url" bson:"url"`
	URLHash           string             `json:"url_hash" bson:"url_hash"`
	OldVersionTime    int64              `json:"old_version_time" bson:"old_version_time"`
	NewVersionTime    int64              `json:"new_version_time" bson:"new_version_time"`
	Diff              []byte             `json:"diff" bson:"diff"`
	DiffPercent       float64            `json:"diff_percent" bson:"diff_percent"`
	ExceptionType     string             `json:"exception_type" bson:"exception_type"`
	ExceptionContent  []byte             `json:"exception_content" bson:"exception_content"`
	FoundAt           time.Time          `json:"found_at" bson:"found_at"`
	OldSnapshot       string             `json:"old_snapshot" bson:"old_snapshot"`
	NewSnapshot       string             `json:"new_snapshot" bson:"new_snapshot"`
	Status            int32              `json:"status" bson:"status"`
	AssetID           string             `json:"asset_id,omitempty" bson:"asset_id"`
	JobID             string             `json:"job_id,omitempty" bson:"job_id"`
	IsExternal        bool               `json:"is_external,omitempty" bson:"is_external"`
	UseStaticSnapshot bool               `json:"use_static_snapshot" bson:"use_static_snapshot"`
}

type FoundBlackLink struct {
	URL        string           `json:"url" bson:"url"`
	Words      map[string][]int `json:"words" bson:"words"` // TODO: 针对每个黑词，es 的 mapping 如何处理
	IsOuterURL bool             `json:"is_outer_url" bson:"is_outer_url"`
}

type FoundBlackLinksDoc struct {
	ID           primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	Host         string             `json:"host" bson:"host"`
	MainframeURL string             `json:"mainframe_url" bson:"mainframe_url"`
	URL          string             `json:"url" bson:"url"`
	URLHash      string             `json:"url_hash" bson:"url_hash"`
	Results      []FoundBlackLink   `json:"results" bson:"results"`
	FoundAt      time.Time          `json:"found_at" bson:"found_at"`
	AssetID      string             `json:"asset_id,omitempty" bson:"asset_id"`
	JobID        string             `json:"job_id,omitempty" bson:"job_id"`
	VersionTime  int64              `json:"version_time" bson:"version_time"`
}

type FoundTrojan struct {
	Confidence      string `json:"confidence" bson:"confidence"`
	Evidence        string `json:"evidence" bson:"evidence"`
	Info            string `json:"info" bson:"info"`
	PlatformType    string `json:"platform_type" bson:"platform_type"`
	ThreatType      string `json:"threat_type" bson:"threat_type"`
	ThreatEntryType string `json:"threat_entry_type" bson:"threat_entry_type"`
	Source          string `json:"source" bson:"source"`
}

type FoundTrojanDoc struct {
	ID      primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	Host    string             `json:"host" bson:"host"`
	URL     string             `json:"url" bson:"url"`
	URLHash string             `json:"url_hash" bson:"url_hash"`
	Results []FoundTrojan      `json:"results" bson:"results"`
	AssetID string             `json:"asset_id,omitempty" bson:"asset_id"`
	JobID   string             `json:"job_id,omitempty" bson:"job_id"`
	FoundAt time.Time          `json:"found_at" bson:"found_at"`
}

type FoundVulsScan struct {
	Method      string             `json:"method" bson:"method"`
	URL         string             `json:"url" bson:"url"`
	Affect      string             `json:"affect" bson:"affect"`
	Data        string             `json:"data" bson:"data,omitempty"`
	Headers     common.HttpHeaders `json:"headers" bson:"headers"`
	Fingerprint string             `json:"fingerprint" bson:"fingerprint"`
}

type FoundVulsVul struct {
	VulXML   string `json:"xml_name" bson:"xml_name"`
	Severity string `json:"severity" bson:"severity"`
	VulURL   string `json:"vul_url" bson:"vul_url"`
	From     string `json:"from" bson:"from"`
}

type FoundVulDoc struct {
	ID      primitive.ObjectID     `json:"id,omitempty" bson:"_id,omitempty"`
	Host    string                 `json:"host" bson:"host"`
	Scan    FoundVulsScan          `json:"scan" bson:"scan"`
	Vul     FoundVulsVul           `json:"vul" bson:"vul"`
	Context map[string]interface{} `json:"context" bson:"context"`
	FoundAt time.Time              `json:"found_at" bson:"found_at"`
	AssetID string                 `json:"asset_id,omitempty" bson:"asset_id"`
	JobID   string                 `json:"job_id,omitempty" bson:"job_id"`
}

type FoundPhishingURL struct {
	URL  string `json:"url" bson:"url"`
	Host string `json:"host" bson:"host"`
}

type FoundPhishingDoc struct {
	ID      primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	Sc      int                `json:"sc" bson:"sc"`
	Pu      FoundPhishingURL   `json:"pu" bson:"pu"`
	Ou      FoundPhishingURL   `json:"ou" bson:"ou"`
	St      int                `json:"st" bson:"st"`
	Host    string             `json:"host" bson:"host"`
	Typ     int                `json:"type" bson:"type"`
	FoundAt time.Time          `json:"found_at" bson:"found_at"`
	AssetID string             `json:"asset_id" bson:"asset_id"`
	JobID   string             `json:"job_id,omitempty" bson:"job_id"`
}

type OcrImageDoc struct {
	ID            primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	Host          string             `json:"host" bson:"host"`
	AssetID       string             `json:"asset_id" bson:"asset_id"`
	JobID         string             `json:"job_id" bson:"job_id"`
	ImageURL      string             `json:"image_url" bson:"image_url"`
	ImageURLHash  string             `json:"image_url_hash" bson:"image_url_hash"`
	ImageMD5      string             `json:"image_md5,omitempty" bson:"image_md5,omitempty"`
	Content       string             `json:"content,omitempty" bson:"content,omitempty"`
	ImageAddress  string             `json:"image_address,omitempty" bson:"image_address,omitempty"`
	FoundAt       time.Time          `json:"found_at" bson:"found_at"`
	LastCheckedAt time.Time          `json:"last_checked_at" bson:"last_checked_at"`
}

type FoundOcrSensitiveWordDoc struct {
	ID      primitive.ObjectID      `json:"id,omitempty" bson:"_id,omitempty"`
	URL     string                  `json:"url" bson:"url"`
	Host    string                  `json:"host" bson:"host"`
	FoundAt time.Time               `json:"found_at" bson:"found_at"`
	AssetID string                  `json:"asset_id" bson:"asset_id"`
	JobID   string                  `json:"job_id" bson:"job_id"`
	Results []*SensitiveImageResult `json:"results" bson:"results"`
}

type SensitiveImageResult struct {
	ImageURL     string       `json:"image_url" bson:"image_url"`
	ImageURLHash string       `json:"image_url_hash" bson:"image_url_hash"`
	ImageAddress string       `json:"image_address" bson:"image_address"`
	Content      string       `json:"content" bson:"content"`
	ResultDetail []WordResult `json:"results" bson:"results"`
}

type HostURLSum struct {
	ID        primitive.ObjectID `bson:"_id,omitempty"`
	Host      string             `bson:"host"`
	Sum       int32              `bson:"sum"`
	Precision float32            `bson:"-"`
}

type AssetURLSum struct {
	ID        primitive.ObjectID `bson:"_id,omitempty"`
	AssetID   string             `bson:"asset_id"`
	JobID     string             `bson:"job_id"`
	Sum       int32              `bson:"sum"`
	Precision float32            `bson:"-"`
}

type ContentDetectFGHK struct {
	ID             primitive.ObjectID `bson:"_id,omitempty"`
	Remark         string             `bson:"remark"`
	Snapshot       string             `bson:"snapshot"`
	SensitiveWords string             `bson:"sensitive_words"`
	URL            string             `bson:"url"`
	UpdateAt       time.Time          `bson:"updated_at"`
}

type ImageDoc struct {
	ID               primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	Host             string             `json:"host" bson:"host"`
	AssetID          string             `json:"asset_id" bson:"asset_id"`
	JobID            string             `json:"job_id" bson:"job_id"`
	ImageURL         string             `json:"image_url" bson:"image_url"`
	ImageURLChecksum string             `json:"image_url_checksum" bson:"image_url_checksum"`
	ContentChecksum  string             `json:"content_checksum,omitempty" bson:"content_checksum,omitempty"`
	Referer          string             `json:"referer" bson:"referer"`
	FoundAt          time.Time          `json:"found_at" bson:"found_at"`
	LastCheckedAt    time.Time          `json:"last_checked_at" bson:"last_checked_at"`
}

type SwfDoc struct {
	ID              primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	Host            string             `json:"host" bson:"host"`
	AssetID         string             `json:"asset_id" bson:"asset_id"`
	JobID           string             `json:"job_id" bson:"job_id"`
	SwfURL          string             `json:"swf_url" bson:"swf_url"`
	SwfURLChecksum  string             `json:"Swf_url_checksum" bson:"Swf_url_checksum"`
	ContentChecksum string             `json:"content_checksum,omitempty" bson:"content_checksum,omitempty"`
	Referer         string             `json:"referer" bson:"referer"`
	FoundAt         time.Time          `json:"found_at" bson:"found_at"`
	LastCheckedAt   time.Time          `json:"last_checked_at" bson:"last_checked_at"`
}

type FoundSwfWithURLDoc struct {
	FoundBlackLinksDoc
	SwfURL string `json:"swf_url" bson:"swf_url"`
}

type FoundSensitiveImgDoc struct {
	ID           string    `json:"source_id"`
	Host         string    `json:"host"`
	ImageClass   string    `json:"img_class"`
	ImageURL     string    `json:"img_url"`
	ImageSnapURL string    `json:"img_snapshot_url"`
	AffectedURL  string    `json:"affected_url"`
	FoundAt      time.Time `json:"found_at"`
	Confidence   string    `json:"confidence"`
	AssetID      string    `json:"asset_id"`
	JobID        string    `json:"job_id"`
}
