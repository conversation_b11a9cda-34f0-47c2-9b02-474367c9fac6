package health

import (
	"encoding/json"
	"net/http"
	"os"
	"runtime"

	"github.com/shirou/gopsutil/process"
)

type Status struct {
	PID             int                     `json:"pid"`
	CPUPercent      float64                 `json:"cpu_percent"`
	MemoryInfo      *process.MemoryInfoStat `json:"memory_info"`
	RuntimeMemStats *runtime.MemStats       `json:"runtime_mem_stats"`
}

func NewStats() (*Status, error) {
	status := Status{}
	pid := os.Getpid()
	status.PID = pid

	p, err := process.NewProcess(int32(pid))
	if err != nil {
		return nil, err
	}

	cpuPercent, err := p.CPUPercent()
	if err != nil {
		return nil, err
	}
	status.CPUPercent = cpuPercent

	info, err := p.MemoryInfo()
	if err != nil {
		return nil, err
	}
	status.MemoryInfo = info

	memStat := runtime.MemStats{}
	runtime.ReadMemStats(&memStat)
	status.RuntimeMemStats = &memStat

	return &status, nil
}

type Resp struct {
	OK   bool    `json:"ok"`
	Msg  string  `json:"msg"`
	Data *Status `json:"data"`
}

func (r Resp) serialize() ([]byte, error) {
	return json.Marshal(r)
}

func Health(w http.ResponseWriter, r *http.Request) {
	resp := Resp{
		OK: false,
	}

	status, err := NewStats()
	if err != nil {
		resp.Msg = err.Error()
	} else {
		resp.OK = true
		resp.Msg = "success"
		resp.Data = status
	}
	data, _ := resp.serialize()
	w.Write(data)
}

func init() {
	http.HandleFunc("/health", Health)
}
