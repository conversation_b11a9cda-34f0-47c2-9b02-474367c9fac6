package main

import (
	"context"
	"strings"
	"time"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/config"
	"websec/detect"
	"websec/utils/log"
	"websec/utils/stream"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func main() {
	settings, err := config.ParseConfig()
	if err != nil {
		log.Errorln(err)
		return
	}

	dbConnection, err := db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis))
	if err != nil {
		log.Errorln(err)
		return
	}
	path := "./detect_js_Trojan.json"
	d, err := detect.NewTrojanDetecter(path)
	if err != nil {
		log.Fatal(err)
	}

	run(dbConnection, settings, d)

}

func run(dbConnection *db.DBConnection, settings *config.Config, d *detect.TrojanDetecter) {
	producer, err := stream.NewProducer(&stream.ProducerOptions{
		Brokers:         settings.Kafka.Brokers,
		MessageMaxBytes: settings.Kafka.MessageMaxBytes,
	})
	if err != nil {
		log.Errorln(err)
		return
	}
	producer.Go()
	defer producer.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	asset := new(schema.Asset)
	err = dbConnection.GetMongoDatabase().Collection(consts.CollectionAssets).FindOne(ctx,
		bson.M{"host": "***********"}).Decode(asset)
	if err != nil {
		log.Errorln(err)
		return
	}

	var str string

	result := make([]*detect.TrojanResult, 0)

	r1, found := d.Detect([]byte(str), detect.FileText)
	if found {

		for _, matchedRule := range r1 {
			trojanResult := &detect.TrojanResult{
				URL:          "http://***********",
				Confidence:   "high",
				Evidence:     "http://***********",
				Info:         matchedRule.Name + ":" + matchedRule.AffectContent,
				Source:       str,
				PlatformType: strings.Join(matchedRule.AffectSystem, ","),
			}
			result = append(result, trojanResult)
		}

	}

	r2, found := d.Detect([]byte(str), detect.FileScript)
	if found {
		for _, matchedRule := range r2 {
			trojanResult := &detect.TrojanResult{
				URL:          "http://***********",
				Confidence:   "high",
				Evidence:     "http://***********",
				Info:         matchedRule.Name + ":" + matchedRule.AffectContent,
				Source:       str,
				PlatformType: strings.Join(matchedRule.AffectSystem, ","),
			}
			result = append(result, trojanResult)
		}
	}

	topicResults := make([]*schema.RawTrojanResult, len(result))
	for k, v := range result {
		trojanResults := make([]schema.FoundTrojan, 1)
		trojanResults[0].Confidence = v.Confidence
		trojanResults[0].Evidence = v.Evidence
		trojanResults[0].Info = v.Info
		trojanResults[0].PlatformType = v.PlatformType
		trojanResults[0].ThreatType = v.ThreatType
		trojanResults[0].ThreatEntryType = v.ThreatEntryType
		trojanResults[0].Source = v.Source

		r := &schema.RawTrojanResult{
			ID:      primitive.NewObjectID(),
			Host:    "***********",
			URL:     "http://***********",
			Results: trojanResults,
			FoundAt: time.Now(),
			AssetID: asset.ID.Hex(),
		}
		topicResults[k] = r
	}

	for _, doc := range topicResults {

		producer.Produce("trojan-results", doc)
	}

	time.Sleep(10 * time.Second)

}
