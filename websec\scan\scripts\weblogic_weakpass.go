package scripts

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/cookiejar"
	"strings"
	"time"
)

func WeblogicWeakpass(args *ScriptScanArgs) (*ScriptScanResult, error) {
	var rawurl = constructURL(args, "/")
	flagList := []string{
		"<title>WebLogic Server Console</title>",
		"javascript/console-help.js",
		"WebLogic Server Administration Console Home",
		"/console/console.portal",
		"console/jsp/common/warnuserlockheld.jsp",
		"/console/actions/common/",
	}
	userList := []string{"weblogic"}
	passList := []string{"Oracle@123", "weblogc", "weblogic1", "weblogic123", "weblogic11", "password", "123456", "12345678",
		"weblogic11g", "weblogic12", "weblogic12g", "weblogic13", "weblogic13g"}
	loginurl := rawurl + "console/login/LoginForm.jsp"

	client := &http.Client{Timeout: 3 * time.Second}
	req, err := http.NewRequest(http.MethodPost, loginurl, nil)

	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 6.2; rv:30.0) Gecko/20150101 Firefox/32.0")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Referer", rawurl+"console/login/LoginForm.jsp")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	curResponse, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	if curResponse.StatusCode != 200 {
		return &invulnerableResult, nil
	}

	cookie := curResponse.Cookies()

	for _, user := range userList {
		for _, password := range passList {
			postStr := fmt.Sprintf("j_username=%v&j_password=%v&j_character_encoding=UTF-8", user, password)
			fmt.Println(postStr)
			body := bytes.NewReader([]byte(postStr))
			if err != nil {
				return nil, err
			}

			for _, c := range cookie {
				req.AddCookie(c)
			}
			jar, err := cookiejar.New(nil)
			if err != nil {
				return nil, err
			}

			jar.SetCookies(curResponse.Request.URL, cookie)
			client = &http.Client{Timeout: 3 * time.Second, Jar: jar}

			response, err := client.Post(rawurl+"console/j_security_check", "application/x-www-form-urlencoded", body)
			if err != nil {
				return nil, err
			}

			if response.StatusCode != 200 {
				return &invulnerableResult, nil
			}

			postBody, err := ioutil.ReadAll(response.Body)
			if err != nil {
				return nil, err
			}
			for _, flag := range flagList {
				if strings.Contains(string(postBody), flag) {
					return &ScriptScanResult{
						Vulnerable: true,
						Output: fmt.Sprintf("%vconsole|Weblogic Weak password %v:%v",
							rawurl, user, password),
						Body: postBody,
					}, nil
				}
			}

		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("Oracle_WebLogic_Weak_password.xml", WeblogicWeakpass)
}
