package filters

import (
	"encoding/json"
	"websec/common/schema"
	"websec/utils/stream"
)

func (filter *Filter) processBlackLink(msg *stream.Message) error {
	var doc = new(schema.RawBlackLinkResult)
	err := json.Unmarshal(msg.Value, doc)
	if err != nil {
		return err
	}

	results := make([]schema.FoundBlackLink, 0, 1)
	for _, v := range doc.Results {

		results = append(results, v)
	}

	if len(results) > 0 {
		doc.Results = results
		filter.AddFinalBlackLink(doc)
	}

	return nil
}
