package tuchuang

import (
	"bytes"
	"crypto/sha1"
	"encoding/hex"
	"errors"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"path"
	"path/filepath"
	"websec/config"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
)

type S3Bucket struct {
	S3cfg *aws.Config // s3 config
	Name  string      // bucket name
	ACL   string      // like public-read, refer to s3
	URL   string      // prefix of request, like http://s3.360.cn/papapa/
}

func NewS3Bucket(cfg *config.S3BucketConfig) *S3Bucket {
	s3Cfg := SimpleS3Cfg(cfg.Endpoint, cfg.Region, cfg.SSL, cfg.Max<PERSON>ry, cfg.<PERSON>ey, cfg.SecretKey)
	return &S3Bucket{
		S3cfg: s3Cfg,
		Name:  cfg.BucketName,
		ACL:   cfg.ACL,
		URL:   cfg.URLPrefix,
	}
}

func SimpleS3Cfg(endpoint string, region string, ssl bool, maxRetry int, accessKey string, secretKey string) *aws.Config {
	return aws.NewConfig().
		WithDisableSSL(!ssl).
		WithMaxRetries(maxRetry).
		WithEndpoint(endpoint).
		WithRegion(region).
		WithS3ForcePathStyle(true).
		WithCredentials(credentials.NewStaticCredentials(accessKey, secretKey, ""))
}

func sha1sum(data []byte) string {
	s := sha1.Sum(data)
	return hex.EncodeToString(s[:])
}

func (bucket *S3Bucket) upload(buffer []byte, path string) error {
	length := int64(len(buffer))
	svc := s3.New(session.New(bucket.S3cfg))
	input := &s3.PutObjectInput{
		ACL:           aws.String(bucket.ACL),
		Body:          bytes.NewReader(buffer),
		ContentLength: aws.Int64(int64(length)),
		ContentType:   aws.String(http.DetectContentType(buffer)),
		Bucket:        aws.String(bucket.Name),
		Key:           aws.String(path),
	}
	_, err := svc.PutObject(input)
	if err != nil {
		return err
	}
	return nil
}

func (bucket *S3Bucket) UploadImg(imgData []byte, ext string, prefix string) (string, error) {
	if len(imgData) == 0 {
		return "", errors.New("imgData len == 0")
	}

	objPath := path.Join(prefix, sha1sum(imgData)+ext)
	err := bucket.upload(imgData, objPath)
	if err != nil {
		return "", err
	}
	u, _ := url.Parse(bucket.URL)
	u.Path = path.Join(u.Path, objPath)
	return u.String(), nil
}

func (bucket *S3Bucket) UploadImgFile(imgPath string, prefix string) (string, error) {
	imgData, err := ioutil.ReadFile(imgPath)
	if err != nil {
		return "", err
	}
	ext := filepath.Ext(imgPath)
	return bucket.UploadImg(imgData, ext, prefix)
}

func (bucket *S3Bucket) UploadWithS3Manager(r io.Reader, contentType string, key string) error {
	configProvider := session.New(bucket.S3cfg)
	uploader := s3manager.NewUploader(configProvider)

	input := &s3manager.UploadInput{
		ACL:         aws.String(bucket.ACL),
		Bucket:      aws.String(bucket.Name),
		Key:         aws.String(key),
		ContentType: aws.String(contentType),
		Body:        r,
	}

	_, err := uploader.Upload(input)
	return err
}
