package utils

import (
	"bytes"
	"compress/gzip"
	"crypto/md5"
	"encoding/hex"
	"hash/fnv"
	"io/ioutil"
	"math/rand"
	"net/url"
	"strings"
	"time"
)

func init() {
	rand.Seed(time.Now().UnixNano())
}

func RandBetweenInt32(a, b int32) int32 {
	if a > b {
		return rand.Int31n(a-b) + b + 1
	} else if a == b {
		return a
	} else {
		return rand.Int31n(b-a) + a + 1
	}
}

func GetDomain(rawURL string) string {
	parts, err := url.Parse(rawURL)
	if err != nil {
		return ""
	}
	return parts.Hostname()
}

func GetLargeDomain(host string) string {
	host = stripPort(host)
	colon := strings.IndexByte(host, '.')
	if colon == -1 || colon+1 >= len(host) {
		return host
	}
	return host[colon+1:]
}

func stripPort(host string) string {
	colon := strings.IndexByte(host, ':')
	if colon == -1 {
		return host
	}
	if i := strings.IndexByte(host, ']'); i != -1 {
		return strings.TrimPrefix(host[:i], "[")
	}
	return host[:colon]
}

func Md5(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

func Md5Header(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	b := h.Sum(nil)
	if len(b) > 6 {
		return hex.EncodeToString(b[0:6])
	} else {
		return hex.EncodeToString(b)
	}
}

func Hash32(s string) uint32 {
	a := fnv.New32a()
	a.Write([]byte(s))
	return a.Sum32()
}

func Hash64(s string) uint64 {
	a := fnv.New64a()
	a.Write([]byte(s))
	return a.Sum64()
}

func GetReverseDomain(rawURL string) string {
	domain := GetDomain(rawURL)
	if domain == "" {
		return ""
	}

	ss := strings.Split(domain, ".")
	length := len(ss)
	half := length / 2
	for i := 0; i < half; i++ {
		ss[i], ss[length-i-1] = ss[length-i-1], ss[i]
	}

	return strings.Join(ss, ".")
}

func Unzip(data []byte) ([]byte, error) {
	gr, err := gzip.NewReader(bytes.NewBuffer(data))
	if err != nil {
		return nil, err
	}
	defer gr.Close()
	data, err = ioutil.ReadAll(gr)
	if err != nil {
		return nil, err
	}
	return data, err
}

func Zip(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	w := gzip.NewWriter(&buf)
	_, err := w.Write(data)
	if err != nil {
		return nil, err
	}
	err = w.Flush()
	if err != nil {
		return nil, err
	}
	err = w.Close()
	if err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

func GetDayLeftSecond() int64 {
	now := time.Now().Unix()
	nextDay := time.Now().AddDate(0, 0, 1)
	next := nextDay.Unix() - int64(nextDay.Hour()*3600) - int64(nextDay.Minute()*60) - int64(nextDay.Second())
	return next - now
}
