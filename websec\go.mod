module websec

go 1.13

require (
	github.com/PuerkitoBio/goquery v1.5.1
	github.com/StackExchange/wmi v0.0.0-20190523213315-cbe66965904d // indirect
	github.com/agnivade/levenshtein v1.1.1
	github.com/apache/thrift v0.12.0
	github.com/aristanetworks/goarista v0.0.0-20190712234253-ed1100a1c015 // indirect
	github.com/aws/aws-sdk-go v1.21.6
	github.com/confluentinc/confluent-kafka-go v1.4.2
	github.com/cznic/b v0.0.0-20181122101859-a26611c4d92d // indirect
	github.com/cznic/mathutil v0.0.0-20181122101859-297441e03548 // indirect
	github.com/cznic/strutil v0.0.0-20181122101858-275e90344537 // indirect
	github.com/fatih/color v1.7.0
	github.com/go-cmd/cmd v1.4.1
	github.com/go-ini/ini v1.44.0
	github.com/go-ole/go-ole v1.2.4 // indirect
	github.com/go-redis/redis v6.15.3+incompatible
	github.com/go-sql-driver/mysql v1.5.0
	github.com/go-stack/stack v1.8.0 // indirect
	github.com/gogo/protobuf v1.3.1
	github.com/golang/mock v1.3.1 // indirect
	github.com/golang/protobuf v1.3.3
	github.com/golang/snappy v0.0.1 // indirect
	github.com/google/go-cmp v0.3.1 // indirect
	github.com/google/uuid v1.1.1
	github.com/gorilla/mux v1.7.3
	github.com/gorilla/websocket v1.4.0
	github.com/jinzhu/gorm v1.9.16
	github.com/jlaffaye/ftp v0.0.0-20190721194432-7cd8b0bcf3fc
	github.com/lestrrat/go-libxml2 v0.0.0-20180221004755-bb78334e2019
	github.com/marten-seemann/quic-conn v0.0.0-20191204020628-6e719687462b
	github.com/mattn/go-colorable v0.1.2 // indirect
	github.com/mattn/go-isatty v0.0.12 // indirect
	github.com/mhqiang/logger v0.0.0-20220615031750-aacec24c3a09
	github.com/natefinch/lumberjack v2.0.0+incompatible
	github.com/panjf2000/ants v1.3.0
	github.com/remyoudompheng/bigfft v0.0.0-20190728182440-6a916e37a237 // indirect
	github.com/robertkrimen/otto v0.0.0-20200922221731-ef014fd054ac
	github.com/robfig/cron/v3 v3.0.0
	github.com/samuel/go-zookeeper v0.0.0-20180130194729-c4fab1ac1bec // indirect
	github.com/scylladb/go-set v1.0.2
	github.com/sergi/go-diff v1.0.0
	github.com/shirou/gopsutil v2.19.11+incompatible
	github.com/shirou/w32 v0.0.0-20160930032740-bb4de0191aa4 // indirect
	github.com/sirupsen/logrus v1.4.2
	github.com/smartystreets/goconvey v0.0.0-20190731233626-505e41936337 // indirect
	github.com/steakknife/bloomfilter v0.0.0-20180922174646-6819c0d2a570
	github.com/steakknife/hamming v0.0.0-20180906055917-c99c65617cd3 // indirect
	github.com/tidwall/pretty v1.0.0 // indirect
	github.com/tsuna/gohbase v0.0.0-20190725190657-3dd1fa8c4feb
	github.com/ugorji/go/codec v1.1.7
	github.com/valyala/bytebufferpool v1.0.0
	github.com/valyala/fasthttp v1.4.0
	github.com/vmihailenco/msgpack v4.0.4+incompatible
	github.com/xdg/scram v0.0.0-20180814205039-7eeb5667e42c // indirect
	github.com/xdg/stringprep v1.0.0 // indirect
	github.com/yanyiwu/gojieba v1.3.0
	go.mongodb.org/mongo-driver v1.0.4
	go.uber.org/zap v1.21.0
	golang.org/x/net v0.0.0-20210405180319-a5a99cb37ef4
	golang.org/x/text v0.3.3
	golang.org/x/time v0.0.0-20190308202827-9d24e82272b4 // indirect
	google.golang.org/appengine v1.6.1 // indirect
	gopkg.in/ini.v1 v1.46.0 // indirect
	gopkg.in/mgo.v2 v2.0.0-20180705113604-9856a29383ce
	gopkg.in/olivere/elastic.v5 v5.0.81
	gopkg.in/sourcemap.v1 v1.0.5 // indirect
	gopkg.in/xmlpath.v1 v1.0.0-20140413065638-a146725ea6e7 // indirect
	gopkg.in/xmlpath.v2 v2.0.0-20150820204837-860cbeca3ebc
	launchpad.net/gocheck v0.0.0-20140225173054-000000000087 // indirect
	launchpad.net/xmlpath v0.0.0-20130614043138-000000000004 // indirect
)
