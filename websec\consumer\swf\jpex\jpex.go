package jpex

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"regexp"
	"strings"
)

var (
	urlRe0 = regexp.MustCompile(`getUrl\("(.*?)"`)
	urlRe1 = regexp.MustCompile(`getURL\("(.*?)"`)
)

type jpexRequest struct {
	*http.Request
}

func newJpexRequest(ctx context.Context, url string, swf []byte) (*jpexRequest, error) {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile("fileBody", "testswf")
	if err != nil {
		return nil, err
	}
	_, err = io.Copy(part, bytes.NewReader(swf)) // TODO: loop count the writtern sum ?
	if err != nil {
		return nil, err
	}
	err = writer.Close()
	if err != nil {
		return nil, err
	}
	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, url, body)
	httpReq.Header.Set("Content-Type", writer.FormDataContentType())
	if err != nil {
		return nil, err
	}
	return &jpexRequest{httpReq}, nil
}

type jpexResponse struct {
	Status    uint     `json:"status"`
	SourceLst []string `json:"source"`
	ErrorStr  string   `json:"error"`
}

func (r jpexResponse) statusOK() bool {
	return r.Status == 0 && len(r.ErrorStr) == 0
}

func (r jpexResponse) embeddedURLs() ([]string, error) {
	if !r.statusOK() {
		return nil, errors.New(r.ErrorStr)
	}

	urlLst := make([]string, 0)
	for _, s := range r.SourceLst {
		if strings.HasPrefix(s, "getUrl(") {
			match := urlRe0.FindAllStringSubmatch(s, 1) // only find first one
			if match == nil {
				continue
			}
			if len(match) > 0 {
				url := match[0][1]
				urlLst = append(urlLst, url)
			}

			if len(urlLst) > 20 {
				return urlLst, nil
			}
		}

		if strings.HasPrefix(s, "getURL(") {
			match := urlRe1.FindAllStringSubmatch(s, 1) // only find first one
			if match == nil {
				continue
			}
			if len(match) > 0 {
				url := match[0][1]
				urlLst = append(urlLst, url)
			}

			if len(urlLst) > 20 {
				return urlLst, nil
			}
		}
	}
	return urlLst, nil
}

type JpexClient struct {
	url string
}

func NewJpexClient(url string) *JpexClient {
	return &JpexClient{
		url: url,
	}
}

func (c JpexClient) doRequest(ctx context.Context, swf []byte) (*jpexResponse, error) {
	jpexReq, err := newJpexRequest(ctx, c.url, swf)
	if err != nil {
		return nil, err
	}

	httpClient := http.DefaultClient
	httpResp, err := httpClient.Do(jpexReq.Request)
	if err != nil {
		return nil, err
	}
	defer httpResp.Body.Close()

	respBody, err := ioutil.ReadAll(httpResp.Body)
	if err != nil {
		return nil, err
	}

	jpexResp := &jpexResponse{}
	err = json.Unmarshal(respBody, jpexResp)
	if err != nil {
		return nil, err
	}

	return jpexResp, nil
}

func (c JpexClient) CheckSwfFileEmbededURLs(ctx context.Context, swf []byte) ([]string, error) {
	r, err := c.doRequest(ctx, swf)
	if err != nil {
		return nil, err
	}
	return r.embeddedURLs()
}
