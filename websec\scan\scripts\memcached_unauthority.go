package scripts

import (
	"bytes"
	"net"
	"time"
)

func MemcachedUnauthority(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addr := args.Host + ":11211"
	conn, err := net.DialTimeout("tcp", addr, time.Second*3)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
	conn.Write([]byte("stats\n"))
	response := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(time.Second * 5))
	conn.Read(response)
	if bytes.Contains(response, []byte("STAT pid")) {
		return &ScriptScanResult{Vulnerable: true, Output: addr, Body: response}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("memcached_unauthority.xml", MemcachedUnauthority)
}
