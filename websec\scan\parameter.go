package scan

import (
	"fmt"
	"net/url"
	"websec/scan/rules"
	"websec/utils"
	"websec/utils/log"
)

func genParameterTestURL(rawurl string, param *rules.Param, vul *rules.Vulnerability, fields ...string) string {
	parts, err := url.Parse(rawurl)
	if err != nil {
		log.Errorln("failed to parse url for directory test:", rawurl)
		return ""
	}
	if len(fields) == 0 || parts.RawQuery == "" {
		return rawurl
	}

	field := fields[0]
	qs := parts.Query()
	var value string
	if vs, ok := qs[field]; ok {
		if len(vs) == 0 {
			value = ""
		} else {
			value = vs[0]
		}
		qs.Del(field)
	} else {
		return rawurl
	}

	if vul.DLL == "libparam.dll" {
		value += param.Value
	} else {
		value = param.Value
	}
	tmpQuery := qs.Encode()
	if tmpQuery != "" {
		tmpQuery += "&"
	}

	tmpQuery += fmt.Sprintf("%v=%v", field, value)
	parts.RawQuery = tmpQuery

	return utils.ToString(parts)
}

func genParameterPostData(link *AffectLink, vul *rules.Vulnerability, param *rules.Param, fields ...string) string {
	field := fields[0]
	qs, err := url.ParseQuery(link.Data)
	if err != nil {
		log.Errorln("failed to parse query of link data:", link.Data)
		return ""
	}
	value := qs.Get(field)
	qs.Del(field)
	if param != nil {
		if vul.DLL == "libparam.dll" {
			value += param.Value
		} else {
			value = param.Value
		}
	}
	tmpQuery := qs.Encode()
	if tmpQuery != "" {
		tmpQuery += "&"
	}

	tmpQuery += fmt.Sprintf("%v=%v", field, value)
	return tmpQuery
}
