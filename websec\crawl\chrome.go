package crawl

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"websec/common"
	"websec/utils/log"

	"github.com/gorilla/websocket"
)

var StaticResourceType = map[string]bool{
	"Document": true, "Stylesheet": true, "Image": true,
	"Media": true, "Font": true, "Script": true,
}

func decode(resp *http.Response, v interface{}) error {
	defer resp.Body.Close()
	err := json.NewDecoder(resp.Body).Decode(v)

	return err
}

func unmarshal(payload []byte) (map[string]interface{}, error) {
	var response map[string]interface{}
	err := json.Unmarshal(payload, &response)
	if err != nil {
		log.Errorln("unmarshal", string(payload), len(payload), err)
	}
	return response, err
}

func convertHeaders(headers map[string]string) common.HttpHeaders {
	ret := make(common.HttpHeaders)
	for key, value := range headers {

		ret.Add(key, value)
	}
	return ret
}

func mergeHeaders(old common.HttpHeaders, patch common.HttpHeaders) common.HttpHeaders {
	ret := make(common.HttpHeaders)
	for k, vs := range old {
		for i := range vs {
			ret.Add(k, vs[i])
		}
	}
	for k, vs := range patch {
		for i := range vs {
			ret.Add(k, vs[i])
		}
	}
	return ret
}

const (
	EventClosed = "Tab.closed"
)

var (
	ErrorNoWsURL = errors.New("no websocket URL")

	MaxReadBufferSize = 0

	MaxWriteBufferSize = 100 * 1024
)

type chromeHeaders = map[string]string

func toChromeHeaders(headers common.HttpHeaders) chromeHeaders {
	cHeaders := chromeHeaders{}
	for k, v := range headers {
		cHeaders[k] = strings.Join(v, " ")
	}
	return cHeaders
}

type MethodReqParams struct {
	ID     int         `json:"id"`
	Method string      `json:"method"`
	Params interface{} `json:"params"`
}

type Params map[string]interface{}

func (p Params) String(k string) string {
	return p[k].(string)
}

func (p Params) Int(k string) int {
	return int(p[k].(float64))
}

func (p Params) Map(k string) map[string]interface{} {
	return p[k].(map[string]interface{})
}

type EventCallback func(params json.RawMessage)

type wsMessage struct {
	ID     int             `json:"id"`
	Result json.RawMessage `json:"result"`

	Method string          `json:"method"`
	Params json.RawMessage `json:"params"`
}

type FetchResponse struct {
	URL          string            `json:"url"`
	EffectiveURL string            `json:"effective_url"`
	StatusCode   int               `json:"status_code"`
	Headers      map[string]string `json:"headers"`
	Content      []byte            `json:"content"`
	Time         int64             `json:"time"`
	CaptureURLS  []string          `json:"capture_urls"`
}

type Tab struct {
	ID               string `json:"id"`
	Type             string `json:"type"`
	Description      string `json:"description"`
	Title            string `json:"title"`
	URL              string `json:"url"`
	WsURL            string `json:"webSocketDebuggerUrl"`
	DevURL           string `json:"devtoolsFrontendUrl"`
	InjectJavaScript *common.InjectJavaScript

	ws         *websocket.Conn
	reqID      int
	finished   bool
	goroutines sync.WaitGroup

	frameID      string
	requestID    string
	requestURL   string
	pageURL      string
	loaded       chan bool
	refreshed    chan bool
	targetIDChan chan string

	networkIdled    bool
	lifecycleEvents sync.Map
	recResponses    map[string]*Response
	sentRequests    map[string][]eventRequestWillBeSent
	abortedURLS     map[string]bool

	requests  chan json.RawMessage
	responses map[int]chan json.RawMessage
	callbacks map[string]EventCallback
	events    chan wsMessage

	mutexCallbacks    *sync.Mutex
	mutexResponses    *sync.Mutex
	mutexRecResponses *sync.Mutex
	mutexAbortedURLS  *sync.Mutex
}

func (t *Tab) Init() {
	t.reqID = 0
	t.frameID = ""
	t.requestURL = ""
	t.pageURL = ""
	t.finished = false
	t.loaded = make(chan bool, 2)
	t.refreshed = make(chan bool, 2)
	t.targetIDChan = make(chan string, 20)
	t.recResponses = map[string]*Response{}
	t.sentRequests = map[string][]eventRequestWillBeSent{}
	t.requests = make(chan json.RawMessage, 30)
	t.responses = map[int]chan json.RawMessage{}
	t.callbacks = map[string]EventCallback{}
	t.events = make(chan wsMessage, 256)
	t.mutexCallbacks = &sync.Mutex{}
	t.mutexResponses = &sync.Mutex{}
	t.mutexRecResponses = &sync.Mutex{}
	t.mutexAbortedURLS = &sync.Mutex{}
	t.lifecycleEvents = sync.Map{}
	t.abortedURLS = map[string]bool{}
}

type ChromeClient struct {
	Host             string
	Port             uint16
	HTTPClient       *http.Client
	CurTabCount      int32
	InjectJavaScript *common.InjectJavaScript
}

// func (c *ChromeClient) NewTab() (*Tab, error) {
// 	u := fmt.Sprintf("http://%s:%d/json/new", c.Host, c.Port)
// 	resp, err := c.HTTPClient.Get(u)
// 	if err != nil {
// 		log.Errorln(err)
// 		return nil, err
// 	}

// 	var tab Tab
// 	if err = decode(resp, &tab); err != nil {
// 		log.Errorln("decode:", err, resp)
// 		return nil, err
// 	}
// 	tab.InjectJavaScript = c.InjectJavaScript
// 	atomic.AddInt32(&c.CurTabCount, 1)
// 	return &tab, nil
// }

func (c *ChromeClient) NewTab() (*Tab, error) {
	u := fmt.Sprintf("http://%s:%d/json/new", c.Host, c.Port)
	req, err := http.NewRequest(http.MethodPut, u, nil)
	if err != nil {
		log.Errorln("create request:", err)
		return nil, err
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		log.Errorln("send request:", err)
		return nil, err
	}
	defer resp.Body.Close()

	var tab Tab
	if err = decode(resp, &tab); err != nil {
		log.Errorln("decode:", u, err, resp)
		return nil, err
	}
	tab.InjectJavaScript = c.InjectJavaScript
	atomic.AddInt32(&c.CurTabCount, 1)
	return &tab, nil
}

func (c *ChromeClient) CloseTab(ID string) bool {
	u := fmt.Sprintf("http://%s:%d/json/close/%s", c.Host, c.Port, ID)
	resp, err := c.HTTPClient.Get(u)
	atomic.AddInt32(&c.CurTabCount, -1)
	if resp != nil {
		defer resp.Body.Close()
	}

	if err != nil || resp.StatusCode != 200 {
		log.Errorln("close tab error:", err)
		return false
	}
	return true
}

func (c *ChromeClient) SyncTabCount() {
	go func() {
		ticker := time.NewTicker(60 * time.Second)
		for t := range ticker.C {
			tabCount := c.getTabCount()
			log.Infof("---- sync chrome tab count ---- at:%v, port:%v, count:%v\n", t.String(), c.Port, tabCount)
			atomic.StoreInt32(&c.CurTabCount, tabCount)
		}
	}()
}

func (c *ChromeClient) getTabCount() int32 {

	var countWhenFail int32 = 200
	u := fmt.Sprintf("http://%s:%d/json", c.Host, c.Port)
	resp, err := c.HTTPClient.Get(u)
	if resp != nil {
		defer resp.Body.Close()
	}

	if err != nil || resp.StatusCode != 200 {
		return countWhenFail
	}

	var tabs []Tab
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorln("Read chrome tabcount response error.", err)
		return countWhenFail
	}

	if err = json.Unmarshal(body, &tabs); err != nil {
		log.Errorln("Unmarshal chrome tabcount response error.", err)
		return countWhenFail
	}
	return int32(len(tabs))
}

func (c *ChromeClient) Navigate(link *common.Link) ([]*common.Webpage, error) {
	t, err := c.NewTab()
	if err != nil {
		log.Errorln("new tab error:", link.URL, err)
		return nil, err
	}
	defer c.CloseTab(t.ID)
	defer t.Close()

	t.Init()
	err = t.Connect()
	if err != nil {
		log.Errorln("tab connect error:", link.URL, err)
		return nil, err
	}
	err = t.InitProtocol()
	if err != nil {
		log.Errorln("chrome init protocol error.", link.URL, err)
		return nil, err
	}

	webpages, err := t.Navigate(link)
	if err != nil {
		log.Errorln("chrome navigate error.", link.URL, link.Depth, err)
		return webpages, err
	}
	return webpages, err
}

func (t *Tab) InitProtocol() error {
	if err := t.networkEvents(true); err != nil {
		log.Errorln(err)
		return err
	}
	if err := t.pageEvents(true); err != nil {
		log.Errorln(err)
		return err
	}
	if err := t.domEvents(true); err != nil {
		log.Errorln(err)
		return err
	}
	if err := t.securityEvents(true); err != nil {
		log.Errorln(err)
		return err
	}
	/*
		if err := t.runtimeEvents(true); err != nil {
			logger.Println(err)
			return err
		}
		if err := t.targetEvents(true); err != nil {
			logger.Println(err)
			return err
		}
		if err := t.setCacheDisabled(true); err != nil {
			log.Errorln(err)
			return err
		}
		if err := t.ClearBrowserCache(); err != nil {
			log.Errorln(err)
			return err
		}
		if err := t.ClearBrowserCookies(); err != nil {
			log.Errorln(err)
			return err
		}
	*/
	if err := t.SetOverrideCertificateErrors(true); err != nil {
		log.Errorln(err)
		return err
	}
	if err := t.setAdBlockingEnabled(true); err != nil {
		log.Errorln(err)
		return err
	}
	if err := t.SetLifecycleEventsEnabled(true); err != nil {
		log.Errorln(err)
		return err
	}
	/*
		if err := t.setDiscoverTargets(true); err != nil {
			logger.Println(err)
			return err
		}
		if err := t.setRequestInterception("*", "Request"); err != nil {
			logger.Println(err)
			return err
		}
	*/

	t.CallbackEvent("Network.requestWillBeSent", t.requestWillBeSentCB)
	t.CallbackEvent("Network.responseReceived", t.responseReceivedCB)
	t.CallbackEvent("Page.javascriptDialogOpening", t.javascriptDialogOpeningCB)
	t.CallbackEvent("Security.certificateError", t.certificateErrorCB)
	t.CallbackEvent("Page.lifecycleEvent", t.lifecycleEventCB)

	return nil
}

func (t *Tab) Connect() error {
	err := t.connectWS()
	if err != nil {
		return err
	}

	t.goroutines.Add(2)
	go t.sendMessage()
	go t.processEvents()

	return nil
}

func (t *Tab) connectWS() error {
	if len(t.WsURL) == 0 {
		return ErrorNoWsURL
	}

	d := &websocket.Dialer{
		ReadBufferSize:  MaxReadBufferSize,
		WriteBufferSize: MaxWriteBufferSize,
	}

	ws, _, err := d.Dial(t.WsURL, nil)
	if err != nil {
		return err
	}

	t.ws = ws

	err = t.ws.SetReadDeadline(time.Now().Add(time.Second * 40))
	if err != nil {
		log.Errorln("SetReadDeadline failed.", err)
		return err
	}
	err = t.ws.SetWriteDeadline(time.Now().Add(time.Second * 40))
	if err != nil {
		log.Errorln("SetWriteDeadline failed.", err)
		return err
	}

	t.goroutines.Add(1)
	go t.readMessage()
	return nil
}

func (t *Tab) Close() (err error) {
	t.finished = true

	if t.ws != nil {
		err = t.ws.Close()
		if err != nil {
			log.Errorln(err)
		}
		t.goroutines.Wait()
	}
	return
}

func permanentError(err error) bool {
	if strings.Contains(err.Error(), "i/o timeout") {
		return true
	}

	if websocket.IsUnexpectedCloseError(err) {
		return true
	}

	if neterr, ok := err.(net.Error); ok && !neterr.Temporary() {
		return true
	}

	log.Errorln(err)
	return false
}

func (t *Tab) readMessage() {
	defer t.goroutines.Done()
	defer func() {
		if err := recover(); err != nil {
			log.Errorln("readMessage recover error.", err)
		}
	}()

reading:
	for {
		if t.finished {
			break reading
		}

		_, bytes, err := t.ws.ReadMessage()
		if err != nil {
			if permanentError(err) {
				break reading
			}
		} else {

			var message wsMessage

			if err := json.Unmarshal(bytes, &message); err != nil {
				log.Errorln("unmarshal", string(bytes), len(bytes), err)
			} else if message.Method != "" {
				t.mutexCallbacks.Lock()
				_, ok := t.callbacks[message.Method]
				t.mutexCallbacks.Unlock()

				if !ok {
					continue // don't queue unrequested events
				}

				t.events <- message

			} else {

				t.mutexResponses.Lock()
				ch := t.responses[message.ID]
				t.mutexResponses.Unlock()

				if ch != nil {
					ch <- message.Result
				}
			}
		}
	}
}

func (t *Tab) sendMessage() {
	defer t.goroutines.Done()

sending:
	for {
		if t.finished {
			break sending
		}

		select {
		case message := <-t.requests:
			err := t.ws.WriteMessage(websocket.TextMessage, message)
			if err != nil {
				log.Errorln("write message:", err, string(message))
				if strings.Contains(err.Error(), "i/o timeout") {
					break sending
				}
			}
		default:
			time.Sleep(time.Millisecond * 10)
		}
	}
}

func (t *Tab) SendRequest(method string, mParams interface{}) (json.RawMessage, error) {
	rawReply, err := t.sendRawReplyRequest(method, mParams)
	if err != nil || rawReply == nil {
		return nil, err
	}
	return rawReply, nil
}

func (t *Tab) sendRawReplyRequest(method string, mParams interface{}) (json.RawMessage, error) {
	responseChan := make(chan json.RawMessage, 1)

	t.mutexResponses.Lock()
	reqID := t.reqID
	t.reqID++
	t.responses[reqID] = responseChan
	t.mutexResponses.Unlock()

	defer t.delRespChan(reqID)

	command := MethodReqParams{
		ID:     reqID,
		Method: method,
		Params: mParams,
	}

	rawCmd, err := json.Marshal(command)
	if err != nil {
		return []byte{}, err
	}

	if t.finished {
		log.Infoln("Chrome navigate finished. rawCmd: ", string(rawCmd))
		return []byte{}, errors.New("Chrome navigate finished")
	}

	timer := time.NewTimer(time.Second * 10)
	select {
	case t.requests <- rawCmd:
		timer.Stop()
	case <-timer.C:
		log.Errorln("Send Request to t.requests error", t.requestURL, string(rawCmd))
		return nil, errors.New("Send Request to t.requests error")
	}

	timer = time.NewTimer(time.Second * 10)
	select {
	case reply := <-responseChan:
		return reply, nil
	case <-timer.C:
		log.Errorln(string(rawCmd), ", receive reply timeout.")
		return nil, errors.New("receive reply timeout")
	}
}

func (t *Tab) delRespChan(reqID int) {
	t.mutexResponses.Lock()
	delete(t.responses, reqID)
	t.mutexResponses.Unlock()
}

func (t *Tab) processEvents() {
	defer t.goroutines.Done()

processing:
	for {
		if t.finished {
			break processing
		}

		select {
		case ev := <-t.events:
			t.mutexCallbacks.Lock()
			cb := t.callbacks[ev.Method]
			t.mutexCallbacks.Unlock()

			if cb != nil {
				cb(ev.Params)
			}
		default:
			time.Sleep(time.Millisecond * 20)
		}
	}
}

func (t *Tab) closeOpenedTargets() {
	defer t.goroutines.Done()

close:
	for {
		if t.finished {
			break close
		}

		select {
		case targetID := <-t.targetIDChan:
			success, err := t.closeTarget(targetID)
			if err != nil {
				log.Errorln(err)
				break
			}
			log.Infoln("close target:", targetID, "success:", success)
		default:
			time.Sleep(time.Millisecond * 50)
		}
	}
}

func (t *Tab) CallbackEvent(method string, cb EventCallback) {
	t.mutexCallbacks.Lock()
	t.callbacks[method] = cb
	t.mutexCallbacks.Unlock()
}

func (t *Tab) Navigate(link *common.Link) ([]*common.Webpage, error) {
	log.Infoln("chrome start navigate:", link.URL, link.Depth)
	webPages := []*common.Webpage{}
	t.requestURL = link.URL
	linkDomain, err := GetDomain(link.URL)
	if err != nil {
		return webPages, err
	}
	wp := new(common.Webpage)
	wp.IsStatic = link.Static
	wp.Referer = link.Referer
	wp.Data = link.Data
	wp.Method = link.Method
	wp.URL = link.URL
	wp.Depth = link.Depth
	wp.JobID = link.JobID
	reqHeaders := link.Headers
	if reqHeaders != nil {
		err = t.SetExtraHTTPHeaders(reqHeaders)
		if err != nil {
			log.Errorln(err)
			return webPages, err
		}
	}
	if link.UserAgent != "" {
		err = t.SetUserAgent(link.UserAgent)
		if err != nil {
			log.Errorln(err)
			return webPages, err
		}
	}

	/*
		if link.Cookiejar != nil {
			cookieParams := GetCookieParams(link.Cookiejar)
			if len(cookieParams) > 0 {
				args := NewSetCookiesArgs(cookieParams)
				err := t.SetCookies(args)
				if err != nil {
					logger.Println(err)
					return nil, err
				}
			}
		}
	*/

	if err := t.AddScriptToEvaluateOnNewDocument(t.InjectJavaScript.InjectBeforeLoad); err != nil {
		log.Errorln(err)
	}

	frameID, err := t.navigate(link.URL)
	if err != nil {
		log.Errorln(err)
		return webPages, err
	}
	t.frameID = frameID

	t.waitNetworkIdle()

	if err := t.Evaluate(t.InjectJavaScript.InjectAfterLoad, true); err != nil {
		log.Errorln(err)
	}

	frameTree, err := t.GetResourceTree()
	if err != nil {
		log.Errorln(err)
		return webPages, err
	}

	rootNodeID, err := t.getRootNodeID()
	if err != nil {
		return webPages, errors.New("get root nodeId err")
	}

	wp.Content = t.getHTML(rootNodeID)
	if len(wp.Content) == 0 {
		return webPages, errors.New("main frame content is nil")
	}

	statusCode, headers, pageURL := t.getHeaders(frameTree.Frame.URL)
	if statusCode != 599 && pageURL != "" {

		childFrames := []Frame{}
		t.getChildFrames(linkDomain, frameTree, &childFrames)

		filterURLS := map[string]bool{}
		filterURLS[link.URL] = true
		filterURLS[pageURL] = true
		for i := range childFrames {
			filterURLS[childFrames[i].URL] = true
		}

		childWebpages := t.getChildWebpages(&childFrames, linkDomain, filterURLS)
		for i := range childWebpages {
			tmpWP := childWebpages[i]
			tmpWP.Depth = link.Depth
			tmpWP.Referer = link.URL
			tmpWP.AssetID = link.AssetID
			tmpWP.JobID = link.JobID
			tmpStatusCode, tmpHeaders, effectiveURL := t.getHeaders(childWebpages[i].URL)
			tmpWP.StatusCode = int(tmpStatusCode)
			tmpWP.Headers = mergeHeaders(link.Headers, convertHeaders(tmpHeaders))
			tmpWP.EffectiveURL = effectiveURL
			tmpWP.MainFrameURL = wp.URL
			for j := range tmpWP.NetworkWebpages {
				ptr := &tmpWP.NetworkWebpages[j]
				ptr.Headers = mergeHeaders(link.Headers, ptr.Headers)
			}
			webPages = append(webPages, tmpWP)
		}
		/*

			if link.Depth == 0 {
				t.simulatedClick(rootNodeID)
			}
		*/

		networkWebpages := t.getNetworkWebpages(linkDomain, frameTree.Frame.ID, pageURL, filterURLS)
		for i := range networkWebpages {
			ptr := &networkWebpages[i]
			ptr.Headers = mergeHeaders(link.Headers, ptr.Headers)
		}
		wp.NetworkWebpages = networkWebpages
	}

	wp.EffectiveURL = pageURL
	wp.StatusCode = int(statusCode)
	wp.Headers = mergeHeaders(link.Headers, convertHeaders(headers))
	wp.ByChrome = true
	wp.IsOuterURL = !IsDomainEqual(linkDomain, pageURL)
	if link.MainFrameURL != "" {
		wp.MainFrameURL = link.MainFrameURL
	}
	webPages = append(webPages, wp)

	t.recResponses = map[string]*Response{}
	t.sentRequests = map[string][]eventRequestWillBeSent{}
	t.responses = map[int]chan json.RawMessage{}
	t.callbacks = map[string]EventCallback{}

	err = nil
	if wp.StatusCode == 599 {
		err = errors.New("chrome 599 errors")
	}
	return webPages, err
}

func (t *Tab) domainEvents(domain string, enable bool) error {
	method := domain

	if enable {
		method += ".enable"
	} else {
		method += ".disable"
	}

	_, err := t.SendRequest(method, nil)
	return err
}

func (t *Tab) pageEvents(enable bool) error {
	return t.domainEvents("Page", enable)
}

func (t *Tab) domEvents(enable bool) error {
	return t.domainEvents("DOM", enable)
}

func (t *Tab) networkEvents(enable bool) error {
	return t.domainEvents("Network", enable)
}

func (t *Tab) securityEvents(enable bool) error {
	return t.domainEvents("Security", enable)
}

func (t *Tab) runtimeEvents(enable bool) error {
	return t.domainEvents("Runtime", enable)
}

func (t *Tab) targetEvents(enable bool) error {
	return t.domainEvents("Target", enable)
}

type navigateParams struct {
	URL     string `json:"url"`
	Referer string `json:"referer,omitempty"`
}

type navigateReturns struct {
	FrameID   string `json:"frameId,omitempty"`
	LoaderID  string `json:"loaderId,omitempty"`
	ErrorText string `json:"errorText,omitempty"`
}

func (t *Tab) navigate(u string) (string, error) {
	res, err := t.SendRequest("Page.navigate", navigateParams{
		URL: u,
	})

	if err != nil {
		return "", err
	}

	var ret navigateReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return "", err
	}
	return ret.FrameID, nil
}

type setUserAgentParams struct {
	UserAgent string `json:"userAgent"`
}

func (t *Tab) SetUserAgent(userAgent string) error {
	_, err := t.SendRequest("Network.setUserAgentOverride", setUserAgentParams{
		UserAgent: userAgent,
	})
	return err
}

type setBlockedURLSParams struct {
	Urls []string `json:"urls"`
}

func (t *Tab) SetBlockedURLs(urls ...string) error {
	_, err := t.SendRequest("Network.setBlockedURLs", setBlockedURLSParams{
		Urls: urls,
	})
	return err
}

func (t *Tab) ClearBrowserCache() error {
	_, err := t.SendRequest("Network.clearBrowserCache", nil)
	return err
}

func (t *Tab) ClearBrowserCookies() error {
	_, err := t.SendRequest("Network.clearBrowserCookies", nil)
	return err
}

type setOverrideCertificateErrorsParams struct {
	Override bool `json:"override"`
}

func (t *Tab) SetOverrideCertificateErrors(override bool) error {
	_, err := t.SendRequest("Security.setOverrideCertificateErrors", setOverrideCertificateErrorsParams{
		Override: override,
	})
	return err
}

type setExtraHTTPHeadersParams struct {
	Headers chromeHeaders `json:"headers"`
}

func (t *Tab) SetExtraHTTPHeaders(headers common.HttpHeaders) error {
	_, err := t.SendRequest("Network.setExtraHTTPHeaders", setExtraHTTPHeadersParams{
		Headers: toChromeHeaders(headers),
	})
	return err
}

type Node struct {
	NodeID           int64    `json:"nodeId"`
	ParentID         int64    `json:"parentId,omitempty"`
	NodeType         int64    `json:"nodeType"`
	NodeName         string   `json:"nodeName"`
	LocalName        string   `json:"localName"`
	NodeValue        string   `json:"nodeValue"`
	ChildNodeCount   int64    `json:"childNodeCount,omitempty"`
	Children         []*Node  `json:"children,omitempty"`
	Attributes       []string `json:"attributes,omitempty"`
	DocumentURL      string   `json:"documentURL,omitempty"`
	BaseURL          string   `json:"baseURL,omitempty"`
	PublicID         string   `json:"publicId,omitempty"`
	SystemID         string   `json:"systemId,omitempty"`
	InternalSubset   string   `json:"internalSubset,omitempty"`
	XMLVersion       string   `json:"xmlVersion,omitempty"`
	Name             string   `json:"name,omitempty"`
	Value            string   `json:"value,omitempty"`
	PseudoType       string   `json:"pseudoType,omitempty"`
	ShadowRootType   string   `json:"shadowRootType,omitempty"`
	FrameID          string   `json:"frameId,omitempty"`
	ContentDocument  *Node    `json:"contentDocument,omitempty"`
	ShadowRoots      []*Node  `json:"shadowRoots,omitempty"`
	TemplateContent  *Node    `json:"templateContent,omitempty"`
	PseudoElements   []*Node  `json:"pseudoElements,omitempty"`
	ImportedDocument *Node    `json:"importedDocument,omitempty"`
	IsSVG            bool     `json:"isSVG,omitempty"`
}

type getDocumentReturns struct {
	Root *Node `json:"root,omitempty"`
}

func (t *Tab) GetDocument() (*Node, error) {
	rawResult, err := t.SendRequest("DOM.getDocument", nil)
	if err != nil {
		return nil, err
	}
	var root getDocumentReturns
	err = json.Unmarshal(rawResult, &root)
	if err != nil {
		return nil, err
	}
	return root.Root, nil
}

func (t *Tab) getRootNodeID() (int64, error) {
	res, err := t.GetDocument()
	if err != nil {
		return 0, err
	}

	return res.NodeID, nil
}

type getOuterHTMLParams struct {
	NodeID int64 `json:"nodeId"`
}

type getOuterHTMLReturns struct {
	OuterHTML string `json:"outerHTML,omitempty"`
}

func (t *Tab) GetOuterHTML(nodeID int64) ([]byte, error) {
	res, err := t.SendRequest("DOM.getOuterHTML", getOuterHTMLParams{
		NodeID: nodeID,
	})
	if err != nil {
		log.Errorln(err)
		return []byte{}, err
	}

	var oHTML getOuterHTMLReturns
	err = json.Unmarshal(res, &oHTML)
	if err != nil {
		log.Errorln(err)
		return []byte{}, err
	}

	return []byte(oHTML.OuterHTML), nil
}

type handleJavaScriptDialogParams struct {
	Accept     bool   `json:"accept"`
	PromptText string `json:"promptText"`
}

func (t *Tab) HandleJavaScriptDialog(accept bool, promptText string) error {
	_, err := t.SendRequest("Page.handleJavaScriptDialog", handleJavaScriptDialogParams{
		Accept:     accept,
		PromptText: promptText,
	})

	return err
}

func (t *Tab) javascriptDialogOpeningCB(params json.RawMessage) {
	err := t.HandleJavaScriptDialog(true, "")
	if err != nil {
		log.Errorln(err)
	}

}

type getResponseBodyParams struct {
	RequestID string `json:"requestId"`
}

type getResponseBodyReturns struct {
	Body          string `json:"body,omitempty"`
	Base64encoded bool   `json:"base64Encoded,omitempty"`
}

func (t *Tab) GetResponseBody(requestID string) ([]byte, error) {

	res, err := t.SendRequest("Network.getResponseBody", getResponseBodyParams{
		RequestID: requestID,
	})
	if err != nil {
		return []byte{}, err
	}

	var ret getResponseBodyReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return []byte{}, err
	}

	if ret.Base64encoded {
		return base64.StdEncoding.DecodeString(ret.Body)
	}
	return []byte(ret.Body), nil
}

type setCacheDisableedParams struct {
	CacheDisabled bool `json:"cacheDisabled"`
}

func (t *Tab) setCacheDisabled(cacheDisabled bool) error {
	_, err := t.SendRequest("Network.setCacheDisabled", setCacheDisableedParams{
		CacheDisabled: cacheDisabled,
	})

	return err
}

type Frame struct {
	ID             string `json:"id"`
	ParentID       string `json:"parentId,omitempty"`
	LoaderID       string `json:"loaderId"`
	Name           string `json:"name"`
	URL            string `json:"url"`
	SecurityOrigin string `json:"securityOrigin"`
	MimeType       string `json:"mimeType"`
	UnreachableURL string `json:"unreachableUrl,omitempty"`
}

type FrameTree struct {
	Frame       Frame       `json:"frame"`
	ChildFrames []FrameTree `json:"childFrames,omitempty"`
}

type GetResourceTreeReturns struct {
	FrameTree FrameTree `json:"frameTree,omitempty"`
}

func (t *Tab) GetResourceTree() (*FrameTree, error) {
	res, err := t.SendRequest("Page.getResourceTree", nil)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}

	var ret GetResourceTreeReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}

	return &ret.FrameTree, nil
}

func (t *Tab) getChildFrames(domain string, frameTree *FrameTree, frames *[]Frame) {
	if len(frameTree.ChildFrames) > 0 {
		for i := range frameTree.ChildFrames {

			frameURL := frameTree.ChildFrames[i].Frame.URL
			if "about:blank" == frameURL {
				continue
			}

			if IsBlocked(frameURL) {
				continue
			}

			*frames = append(*frames, frameTree.ChildFrames[i].Frame)
			if len(frameTree.ChildFrames[i].ChildFrames) > 0 {
				t.getChildFrames(domain, &frameTree.ChildFrames[i], frames)
			}
		}
	}
}

func (t *Tab) getChildWebpages(frames *[]Frame, domain string, filterURLS map[string]bool) []*common.Webpage {
	webpages := []*common.Webpage{}
	for i := range *frames {
		chromeFrame := (*frames)[i]
		content, err := t.GetResourceContent(chromeFrame.ID, chromeFrame.URL)
		if err != nil {
			log.Errorln(err, chromeFrame)
			continue
		}

		wp := common.Webpage{
			URL:             chromeFrame.URL,
			EffectiveURL:    chromeFrame.URL,
			ByChrome:        true,
			Method:          http.MethodGet,
			NetworkWebpages: t.getNetworkWebpages(domain, chromeFrame.ID, chromeFrame.URL, filterURLS),
		}
		wp.Content = content

		statusCode, _, _ := t.getHeaders((*frames)[i].URL)
		wp.StatusCode = int(statusCode)

		wp.IsOuterURL = !IsDomainEqual(domain, wp.URL)
		webpages = append(webpages, &wp)
	}

	return webpages
}

type getResourceContentParams struct {
	FrameID string `json:"frameId"`
	URL     string `json:"url"`
}

type getResourceContentReturns struct {
	Content       string `json:"content,omitempty"`
	Base64encoded bool   `json:"base64Encoded,omitempty"`
}

func (t *Tab) GetResourceContent(frameID, u string) ([]byte, error) {
	res, err := t.SendRequest("Page.getResourceContent", getResourceContentParams{
		FrameID: frameID,
		URL:     u,
	})

	if err != nil {
		return nil, err
	}

	var ret getResourceContentReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return nil, err
	}

	var dec []byte
	if ret.Base64encoded {
		dec, err = base64.StdEncoding.DecodeString(ret.Content)
		if err != nil {
			return nil, err
		}
	} else {
		dec = []byte(ret.Content)
	}
	return dec, nil
}

type handleCertificateErrorParams struct {
	Action  string `json:"action"`
	EventID int64  `json:"eventId"`
}

func (t *Tab) handleCertificateError(action string, eventID int64) error {
	_, err := t.SendRequest("Security.handleCertificateError", handleCertificateErrorParams{
		Action:  action,
		EventID: eventID,
	})

	return err
}

type eventCertificateError struct {
	EventID    int64  `json:"eventId"`
	ErrorType  string `json:"errorType"`
	RequestURL string `json:"requestURL"`
}

func (t *Tab) certificateErrorCB(params json.RawMessage) {
	var ev eventCertificateError
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
	}
	eventID := ev.EventID
	err = t.handleCertificateError("continue", eventID)
	if err != nil {
		log.Errorln(err)
	}
}

func (t *Tab) loadEventFiredCB(params json.RawMessage) {
	timer := time.NewTimer(10 * time.Second)
	select {
	case t.loaded <- true:
		return
	case <-timer.C:
		log.Infoln("write loadEventFired to chan failed.", t.requestURL)
		return
	}
}

type Response struct {
	URL                string            `json:"url"`                          // Response URL. This URL can be different from CachedResource.url in case of redirect.
	Status             int64             `json:"status"`                       // HTTP response status code.
	StatusText         string            `json:"statusText"`                   // HTTP response status text.
	Headers            map[string]string `json:"headers"`                      // HTTP response headers.
	HeadersText        string            `json:"headersText,omitempty"`        // HTTP response headers text.
	MimeType           string            `json:"mimeType"`                     // Resource mimeType as determined by the browser.
	RequestHeaders     map[string]string `json:"requestHeaders,omitempty"`     // Refined HTTP request headers that were actually transmitted over the network.
	RequestHeadersText string            `json:"requestHeadersText,omitempty"` // HTTP request headers text.
	ConnectionReused   bool              `json:"connectionReused"`             // Specifies whether physical connection was actually reused for this request.
	ConnectionID       float64           `json:"connectionId"`                 // Physical connection id that was actually used for this request.
	RemoteIPAddress    string            `json:"remoteIPAddress,omitempty"`    // Remote IP address.
	RemotePort         int64             `json:"remotePort,omitempty"`         // Remote port.
	FromDiskCache      bool              `json:"fromDiskCache,omitempty"`      // Specifies that the request was served from the disk cache.
	FromServiceWorker  bool              `json:"fromServiceWorker,omitempty"`  // Specifies that the request was served from the ServiceWorker.
	EncodedDataLength  float64           `json:"encodedDataLength"`            // Total number of bytes received for this request so far.
	Protocol           string            `json:"protocol,omitempty"`           // Protocol used to fetch this request.
}

type eventResponseReceived struct {
	RequestID string    `json:"requestId"`
	LoaderID  string    `json:"loaderId"`
	Type      string    `json:"type"`
	FrameID   string    `json:"frameId"`
	Response  *Response `json:"response"`
}

func (t *Tab) responseReceivedCB(params json.RawMessage) {
	var ev eventResponseReceived
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
	}
	t.mutexRecResponses.Lock()
	defer t.mutexRecResponses.Unlock()
	t.recResponses[ev.Response.URL] = ev.Response
}

type Request struct {
	URL              string            `json:"url"`                        // Request URL.
	Method           string            `json:"method"`                     // HTTP request method.
	Headers          map[string]string `json:"headers"`                    // HTTP request headers.
	PostData         string            `json:"postData,omitempty"`         // HTTP POST request data.
	MixedContentType string            `json:"mixedContentType,omitempty"` // The mixed content type of the request.
	InitialPriority  string            `json:"initialPriority"`            // Priority of the resource request at the time request is sent.
	ReferrerPolicy   string            `json:"referrerPolicy"`             // The referrer policy of the request, as defined in https://www.w3.org/TR/referrer-policy/
	IsLinkPreload    bool              `json:"isLinkPreload,omitempty"`    // Whether is loaded via link preload.
}

type eventRequestWillBeSent struct {
	RequestID        string    `json:"requestId"`
	LoaderID         string    `json:"loaderId"`
	DocumentURL      string    `json:"documentURL"`
	Request          *Request  `json:"request"`
	RedirectResponse *Response `json:"redirectResponse"`
	Type             string    `json:"type"`
	FrameID          string    `json:"frameId"`
}

func (t *Tab) requestWillBeSentCB(params json.RawMessage) {
	var ev eventRequestWillBeSent
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
		return
	}

	frameID := ev.FrameID
	request := ev.Request
	curReqURL := request.URL
	tmpReqURL := NoSchemeTailURL(t.requestURL)
	if tmpReqURL != NoSchemeTailURL(curReqURL) {
		if _, ok := t.sentRequests[frameID]; ok {
			t.sentRequests[frameID] = append(t.sentRequests[frameID], ev)
		} else {
			t.sentRequests[frameID] = []eventRequestWillBeSent{ev}
		}
	}
}

func (t *Tab) getNetworkWebpages(linkDomain string, frameID string, pageURL string, filterURLS map[string]bool) []common.NetworkWebpage {
	ret := []common.NetworkWebpage{}
	filter := map[string]bool{}
	var err error

	if _, ok := t.sentRequests[frameID]; !ok {
		return ret
	}
	sentRequests := t.sentRequests[frameID]
	for i := range sentRequests {
		u := sentRequests[i].Request.URL
		if _, ok := filter[u]; ok {

			continue
		}

		if _, ok := filterURLS[u]; ok {
			continue
		}

		if IsBlocked(u) {
			continue
		}

		newPage := common.NetworkWebpage{}
		newPage.URL = sentRequests[i].Request.URL
		newPage.Method = sentRequests[i].Request.Method
		newPage.Data = sentRequests[i].Request.PostData

		statueCode, headers, _ := t.getHeaders(newPage.URL)
		newPage.StatusCode = int(statueCode)
		newPage.Headers = convertHeaders(headers)

		resType := sentRequests[i].Type
		if _, ok := StaticResourceType[resType]; ok {
			newPage.IsStatic = true
		} else {
			newPage.IsStatic = false
		}

		content := []byte{}
		if resType == "Document" {
			if _, ok := t.abortedURLS[sentRequests[i].Request.URL]; !ok {
				content, err = t.GetResponseBody(sentRequests[i].RequestID)
				if err != nil {
					content = []byte{}
					log.Errorln(err, sentRequests[i], pageURL)
				}
			}
		}
		newPage.Content = content

		newPage.IsOuterURL = !IsDomainEqual(linkDomain, newPage.URL)

		ret = append(ret, newPage)
		filter[newPage.URL] = true
	}
	return ret
}

func (t *Tab) waitNetworkIdle() {
	timer := time.NewTimer(30 * time.Second)
	reCheck := false

checking:
	for {
		time.Sleep(1 * time.Second)

		select {
		case <-timer.C:
			break checking
		default:
		}

		tmpNetworkIdle := true
		t.lifecycleEvents.Range(func(k, v interface{}) bool {
			tmpIdle := false
			for _, vv := range v.([]string) {
				if vv == "networkIdle" {
					tmpIdle = true
					break
				}
			}
			if !tmpIdle {
				tmpNetworkIdle = false
				return false
			}
			return true
		})

		if tmpNetworkIdle && !reCheck {
			reCheck = true
			continue
		} else if !tmpNetworkIdle && !reCheck {
			continue
		} else if tmpNetworkIdle && reCheck {
			load := true
			t.lifecycleEvents.Range(func(k, v interface{}) bool {
				tmpLoad := false
				for _, vv := range v.([]string) {
					if vv == "load" {
						tmpLoad = true
						break
					}
				}
				if !tmpLoad {
					load = false
					return false
				}
				return true
			})

			if load {
				break checking
			} else {
				continue
			}
		}
	}
	t.networkIdled = true
}

func (t *Tab) waitLoaded() {
	timer := time.NewTimer(30 * time.Second)
	select {
	case <-t.loaded:
		return
	case <-t.refreshed:
		return
	case <-timer.C:
		return
	}
}

func (t *Tab) waitRefreshed() {
	timer := time.NewTimer(1 * time.Second)
	select {
	case <-t.refreshed:
		return
	case <-timer.C:
		return
	}
}

func (t *Tab) getHTML(nodeID int64) []byte {
	html, err := t.GetOuterHTML(nodeID)
	if err != nil {
		log.Errorln(err)
		return []byte("")
	}
	return html
}

func (t *Tab) getHeaders(u string) (int64, map[string]string, string) {
	var status int64 = 599
	headers := map[string]string{}
	pageURL := ""

	t.mutexRecResponses.Lock()
	defer t.mutexRecResponses.Unlock()
	if resp, ok := t.recResponses[u]; ok {
		status = resp.Status
		headers = resp.Headers
		pageURL = resp.URL
	}
	return status, headers, pageURL
}

type setAdBlockingEnabledParams struct {
	Enabled bool `json:"enabled"`
}

func (t *Tab) setAdBlockingEnabled(enabled bool) error {
	_, err := t.SendRequest("Page.setAdBlockingEnabled", setAdBlockingEnabledParams{
		Enabled: enabled,
	})

	return err
}

func (t *Tab) SetCookie(args *SetCookieArgs) error {
	_, err := t.SendRequest("Network.setCookie", args)
	return err
}

func (t *Tab) SetCookies(args *SetCookiesArgs) error {
	_, err := t.SendRequest("Network.setCookies", args)
	return err
}

func (t *Tab) Evaluate(expression string, returnByValue bool) error {
	if len(expression) == 0 {
		return errors.New("expression is nil")
	}

	_, err := t.SendRequest("Runtime.evaluate", evaluateParams{
		Expression:    expression,
		ReturnByValue: returnByValue,
	})
	return err
}

func (t *Tab) AddScriptToEvaluateOnNewDocument(source string) error {
	if len(source) == 0 {
		return errors.New("source is nil")
	}

	_, err := t.SendRequest(
		"Page.addScriptToEvaluateOnNewDocument",
		addScriptToEvaluateOnNewDocumentParams{
			Source: source,
		})
	return err
}

func (t *Tab) SetLifecycleEventsEnabled(enabled bool) error {
	_, err := t.SendRequest(
		"Page.setLifecycleEventsEnabled",
		setLifecycleEventsEnabledParams{
			Enabled: enabled,
		})
	return err
}

func (t *Tab) lifecycleEventCB(params json.RawMessage) {
	var ev lifecycleEvent
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
		return
	}

	frameID := ev.FrameID
	if _, ok := t.lifecycleEvents.Load(frameID); ok {
		if ev.Name == "init" {

			t.lifecycleEvents.Store(frameID, []string{})
		} else {
			v, _ := t.lifecycleEvents.Load(frameID)
			v = append(v.([]string), ev.Name)
			t.lifecycleEvents.Store(frameID, v)
		}
	} else {
		v := []string{ev.Name}
		t.lifecycleEvents.Store(frameID, v)
	}
}

func (t *Tab) windowOpenCB(params json.RawMessage) {
	var ev eventWindowOpen
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
		return
	}

	log.Infoln(ev.URL, ev.WindowName, ev.WindowFeatures, ev.UserGesture)
}

func (t *Tab) targetCreatedCB(params json.RawMessage) {
	var ev eventTargetCreated
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
		return
	}

	info := ev.TargetInfo
	log.Infoln(info.URL, info.Type, info.Title, info.TargetID, info.OpenerID, info.BrowserContextID, info.Attached)

	timer := time.NewTimer(1 * time.Second)
	select {
	case t.targetIDChan <- info.TargetID:
		return
	case <-timer.C:
		return
	}
}

func (t *Tab) setDiscoverTargets(discover bool) error {
	_, err := t.SendRequest(
		"Target.setDiscoverTargets",
		setDiscoverTargetsParams{
			Discover: discover,
		})
	return err
}

func (t *Tab) closeTarget(targetID string) (bool, error) {
	res, err := t.SendRequest(
		"Target.closeTarget",
		closeTargetParams{
			TargetID: targetID,
		})

	if err != nil {
		log.Errorln("close target error, targetId:", targetID, err)
		return false, err
	}

	var ret closeTargetReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return false, err
	}

	return ret.Success, nil
}

func (t *Tab) setRequestInterception(pattern, interceptionStage string) error {
	_, err := t.SendRequest(
		"Network.setRequestInterception",
		setRequestInterceptionParams{
			Patterns: []requestPattern{
				requestPattern{
					URLPattern:        pattern,
					InterceptionStage: interceptionStage,
				},
			},
		})
	return err
}

func (t *Tab) requestInterceptedCB(params json.RawMessage) {
	var ev eventRequestIntercepted
	err := json.Unmarshal(params, &ev)
	if err != nil {
		log.Errorln(err)
		return
	}

	if ev.IsNavigationRequest && t.networkIdled {

		if err := t.continueInterceptedRequest(ev.InterceptionID, "Aborted"); err == nil {
			t.mutexAbortedURLS.Lock()
			defer t.mutexAbortedURLS.Unlock()
			t.abortedURLS[ev.Request.URL] = true
		} else {
			log.Errorln(ev, ev.Request.URL, "abort failed.")
		}
	} else {
		if err := t.continueInterceptedRequest(ev.InterceptionID, ""); err != nil {
			log.Errorln(ev, ev.Request.URL, err, "continue failed.")
		}
	}
}

func (t *Tab) continueInterceptedRequest(interceptionID, errorReason string) error {
	_, err := t.SendRequest(
		"Network.continueInterceptedRequest",
		continueInterceptedRequestParams{
			InterceptionID: interceptionID,
			ErrorReason:    errorReason,
		})
	return err
}

func (t *Tab) getNodeCenterCoordinate(nodeID int64) (int64, int64, error) {
	res, err := t.SendRequest(
		"DOM.getBoxModel",
		getBoxModelParams{
			NodeID: nodeID,
		})
	if err != nil {
		return 0, 0, err
	}

	var ret getBoxModelReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return 0, 0, err
	}

	x, y := ret.Model.getCenterCoordinate()
	return x, y, nil
}

func (t *Tab) dispatchMouseEvent(typ string, x, y int64) error {

	_, err := t.SendRequest(
		"Input.dispatchMouseEvent",
		dispatchMouseEventParams{
			Type:       typ,
			X:          x,
			Y:          y,
			Button:     "left",
			ClickCount: 1,
		})
	return err
}

func (t *Tab) querySelectorAll(nodeID int64, selector string) ([]int64, error) {
	res, err := t.SendRequest(
		"DOM.querySelectorAll",
		querySelectorAllParams{
			NodeID:   nodeID,
			Selector: selector,
		})
	if err != nil {
		return []int64{}, err
	}

	var ret querySelectorAllReturns
	err = json.Unmarshal(res, &ret)
	if err != nil {
		return []int64{}, err
	}

	return ret.NodeIDs, nil
}

func (t *Tab) simulatedClick(rootNodeID int64) {
	nodeIDs, err := t.querySelectorAll(rootNodeID, "a:not([href])")
	if err != nil {
		log.Errorln(err)
		return
	}

	timer := time.NewTimer(5 * time.Second)
	for i := range nodeIDs {

		select {
		case <-timer.C:
			return
		default:
			break
		}

		x, y, err := t.getNodeCenterCoordinate(nodeIDs[i])
		if err != nil {
			continue
		}

		err = t.dispatchMouseEvent("mousePressed", x, y)
		if err != nil {
			log.Errorln(err)
		}
		err = t.dispatchMouseEvent("mouseReleased", x, y)
		if err != nil {
			log.Errorln(err)
		}
	}
}
