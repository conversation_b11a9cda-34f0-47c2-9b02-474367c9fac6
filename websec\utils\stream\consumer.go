package stream

import (
	"sync"
	"websec/utils/log"

	"github.com/confluentinc/confluent-kafka-go/kafka"
)

type ConsumerOptions struct {
	Brokers              string
	Group                string
	FetchMessageMaxBytes int
	QueuedMinMessages    int
	MaxMessageKBytes     int
}

type Consumer struct {
	kafkaConsumer *kafka.Consumer

	messageChan chan *Message
	waitGroup   sync.WaitGroup
	closeOnce   sync.Once
}

func (consumer *Consumer) Messages() <-chan *Message {
	return consumer.messageChan
}

func (consumer *Consumer) consume() {
	defer func() {
		consumer.waitGroup.Done()
	}()
	for ev := range consumer.kafkaConsumer.Events() {
		switch e := ev.(type) {
		case kafka.AssignedPartitions:
			log.Infof("kafka.AssignedPartitions  %v", e)
			consumer.kafkaConsumer.Assign(e.Partitions)
		case kafka.RevokedPartitions:
			log.Infof("kafka.RevokedPartitions  %v", e)
			consumer.kafkaConsumer.Unassign()
		case *kafka.Message:
			log.Infof("consumer receive kafka msg  topicpartition:%v, header:%v, timestamp:%d",
				e.TopicPartition, e.Headers, e.Timestamp.Unix())
			consumer.messageChan <- &Message{
				Topic: *e.TopicPartition.Topic,
				Value: e.Value,
			}
		case kafka.PartitionEOF:
			log.Infof("PartitionEOF Reached %v", e)
		case kafka.Error:
			log.Error("kafka.Error %v", e)
		}
	}
}

func (consumer *Consumer) rebalanceCB() kafka.RebalanceCb {
	return func(c *kafka.Consumer, event kafka.Event) error {
		switch e := event.(type) {
		case kafka.AssignedPartitions:
			log.Infoln("rebalance assigned", e.Partitions)
			c.Assign(e.Partitions)
		case kafka.RevokedPartitions:
			log.Infoln("rebalance unassigned", e.Partitions)
			c.Unassign()
		}
		return nil
	}
}

func (consumer *Consumer) SubscribeTopics(topics []string) error {
	return consumer.kafkaConsumer.SubscribeTopics(topics, consumer.rebalanceCB())
}

func (consumer *Consumer) Go() {
	consumer.waitGroup.Add(1)
	go consumer.consume()
}

func (consumer *Consumer) Close() {
	consumer.closeOnce.Do(func() {
		consumer.kafkaConsumer.Close()
		consumer.waitGroup.Wait()
		close(consumer.messageChan)
	})
}

func NewConsumer(options *ConsumerOptions) (*Consumer, error) {
	if options.QueuedMinMessages == 0 {
		options.QueuedMinMessages = defaultQueuedMinMessages
	}
	if options.MaxMessageKBytes == 0 {
		options.MaxMessageKBytes = defaultMaxMessageKBytes
	}

	kafkaConsumer, err := kafka.NewConsumer(&kafka.ConfigMap{
		"bootstrap.servers":               options.Brokers,
		"group.id":                        options.Group,
		"session.timeout.ms":              30 * 1000,
		"fetch.message.max.bytes":         options.FetchMessageMaxBytes,
		"go.events.channel.enable":        true,
		"go.application.rebalance.enable": true,
		"default.topic.config":            kafka.ConfigMap{"auto.offset.reset": "earliest"},
		"heartbeat.interval.ms":           5000,
		"queued.max.messages.kbytes":      options.MaxMessageKBytes,
		"queued.min.messages":             options.QueuedMinMessages,
	})
	if err != nil {
		return nil, err
	}
	consumer := &Consumer{
		kafkaConsumer: kafkaConsumer,
		messageChan:   make(chan *Message, 100),
	}
	return consumer, nil
}
