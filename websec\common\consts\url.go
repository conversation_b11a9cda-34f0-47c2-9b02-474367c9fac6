package consts

import "net/http"

const (
	MethodBitGet int32 = 1 << iota
	MethodBitHead
	MethodBitPost
	MethodBitPut
	MethodBitDelete
	MethodBitConnect
	MethodBitOptions
	MethodBitTrace
	MethodBitPatch
)

func GetMethodBit(m string) int32 {
	switch m {
	case http.MethodHead:
		return MethodBitHead
	case http.MethodGet:
		return MethodBitGet
	case http.MethodPost:
		return MethodBitPost
	case http.MethodPut:
		return MethodBitPut
	case http.MethodPatch:
		return MethodBitPatch
	case http.MethodOptions:
		return MethodBitOptions
	default:
		return 0
	}
}

func GetHttpMethod(m int32) string {
	switch m {
	case MethodBitGet:
		return http.MethodGet
	case MethodBitHead:
		return http.MethodHead
	case MethodBitPost:
		return http.MethodPost
	case MethodBitPut:
		return http.MethodPut
	case MethodBitDelete:
		return http.MethodDelete
	case MethodBitConnect:
		return http.MethodConnect
	case MethodBitOptions:
		return http.MethodOptions
	case MethodBitTrace:
		return http.MethodTrace
	case MethodBitPatch:
		return http.MethodPatch
	default:
		return ""
	}
}
