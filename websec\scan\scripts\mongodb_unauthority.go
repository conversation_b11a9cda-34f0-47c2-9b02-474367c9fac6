package scripts

import (
	"bytes"
	"net"
	"time"
)

func MongodbUnauthority(args *ScriptScanArgs) (*ScriptScanResult, error) {
	addr := args.Host + ":27017"
	conn, err := net.DialTimeout("tcp", addr, 3*time.Second)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
	conn.Write([]byte("\x3F\x00\x00\x00\x7E\x00\x00\x00\x00\x00\x00\x00\xD4\x07\x00\x00\x04\x00\x00\x00\x61\x64\x6D\x69\x6E\x2E\x24\x63\x6D\x64\x00\x00\x00\x00\x00\xFF\xFF\xFF\xFF\x18\x00\x00\x00\x10\x6C\x69\x73\x74\x44\x61\x74\x61\x62\x61\x73\x65\x73\x00\x01\x00\x00\x00\x00"))
	response := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(time.Second * 5))
	_, err = conn.Read(response)
	if err != nil {
		return nil, err
	}
	if bytes.Contains(response, []byte("local")) {
		return &ScriptScanResult{Vulnerable: true, Output: "http://" + addr, Body: response}, nil
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("mongodb_unauthority.xml", MongodbUnauthority)
}
