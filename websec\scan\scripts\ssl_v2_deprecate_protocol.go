package scripts

import (
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"time"
)

func SSLDeprecateProtocol(args *ScriptScanArgs) (*ScriptScanResult, error) {

	host := args.Host

	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()

	var cmd *exec.Cmd
	var output []byte
	cmd = exec.CommandContext(ctx, "sh", "-c", fmt.Sprintf(
		"`which nmap` -Pn -script=sslv2 -sT -p443 --host-timeout=20 %v", host))
	output, err := cmd.CombinedOutput()

	if err != nil {

		return nil, err
	}

	if bytes.Contains(output, []byte("443/tcp open")) {
		if bytes.Contains(output, []byte("sslv2")) {
			return &ScriptScanResult{Vulnerable: true, Output: fmt.Sprintf("http://%v:443", host), Body: output}, nil
		}
	}
	return &invulnerableResult, nil
}

func init() {
	registerHandler("ssl_v2_deprecate_protocol.xml", SSLDeprecateProtocol)
}
