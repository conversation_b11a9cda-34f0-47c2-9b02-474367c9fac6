package schedule

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/distributed/protocol"
	"websec/distributed/server"
	"websec/utils/log"
	"websec/utils/semaphore"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	workers *WorkerManager

	todoTasks = make(chan *schema.Task, 1000)

	finishedTasks = make(chan *protocol.TaskFinishBody, 1000)

	failedTasks = make(chan *schema.Task, 1000)
)

const (
	zombieWorkerKey = "zombie_workers"
)

type Scheduler struct {
	server *server.Server

	mongodb      *mongo.Database
	dbConnection *db.DBConnection

	assets map[string]*schema.Asset

	dispatchSema     *semaphore.Weighted
	dispatchingTasks sync.Map
	maxTaskCount     int32
	tag              string

	breakScan *BreakScan
}

func (scheduler *Scheduler) loadAssets() (map[string]*schema.Asset, error) {
	ctx, ctxCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer ctxCancel()

	var batchSize int32 = 5000
	log.Info("loadAsset", scheduler.tag)
	cursor, err := scheduler.mongodb.Collection(consts.CollectionAssets).Find(ctx,
		bson.M{
			"status":               1,
			"options.expired_at":   bson.M{"$gt": time.Now()},
			"options.schedule_tag": scheduler.tag,
		},
		options.Find().SetSort(bson.M{"_id": 1}).SetBatchSize(batchSize))

	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	assets := map[string]*schema.Asset{}

	for cursor.Next(ctx) {
		var result schema.Asset
		err := cursor.Decode(&result)
		if err != nil {
			log.Errorln("failed to decode:", err)
			continue
		}
		assets[result.ID.Hex()] = &result

		scheduler.mongodb.Collection(consts.CollectionAssets).UpdateOne(ctx,
			bson.M{
				"_id": result.ID,
			},
			bson.M{
				"$set": bson.M{
					"options.expired_at": time.Now(),
				},
			},
		)
	}
	if err = cursor.Err(); err != nil {
		return nil, err
	}
	return assets, nil
}

func (scheduler *Scheduler) dispatchTask(task *schema.Task) error {
	log.Infoln("dispatch task:", task.ID)

	defer scheduler.dispatchingTasks.Delete(task.ID)
	defer scheduler.dispatchSema.Release(1)

	worker, err := workers.ChooseWorkerRR(task)
	if err != nil {
		log.Warnln("failed to choose worker,", err)
	}

	if worker == nil {
		if task.Schedule.Tag == consts.TaskTagScanAtOnce {
			worker = workers.chooseWorkerForScanAtOnce(task)
			if worker == nil {
				log.Warnln("failed to find worker for scan atonce task:", task.ID)
				return errors.New("failed to find worker")
			}
		} else {
			log.Warnln("failed to find worker for task:", task.ID)
			return errors.New("failed to find worker")
		}
	}
	err = worker.DispatchTask(task)
	if err != nil {
		return fmt.Errorf("failed to dispatch task: %s, %s", task.ID, err.Error())
	}
	scheduler.updateTask(task.ID, bson.M{
		"status":          consts.TaskStatusRunning,
		"track.client_id": worker.Name,
		"track.address":   task.Track.Address,
		"start_at":        time.Now(),
	})
	return nil
}

func (scheduler *Scheduler) updateTask(taskID primitive.ObjectID, updates bson.M) error {
	ctx, ctxCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer ctxCancel()

	var err error

	for retry := 0; retry < 3; retry++ {
		_, err = scheduler.mongodb.Collection(consts.CollectionTasks).UpdateOne(ctx,
			bson.M{"_id": taskID},
			bson.M{"$set": updates, "$currentDate": bson.M{"updated_at": true}},
		)
		if err != nil {
			log.Errorln("failed to update task:", taskID, err)
			continue
		}
		break
	}
	return err
}

func (scheduler *Scheduler) archiveTask(task *schema.Task) error {
	coll := scheduler.mongodb.Collection(consts.CollectionTasks)
	archiveColl := scheduler.mongodb.Collection(consts.CollectionTasksArchive)

	var err error
	newTask := scheduler.generateTaskForNextPeriod(task)
	if newTask != nil {
		err = scheduler.InsertTask(newTask)
		if err != nil {
			log.Errorln("failed to insert task for next period:", task.ID, err)
			return err
		}
	}

	ctx, ctxCancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer ctxCancel()

	_, err = archiveColl.UpdateOne(ctx, bson.M{"_id": task.ID, "host": task.Host}, bson.M{"$set": task}, options.Update().SetUpsert(true))
	if err != nil {
		log.Errorln("failed to insert task to archive:", task.ID, err)
		return err
	}

	_, err = coll.DeleteOne(ctx, bson.M{"_id": task.ID})
	if err != nil {
		log.Errorln("failed to delete task:", task.ID, err)
		return err
	}

	return nil
}

func (scheduler *Scheduler) watchArrivedWorkers() {
	for client := range scheduler.server.ArrivedClients() {
		workers.AddPending(client)
	}
}

func (scheduler *Scheduler) restoreTask(task *schema.Task) error {
	if task.Track.ClientID == "" {

		return fmt.Errorf("track not found: %v", task.ID)
	}
	workers.RestoreTask(task)
	return nil
}

func (scheduler *Scheduler) watchTasks() {
	var err error
	var maxRetry int32 = 3

	for {
		select {
		case body := <-finishedTasks:
			err = scheduler.updateTask(body.TaskID, bson.M{
				"status":          consts.TaskStatusFinished,
				"finished_at":     time.Now(),
				"finished_status": body.Status,
			})
			if err != nil {
				log.Errorln("failed to update task status to finished:", body.TaskID, err)
				finishedTasks <- body
				time.Sleep(time.Second * 3)
			}
		case task := <-failedTasks:
			if task.Schedule.Retry < maxRetry {
				err = scheduler.updateTask(task.ID, bson.M{
					"status":         consts.TaskStatusNew,
					"schedule.retry": task.Schedule.Retry + 1,
				})
			} else {
				ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
				_, err = scheduler.mongodb.Collection(consts.CollectionTasks).DeleteOne(ctx, bson.M{"_id": task.ID})
				if err != nil {
					log.Error("del mongo err:", err)
				}
				cancel()
			}

			if err != nil {
				log.Errorln("failed to update task status to finished:", task.ID, err)
				failedTasks <- task
				continue
			}
		}
	}
}

func (scheduler *Scheduler) archiveTasks() {
	log.Infoln("archiving tasks...")

	ctx, ctxCancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer ctxCancel()

	cursor, err := scheduler.mongodb.Collection(consts.CollectionTasks).Find(ctx,
		bson.M{
			"status": consts.TaskStatusFinished,
		},
	)
	if err != nil {
		log.Errorln("failed to fetch finished tasks:", err)
		return
	}

	tasks := make([]*schema.Task, 0, 100)
	for cursor.Next(ctx) {
		var task = new(schema.Task)
		err := cursor.Decode(task)
		if err != nil {
			log.Errorln("failed to decode:", err)
			continue
		}
		tasks = append(tasks, task)
	}
	cursor.Close(ctx)

	for _, task := range tasks {
		err = scheduler.archiveTask(task)
		if err != nil {
			log.Errorln("failed to archive task:", task.ID, err)
			continue
		}
	}
}

func (scheduler *Scheduler) timeGenerateTasks() {
	ticker := time.NewTicker(time.Second * 30)
	defer ticker.Stop()

	scheduler.generateTasks()
	for range ticker.C {
		scheduler.generateTasks()
	}
}

func (scheduler *Scheduler) timeArchiveTasks() {
	ticker := time.NewTicker(300 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		scheduler.archiveTasks()
	}
}

func (scheduler *Scheduler) clearExpireTasks() {
	t := time.NewTicker(12 * time.Hour)
	defer t.Stop()

	scheduler.clearExpireTask()
	for range t.C {
		scheduler.clearExpireTask()
	}
}

func (scheduler *Scheduler) clearExpireTask() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	cursor, err := scheduler.dbConnection.GetMongoDatabase().Collection(consts.CollectionAssets).Find(ctx,
		bson.M{
			"status":               1,
			"options.expired_at":   bson.M{"$lt": time.Now()},
			"options.schedule_tag": scheduler.tag},
		options.Find().SetProjection(bson.M{"_id": 0, "host": 1}))
	if err != nil {
		log.Errorln(err)
		return
	}

	assets := make([]*schema.Asset, 0, 10)
	for cursor.Next(ctx) {
		asset := new(schema.Asset)
		err = cursor.Decode(asset)
		if err != nil {
			log.Errorln("decode failed", err)
			continue
		}
		assets = append(assets, asset)
	}
	cursor.Close(ctx)

	for _, v := range assets {
		res, err := scheduler.dbConnection.GetMongoDatabase().Collection(consts.CollectionTasks).DeleteMany(context.TODO(),
			bson.M{
				"host":   v.Host,
				"status": consts.TaskStatusNew,
			})
		log.Infoln("del expire asset task", v.Host, res, res.DeletedCount, err)
	}
}

func (scheduler *Scheduler) loadTaskAndDispatch() error {
	log.Infoln("begin to load tasks ...")

	var batchSize int32 = 10000

	ctx, ctxCancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer ctxCancel()

	cursor, err := scheduler.mongodb.Collection(consts.CollectionTasks).Find(ctx,
		bson.M{
			"status":        consts.TaskStatusNew,
			"schedule.plan": bson.M{"$lt": time.Now()},
		},
		options.Find().SetSort(
			bson.D{
				{Key: "schedule.priority", Value: -1},
				{Key: "schedule.plan", Value: 1},
			}).SetBatchSize(batchSize),
	)
	if err != nil {
		log.Errorln("failed to fetch tasks:", err)
		return err
	}

	tasks := make([]*schema.Task, 0, batchSize)
	for cursor.Next(ctx) {
		task := new(schema.Task)
		err := cursor.Decode(task)
		if err != nil {
			log.Errorln("failed to decode:", err)
			continue
		}

		if asset, ok := scheduler.assets[task.AssetID.Hex()]; !ok {
			log.Debugln("this task is not in charge:", task.ID, task.Host, task.Schedule.Tag)

			log.Info("try to delete this host-less task")
			ctx, ctxCancel := context.WithTimeout(context.Background(), 10*time.Second)
			coll := scheduler.mongodb.Collection(consts.CollectionTasks)
			coll.DeleteOne(ctx, bson.M{"_id": task.ID})
			ctxCancel()
			continue
		} else {
			if asset.Options.ExpiredAt.Before(time.Now().UTC()) {
				ctx, ctxCancel := context.WithTimeout(context.Background(), 10*time.Second)
				coll := scheduler.mongodb.Collection(consts.CollectionTasks)
				coll.DeleteOne(ctx, bson.M{"_id": task.ID})
				ctxCancel()
				continue
			}
			if len(asset.Options.ScheduleRanges) > 0 {
				loc, _ := time.LoadLocation("Asia/Shanghai") // emmm...
				now := time.Now().In(loc)
				minuteOfDay := 60*now.Hour() + now.Minute()
				for _, scheduleRange := range asset.Options.ScheduleRanges.Merge() {
					minuteStart := toDayMinute(scheduleRange.Start)
					minuteEnd := toDayMinute(scheduleRange.End)
					if minuteOfDay >= minuteStart && minuteOfDay < minuteEnd {
						tasks = append(tasks, task)
						break
					}
				}
			} else {
				tasks = append(tasks, task)
			}
		}
	}
	cursor.Close(ctx)

	for _, task := range tasks {
		if task.Type == consts.TaskTypeScan && task.Schedule.Tag == consts.TaskTagScanAll &&
			scheduler.breakScan.IsStop(task.AssetID.Hex()) {
			log.Infoln("task is stoped", task.Host, task.AssetID.Hex())
			continue
		}

		if _, ok := scheduler.dispatchingTasks.Load(task.ID); ok {
			log.Debugln("task is in dispatching:", task.ID)
			continue
		}
		err = scheduler.dispatchSema.Acquire(context.TODO(), 1)
		if err == nil {
			scheduler.dispatchingTasks.Store(task.ID, nil)
			scheduler.dispatchTask(task)
		} else {
			log.Errorln("failed to acquire semaphore:", err)
		}
	}
	log.Infoln("dispatch finished")
	return nil
}

func (scheduler *Scheduler) retryZombieTasksByClientID(ctx context.Context, clientID string) error {
	log.Infoln("begin to process zombie tasks ...:", clientID)
	var errx error

	defer func(e *error) {
		if *e != nil {
			ctx, cancel := context.WithTimeout(context.TODO(), time.Second*60)
			defer cancel()

			_, err := scheduler.mongodb.Collection(consts.CollectionTasks).DeleteMany(ctx,
				bson.M{
					"status":          consts.TaskStatusRunning,
					"track.client_id": clientID,
				})
			if err != nil {
				log.Errorln("failed to delete zombie tasks clientID", clientID, err)

				return
			}
			log.Info("delete all zombie tasks since error happend", clientID)
			err = scheduler.delZombieClientID(clientID)
			if err != nil {
				log.Error("trigger on error: failed to del zombie clinet id", clientID, err)
				return
			} else {
				log.Info("remove zombie clientID from redis", clientID)
			}
		}
	}(&errx)

	cursor, err := scheduler.mongodb.Collection(consts.CollectionTasks).Find(ctx,
		bson.M{
			"status":          consts.TaskStatusRunning,
			"track.client_id": clientID,
		})
	if err != nil {
		errx = err
		log.Errorf("failed to fetch zombie tasks clientid: %s: %s\n", clientID, err)
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		task := new(schema.Task)
		err := cursor.Decode(task)
		if err != nil {
			log.Errorln("failed to decode:", err)
			continue
		}
		select {
		case failedTasks <- task:
			log.Info("add zombie task to failedTasks", task.ID)
		case <-ctx.Done():
			errx = ctx.Err()
			return errx
		}
	}
	log.Infoln("success to clear zombie tasks clientid", clientID)
	return nil
}

func (scheduler *Scheduler) delZombieClientID(clientID string) error {
	return scheduler.dbConnection.HDel(zombieWorkerKey, clientID)
}

func (scheduler *Scheduler) storeZombieClientID(clientID string) error {
	return scheduler.dbConnection.HSet(zombieWorkerKey, clientID, "")
}

func (scheduler *Scheduler) loadZombieClientIDLst() ([]string, error) {
	m, err := scheduler.dbConnection.HGetAll(zombieWorkerKey)
	if err != nil {
		return nil, err
	}
	lst := make([]string, 0, len(m))
	for key := range m {
		lst = append(lst, key)
	}
	return lst, nil
}

func (scheduler *Scheduler) timeRetryZombieTasks() {
	ticker := time.NewTicker(time.Second * 30)
	defer ticker.Stop()

	for range ticker.C {
		log.Infoln("timeRetryZombieTasks...")
		clientLst, err := scheduler.loadZombieClientIDLst()
		if err != nil {
			continue
		}
		for _, id := range clientLst {
			log.Infoln("timeRetryZombieTasks:", id)
			func() {
				ctx, cancel := context.WithTimeout(context.TODO(), time.Second*10)
				defer cancel()

				err := scheduler.retryZombieTasksByClientID(ctx, id)
				if err != nil {
					log.Errorln("faild to retry tasks of client id", id, err)
					return
				}

				err = scheduler.delZombieClientID(id)
				if err != nil {
					log.Errorln("faild to del client id", id, err)
					return
				} else {
					log.Info("success to remove zombie clientID from redis", id)
				}
			}()
		}
	}
}

func processConnectionLost(scheduler *Scheduler) func(string) error {
	return func(clientTag string) error {
		log.Infoln("connection lost:", clientTag)
		stat := workers.Lost(clientTag)
		if stat != nil {
			workers.RemoveLost(stat.Name, time.Minute*6)
			err := scheduler.storeZombieClientID(stat.Name)
			if err != nil {
				log.Error("failed to store clientId into redis", stat.Name, err)
				return err
			}
			log.Infoln("processConnectionLost, store zombile clinetId into redis", stat.Name)
		}
		return nil
	}
}

func toDayMinute(s string) int {

	parts := strings.Split(s, ":")
	if len(parts) != 2 {
		return -1
	}
	hour, err := strconv.Atoi(parts[0])
	minute, err := strconv.Atoi(parts[1])
	if err != nil {
		return -1
	}
	return hour*60 + minute
}

func NewScheduler(addr string, options ...optionFunc) (*Scheduler, error) {
	var err error

	serv, err := server.NewServer("tcp", addr)
	if err != nil {
		log.Fatalln("failed to create server.", err)
		return nil, err
	}
	scheduler := &Scheduler{
		server:       serv,
		dispatchSema: semaphore.NewWeighted(300),
	}
	serv.AddLostHandle(processConnectionLost(scheduler))

	for _, opt := range options {
		err = opt(scheduler)
		if err != nil {
			return nil, err
		}
	}

	workers = newWorkerManager(scheduler.maxTaskCount)

	scheduler.breakScan = NewBreakScan(scheduler)

	return scheduler, nil
}

func (scheduler *Scheduler) Run() {
	go scheduler.server.Run()
	go scheduler.watchArrivedWorkers()
	go scheduler.timeGenerateTasks()
	go scheduler.timeArchiveTasks()
	go scheduler.watchTasks()
	go scheduler.breakScan.RunStopTodoTask()
	go scheduler.clearExpireTasks()
	go scheduler.timeRetryZombieTasks()

	for {
		if workers.Count() <= 0 {
			log.Warnln("no available clients")
			time.Sleep(time.Second * 5)
			continue
		}
		time.Sleep(1 * time.Second)
		err := scheduler.loadTaskAndDispatch()
		if err == nil {
			time.Sleep(1 * time.Second)
		} else {
			time.Sleep(5 * time.Second)
		}
	}
}

func (scheduler *Scheduler) Restore() error {
	log.Infoln("restoring ...")

	ctx, ctxCancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer ctxCancel()

	cursor, err := scheduler.mongodb.Collection(consts.CollectionTasks).Find(ctx,
		bson.M{
			"status": consts.TaskStatusRunning,
		},
	)
	if err != nil {
		log.Errorln("failed to fetch running tasks:", err)
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var task = new(schema.Task)
		err := cursor.Decode(task)
		if err != nil {
			log.Errorln("failed to decode:", err)
			return err
		}
		err = scheduler.restoreTask(task)
		if err != nil {
			return err
		}
	}
	return nil
}

func (scheduler *Scheduler) InsertTask(task *schema.Task) error {
	ctx, ctxCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer ctxCancel()

	res, err := scheduler.mongodb.Collection(consts.CollectionTasks).InsertOne(ctx, task)
	if err != nil {
		return err
	}
	task.ID = res.InsertedID.(primitive.ObjectID)
	return nil
}
