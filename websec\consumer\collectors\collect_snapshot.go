package collectors

import (
	"encoding/json"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/stream"
)

func (collector *Collector) processSnapshotContentChange(msg *stream.Message) error {
	var doc = new(schema.FinalSnapshotContentChangeResult)
	err := json.Unmarshal(msg.Value, doc)
	if err != nil {
		log.Errorln("--------------------- collect_snapshot.go processSnapshotContentChange Error:", err)
		return err
	}

	collector.AddSnapshotContentChange(doc)
	return nil
}

func (collector *Collector) processSnapshotSensitiveWord(msg *stream.Message) error {
	var doc = new(schema.SnapshotSensitiveWordDoc)
	err := json.Unmarshal(msg.Value, doc)
	if err != nil {
		log.Errorln("--------------------- collect_snapshot.go processSnapshotSensitiveWord Error:", err)
		return err
	}

	collector.AddSnapshotSensitiveWord(doc)
	return nil
}
