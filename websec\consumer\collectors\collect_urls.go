package collectors

import (
	"context"
	"encoding/json"
	"strings"
	"time"
	"websec/common/consts"
	"websec/common/schema"
	"websec/utils/log"
	"websec/utils/stream"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var imageExts = map[string]bool{
	"jpg": true, "png": true, "gif": true,
}

func (collector *Collector) processNewURL(msg *stream.Message) error {
	var doc = new(schema.FoundNewURLs)
	err := json.Unmarshal(msg.Value, doc)
	if err != nil {
		return err
	}
	imgUrls := []*schema.SiteURL{}

	if collector.newURL && len(doc.URLs) > 0 {
		imgUrls, err = collector.saveNewURL(doc.URLs)
		if err != nil {
			return err
		}
	}

	if len(imgUrls) > 0 {
		collector.saveNewImg(imgUrls)
	}

	return nil
}

func (collector *Collector) saveNewURL(saveURLs []*schema.SiteURL) ([]*schema.SiteURL, error) {
	rawCount := len(saveURLs)
	models := make([]mongo.WriteModel, rawCount)
	imgUrls := []*schema.SiteURL{}

	for k, v := range saveURLs {
		log.Warnln("collect_urls.go saveNewURL", v.Host, "-", v.AssetID, "-", v.JobID)

		pathParts := strings.Split(v.URI, ".")
		tail := pathParts[len(pathParts)-1]
		if _, ok := imageExts[tail]; ok {
			imgUrls = append(imgUrls, v)
		}

		var update bson.M

		if v.PostData != "" {
			update = bson.M{
				"$setOnInsert": bson.M{
					"host":       v.Host,
					"scheme":     v.Scheme,
					"uri":        v.URI,
					"depth":      v.Depth,
					"media_type": v.MediaType,
					"asset_id":   v.AssetID,
					"job_id":     v.JobID,
					"url":        v.URL,
					"refer_url":  v.ReferURL,
				},
				"$set": bson.M{
					"post_data": v.PostData,
				},
				"$bit": bson.M{
					"methods": bson.M{
						"or": v.Methods,
					},
				},
				"$currentDate": bson.M{
					"updated_at": true,
				},
			}
		} else {
			update = bson.M{
				"$setOnInsert": bson.M{
					"host":       v.Host,
					"scheme":     v.Scheme,
					"uri":        v.URI,
					"depth":      v.Depth,
					"media_type": v.MediaType,
					"asset_id":   v.AssetID,
					"job_id":     v.JobID,
					"url":        v.URL,
					"refer_url":  v.ReferURL,
				},
				"$bit": bson.M{
					"methods": bson.M{
						"or": v.Methods,
					},
				},
				"$currentDate": bson.M{
					"updated_at": true,
				},
			}
		}

		models[k] = mongo.NewUpdateOneModel().SetFilter(bson.M{
			"asset_id": v.AssetID,
			"job_id":   v.JobID,
			"host":     v.Host,
			"url_hash": v.URLHash,
		}).SetUpdate(update).SetUpsert(true)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	res, err := collector.dbConnection.GetMongoDatabase().Collection(consts.CollectionURLs).
		BulkWrite(ctx, models, options.BulkWrite().SetOrdered(false))
	if err != nil {
		log.Errorf("Save New URL error %v", err)
	} else {
		log.Infoln("Save New URL result:", res.InsertedCount, res.ModifiedCount, res.UpsertedCount)
		if res.UpsertedCount != int64(rawCount) {
			log.Warnf("Save New URL not all UpsertedCount%d rawCount%d modifiedCount%d", res.UpsertedCount, rawCount, res.ModifiedCount)
		} else {
			log.Infoln("Save New URL sucess all", res.UpsertedCount, rawCount)
		}
	}

	return imgUrls, nil
}
