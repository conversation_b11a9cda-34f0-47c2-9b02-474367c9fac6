package main

import (
	"context"
	"flag"
	"regexp"
	"sync"
	"time"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/config"
	"websec/tools/phish/hawk"
	"websec/utils/log"
	"websec/utils/semaphore"
	"websec/utils/stream"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	timedHour     = flag.Int("hour", 10, "Timed execution time")
	wg            sync.WaitGroup
	routines      = semaphore.NewWeighted(10)
	err           error
	dbConnection  *db.DBConnection
	producer      *stream.Producer
	settings      *config.Config
	lastTime      int64
	numberPattern = regexp.MustCompile(`\b\d+\b`)
	httpsPattern  = regexp.MustCompile(`https`)
)

func main() {
	settings, err = config.ParseConfig()
	if err != nil {
		log.Errorln(err)
		return
	}

	dbConnection, err = db.NewDBConnection(
		db.WithMongoConfig(settings.MongoDB),
		db.WithRedisConfig(settings.Redis))
	if err != nil {
		log.Errorln(err)
		return
	}

	go func() {
		t := time.NewTicker(1 * time.Minute)
		for {
			select {
			case <-t.C:
				hour := time.Now().Hour()
				if hour == *timedHour {
					run()
				}
			}
		}
	}()

	select {}
}

func run() {
	var startID primitive.ObjectID
	var limited int64 = 1000
	var count int64

	if time.Now().Unix() < lastTime+12*3600 {
		return
	}

	producer, err = stream.NewProducer(&stream.ProducerOptions{
		Brokers:         settings.Kafka.Brokers,
		MessageMaxBytes: settings.Kafka.MessageMaxBytes,
	})
	if err != nil {
		log.Errorln(err)
		return
	}
	producer.Go()
	defer producer.Close()

	for {
		count = 0
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		cursor, err := dbConnection.GetMongoDatabase().Collection(consts.CollectionAssets).Find(ctx,
			bson.M{"_id": bson.M{"$gt": startID}},
			options.Find().SetSort(bson.M{"_id": 1}).SetLimit(limited))
		if err != nil {
			log.Errorln(err)
			cancel()
			break
		}

		assets := make([]*schema.Asset, 0, limited)
		for cursor.Next(ctx) {
			count++
			asset := new(schema.Asset)
			err = cursor.Decode(asset)
			if err != nil {
				log.Errorln(err)
				continue
			}
			assets = append(assets, asset)
			startID = asset.ID
		}
		cursor.Close(ctx)
		cancel()

		for _, v := range assets {
			if err = routines.Acquire(context.Background(), 1); err == nil {
				wg.Add(1)
				go getRelatedWebsite(v)
			}
		}

		wg.Wait()
		log.Infoln("len:", len(assets))
		if count != limited {
			break
		}
	}

	lastTime = time.Now().Unix()
}

func getRelatedWebsite(asset *schema.Asset) {
	defer func() {
		routines.Release(1)
		wg.Done()
	}()

	if asset.Name == "" || asset.Name == "无" {
		return
	}

	if numberPattern.MatchString(asset.Name) {
		return
	}

	log.Debugln("asset Name", asset.Name)

	lastSearchTime, err := dbConnection.HGetInt64(consts.RedisHawkTime, asset.Name)
	if err != nil {
		lastSearchTime = time.Now().Unix() - 7*3600*24
	}

	page := 1

	for {
		var resp *hawk.Response
		for i := 0; i < 3; i++ {
			resp, err = hawk.GetRelatedWebSite(asset.Name, lastSearchTime, time.Now().Unix(), page)
			if err != nil || resp.Status != 200 {
				log.Errorln(err)
				time.Sleep(1 * time.Second)
				continue
			} else {
				break
			}
		}

		if err != nil {
			log.Errorln("retry failed", err)
			break
		}

		for _, v := range resp.Data.List {
			site := httpsPattern.ReplaceAllString(v, "http")
			doc := &schema.FoundPhishingDoc{
				Pu:      schema.FoundPhishingURL{Host: asset.Host},
				Ou:      schema.FoundPhishingURL{Host: asset.Host},
				Host:    site,
				FoundAt: time.Now(),
			}
			log.Infoln("related website:", v)
			producer.Produce(consts.TopicRawPhishingResults, doc)
		}

		if page < resp.Data.TotalPage {
			page++
		} else {
			dbConnection.HSet(consts.RedisHawkTime, asset.Name, time.Now().Unix())
			break
		}
	}
}
