package tuchuang

import "websec/config"

type IUpload interface {
	UploadImg(imgData []byte, ext string, prefix string) (string, error)
	UploadImgFile(imgPath string, prefix string) (string, error)
}

const (
	BucketTypeS3 int = iota
	BucketTypeSimple
	BucketTypeLocalFS
)

type BucketSelect struct {
	Bucket IUpload
}

func (b *BucketSelect) UploadImg(imgData []byte, ext string, prefix string) (string, error) {
	return b.Bucket.UploadImg(imgData, ext, prefix)
}

func (b *BucketSelect) UploadImgFile(imgPath string, prefix string) (string, error) {
	return b.Bucket.UploadImgFile(imgPath, prefix)
}

func NewBucketSelect(cfg *config.S3BucketConfig, addr string, bucketType int) *BucketSelect {
	var bucket IUpload
	switch bucketType {
	case BucketTypeS3:
		bucket = NewS3Bucket(cfg)
	case BucketTypeSimple:
		bucket = NewSimpleBucket(addr, cfg.URLPrefix)
	case BucketTypeLocalFS:
		bucket = NewLocalFSBucket(cfg.LocalDir, cfg.URLPrefix)
	default:
		bucket = NewLocalFSBucket(cfg.LocalDir, cfg.URLPrefix)
	}

	return &BucketSelect{
		Bucket: bucket,
	}
}
