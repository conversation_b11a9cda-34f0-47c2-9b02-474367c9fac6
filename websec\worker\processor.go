package main

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"
	"websec/common"
	"websec/common/consts"
	"websec/common/db"
	"websec/common/schema"
	"websec/crawl"
	"websec/distributed/protocol"
	"websec/scan"
	"websec/utils/log"
	"websec/utils/semaphore"
)

const (
	ProcessorStart   = 10021
	ProcessorSuccess = 10022
	ProcessorFailed  = 10050
)

type Processor struct {
	host      string
	asset     *schema.Asset
	manager   *processorManager
	sema      *semaphore.Weighted
	wg        sync.WaitGroup
	ctx       context.Context
	ctxCancel context.CancelFunc

	waitOnce sync.Once

	crawlerMap sync.Map
	scannerMap sync.Map
}

func (processor *Processor) Process(task *schema.Task) (<-chan *protocol.TaskFinishBody, error) {
	log.Debugln("process task:", task.ID)

	future := make(chan *protocol.TaskFinishBody)
	switch task.Type {
	case consts.TaskTypeCrawl:
		crawler, err := processor.createCrawler(task)
		if err != nil {
			return nil, err
		}
		processor.crawlerMap.Store(task.ID.Hex(), crawler)
		processor.wg.Add(1)
		stopCh := make(chan struct{})
		go processor.SyncCrawlerCurrentLinkPeriod(crawler, stopCh)
		go func() {
			processor.watchCrawler(task, crawler, future)
			stopCh <- struct{}{}
		}()
	case consts.TaskTypeScan:
		scanner, err := processor.createScanner(task)
		if err != nil {
			return nil, err
		}
		processor.scannerMap.Store(task.ID.Hex(), scanner)
		processor.wg.Add(2)
		go processor.feedScanner(task, scanner)
		go processor.watchScanner(task, scanner, future)
	}

	processor.waitOnce.Do(func() {
		go processor.Wait()
	})
	return future, nil
}

func (processor *Processor) Stop() {
	processor.ctxCancel()
	processor.crawlerMap.Range(func(key, value interface{}) bool {
		value.(crawl.Crawler).Stop()
		return true
	})

	processor.scannerMap.Range(func(key, value interface{}) bool {
		value.(scan.Scanner).Stop()
		return true
	})
}

func (processor *Processor) Clear() {
	processor.crawlerMap = sync.Map{}
	processor.scannerMap = sync.Map{}
}

func (processor *Processor) Dump() {
	processor.crawlerMap.Range(func(key, value interface{}) bool {
		log.Debugln("cancel crawl offset", value.(crawl.Crawler).GetOffset())
		return true
	})

	processor.scannerMap.Range(func(key, value interface{}) bool {
		log.Debugln("cancel scan offset", value.(scan.Scanner).GetOffset())
		return true
	})
}

func (processor *Processor) CancelTask(taskID string) bool {
	processor.scannerMap.Range(func(k, v interface{}) bool {
		log.Info("taskID", k, v)
		return true
	})
	log.Info("cancel task id ", taskID)
	if scanner, ok := processor.scannerMap.Load(taskID); ok {
		scanner.(scan.Scanner).Cancel()
		return true
	}

	return false
}

func (processor *Processor) Wait() {
	processor.wg.Wait()
	processor.manager.Delete(processor.asset.ID.Hex())
}

func NewProcessor(asset *schema.Asset, mgr *processorManager, address string) *Processor {
	ctx, ctxCancel := context.WithCancel(context.Background())

	concurrency := asset.Options.Concurrency
	if concurrency == 0 {
		concurrency = 10
	}

	processor := &Processor{
		host:      asset.Host,
		asset:     asset,
		manager:   mgr,
		ctx:       ctx,
		ctxCancel: ctxCancel,
		sema:      mgr.getSemaphore(address, int64(concurrency)),
	}
	return processor
}

func (processor *Processor) GetDBConnection() *db.DBConnection {
	return processor.manager.dbConnection
}

func (processor *Processor) AddVul(val *schema.FoundVulDoc) {
	processor.manager.producer.Produce(consts.TopicRawVulResults, val)
}

func (processor *Processor) AddWebpage(val *common.Webpage) {
	processor.manager.producer.ProduceBytes(consts.TopicCrawledWebpages, val)
}

func (processor *Processor) AddTaskStats(val *schema.TaskStats) {
	processor.manager.producer.Produce(consts.TopicTaskStats, val)
}

func (processor *Processor) AddImage(val *common.Webpage) {
	log.Infoln("---------------- AddImage", val.URL, "images ", val.Images)
	if len(val.Images) == 0 {
		return
	}
	images := val.Images
	message := &schema.CrawledImageURLS{
		AssetID: val.AssetID,
		JobID:   val.JobID,
		URL:     val.URL,
		Host:    val.Host,
		FoundAt: val.CrawledAt,
		Images:  images,
	}
	processor.manager.producer.ProduceBytes(consts.TopicCrawledImageURLS, message)
}

func (processor *Processor) AddSwf(val *common.Webpage) {
	if len(val.Swfs) == 0 {
		return
	}
	swfs := val.Swfs
	message := &schema.CrawledSwfURLs{
		AssetID: val.AssetID,
		JobID:   val.JobID,
		URL:     val.URL,
		Host:    val.Host,
		FoundAt: val.CrawledAt,
		Swfs:    swfs,
	}
	processor.manager.producer.ProduceBytes(consts.TopicCrawledSwfURLs, message)
}

func (processor *Processor) getProgress(taskID string) string {
	key := consts.RedisWorkerProgressPrefix + taskID
	offset, err := processor.GetDBConnection().GetRedisString(key)
	if err != nil {
		return ""
	}
	return offset
}

func (processor *Processor) setProgress(taskID, offset string) error {
	key := consts.RedisWorkerProgressPrefix + taskID
	processor.GetDBConnection().RedisSetEx(key, offset, 3600*48)
	return nil
}

func (processor *Processor) delProgress(taskID string) error {
	key := consts.RedisWorkerProgressPrefix + taskID
	return processor.GetDBConnection().DelKey(key)
}

func (processor *Processor) AddVuls(val *schema.FoundVulDoc) {
	processor.manager.producer.Produce(consts.TopicRawVulResults, val)
}

func (processor *Processor) SyncCrawlerCurrentLinkPeriod(crawleri crawl.Crawler, stopCh chan struct{}) {
	crawler, ok := crawleri.(*crawl.WSCrawler)
	if !ok {
		return
	}

	key := fmt.Sprintf("link_num_%s", crawler.Options.Task.AssetID.Hex())

	for {
		linkNum := atomic.LoadUint64(&crawler.CurrentLinkNum)
		processor.GetDBConnection().RedisSetEx(key, linkNum, 300)
		select {
		case <-stopCh:
			return
		case <-time.After(time.Second * 2):
		}
	}
}
