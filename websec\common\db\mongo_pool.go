package db

import (
	"context"
	"sync"
	"time"
	"websec/config"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"go.mongodb.org/mongo-driver/x/network/connstring"
)

type mongoClientPool struct {
	clients map[string]*mongo.Client
	lock    *sync.RWMutex
}

func newMongoClientPool() *mongoClientPool {
	return &mongoClientPool{
		clients: make(map[string]*mongo.Client),
		lock:    new(sync.RWMutex),
	}
}

func (m *mongoClientPool) Get(info config.MongoConfig) (*mongo.Client, error) {
	key := info.URI
	m.lock.Lock()
	if session, ok := m.clients[key]; ok {
		m.lock.Unlock()
		return session, nil
	}
	m.lock.Unlock()

	opts := options.Client().
		ApplyURI(key).
		SetSocketTimeout(60 * time.Second).          // 减少socket超时时间
		SetConnectTimeout(30 * time.Second).         // 添加连接超时
		SetServerSelectionTimeout(30 * time.Second). // 添加服务器选择超时
		SetMaxConnIdleTime(300 * time.Second).
		SetHeartbeatInterval(15 * time.Second).
		SetMaxPoolSize(10).
		SetReadPreference(readpref.SecondaryPreferred()).
		SetRetryWrites(true) // 启用写重试

	// 增加连接超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	log.Infoln("Attempting to connect to MongoDB:", key)
	client, err := mongo.Connect(ctx, opts)
	if err != nil {
		log.Errorf("Failed to connect to MongoDB %s: %v", key, err)
		return nil, err
	}

	// 验证连接是否可用
	pingCtx, pingCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer pingCancel()

	err = client.Ping(pingCtx, nil)
	if err != nil {
		log.Errorf("Failed to ping MongoDB %s: %v", key, err)
		client.Disconnect(context.Background()) // 清理失败的连接
		return nil, err
	}

	log.Infoln("Successfully connected and pinged MongoDB:", key)
	m.lock.Lock()
	m.clients[key] = client
	m.lock.Unlock()

	return client, nil
}

var mongoClientPools = newMongoClientPool()

func GetMongo(info config.MongoConfig) (*mongo.Client, error) {
	return mongoClientPools.Get(info)
}

func GetMongoDatabase(info config.MongoConfig) (*mongo.Database, error) {
	c, err := mongoClientPools.Get(info)
	if err != nil {
		return nil, err
	}

	cs, err := connstring.Parse(info.URI)
	if err != nil {
		return nil, err
	}

	return c.Database(cs.Database), nil
}

// DiagnoseMongoConnection 诊断MongoDB连接问题
func DiagnoseMongoConnection(info config.MongoConfig) error {
	log.Infoln("Starting MongoDB connection diagnosis...")

	// 解析连接字符串
	cs, err := connstring.Parse(info.URI)
	if err != nil {
		log.Errorf("Failed to parse MongoDB URI: %v", err)
		return err
	}

	log.Infof("MongoDB connection details:")
	log.Infof("  - Hosts: %v", cs.Hosts)
	log.Infof("  - Database: %s", cs.Database)
	log.Infof("  - Username: %s", cs.Username)
	log.Infof("  - SSL: %t", cs.SSL)

	// 尝试连接
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	opts := options.Client().
		ApplyURI(info.URI).
		SetConnectTimeout(10 * time.Second).
		SetServerSelectionTimeout(10 * time.Second).
		SetSocketTimeout(30 * time.Second)

	client, err := mongo.Connect(ctx, opts)
	if err != nil {
		log.Errorf("Connection failed: %v", err)
		return err
	}
	defer client.Disconnect(context.Background())

	// 测试ping
	pingCtx, pingCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer pingCancel()

	err = client.Ping(pingCtx, nil)
	if err != nil {
		log.Errorf("Ping failed: %v", err)
		return err
	}

	log.Infoln("MongoDB connection diagnosis completed successfully!")
	return nil
}
