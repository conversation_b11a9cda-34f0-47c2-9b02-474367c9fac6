package db

import (
	"context"
	"sync"
	"time"
	"websec/config"
	"websec/utils/log"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"go.mongodb.org/mongo-driver/x/network/connstring"
)

type mongoClientPool struct {
	clients map[string]*mongo.Client
	lock    *sync.RWMutex
}

func newMongoClientPool() *mongoClientPool {
	return &mongoClientPool{
		clients: make(map[string]*mongo.Client),
		lock:    new(sync.RWMutex),
	}
}

func (m *mongoClientPool) Get(info config.MongoConfig) (*mongo.Client, error) {
	key := info.URI
	m.lock.Lock()
	if session, ok := m.clients[key]; ok {
		m.lock.Unlock()
		return session, nil
	}
	m.lock.Unlock()

	opts := options.Client().
		ApplyURI(key).
		SetSocketTimeout(300 * time.Second).
		SetMaxConnIdleTime(300 * time.Second).
		SetHeartbeatInterval(15 * time.Second).
		SetMaxPoolSize(10).
		SetReadPreference(readpref.SecondaryPreferred())
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	client, err := mongo.Connect(ctx, opts)
	if err == nil {
		m.lock.Lock()
		m.clients[key] = client
		m.lock.Unlock()
	} else {
		log.Errorln(err)
	}
	return client, err
}

var mongoClientPools = newMongoClientPool()

func GetMongo(info config.MongoConfig) (*mongo.Client, error) {
	return mongoClientPools.Get(info)
}

func GetMongoDatabase(info config.MongoConfig) (*mongo.Database, error) {
	c, err := mongoClientPools.Get(info)
	if err != nil {
		return nil, err
	}

	cs, err := connstring.Parse(info.URI)
	if err != nil {
		return nil, err
	}

	return c.Database(cs.Database), nil
}
