package scan

import (
	"bytes"
	"encoding/json"
	"mime/multipart"
	"net/http"
	"strings"
	"time"
	"websec/common"
	"websec/crawl"
	"websec/scan/rules"
	"websec/utils"
	"websec/utils/log"

	"github.com/PuerkitoBio/goquery"
	"github.com/valyala/fasthttp"
)

var (
	weakUsernames = []string{
		"admin",
		"admin'--",
		"admin' or ''=''--",
		"admin' or 1=1--",
		"'or 1=1--",
		"admins",
		"base",
		"admin'or''='",
		"user",
		"root",
		"ceshi",
		"super1",
		"ceshi123",
		"admin123",
		"sysadmin",
		"test",
		"test1",
		"test2",
		"test123",
		"user123",
		"'or'='or'",
		"'or''='",
		"adm",
		"!root",
		"$ALOC$",
		"$system",
		"11111111",
		"22222222",
		"pobear",
		"master",
		"sys",
		"adams",
		"admn",
		"0admin",
		"0manager",
		"advmail",
		"allin1",
		"allin1mail",
		"allinone",
		"ap2svp",
		"apl2pp",
		"applsys",
		"apps",
		"aqdemo",
		"aquser",
		"archivist",
		"autolog1",
		"administrator",
		"anonymous",
		"any",
		"backup",
		"batch",
		"batch1",
		"batch2",
		"blake",
		"catalog",
		"ccc",
		"cdemo82",
		"cdemocor",
		"cdemorid",
		"cdemoucb",
		"clark",
		"cmsbatch",
		"cmsuser",
		"company",
		"cpnuc",
		"cprm",
		"cspuser",
		"ctxdemo",
		"ctxsys",
		"cview",
		"dba",
		"dbsnmp",
		"dcl",
		"ddic",
		"decmail",
		"default",
		"demo",
		"demo1",
		"demo2",
		"demo3",
		"demo4",
		"demo8",
		"desquetop",
		"direct",
		"dirmaint",
		"diskcnt",
		"ds",
		"dsa",
		"emp",
		"erep",
		"event",
		"fax",
		"guest",
		"sysadm",
		"systest",
		"temp",
		"tmp",
		"ab",
		"aaa",
	}
	weakPasswords = []string{
		"*********",
		"a123456",
		"123456",
		"a*********",
		"*********0",
		"woaini1314",
		"qq123456",
		"abc123456",
		"123456a",
		"*********a",
		"*********",
		"zxcvbnm",
		"*********",
		"*********10",
		"abc123",
		"qq*********",
		"*********.",
		"7708801314520",
		"woaini",
		"5201314520",
		"q123456",
		"123456abc",
		"1233211234567",
		"*********",
		"123456.",
		"0*********",
		"asd123456",
		"aa123456",
		"*********",
		"q*********",
		"abcd123456",
		"*********00",
		"woaini520",
		"woaini123",
		"zxcvbnm123",
		"1111111111111111",
		"w123456",
		"aini1314",
		"abc*********",
		"111111",
		"woaini521",
		"qwertyuiop",
		"1314520520",
		"*********1",
		"qwe123456",
		"asd123",
		"000000",
		"*********0",
		"*********0",
		"789456123",
		"*********abc",
		"z123456",
		"*********9",
		"aaa123456",
		"abcd1234",
		"www123456",
		"*********q",
		"123abc",
		"qwe123",
		"w*********",
		"7894561230",
		"123456qq",
		"zxc123456",
		"*********qq",
		"1111111111",
		"111111111",
		"0000000000000000",
		"*********1234567",
		"qazwsxedc",
		"qwerty",
		"123456..",
		"zxc123",
		"asdfghjkl",
		"0000000000",
		"1234554321",
		"123456q",
		"123456aa",
		"*********0",
		"110120119",
		"qaz123456",
		"qq5201314",
		"123698745",
		"5201314",
		"000000000",
		"as123456",
		"123123",
		"5841314520",
		"z*********",
		"52013145201314",
		"a123123",
		"caonima",
		"a5201314",
		"wang123456",
		"abcd123",
		"*********..",
		"woaini1314520",
		"123456asd",
		"aa*********",
		"741852963",
		"a12345678",
	}
)

var weakPassVul = rules.Vulnerability{
	Name:     "Weak Password",
	VulXML:   "weak_password.xml",
	Severity: "high",
}

type _FormField struct {
	Name  string
	Value string
}

type _LoginForm struct {
	Action     string
	Method     string
	EncType    string
	Username   _FormField
	Password   _FormField
	CSRFToken  _FormField
	Captcha    _FormField
	CaptchaURL string
}

func (form *_LoginForm) MakeBody() []byte {
	args := fasthttp.AcquireArgs()
	args.Set(form.Username.Name, form.Username.Value)
	args.Set(form.Password.Name, form.Password.Value)
	if form.CSRFToken.Value != "" {
		args.Set(form.CSRFToken.Name, form.CSRFToken.Value)
	}
	if form.Captcha.Value != "" {
		args.Set(form.Captcha.Name, form.Captcha.Value)
	}
	return args.QueryString()
}

func parseLoginForm(body []byte) (*_LoginForm, error) {
	r := bytes.NewReader(body)
	doc, err := goquery.NewDocumentFromReader(r)
	if err != nil {
		return nil, err
	}
	var form *_LoginForm
	doc.Find("form").EachWithBreak(func(i int, s *goquery.Selection) bool {
		action := s.AttrOr("action", "")
		method := strings.ToUpper(s.AttrOr("method", "GET"))
		enctype := s.AttrOr("enctype", "application/x-www-form-urlencoded")

		if method == http.MethodPost {
			form = new(_LoginForm)
			form.Action = action
			form.Method = method
			form.EncType = enctype
			s.Find("input").Each(func(j int, child *goquery.Selection) {
				name := child.AttrOr("name", "")
				inputType := child.AttrOr("type", "text")
				lowerName := strings.ToLower(name)
				if name == "" {
					return
				}
				switch inputType {
				case "hidden":
					if strings.Contains(lowerName, "csrf") || strings.Contains(lowerName, "token") {
						form.CSRFToken.Name = name
						form.CSRFToken.Value = child.AttrOr("value", "")
					}
				case "password":
					form.Password.Name = name
				case "text":
					if strings.Contains(lowerName, "name") || strings.Contains(lowerName, "account") {
						form.Username.Name = name
					} else if strings.Contains(lowerName, "pass") || strings.Contains(lowerName, "pwd") {
						form.Password.Name = name
					} else {
						var parent *goquery.Selection
						var html string
						if len(child.Siblings().Nodes) < 1 {
							parent = child.Parent().Parent()
						} else {
							parent = child.Parent()
						}
						html, err = parent.Html()
						if err != nil {
							log.Errorln("failed to get parent html:", err)
						} else if strings.Contains(html, "验证码") {
							form.Captcha.Name = name
							parent.Find("img").EachWithBreak(func(k int, img *goquery.Selection) bool {
								src := img.AttrOr("src", "")
								if src != "" {
									form.CaptchaURL = src
									return false
								}
								return true
							})
						}
					}
				}
			})
			return form.Username.Name == "" || form.Password.Name == ""
		}
		return true
	})
	if form != nil {
		if form.Username.Name == "" || form.Password.Name == "" {
			return nil, nil
		}
	}
	return form, nil
}

type CaptchaServiceResult struct {
	Status     string
	Prediction string
}

func recognizeCaptcha(client *fasthttp.Client, serviceURL string, img []byte) (string, error) {
	var request = fasthttp.AcquireRequest()
	var response = fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	postBody := &bytes.Buffer{}
	writer := multipart.NewWriter(postBody)
	part, err := writer.CreateFormFile("file", "captcha.png")
	if err != nil {
		log.Errorln("failed to create from file:", err)
		return "", err
	}
	part.Write(img)
	err = writer.Close()
	if err != nil {
		log.Errorln("failed to close writer:", err)
		return "", err
	}

	request.Header.SetMethod(http.MethodPost)
	request.Header.SetRequestURI(serviceURL)
	request.Header.SetContentType(writer.FormDataContentType())
	request.SetBody(postBody.Bytes())

	err = client.DoTimeout(request, response, time.Second*10)
	if err != nil {
		log.Errorln("failed to request service:", err)
		return "", err
	}
	result := CaptchaServiceResult{}
	err = json.Unmarshal(response.Body(), &result)
	if err != nil {
		log.Errorln("failed to decode captcha service:", err, response.Body())
		return "", err
	}
	if result.Status == "OK" {
		prediction := strings.ToLower(result.Prediction)
		if strings.Contains(prediction, "|") {
			prediction = strings.SplitN(prediction, "|", 2)[0]
		}
		return prediction, nil
	}
	return "", nil
}

func parseCaptcha(client *fasthttp.Client, captchaURL string, cookiejar *myCookieJar, serviceURL string) (string, error) {
	var request = fasthttp.AcquireRequest()
	var response = fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(request)
	defer fasthttp.ReleaseResponse(response)

	request.Header.SetMethod(http.MethodGet)
	request.Header.SetRequestURI(captchaURL)
	cookiejar.CopyToRequest(request)

	err := utils.DoHTTPRequest(client, request, response)
	if err != nil {
		return "", err
	}

	cookiejar.CopyFromResponse(response)

	body, err := utils.GetOriginalBody(response)
	if err != nil {
		return "", err
	}
	return recognizeCaptcha(client, serviceURL, body)
}

func (scanner *WSScanner) scanWeakPass(link *AffectLink) {
	log.Infoln("scan weakpass for link:", link.URL)
	if scanner.specificVulXMLs != nil {
		if _, ok := scanner.specificVulXMLs["weak_password.xml"]; !ok {
			return
		}
	}

	requestCount, failedCount, passes := doScanWeakPass(link, scanner.Options.CaptchaService)

	if len(passes) > 0 {
		foundVul := FoundVul{
			Link:     link,
			Vul:      &weakPassVul,
			VulURL:   link.URL,
			Severity: weakPassVul.Severity,
			Context: map[string]interface{}{
				"valid": passes,
			},
		}
		result := ScanResult{
			RequestCount: requestCount,
			ErrorCount:   failedCount,
			FoundVuls:    []*FoundVul{&foundVul},
		}
		scanner.outputResult(&result)

	}
}

func doScanWeakPass(link *AffectLink, captchaServiceURL string) (int64, int64, []string) {
	client := newHTTPClient("360WS yunjiance Weak Password Scan")
	var (
		failedCount  int64
		requestCount int64
		passes       []string
	)

	if link.Method == http.MethodGet {
		var request = fasthttp.AcquireRequest()
		var response = fasthttp.AcquireResponse()
		defer fasthttp.ReleaseRequest(request)
		defer fasthttp.ReleaseResponse(response)

	Trying:
		for _, password := range weakPasswords {
			for _, username := range weakUsernames {
				linkCopy := link.DeepCopy()
				cookiejar := newMyCookieJar()
				defer cookiejar.Release()

				requestCount++
				request.Reset()
				response.Reset()
				request.Header.SetMethod(http.MethodGet)
				request.Header.SetRequestURI(linkCopy.URL)
				err := utils.DoHTTPRequest(client, request, response)

				if err != nil || response.StatusCode() != http.StatusOK {
					failedCount++
					log.Errorln("failed to request login page:", linkCopy.URL, err, response.StatusCode())
					goto Return
				}
				cookiejar.CopyFromResponse(response)

				body, err := utils.GetUtf8Body(response)
				if err != nil {
					log.Errorln("failed to get utf8 body of request", linkCopy.URL)
					goto Return
				}
				form, err := parseLoginForm(body)
				if err != nil {
					log.Errorln("failed to parse login form:", linkCopy.URL, err)
					goto Return
				}

				if form == nil {

					goto Return
				}
				form.Action, err = utils.URLJoin(linkCopy.URL, form.Action)
				if err != nil {
					log.Errorln("failed to URLJoin:", linkCopy.URL, form.Action)
					goto Return
				}
				if form.CaptchaURL != "" {
					form.CaptchaURL, err = utils.URLJoin(linkCopy.URL, form.CaptchaURL)
					if err != nil {
						log.Errorln("failed to URLJoin:", linkCopy.URL, form.CaptchaURL)
						goto Return
					}
				}

				time.Sleep(time.Second * 1)

				if failedCount > 10 {
					break Trying
				}
				form.Username.Value = username
				form.Password.Value = password
				if form.CaptchaURL != "" {
					requestCount++
					captcha, err := parseCaptcha(client, form.CaptchaURL, cookiejar, captchaServiceURL)
					if err != nil {
						log.Errorln("failed to parse captcha:", form.CaptchaURL, err)
						break Trying
					}
					form.Captcha.Value = captcha
				}
				request.Reset()
				response.Reset()
				request.Header.SetRequestURI(form.Action)
				request.Header.SetMethod(form.Method)
				request.Header.SetReferer(linkCopy.URL)
				if form.Method == http.MethodPost {
					body := form.MakeBody()
					request.SetBody(body)
				} else {
					log.Errorln("not supported form method.", linkCopy.URL, form.Action)
					goto Return
				}
				cookiejar.CopyToRequest(request)
				requestCount++
				err = client.DoTimeout(request, response, time.Second*10)
				if err != nil {
					log.Errorln("failed to request:", err, request)
					break Trying
				}
				status := response.StatusCode()
				if status >= 400 {
					break Trying
				}
				cookiejar.CopyFromResponse(response)
				body, err = utils.GetUtf8Body(response)
				if err != nil {
					log.Errorln("failed to get original body of request:", request, err)
					failedCount++
					continue
				}

				success := false

				if status == 302 {
					newURL := string(response.Header.Peek("Location"))
					if !strings.Contains(newURL, "err") && !strings.Contains(newURL, "fail") {

						request.Reset()
						response.Reset()
						newURL, _ = utils.URLJoin(form.Action, newURL)
						request.Header.SetRequestURI(newURL)
						request.Header.SetMethod(http.MethodGet)
						request.Header.SetReferer(form.Action)
						cookiejar.CopyToRequest(request)

						err = utils.DoHTTPRequest(client, request, response)
						if err != nil {
							log.Errorln("failed to get original body of request:", request, err)
							failedCount++
							continue
						}
						body, err = utils.GetUtf8Body(response)
						if err != nil {
							log.Errorln("failed to get original body of request:", request, err)
							failedCount++
							continue
						}
						success = true
					}

				} else if status == 200 {
					if bytes.Contains(body, []byte("成功")) || bytes.Contains(bytes.ToLower(body), []byte("success")) {
						success = true
					}
				}
				if success {

					form2, err := parseLoginForm(body)
					if err != nil {
						log.Errorln("failed to parse login form:", request, err)
						failedCount++
						continue
					}
					if form2 == nil {
						log.Infoln("success", username, password)
						passes = append(passes, form.Username.Value+":"+form.Password.Value)
					}
				}
			}
		}
	}

Return:
	return requestCount, failedCount, passes
}

func (scanner *WSScanner) scanWeakPassWithChrome(link *AffectLink) {
	log.Infoln("scan weakpass with Chrome for link:", link.URL)
	if link.Method == http.MethodGet {
		client := crawl.ChromeClient{
			Host:       "",
			Port:       9222,
			HTTPClient: new(http.Client),
		}
		for _, username := range weakUsernames {
			for _, password := range weakPasswords {
				log.Infoln("scan weakpass ", link.URL, username, password)
				tab, err := client.NewTab()
				if err != nil {
					log.Errorln("failed to create tab:", err)
					continue
				}
				defer client.CloseTab(tab.ID)
				defer tab.Close()
				tab.Init()
				err = tab.Connect()
				if err != nil {
					log.Errorln("failed to connect chrome:", err)
					continue
				}
				err = tab.InitProtocol()
				if err != nil {
					log.Errorln("chrome init protocol error.", link.URL, err)
					continue
				}
				tab.Navigate(&common.Link{URL: link.URL, Method: link.Method})

			}
		}
	}
}
