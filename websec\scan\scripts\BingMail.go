package scripts

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"golang.org/x/net/publicsuffix"
)

func BingMail(args *ScriptScanArgs) (*ScriptScanResult, error) {
	if ipRegex.MatchString(args.Host) {
		return &invulnerableResult, nil
	}
	rootDomain, err := publicsuffix.EffectiveTLDPlusOne(args.Host)
	if err != nil {
		return nil, err
	}
	keyword := fmt.Sprintf(`+"@%v"`, rootDomain)
	pat, err := regexp.Compile(`([\.a-zA-Z0-9_-]+<strong>@` + strings.Replace(rootDomain, ".", `\.`, -1) + ")")
	if err != nil {

		return nil, err
	}
	_, body, err := httpClient.GetTimeout(nil, "http://cn.bing.com/search?q="+keyword, 20*time.Second)
	if err != nil {

		return nil, err
	}

	results := map[string]bool{}
	for _, match := range pat.FindAll(body, -1) {
		results[strings.Replace(string(match), "<strong>", "", -1)] = true
	}

	if len(results) == 0 {

		return &invulnerableResult, nil
	}
	values := make([]string, 0, len(results))
	for k := range results {
		values = append(values, k)
	}
	return &ScriptScanResult{Vulnerable: true, Output: strings.Join(values, "|")}, nil
}

func init() {
	registerHandler("BingMail.xml", BingMail)
}
